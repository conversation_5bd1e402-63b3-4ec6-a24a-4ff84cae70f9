﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using YunDa.ISAS.Web.Core.Controllers;
using RouteAttribute = Microsoft.AspNetCore.Mvc.RouteAttribute;

namespace YunDa.ISAS.Web.MVC.Controllers
{
    public class DataMonitoringController : ISASControllerBase
    {
        public IActionResult DMAlarmCategory(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult Linkage(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult LinkageResult(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult TelecomDataConfigration(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult DeviceTelecomDataConfigration(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult TelemeteringAnalysis(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }

        public IActionResult TeleAlarmResult(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }

        public IActionResult TelesignalisationResult(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }

        public IActionResult TelecommandResult(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult TeleCommandPlan(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult SelfCheckingConfiguration(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult MultidimensionalCheckResult(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }
        public IActionResult EnergyManagement(bool isEdit = false)
        {
            ViewData["IsEdit"] = isEdit;
            return View();
        }


    }
}
