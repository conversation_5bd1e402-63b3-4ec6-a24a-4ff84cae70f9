﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace MqttWpfApp
{
    internal class TransferDllResolving
    {
        //方式1
        [DllImport("MqttDll1.dll", EntryPoint = "sumTest2")]
        public static extern int sumTest2(IntPtr ptrArr, int arrLenght, int resolveType,ref string data);
    }
}
