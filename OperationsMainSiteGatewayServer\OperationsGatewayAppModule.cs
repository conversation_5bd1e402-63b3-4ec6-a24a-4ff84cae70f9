﻿using Abp.AspNetCore;
using Abp.AspNetCore.Configuration;
using Abp.Dependency;
using Abp.Modules;
using Abp.Reflection.Extensions;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Configuration;
using YunDa.ISAS.Configuration;
using YunDa.ISAS.Core;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.EntityFrameworkCore.EntityFrameworkCore;
using YunDa.ISAS.MongoDB;
using YunDa.ISAS.MongoDB.Configuration;
using YunDa.ISAS.Redis;
using YunDa.ISAS.Redis.Configuration;
using YunDa.ISMS.BASE.Entities;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer
{
    [DependsOn(
      typeof(ISASCoreModule),
      typeof(ISASEntityFrameworkCoreModule),
      typeof(ISMS_BASEEntityFrameworkCoreModule),
      typeof(ISASMongoDBModule),
      typeof(ISASRedisModule)
    )]
    public class OperationsGatewayAppModule : AbpModule
    {
        public OperationsGatewayAppModule(ISASEntityFrameworkCoreModule isasEntityFrameworkModule)
        {
            isasEntityFrameworkModule.SkipDbSeed = true;

        }
        public override void PreInitialize()
        {
            // 预初始化阶段，可以进行一些配置
            Debug.WriteLine("PreInitialize: ABP Module Setup");
            var _appConfiguration = new ConfigurationBuilder()
             .AddJsonFile("appsettings.json")
             .Build();
            // 后初始化阶段
            Debug.WriteLine("PostInitialize: ABP Module Post Initialization");
            Configuration.DefaultNameOrConnectionString = _appConfiguration.GetConnectionString(
               ISASConsts.ConnectionMysqlStringKey
           );

            // 输出时间格式
            Debug.WriteLine("MySql数据库连接：" + _appConfiguration.GetConnectionString(ISASConsts.ConnectionMysqlStringKey));

            #region 设置MogngoDBConfig

            var mongoConfiguration = Configuration.Modules.ISASConfiguration<IMongoDBConfiguration>();
            ConfigurationHelper.SetMongoDBConfiguration(_appConfiguration, ref mongoConfiguration);
            Console.WriteLine("MongoDB数据库连接：" + mongoConfiguration.HostString + ":" + mongoConfiguration.Port);

            #endregion 设置MogngoDBConfig

            #region 设置redisconfig
            var redisConfiguration = Configuration.Modules.ISASConfiguration<IRedisConfiguration>();
            ConfigurationHelper.SetRedisConfiguration(_appConfiguration, ref redisConfiguration);
            Console.WriteLine("Redis数据库连接：" + redisConfiguration.Host + ":" + redisConfiguration.Port);
            #endregion

            
        }

        public override void Initialize()
        {
            // 初始化阶段
            IocManager.RegisterAssemblyByConvention(typeof(OperationsGatewayAppModule).GetAssembly());
            Console.WriteLine("Initialize: ABP Module Initialized");
            //ServiceCollectionRegistrar.Register(IocManager);
        }

        public override void PostInitialize()
        {
            
        }

        public override void Shutdown()
        {
            // 关闭时调用
            Console.WriteLine("Shutdown: ABP Module Shut down");
        }
    }
}
