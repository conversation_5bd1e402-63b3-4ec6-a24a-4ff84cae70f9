﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImManufacturer
    {
        public ImManufacturer()
        {
            ImAsdu140Fltacttype = new HashSet<ImAsdu140Fltacttype>();
            ImAsdu140Fltcurrinfo = new HashSet<ImAsdu140Fltcurrinfo>();
            ImAsdu142info = new HashSet<ImAsdu142info>();
            ImCtrlworddef = new HashSet<ImCtrlworddef>();
            ImDevicedzenum = new HashSet<ImDevicedzenum>();
            ImPuCtgy = new HashSet<ImPuCtgy>();
        }

        public string Manucode { get; set; }
        public string Manuname { get; set; }

        public virtual ICollection<ImAsdu140Fltacttype> ImAsdu140Fltacttype { get; set; }
        public virtual ICollection<ImAsdu140Fltcurrinfo> ImAsdu140Fltcurrinfo { get; set; }
        public virtual ICollection<ImAsdu142info> ImAsdu142info { get; set; }
        public virtual ICollection<ImCtrlworddef> ImCtrlworddef { get; set; }
        public virtual ICollection<ImDevicedzenum> ImDevicedzenum { get; set; }
        public virtual ICollection<ImPuCtgy> ImPuCtgy { get; set; }
    }
}
