﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDevicedz
    {
        public string Id { get; set; }
        public string Deviceid { get; set; }
        public int CpuIndex { get; set; }
        public int DzIndex { get; set; }
        public string DzName { get; set; }
        public string DzComment { get; set; }
        public string DzRange { get; set; }
        public double? DzMin { get; set; }
        public double? DzMax { get; set; }
        public string DzUnit { get; set; }
        public double? Dz<PERSON>oeff { get; set; }
        public int? DzPrecise { get; set; }
        public string DzUnit1 { get; set; }
        public double? DzCoeff1 { get; set; }
        public int? DzPrecise1 { get; set; }
        public int? CtrlwordTypeid { get; set; }
        public int DzType { get; set; }
        public int? EnumTypeid { get; set; }
        public double? DzUnitcvtCoeff { get; set; }
        public string RelateptId { get; set; }
        public string RelatectId { get; set; }
        public int Readonly { get; set; }
        public string Hidden { get; set; }

        public virtual ImProtectdevice Device { get; set; }
        public virtual ImDztype DzTypeNavigation { get; set; }
    }
}
