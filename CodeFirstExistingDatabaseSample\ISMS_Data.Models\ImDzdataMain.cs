﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_Data.Models
{
    public partial class ImDzdataMain
    {
        public ImDzdataMain()
        {
            ImDzdataDetails = new HashSet<ImDzdataDetail>();
        }

        public string DzdataMainId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int ZoneNo { get; set; }
        public DateTime CreateTime { get; set; }
        public string UserName { get; set; } = null!;

        public virtual ICollection<ImDzdataDetail> ImDzdataDetails { get; set; }
    }
}
