﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImFaultacttype20102
    {
        public int Puctgycode { get; set; }
        public int Actcode { get; set; }
        public string Actname { get; set; }

        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
    }
}
