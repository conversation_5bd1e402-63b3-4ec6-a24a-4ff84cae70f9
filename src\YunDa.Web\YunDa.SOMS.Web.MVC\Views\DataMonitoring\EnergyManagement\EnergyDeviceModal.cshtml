<div class="modal inmodal fade" id="energyDeviceModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">关闭</span></button>
                <h4 class="modal-title">{{header}}</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="editEnergyDeviceForm">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="seqNo">序号：</label>
                                <div class="col-sm-9">
                                    <input type="number" class="form-control" placeholder="请输入序号" v-model.number="seqNo" name="seqNo">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label">设备类型：</label>
                                <div class="col-sm-9">
                                    <vue-chosen :placeholder="'请选择设备类型'" :default_value="deviceType" :options="deviceTypes" v-on:change="setDeviceType" name="deviceType"></vue-chosen>
                                </div>
                            </div> 
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label">关联设备：</label>
                                <div class="col-sm-9">
                                    <vue-chosen :placeholder="'请选择关联设备'" :default_value="equipmentInfoId" :options="equipmentInfos" v-on:change="setEquipmentInfoId" name="equipmentInfoId"></vue-chosen>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label"><span class="text-danger">*</span>设备状态：</label>
                                <div class="col-sm-9">
                                    <vue-chosen :placeholder="'请选择设备状态'" :default_value="status" :options="statusOptions" v-on:change="setStatus" name="status"></vue-chosen>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">是否投运：</label>
                                <div class="col-sm-9">
                                    <div class="checkbox checkbox-primary">
                                        <input type="checkbox" v-model="isOperating">
                                        <label></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="name"><span class="text-danger">*</span>名称：</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" placeholder="请输入名称" v-model="name" name="name">
                                </div>
                            </div>
                           
                            <div class="form-group">
                                <label class="col-sm-3 control-label">最后投运时间：</label>
                                <div class="col-sm-9">
                                    <vue-datepicker :time-format="'yyyy-mm-dd hh:mm:ss'" :default-date="lastOperationTime" name="lastOperationTime" v-on:change="setLastOperationTime"></vue-datepicker>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">监测配置：</label>
                                <div class="col-sm-9">
                                    <textarea class="form-control" rows="3" placeholder="JSON格式配置，留空使用默认配置" v-model="monitoringConfig" name="monitoringConfig"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">是否启用：</label>
                                <div class="col-sm-9">
                                    <div class="checkbox checkbox-primary">
                                        <input type="checkbox" v-model="isActive">
                                        <label></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">备注：</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="2" placeholder="请输入备注" v-model="remark" name="remark"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-white" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" v-on:click="save">保存</button>
            </div>
        </div>
    </div>
</div>
