﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImStation
    {
        public ImStation()
        {
            ImGateway = new HashSet<ImGateway>();
            ImNoticeboard = new HashSet<ImNoticeboard>();
            ImProtectdevice = new HashSet<ImProtectdevice>();
            ImShebei = new HashSet<ImShebei>();
            ImVariantBool = new HashSet<ImVariantBool>();
        }

        public string Statcode { get; set; }
        public string Prjid { get; set; }
        public string Statname { get; set; }
        public int Seqno { get; set; }

        public virtual ImProject Prj { get; set; }
        public virtual ICollection<ImGateway> ImGateway { get; set; }
        public virtual ICollection<ImNoticeboard> ImNoticeboard { get; set; }
        public virtual ICollection<ImProtectdevice> ImProtectdevice { get; set; }
        public virtual ICollection<ImShebei> ImShebei { get; set; }
        public virtual ICollection<ImVariantBool> ImVariantBool { get; set; }
    }
}
