﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISMSTcpCmdWpfAppDemo
{
    // 精简版定义
    // 精简版定义
    public readonly struct ProcessResult
    {
        public bool IsValid { get; }
        public bool IsJArray { get; }
        public string JsonData { get; }
        public int ConsumedBytes { get; }
        public string? ErrorMessage { get; }

        public static ProcessResult Success(bool isJArray, string json, int consumed)
            => new(true, isJArray, json, consumed, null);

        public static ProcessResult Error(string message, int consumed)
            => new(false, false, null, consumed, message);

        private ProcessResult(bool isValid, bool isJArray, string? json, int consumed, string? error)
        {
            IsValid = isValid;
            IsJArray = isJArray;
            JsonData = json ?? string.Empty;
            ConsumedBytes = consumed;
            ErrorMessage = error;
        }
    }

}
