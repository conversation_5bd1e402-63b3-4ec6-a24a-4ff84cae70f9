﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServerTempDB.Models
{
    public partial class ExecutionCache
    {
        public Guid ExecutionCacheId { get; set; }
        public Guid ReportId { get; set; }
        public int ExpirationFlags { get; set; }
        public DateTime? AbsoluteExpiration { get; set; }
        public int? RelativeExpiration { get; set; }
        public Guid SnapshotDataId { get; set; }
        public DateTime LastUsedTime { get; set; }
        public int ParamsHash { get; set; }
    }
}
