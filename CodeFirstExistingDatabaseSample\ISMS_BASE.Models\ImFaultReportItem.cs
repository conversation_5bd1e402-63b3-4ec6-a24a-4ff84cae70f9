﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFaultReportItem
    {
        public ImFaultReportItem()
        {
            ImPuctgyFltRptItems = new HashSet<ImPuctgyFltRptItem>();
        }

        public string ItemName { get; set; } = null!;
        public int SeqNo { get; set; }

        public virtual ICollection<ImPuctgyFltRptItem> ImPuctgyFltRptItems { get; set; }
    }
}
