﻿using Configuration;
using System;
using System.Runtime.InteropServices;
using System.Text;

namespace Client104Side
{
    public class ICE104CommunicationCallback
    {
        public static bool Started = false;

        private static CLN104_PARAM _cLN104_PARAM = new CLN104_PARAM() { dev_type = 0 };
        /// <summary>
        /// 启动成功回调委托
        /// </summary>
        /// <param name="pContext"></param>
        /// <returns></returns>
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate int CALLBACK_DEV_ON(byte pContext);
        /// <summary>
        /// 启动成功回调声明
        /// </summary>
        public static CALLBACK_DEV_ON callbackOn = CALLBACK_DEV_ONCallback;

        /// <summary>
        /// 启动成功回调函数
        /// </summary>
        /// <param name="pContext"></param>
        /// <returns></returns>
        public static int CALLBACK_DEV_ONCallback(byte pContext)
        {
            Started = true;
            return 0;
        }

        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate int CALLBACK_DEV_OFF(byte pContext);

        public static CALLBACK_DEV_OFF callbackOff = CALLBACK_DEV_OffCallback;
        private static bool IsManualStop = false;
        /// <summary>
        /// 断开连接回调函数
        /// </summary>
        /// <param name="pContext"></param>
        /// <returns></returns>
        public static int CALLBACK_DEV_OffCallback(byte pContext)
        {
            Started = false;
            return 0;
        }

        private static byte[] Get_Dev_20bit_Ip_Array(string ip)
        {
            if (string.IsNullOrEmpty(ip))
                return null;
            var ips = Encoding.UTF8.GetBytes(ip);
            var bytes = new byte[20];
            for (int i = 0; i < 20; i++)
            {
                if (i < ips.Length)
                {
                    bytes[i] = ips[i];
                }
            }
            return bytes;
        }
        private static CommDeviceConfig commDeviceConfig = null;
        public static bool Start(CommDeviceConfig para)
        {
            IsManualStop = false;
            commDeviceConfig = para;
            _cLN104_PARAM.dev_ip = Get_Dev_20bit_Ip_Array(para.IP);
            _cLN104_PARAM.dev_port = (ushort)para.Port;
            _cLN104_PARAM.dev_addr = (byte)para.Address;
            _cLN104_PARAM.dev_yc_num = (uint)para.TelemeteringCount;
            _cLN104_PARAM.dev_yc_start = (uint)para.TelemeteringStartAddress;
            _cLN104_PARAM.dev_yx_num = (uint)para.TelesignalisationCount;
            _cLN104_PARAM.dev_yx_start = (uint)para.TelesignalisationStartAddress;
            _cLN104_PARAM.dev_dd_num = (uint)para.ElectricalDegreeCount;
            _cLN104_PARAM.dev_dd_start = (uint)para.ElectricalDegreeStartAddress;
            try
            {
                int res = ICE104EndPointController.Iec104clnStart(_cLN104_PARAM, callbackOn, callbackOff);
                return res == 0;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return false;

        }

        public static bool Stop()
        {
            IsManualStop = true;
            try
            {
                int res = res = ICE104EndPointController.Iec104ClnEnd();
                return res == 0;
            }
            catch// (Exception ex)
            {
                return false;
            }
        }
    }
}