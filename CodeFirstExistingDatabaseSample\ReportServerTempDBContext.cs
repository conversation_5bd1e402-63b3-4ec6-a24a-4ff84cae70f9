﻿using System;
using System.Collections.Generic;
using CodeFirstExistingDatabaseSample.ReportServerTempDB.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace CodeFirstExistingDatabaseSample
{
    public partial class ReportServerTempDBContext : DbContext
    {
        public ReportServerTempDBContext()
        {
        }

        public ReportServerTempDBContext(DbContextOptions<ReportServerTempDBContext> options)
            : base(options)
        {
        }

        public virtual DbSet<ChunkDatum> ChunkData { get; set; } = null!;
        public virtual DbSet<ChunkSegmentMapping> ChunkSegmentMappings { get; set; } = null!;
        public virtual DbSet<DbupgradeHistory> DbupgradeHistories { get; set; } = null!;
        public virtual DbSet<ExecutionCache> ExecutionCaches { get; set; } = null!;
        public virtual DbSet<PersistedStream> PersistedStreams { get; set; } = null!;
        public virtual DbSet<Segment> Segments { get; set; } = null!;
        public virtual DbSet<SegmentedChunk> SegmentedChunks { get; set; } = null!;
        public virtual DbSet<SessionDatum> SessionData { get; set; } = null!;
        public virtual DbSet<SessionLock> SessionLocks { get; set; } = null!;
        public virtual DbSet<SnapshotDatum> SnapshotData { get; set; } = null!;
        public virtual DbSet<TempCatalog> TempCatalogs { get; set; } = null!;
        public virtual DbSet<TempDataSet> TempDataSets { get; set; } = null!;
        public virtual DbSet<TempDataSource> TempDataSources { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {

                optionsBuilder.UseSqlServer("Server=192.168.110.161;User ID=**;Password=**;Database=ReportServerTempDB;Trusted_Connection=False;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("Latin1_General_CI_AS_KS_WS");

            modelBuilder.Entity<ChunkDatum>(entity =>
            {
                entity.HasKey(e => e.ChunkId)
                    .IsClustered(false);

                entity.HasIndex(e => new { e.SnapshotDataId, e.ChunkType, e.ChunkName }, "IX_ChunkData")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.ChunkId)
                    .ValueGeneratedNever()
                    .HasColumnName("ChunkID");

                entity.Property(e => e.ChunkName).HasMaxLength(260);

                entity.Property(e => e.Content).HasColumnType("image");

                entity.Property(e => e.MimeType).HasMaxLength(260);

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");
            });

            modelBuilder.Entity<ChunkSegmentMapping>(entity =>
            {
                entity.HasKey(e => new { e.ChunkId, e.SegmentId });

                entity.ToTable("ChunkSegmentMapping");

                entity.HasIndex(e => e.SegmentId, "IX_ChunkSegmentMapping_SegmentId");

                entity.HasIndex(e => new { e.ChunkId, e.StartByte }, "UNIQ_ChunkId_StartByte")
                    .IsUnique();
            });

            modelBuilder.Entity<DbupgradeHistory>(entity =>
            {
                entity.HasKey(e => e.UpgradeId);

                entity.ToTable("DBUpgradeHistory");

                entity.Property(e => e.UpgradeId).HasColumnName("UpgradeID");

                entity.Property(e => e.DateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DbVersion).HasMaxLength(25);

                entity.Property(e => e.User)
                    .HasMaxLength(128)
                    .HasDefaultValueSql("(suser_sname())");
            });

            modelBuilder.Entity<ExecutionCache>(entity =>
            {
                entity.HasKey(e => e.ExecutionCacheId)
                    .IsClustered(false);

                entity.ToTable("ExecutionCache");

                entity.HasIndex(e => new { e.ReportId, e.ParamsHash, e.AbsoluteExpiration }, "IX_CacheLookup");

                entity.HasIndex(e => new { e.AbsoluteExpiration, e.ReportId, e.SnapshotDataId }, "IX_ExecutionCache")
                    .IsUnique()
                    .IsClustered();

                entity.HasIndex(e => e.SnapshotDataId, "IX_SnapshotDataID");

                entity.Property(e => e.ExecutionCacheId)
                    .ValueGeneratedNever()
                    .HasColumnName("ExecutionCacheID");

                entity.Property(e => e.AbsoluteExpiration).HasColumnType("datetime");

                entity.Property(e => e.LastUsedTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");
            });

            modelBuilder.Entity<PersistedStream>(entity =>
            {
                entity.HasKey(e => new { e.SessionId, e.Index });

                entity.ToTable("PersistedStream");

                entity.Property(e => e.SessionId)
                    .HasMaxLength(32)
                    .IsUnicode(false)
                    .HasColumnName("SessionID");

                entity.Property(e => e.Content).HasColumnType("image");

                entity.Property(e => e.Encoding).HasMaxLength(260);

                entity.Property(e => e.Error).HasMaxLength(512);

                entity.Property(e => e.ExpirationDate).HasColumnType("datetime");

                entity.Property(e => e.Extension).HasMaxLength(260);

                entity.Property(e => e.MimeType).HasMaxLength(260);

                entity.Property(e => e.Name).HasMaxLength(260);
            });

            modelBuilder.Entity<Segment>(entity =>
            {
                entity.ToTable("Segment");

                entity.HasIndex(e => e.SegmentId, "IX_SegmentMetadata")
                    .IsUnique();

                entity.Property(e => e.SegmentId).HasDefaultValueSql("(newsequentialid())");
            });

            modelBuilder.Entity<SegmentedChunk>(entity =>
            {
                entity.ToTable("SegmentedChunk");

                entity.HasIndex(e => new { e.ChunkId, e.SnapshotDataId }, "IX_ChunkId_SnapshotDataId");

                entity.HasIndex(e => new { e.SnapshotDataId, e.ChunkType, e.ChunkName }, "UNIQ_SnapshotChunkMapping")
                    .IsUnique();

                entity.Property(e => e.ChunkId).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.ChunkName).HasMaxLength(260);

                entity.Property(e => e.Machine).HasMaxLength(512);

                entity.Property(e => e.MimeType).HasMaxLength(260);
            });

            modelBuilder.Entity<SessionDatum>(entity =>
            {
                entity.HasNoKey();

                entity.HasIndex(e => e.SessionId, "IDX_SessionData")
                    .IsUnique()
                    .IsClustered();

                entity.HasIndex(e => e.EditSessionId, "IX_EditSessionID");

                entity.HasIndex(e => e.Expiration, "IX_SessionCleanup");

                entity.HasIndex(e => e.SnapshotDataId, "IX_SessionSnapshotID");

                entity.Property(e => e.CreationTime).HasColumnType("datetime");

                entity.Property(e => e.DataSourceInfo).HasColumnType("image");

                entity.Property(e => e.EditSessionId)
                    .HasMaxLength(32)
                    .IsUnicode(false)
                    .HasColumnName("EditSessionID");

                entity.Property(e => e.EffectiveParams).HasColumnType("ntext");

                entity.Property(e => e.Expiration).HasColumnType("datetime");

                entity.Property(e => e.HistoryDate).HasColumnType("datetime");

                entity.Property(e => e.OwnerId).HasColumnName("OwnerID");

                entity.Property(e => e.ReportDefinitionPath).HasMaxLength(464);

                entity.Property(e => e.ReportPath).HasMaxLength(464);

                entity.Property(e => e.SessionId)
                    .HasMaxLength(32)
                    .IsUnicode(false)
                    .HasColumnName("SessionID");

                entity.Property(e => e.ShowHideInfo).HasColumnType("image");

                entity.Property(e => e.SitePath).HasMaxLength(440);

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");

                entity.Property(e => e.SnapshotExpirationDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<SessionLock>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("SessionLock");

                entity.HasIndex(e => e.SessionId, "IDX_SessionLock")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.SessionId)
                    .HasMaxLength(32)
                    .IsUnicode(false)
                    .HasColumnName("SessionID");
            });

            modelBuilder.Entity<SnapshotDatum>(entity =>
            {
                entity.HasNoKey();

                entity.HasIndex(e => new { e.PermanentRefcount, e.ExpirationDate }, "IS_SnapshotExpiration");

                entity.HasIndex(e => new { e.PermanentRefcount, e.TransientRefcount }, "IX_SnapshotCleaning");

                entity.HasIndex(e => new { e.SnapshotDataId, e.ParamsHash }, "IX_SnapshotData")
                    .IsClustered();

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Description).HasMaxLength(512);

                entity.Property(e => e.EffectiveParams).HasColumnType("ntext");

                entity.Property(e => e.ExpirationDate).HasColumnType("datetime");

                entity.Property(e => e.IsCached).HasDefaultValueSql("((0))");

                entity.Property(e => e.Machine).HasMaxLength(512);

                entity.Property(e => e.QueryParams).HasColumnType("ntext");

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");
            });

            modelBuilder.Entity<TempCatalog>(entity =>
            {
                entity.HasKey(e => new { e.EditSessionId, e.ContextPath });

                entity.ToTable("TempCatalog");

                entity.HasIndex(e => e.ExpirationTime, "IX_Cleanup");

                entity.HasIndex(e => e.TempCatalogId, "UNIQ_TempCatalogID")
                    .IsUnique();

                entity.Property(e => e.EditSessionId)
                    .HasMaxLength(32)
                    .IsUnicode(false)
                    .HasColumnName("EditSessionID");

                entity.Property(e => e.ContextPath).HasMaxLength(425);

                entity.Property(e => e.CreationTime).HasColumnType("datetime");

                entity.Property(e => e.DataCacheHash).HasMaxLength(64);

                entity.Property(e => e.ExpirationTime).HasColumnType("datetime");

                entity.Property(e => e.Name).HasMaxLength(425);

                entity.Property(e => e.OwnerId).HasColumnName("OwnerID");

                entity.Property(e => e.TempCatalogId).HasColumnName("TempCatalogID");
            });

            modelBuilder.Entity<TempDataSet>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .HasName("PK_TempDataSet")
                    .IsClustered(false);

                entity.HasIndex(e => e.LinkId, "IX_DataSetLinkID");

                entity.HasIndex(e => new { e.ItemId, e.Name }, "IX_TempDataSet_ItemID_Name")
                    .IsClustered();

                entity.Property(e => e.Id)
                    .ValueGeneratedNever()
                    .HasColumnName("ID");

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.LinkId).HasColumnName("LinkID");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.TempDataSets)
                    .HasPrincipalKey(p => p.TempCatalogId)
                    .HasForeignKey(d => d.ItemId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DataSetItemID");
            });

            modelBuilder.Entity<TempDataSource>(entity =>
            {
                entity.HasKey(e => e.Dsid)
                    .HasName("PK_DataSource");

                entity.HasIndex(e => e.ItemId, "IX_DataSourceItemID");

                entity.Property(e => e.Dsid)
                    .ValueGeneratedNever()
                    .HasColumnName("DSID");

                entity.Property(e => e.ConnectionString).HasColumnType("image");

                entity.Property(e => e.Extension).HasMaxLength(260);

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.Property(e => e.OriginalConnectionString).HasColumnType("image");

                entity.Property(e => e.Password).HasColumnType("image");

                entity.Property(e => e.Prompt).HasColumnType("ntext");

                entity.Property(e => e.UserName).HasColumnType("image");

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.TempDataSources)
                    .HasPrincipalKey(p => p.TempCatalogId)
                    .HasForeignKey(d => d.ItemId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DataSourceItemID");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
