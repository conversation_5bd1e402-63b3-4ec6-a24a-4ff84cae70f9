﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDiagram
    {
        public string Id { get; set; } = null!;
        public string PrjId { get; set; } = null!;
        public int? Width { get; set; }
        public int? Height { get; set; }
        public int? GridSize { get; set; }
        public short? ShowGrid { get; set; }
        public double? Scale { get; set; }
        public int? PwrOnColor { get; set; }
        public int? PwrOffColor { get; set; }
        public int? SwitchOnColor { get; set; }
        public int? SwitchOffColor { get; set; }
        public int? SwitchUnknownColor { get; set; }
        public DateTime? CreateTime { get; set; }
        public string? Creator { get; set; }

        public virtual ImGlyph IdNavigation { get; set; } = null!;
        public virtual ImProject Prj { get; set; } = null!;
    }
}
