﻿using System;
using System.Collections.Generic;
using System.Text;

namespace YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto
{
    public class RealTimePower
    {
        public IEnumerable<FloatTimeOutput> ActivePower { get; set; }
        public IEnumerable<FloatTimeOutput> ReactivePower { get; set; }

        public RealTimePowerTypeEnum RealTimePowerType { get; set; }

    }

    public enum RealTimePowerTypeEnum
    {

        /*提示：统计历史数据时，实时的按照每秒一个点
         *按小时的，按照每分钟一个点
         *按天的，按照每小时一个点
         *按周的，按照每天一个点
         *按月的，按照每天一点
         *按年的，按照每月一个点
         */

        /*时间格式序列化提示：
         实时的，按照 mm:ss
         小时的，按照 HH:mm
         按天的 按照 MM:dd HH
        按月的 按照 yyyyMMdd
        按年的 按照 yyyyMM
         */

        /// <summary>
        /// 实时功率（秒级或毫秒级数据）
        /// </summary>
        RealTime =0,

        /// <summary>
        /// 按小时统计的功率数据
        /// </summary>
        Hourly =1,

        /// <summary>
        /// 按天统计的功率数据
        /// </summary>
        Daily=2,

        /// <summary>
        /// 按周统计的功率数据
        /// </summary>
        Weekly=3,

        /// <summary>
        /// 按月统计的功率数据
        /// </summary>
        Monthly=4,

        /// <summary>
        /// 按年统计的功率数据
        /// </summary>
        Yearly=5
    }

    /// <summary>
    /// 能耗分布数据
    /// </summary>
    public class PowerConsumptionDistribution
    {
        /// <summary>
        /// 变压器名称列表
        /// </summary>
        public List<string> names { get; set; } = new List<string>();

        /// <summary>
        /// 能耗数据项列表
        /// </summary>
        public List<PowerConsumptionItem> data { get; set; } = new List<PowerConsumptionItem>();

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum IntervalType { get; set; }
    }

    /// <summary>
    /// 能耗数据项
    /// </summary>
    public class PowerConsumptionItem
    {
        /// <summary>
        /// 能耗值
        /// </summary>
        public float value { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string name { get; set; }
    }

    /// <summary>
    /// 变电所能耗数据
    /// </summary>
    public class SubstationEnergyData
    {
        /// <summary>
        /// 时间标签数组
        /// </summary>
        public List<string> Times { get; set; } = new List<string>();

        /// <summary>
        /// 有功能耗数组（kWh）
        /// </summary>
        public List<float> ActiveEnergies { get; set; } = new List<float>();

        /// <summary>
        /// 无功能耗数组（kVarh）
        /// </summary>
        public List<float> ReactiveEnergies { get; set; } = new List<float>();

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum IntervalType { get; set; }
    }
}
