﻿using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class Program
{
    static async Task Main()
    {
        using TcpClient client = new TcpClient();
        await client.ConnectAsync("127.0.0.1", 43916);
        var stream = client.GetStream();

        _ = Task.Run(() => ReceiveMessages(stream));

        while (true)
        {
            string input = Console.ReadLine();
            if (input.ToLower() == "exit") break;

            byte[] data = Encoding.UTF8.GetBytes(input);
            await stream.WriteAsync(data, 0, data.Length);
        }
    }

    static async Task ReceiveMessages(NetworkStream stream)
    {
        byte[] buffer = new byte[1024];
        while (true)
        {
            int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
            if (bytesRead == 0) break;
            Console.WriteLine($"接收: {Encoding.UTF8.GetString(buffer, 0, bytesRead)}");
        }
    }
}
