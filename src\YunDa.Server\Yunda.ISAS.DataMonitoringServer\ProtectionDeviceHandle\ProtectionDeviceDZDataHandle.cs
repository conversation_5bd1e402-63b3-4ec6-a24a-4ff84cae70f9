﻿using Abp.Dependency;
using Google.Protobuf.WellKnownTypes;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis;
using Yunda.SOMS.DataMonitoringServer.FTPHandle;
using Yunda.SOMS.DataMonitoringServer.TcpSocket.Server;
using YunDa.ISAS.Redis.Entities.LinkageCategory;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionSettingDto;

namespace Yunda.SOMS.DataMonitoringServer.ProtectionDeviceHandle
{
    public class ProtectionDeviceDZDataHandle : ISingletonDependency
    {
        WebApiRequest _webApiRequest;
        public IRedisRepository<ImDeviceDzOutput, string> _imDeviceDzRedis;

        FtpFile _ftpFile;
        DotNettyTcpServer _dotNettyTcpServer;
        Dictionary<byte,List<ImDeviceDzOutput>> systemDzs = new Dictionary<byte, List<ImDeviceDzOutput>>();
        Dictionary<byte, List<ImDeviceDzOutput>> userDzs = new Dictionary<byte, List<ImDeviceDzOutput>>();

        public ProtectionDeviceDZDataHandle(
            FtpFile ftpFile, 
            DotNettyTcpServer dotNettyTcpServer
            ,WebApiRequest webApiRequest
            , IRedisRepository<ImDeviceDzOutput, string> imDeviceDzRedis)
        {
            _webApiRequest = webApiRequest;
            _ftpFile = ftpFile;
            _dotNettyTcpServer = dotNettyTcpServer;
            _dotNettyTcpServer.MessageReceived += OnMessageReceived; // 订阅事件
            _imDeviceDzRedis = imDeviceDzRedis;
        }
        public void Init(List<ProtectionDeviceCommInfoOutput> divices)
        {
            try
            {
                systemDzs.Clear();
                userDzs.Clear();
                foreach (var item in divices)
                {
                    //系统定值
                    var tempSystemDZs = _webApiRequest.GetDzByDeviceAddr(item.DeviceAddr, 9);
                    systemDzs.Add((byte)item.DeviceAddr, tempSystemDZs);
                    //用户定值
                    var tempUserDZs = _webApiRequest.GetDzByDeviceAddr(item.DeviceAddr, 1);
                    userDzs.Add((byte)item.DeviceAddr, tempUserDZs);

                }
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler(ex.StackTrace, "定值错误信息");
            }

        }
        private object locker = new object();
        private void OnMessageReceived(byte address, byte[] message, byte functionType)
        {

            //>> 用户参数整定成功 1002
            //>> 系统参数整定成功 1004
            //>> 插件参数整定成功 1044
            lock (locker)
            {
                if (functionType == 1) //装置用户定值信息报文
                {

                    string messageStr = BitConverter.ToString(message);
                    string msg = $"装置地址: {address}, 功能码: {functionType}, 消息: {messageStr}\n";
                    MonitoringEventBus.LogHandler(msg, "103客户端信息");
                    if (!userDzs.ContainsKey(address))
                    {
                        MonitoringEventBus.LogHandler("装置定值信息不存在", "103客户端信息");
                        return;
                    }
                    var userDzList = userDzs[address];
                    if (userDzList!=null)
                    {
                        try
                        {
                            short msgFlag = BitConverter.ToInt16(message, 0);
                            if (msgFlag == 1004)
                            {
                                Console.WriteLine(1004);
                                //TODO:下发定值获取命令
                                _dotNettyTcpServer.SendMessageByIp(address, 0, functionType, 0);
                            }
                            if (message.Length >= 5)
                            {
                                byte[] data = message;
                                for (int i = 0; i < data.Length; i += 5)
                                {
                                    if (i / 5 > userDzList.Count - 1)
                                    {
                                        MonitoringEventBus.LogHandler("错误！！！  越界", "103客户端信息");
                                        continue;

                                    }
                                    ImDeviceDzOutput imDeviceDzOutput = userDzList[i / 5];

                                    if (imDeviceDzOutput == null)
                                    {
                                        MonitoringEventBus.LogHandler("错误！！！  未找到定值源", "103客户端信息");
                                        continue;
                                    }
                                    byte[] valueBytes = new byte[4];
                                    Array.Copy(data, i, valueBytes, 0, 4); // 前四个字节表示数值
                                    byte type = data[i + 4]; // 第五个字节表示类型

                                    if (type == 5) // 整数
                                    {
                                        int intValue = BitConverter.ToInt32(valueBytes, 0);
                                        MonitoringEventBus.LogHandler($"定值名称：{imDeviceDzOutput.DzComment} 整数: {intValue}", "定值信息");

                                        imDeviceDzOutput.Value = intValue.ToString();
                                    }
                                    else if (type == 7) // 浮点数
                                    {
                                        float floatValue = BitConverter.ToSingle(valueBytes, 0);
                                        MonitoringEventBus.LogHandler($"定值名称：{imDeviceDzOutput.DzComment} 浮点: {floatValue}", "定值信息");
                                        imDeviceDzOutput.Value = floatValue.ToString();
                                    }
                                    else
                                    {
                                        MonitoringEventBus.LogHandler($"定值名称：{imDeviceDzOutput.DzComment} 错误！！！  类型未知", "定值信息");

                                    }
                                }
                                _imDeviceDzRedis.HashSetUpdateManyAsync($"DZ_{address.ToString("X2")}_User", userDzList.Select(t => t.Id).ToList(), userDzList);

                            }
                        }
                        catch (Exception ex)
                        {
                            MonitoringEventBus.LogHandler(ex.StackTrace, "定值错误信息");
                        }
                    }
                    else
                    {
                        MonitoringEventBus.LogHandler($"获取用户定值信息失败,地址：{address}", "103客户端信息");
                    }


                }
                else if (functionType == 2)//装置厂家定值信息报文
                {
                    string messageStr = BitConverter.ToString(message);
                    string msg = $"装置地址: {address}, 功能码: {functionType}, 消息: {messageStr}\n";
                    MonitoringEventBus.LogHandler(msg, "103客户端信息");
                    if (!systemDzs.ContainsKey(address))
                    {
                        MonitoringEventBus.LogHandler($"装置定值信息不存在,地址：{address}", "103客户端信息");
                        return;
                    }
                    var systemDzList = systemDzs[address];
                    if (systemDzList!=null)
                    {
                        try
                        {
                            short msgFlag = BitConverter.ToInt16(message, 0);
                            if (msgFlag == 1002)
                            {
                                Console.WriteLine(1002);
                                //TODO:下发定值获取命令
                                _dotNettyTcpServer.SendMessageByIp(address, 0, 1, 0);
                            }
                            if (message.Length >= 5)
                            {
                                byte[] data = message;
                                for (int i = 0; i < data.Length; i += 5)
                                {
                                    if (i / 5 > systemDzList.Count - 1)
                                    {
                                        MonitoringEventBus.LogHandler("错误！！！  越界", "定值信息");

                                        continue;

                                    }
                                    ImDeviceDzOutput imDeviceDzOutput = systemDzList[i / 5];
                                    if (imDeviceDzOutput == null)
                                    {
                                        MonitoringEventBus.LogHandler("错误！！！  未找到定值源", "定值信息");
                                        continue;
                                    }
                                    byte[] valueBytes = new byte[4];
                                    Array.Copy(data, i, valueBytes, 0, 4); // 前四个字节表示数值
                                    byte type = data[i + 4]; // 第五个字节表示类型

                                    if (type == 5) // 整数
                                    {
                                        int intValue = BitConverter.ToInt32(valueBytes, 0);
                                        MonitoringEventBus.LogHandler($"定值名称：{imDeviceDzOutput.DzComment} 整数: {intValue}", "定值信息");

                                        imDeviceDzOutput.Value = intValue.ToString();
                                    }
                                    else if (type == 7) // 浮点数
                                    {
                                        float floatValue = BitConverter.ToSingle(valueBytes, 0);
                                        MonitoringEventBus.LogHandler($"定值名称：{imDeviceDzOutput.DzComment} 浮点: {floatValue}", "定值信息");
                                        imDeviceDzOutput.Value = floatValue.ToString();
                                    }
                                    else
                                    {
                                        MonitoringEventBus.LogHandler($"定值名称：{imDeviceDzOutput.DzComment} 错误！！！  类型未知", "定值信息");
                                    }
                                }

                                _imDeviceDzRedis.HashSetUpdateManyAsync($"DZ_{address.ToString("X2")}_System", systemDzList.Select(t => t.Id).ToList(), systemDzList);

                            }
                        }
                        catch (Exception ex)
                        {
                            MonitoringEventBus.LogHandler(ex.StackTrace, "定值错误信息");
                        }
                    }
                    else
                    {
                        MonitoringEventBus.LogHandler($"获取系统定值信息失败,地址：{address}", "103客户端信息");

                    }

                }
            }
           
            
        }
    }
}
