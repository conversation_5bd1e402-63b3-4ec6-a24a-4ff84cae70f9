using Abp.Dependency;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Dlls;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Interfaces;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.DataAnalysis.DataCollection;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection
{
    /// <summary>
    /// Handles telesignal data
    /// </summary>
    public class TelesignalHandler : ITelesignalHandler, ISingletonDependency
    {
        private readonly RedisDataRepository _redisDataRepository;
        private readonly ISecondaryCircuitAlarmChecker _secondaryCircuitAlarmChecker;
        private readonly TelesignalizationResultSaveTask _telesignalizationResultSaveTask;
        public TelesignalHandler(
            RedisDataRepository redisDataRepository,
            TelesignalizationResultSaveTask telesignalizationResultSaveTask,
            ISecondaryCircuitAlarmChecker secondaryCircuitAlarmChecker)
        {
            _redisDataRepository = redisDataRepository;
            _secondaryCircuitAlarmChecker = secondaryCircuitAlarmChecker;
            _telesignalizationResultSaveTask = telesignalizationResultSaveTask;
        }

        /// <summary>
        /// Handles telesignal data processing
        /// </summary>
        public async Task HandleTelesignalDataAsync(RecordRECORDYXBURSTNewTaskInfo ri)
        {
            try
            {
                var yx = ri.Record;
                var categoryValue = ri.Connection.DataSourceCategoryName;
                string redisKey = $"{_redisDataRepository.TelesignalisationModelListRediskey}_{(DataSourceCategoryEnum)categoryValue}";
                string redisChannel = $"{_redisDataRepository.TelesignalisationInflectionInflectionZZChannelRediskey}_{(DataSourceCategoryEnum)categoryValue}";

                string haskey = $"{yx.dev_addr}_{yx.dev_sector}_{yx.dev_inf}_{categoryValue}";
                var yxData = await _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOneAsync(redisKey, haskey);

                if (yxData == null)
                {
                    Log4Helper.Error(GetType(), $"更新数据失败: 地址：{yx.dev_addr} 类型：{categoryValue} 键：{haskey}");
                    return;
                }
                await UpdateTelesignalDataAsync(yxData, yx, redisKey, redisChannel, haskey);
                _telesignalizationResultSaveTask.SaveWithCache(yxData, ri.Connection);
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler($"Error in HandleTelesignalDataAsync: {ex.ToString()}", "Error");
            }
        }

        /// <summary>
        /// Updates telesignal data
        /// </summary>
        public async Task UpdateTelesignalDataAsync(TelesignalisationModel yxData, RECORDYXBURST_New yx, string redisKey, string redisChannel, string haskey)
        {
            yxData.ResultTime = yx.time;
            yxData.ResultValue = yx.yx_val;

            var tasks = new List<Task>
            {
                _redisDataRepository.TelesignalisationModelListRedis.HashSetUpdateOneAsync(redisKey, haskey, yxData),
               
                _secondaryCircuitAlarmChecker.CheckSecondaryCircuitAlarmAsync(haskey, null, yxData),
                Task.Run(() => _redisDataRepository.TelesignalisationModelInflectionListRedis.PublishAsync(redisChannel, yxData))
            };

            await Task.WhenAll(tasks);
        }
    }
}
