﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceDzenumPu
    {
        public int PuctgyCode { get; set; }
        public int EnumTypeId { get; set; }
        public int EnumIndex { get; set; }
        public string? EnumComment { get; set; }

        public virtual ImPuCtgy PuctgyCodeNavigation { get; set; } = null!;
    }
}
