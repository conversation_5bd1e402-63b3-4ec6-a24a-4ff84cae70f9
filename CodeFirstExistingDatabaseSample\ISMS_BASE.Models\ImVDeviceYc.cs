﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVDeviceYc
    {
        public string Id { get; set; } = null!;
        public string Ycname { get; set; } = null!;
        public string? Unit { get; set; }
        public double? Cof { get; set; }
        public int? Precise { get; set; }
        public double? UpLimit { get; set; }
        public double? DownLimit { get; set; }
        public double? Intl4Save { get; set; }
        public string? Unit2 { get; set; }
        public double? Cof2 { get; set; }
        public int? Precise2 { get; set; }
        public string SaveMode { get; set; } = null!;
        public double? UpLimit4Stat { get; set; }
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
    }
}
