﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbBumen
    {
        public string Id { get; set; }
        public string Bmlxbm { get; set; }
        public string Bumenmc { get; set; }
        public string Bumenbm { get; set; }

        public virtual TbBumenlx BmlxbmNavigation { get; set; }
        public virtual TbTreenode IdNavigation { get; set; }
    }
}
