﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaModuleDir
    {
        public IaModuleDir()
        {
            IaModules = new HashSet<IaModule>();
        }

        public string DirId { get; set; } = null!;
        public string DirName { get; set; } = null!;
        public int? SeqNo { get; set; }

        public virtual ICollection<IaModule> IaModules { get; set; }
    }
}
