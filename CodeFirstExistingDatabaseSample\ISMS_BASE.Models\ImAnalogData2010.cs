﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAnalogData2010
    {
        public string DeviceCtgy { get; set; } = null!;
        public int DataCode { get; set; }
        public string DataName { get; set; } = null!;
        public string? Sym1 { get; set; }
        public double Cof1 { get; set; }
        public int Precise1 { get; set; }
        public string? Sym2 { get; set; }
        public double Cof2 { get; set; }
        public int Precise2 { get; set; }
        public string? EnumStr { get; set; }

        public virtual ImDevCtgy DeviceCtgyNavigation { get; set; } = null!;
    }
}
