﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAlerttype
    {
        public string Alerttype { get; set; }
        public string Alerttypename { get; set; }
        public string Alertlevel { get; set; }

        public virtual ImAlertlevel AlertlevelNavigation { get; set; }
    }
}
