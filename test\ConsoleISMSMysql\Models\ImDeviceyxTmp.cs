﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDeviceyxTmp
    {
        public string Id { get; set; }
        public string Yxname { get; set; }
        public string YxType { get; set; }
        public string Swonstr { get; set; }
        public string Swoffstr { get; set; }
        public string Swuncertstr { get; set; }
        public string Alertlevel { get; set; }
        public string Normalstate { get; set; }

        public virtual ImAlertlevel AlertlevelNavigation { get; set; }
        public virtual ImDevicedataTmp IdNavigation { get; set; }
    }
}
