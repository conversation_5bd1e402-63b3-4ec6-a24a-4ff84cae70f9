﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImProgcontrolitem
    {
        public string Id { get; set; }
        public string Progctlid { get; set; }
        public string Ykdataid { get; set; }
        public int Ykstate { get; set; }
        public int Seqno { get; set; }
        public int Waityxtimeout { get; set; }
        public int Delaytime { get; set; }

        public virtual ImProgcontrol Progctl { get; set; }
        public virtual ImDeviceyk Ykdata { get; set; }
    }
}
