﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbTableMap
    {
        public TbTableMap()
        {
            TbTableDataDups = new HashSet<TbTableDataDup>();
        }

        public string TableMapId { get; set; } = null!;
        public string SrcTableId { get; set; } = null!;
        public string DstTableId { get; set; } = null!;
        public int MapSeqNo { get; set; }
        public string MapCtgy { get; set; } = null!;
        public string? BeiZhu { get; set; }

        public virtual TbTable DstTable { get; set; } = null!;
        public virtual TbTable SrcTable { get; set; } = null!;
        public virtual ICollection<TbTableDataDup> TbTableDataDups { get; set; }
    }
}
