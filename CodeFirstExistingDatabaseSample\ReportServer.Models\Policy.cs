﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class Policy
    {
        public Policy()
        {
            Catalogs = new HashSet<Catalog>();
            ModelItemPolicies = new HashSet<ModelItemPolicy>();
            PolicyUserRoles = new HashSet<PolicyUserRole>();
            SecData = new HashSet<SecDatum>();
        }

        public Guid PolicyId { get; set; }
        public byte? PolicyFlag { get; set; }

        public virtual ICollection<Catalog> Catalogs { get; set; }
        public virtual ICollection<ModelItemPolicy> ModelItemPolicies { get; set; }
        public virtual ICollection<PolicyUserRole> PolicyUserRoles { get; set; }
        public virtual ICollection<SecDatum> SecData { get; set; }
    }
}
