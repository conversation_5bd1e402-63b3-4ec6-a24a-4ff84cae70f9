﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImWatchdog
    {
        public string Exename { get; set; }
        public int Tickcount { get; set; }
        public DateTime Lasttime { get; set; }
    }
}
