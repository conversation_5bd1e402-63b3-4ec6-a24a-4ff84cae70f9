﻿using System;
using System.Runtime.InteropServices;

namespace Client104Side
{
    public class ICE104EndPointController
    {
        public ICE104EndPointController()
        {
            string[] test = new string[20];
        }

        /// <summary>
        /// 结构体转IntPtr
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="info"></param>
        /// <returns></returns>
        public static IntPtr StructToIntPtr<T>(T info)
        {
            int size = Marshal.SizeOf(info);
            IntPtr intPtr = Marshal.AllocHGlobal(size);
            Marshal.StructureToPtr(info, intPtr, true);
            return intPtr;
        }

        /// <summary>
        /// IntPtrz转结构体
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="info"></param>
        /// <returns></returns>
        public static T IntPtrToStruct<T>(IntPtr info)
        {
            return (T)Marshal.PtrToStructure(info, typeof(T));
        }

        private const string DLLPath = @".\Client104Side\Dlls\iec104cln_dll.dll";
        //public const string DLLPath = @"..\..\..\DataAnalysis\Dlls\iec104cln_dll.dll";

        /// <summary>
        /// 启动104客户端
        /// </summary>
        /// <param name="cln104_param"></param>
        /// <param name="callback_dev_on"></param>
        /// <param name="callback_dev_off"></param>
        /// <returns>返回值 0：启动成功；返回值<0: 启动失败。若启动失败，检查下各配置文件是否完备，各配置文件是否按照要求进行了配置。</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_start", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104clnStart(CLN104_PARAM cln104_param, ICE104CommunicationCallback.CALLBACK_DEV_ON callback_dev_on, ICE104CommunicationCallback.CALLBACK_DEV_OFF callback_dev_off);

        /// <summary>
        /// 获取装置的TCP连接状态，该状态写入参数commState中
        /// </summary>
        /// <param name="devAddr">装置地址</param>
        /// <param name="commState">保存连接状态</param>
        /// <returns>返回值 0：获取状态成功，此时commState指示的状态有效，commState大于等于3 表示已经建立TCP连接；返回值 -1：获取状态失败，此时commState指示的状态无效。可能是该装置地址没有在Config.ini中配置</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_device_comm_state", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104GetDeviceCommState(byte devAddr, ref byte commState);

        /// <summary>
        /// *获取装置的协议连接状态
        /// </summary>
        /// <param name="addr">装置地址</param>
        /// <returns>返回值 0：装置在线，此时可以下发总召唤、遥控等命令；返回值 -1：装置不在线。装置（104从站）还未与104主站连接上</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_dev_status", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104GetDevStatus(byte addr);

        /// <summary>
        /// *获取装置的总召状态
        /// </summary>
        /// <param name="addr">装置地址</param>
        /// <returns>返回值 0：装置已经总召唤完成，可以读取遥信遥测；返回值 -1：传入的装置地址错误，或装置还未完成总召唤，遥信遥测不可用</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_dev_callstatus", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetDevCallStatus(byte addr);

        /// <summary>
        /// 关闭104客户端
        /// </summary>
        /// <returns>返回值 0：关闭成功；返回值<0: 关闭失败</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_end", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnEnd();

        /// <summary>
        /// 获取遥测值
        /// </summary>
        /// <param name="dev_addr">装置地址</param>
        /// <param name="dev_sector">装置扇区</param>
        /// <param name="dev_inf">装置信息序号</param>
        /// <param name="yc_val">存储获取的遥测值</param>
        /// <returns>返回值 0：获取成功，此时yc_val保存的值有效；返回值 -1：获取失败，此时yc_val保存的值无效。可能是点表中没有与(地址、扇区、inf) 这个三元素 匹配的条目</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_yc", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetYC(byte dev_addr, byte dev_sector, ushort dev_inf, ref float yc_val);

        /// <summary>
        /// 获取遥信值
        /// </summary>
        /// <param name="dev_addr">装置地址</param>
        /// <param name="dev_sector"> 装置扇区 </param>
        /// <param name="dev_inf"> 装置信息序号</param>
        /// <param name="yx_val">存储获取的遥信值，遥信为双点遥信。遥信为0x83表示该遥信，装置未上送</param>
        /// <returns>返回值 0：获取成功，此时yx_val保存的值有效； 返回值 -1：获取失败，此时yx_val保存的值无效。可能是点表中没有与(地址、扇区、inf) 这个三元素 匹配的条目</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_yx", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetYX(byte dev_addr, byte dev_sector, ushort dev_inf, ref byte yx_val);

        /// <summary>
        /// 获取电度值
        /// </summary>
        /// <param name="dev_addr">装置地址</param>
        /// <param name="dev_sector"> 装置扇区 </param>
        /// <param name="dev_inf"> 装置信息序号</param>
        /// <param name="dd_val">存储获取的电度值</param>
        /// <returns> 返回值 0：获取成功，此时dd_val保存的值有效；返回值 -1：获取失败，此时dd_val保存的值无效。可能是点表中没有与(地址、扇区、inf) 这个三元素 匹配的条目</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_dd", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetDD(byte dev_addr, byte dev_sector, ushort dev_inf, IntPtr dd_val);

        /// <summary>
        /// 下发遥控命令
        /// </summary>
        /// <param name="dev_addr">装置地址</param>
        /// <param name="dev_sector"> 装置扇区 </param>
        /// <param name="dev_inf"> 装置信息序号</param>
        /// <param name="yk_dco">2 控合，1控分</param>
        /// <returns>返回值 0：下发遥控命令成功；返回值 -1：下发遥控命令失败。可能是装置（104从站）还未与104主站连接上，或者该装置地址没有在Config.ini中配置</returns>

        [DllImport(DLLPath, EntryPoint = "iec104cln_yk", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnYK(byte dev_addr, byte dev_sector, ushort dev_inf, byte yk_dco);

        /// <summary>
        /// 总召唤  0 通信状态总召唤  1 遥信遥测总召唤 2 电度总召唤
        /// </summary>
        /// <param name="call_type">总召唤类型，获取遥信遥测请使用IEC104_YX_CALL</param>
        /// <param name="dev_addr">装置地址</param>
        /// <returns>返回值 0：下发总召唤命令成功；返回值 -1：下发总召唤命令失败。可能是装置（104从站）还未与104主站连接上，或者该装置地址没有在Config.ini中配置</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_call", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnCall(byte call_type, byte dev_addr);

        /// <summary>
        /// 读取遥信变位缓冲区
        /// </summary>
        /// <param name="yx_burst">读取到的遥信变位存储在这个参数中</param>
        /// <returns>返回值：0成功读取，-1未读取（读取失败是因为缓冲区已读完）</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_read_yx_burst_record", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnReadYXBurstRecord(IntPtr yx_burst);

        /// <summary>
        /// 获取数据（遥测、遥信、电度）缓冲区，每次调用自动偏移读取指针
        /// </summary>
        /// <param name="yc_burst">遥测缓冲区数据</param>
        /// <returns>返回值：0成功获取数据，-1数据未变化，-2到达缓冲区末尾</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_yc_buf", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetYCBuf(IntPtr yc_burst);

        /// <summary>
        /// 获取数据（遥测、遥信、电度）缓冲区，每次调用自动偏移读取指针
        /// </summary>
        /// <param name="yx_burst">遥信缓冲区数据</param>
        /// <returns>返回值：0成功获取数据，-1数据未变化，-2到达缓冲区末尾</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_yx_buf", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetYXBuf(IntPtr yx_burst);

        /// <summary>
        /// 获取数据（遥测、遥信、电度）缓冲区，每次调用自动偏移读取指针
        /// </summary>
        /// <param name="dd_burst">电度缓冲区数据</param>
        /// <returns>返回值：0成功获取数据，-1数据未变化，-2到达缓冲区末尾</returns>
        [DllImport(DLLPath, EntryPoint = "iec104cln_get_dd_buf", CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.Cdecl)]
        public static extern int Iec104ClnGetDDBuf(IntPtr dd_burst);
    }

    /// <summary>
    /// cbt add 2020.3.11 104主站动态库 电度
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct DD_TYPE
    {
        public byte addr;
        public byte sector;
        public ushort inf;
        public uint val;
        public uint chgFlag;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 7)]
        public byte[] tm;
    }

    /// <summary>
    /// cbt add 2020.4.2 增加遥信、遥测、电度的自定义结构体
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct YC_TYPE
    {
        public byte addr;//装置地址
        public byte sector;//装置扇区
        public ushort inf;//信息序号（编码地址）
        public float val;//遥测值
        public uint chgFlag;//值变化的标志（0未变化，1变化）

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 7)]
        public byte[] tm;
    }
;

    /// <summary>
    /// cbt add 2020.3.11 104主站动态库 遥信
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct YX_TYPE
    {
        public byte addr;
        public byte sector;
        public ushort inf;
        public byte val;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 3)]
        public byte[] resv;//预留，字节对齐，无实际作用

        public uint chgFlag;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 7)]
        public byte[] tm;
    }
;

    /// <summary>
    /// cbt add 2020.3.11 104主站动态库 增加变位遥信循环缓冲区
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct RECORDYXBURST
    {
        public byte dev_addr;
        public byte dev_sector;
        public uint dev_inf;
        public byte yx_val;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 7)]
        public byte[] tm;
    }

    /// <summary>
    /// cbt add 2020.3.11 104动态库启动不读配置文件，所有配置从参数传入
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct CLN104_PARAM
    {
        //public byte dev_num;//装置数量

        //以下为每个装置的配置
        public byte dev_type;//1我司装置、0标准104协议装置

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 20)]
        public byte[] dev_ip;//装置ip

        public ushort dev_port;//装置的端口号

        public byte dev_addr;//装置的地址

        //[MarshalAs(UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = UnmanagedType.U4)]
        public uint dev_yc_num;//装置的遥测数量

        //[MarshalAs(UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = UnmanagedType.U4)]
        public uint dev_yc_start;//装置的遥测起始地址

        //[MarshalAs(UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = UnmanagedType.U4)]
        public uint dev_yx_num;//装置的遥信数量

        //[MarshalAs(UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = UnmanagedType.U4)]
        public uint dev_yx_start;//装置的遥信起始地址

        //[MarshalAs(UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = UnmanagedType.U4)]
        public uint dev_dd_num;//装置的电度数量

        // [MarshalAs(UnmanagedType.ByValArray, SizeConst = 20, ArraySubType = UnmanagedType.U4)]
        public uint dev_dd_start;//装置的电度起始地址
    }
}