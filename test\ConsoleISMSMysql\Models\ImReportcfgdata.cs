﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImReportcfgdata
    {
        public int Id { get; set; }
        public string Rptcfgid { get; set; }
        public string Dataid { get; set; }
        public string Title { get; set; }
        public int? Showintl { get; set; }
        public string Reserved { get; set; }

        public virtual ImDevicedata Data { get; set; }
        public virtual ImReportcfg Rptcfg { get; set; }
    }
}
