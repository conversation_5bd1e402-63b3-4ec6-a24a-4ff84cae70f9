﻿using MessagePack;
using MessagePack.Formatters;
using MessagePack.Resolvers;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using YunDa.ISAS.Redis.Configuration;
using YunDa.ISAS.Redis.Factory;
//using YunDa.SOMS.Redis.Configuration;
namespace YunDa.ISAS.Redis.Repositories
{
    public class RedisRepository<TEntity, TPrimaryKey> : IRedisRepository<TEntity, TPrimaryKey>
    {
        private readonly IRedisClientFactory _redisClientFactory;
        private readonly IRedisConfiguration _redisConfiguration;
        private readonly IDatabase _database;
        private readonly ISubscriber _subscriber;
        // 全局配置 CompositeResolver
        private static readonly MessagePackSerializerOptions options = MessagePackSerializerOptions.Standard
            .WithResolver(CompositeResolver.Create(
                new IMessagePackFormatter[] { },  // 自定义时间格式化器
                new IFormatterResolver[] {
                    ContractlessStandardResolver.Instance,  // 支持无特性标记的类
                    StandardResolver.Instance
                }
            ));

        // 用于JSON序列化的设置
        private static readonly JsonSerializerSettings jsonSettings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };

        public RedisRepository(IRedisClientFactory redisClientFactory, IRedisConfiguration redisConfiguration)
        {
            _redisClientFactory = redisClientFactory ?? throw new ArgumentNullException(nameof(redisClientFactory));
            _redisConfiguration = redisConfiguration ?? throw new ArgumentNullException(nameof(redisConfiguration));
            _database = _redisClientFactory.InstanceRedisDatabase();
            _subscriber = _redisClientFactory.InstanceRedisSubscriber();
        }

        public async Task<bool> InsertOrUpdateoneAsync(TEntity entity, string key, TimeSpan? ttl = null)
        {
            if (string.IsNullOrEmpty(key) || _database == null || entity == null)
            {
                return false;
            }
            
            var value = GetSerializeObjectString(entity);
            return await _database.StringSetAsync(key, value, ttl);
        }

        public async Task<long> PublishAsync(string channel, TEntity entity)
        {
            if (string.IsNullOrEmpty(channel) || entity == null || _subscriber == null)
            {
                return 0;
            }
            
            var jsonMessage = GetSerializeObjectString(entity);
            var redisChannel = new RedisChannel(channel, RedisChannel.PatternMode.Literal);
            return await _subscriber.PublishAsync(redisChannel, jsonMessage);
        }
        
        public event Action<string, TEntity> OnMessageReceived;
        
        public void Subscribe(string pattern)
        {
            if (string.IsNullOrEmpty(pattern) || _subscriber == null || OnMessageReceived == null)
            {
                return;
            }
            
            var redisChannel = new RedisChannel(pattern, RedisChannel.PatternMode.Pattern);
            _subscriber.Subscribe(redisChannel, (channel, message) =>
            {
                OnMessageReceived?.Invoke(channel, GetDeserialize<TEntity>(message));
            });
        }
        
        public void ListUpdateAll(string key, List<TEntity> value)
        {
            if (string.IsNullOrEmpty(key) || _database == null || value == null || !value.Any())
            {
                return;
            }
            
            _database.KeyDelete(key);
            var batch = _database.CreateBatch();
            var tasks = new List<Task>(value.Count);
            
            foreach (var single in value)
            {
                var s = GetSerializeObjectString(single);
                tasks.Add(batch.ListLeftPushAsync(key, s));
            }
            
            batch.Execute();
            Task.WhenAll(tasks).Wait();
        }

        public async Task DeleteHashKeyAsync(string key, Guid id)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return;
            }
            
            for (int i = 0; i < 3; i++)
            {
                if (await _database.HashDeleteAsync(key, id.ToString()))
                {
                    return;
                }
                await Task.Delay(1000);
            }
        }
        
        public async Task DeleteHashKeiesAsync(string key, List<Guid> ids)
        {
            if (string.IsNullOrEmpty(key) || _database == null || ids == null || !ids.Any())
            {
                return;
            }
            
            var redisValues = ids.Select(id => (RedisValue)id.ToString()).ToArray();
            await _database.HashDeleteAsync(key, redisValues);
        }
        
        public async Task DeleteHashKeyAsync(string key, string id)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(id))
            {
                return;
            }
            
            for (int i = 0; i < 3; i++)
            {
                if (await _database.HashDeleteAsync(key, id))
                {
                    return;
                }
                await Task.Delay(1000);
            }
        }
        
        public async Task DeleteHashKeiesAsync(string key, List<string> ids)
        {
            if (string.IsNullOrEmpty(key) || _database == null || ids == null || !ids.Any())
            {
                return;
            }
            
            var redisValues = ids.Select(id => (RedisValue)id).ToArray();
            await _database.HashDeleteAsync(key, redisValues);
        }
        
        public void ListRightPush(string key, List<TEntity> value)
        {
            if (string.IsNullOrEmpty(key) || _database == null || value == null || !value.Any())
            {
                return;
            }
            
            var batch = _database.CreateBatch();
            var tasks = new List<Task>(value.Count);
            
            foreach (var single in value)
            {
                var s = GetSerializeObjectString(single);
                tasks.Add(batch.ListRightPushAsync(key, s));
            }
            
            batch.Execute();
            Task.WhenAll(tasks).Wait();
        }
        
        public void ListRightPush(string key, TEntity value)
        {
            if (string.IsNullOrEmpty(key) || _database == null || value == null)
            {
                return;
            }
            
            var s = GetSerializeObjectString(value);
            _database.ListRightPush(key, s);
        }

        public async Task<long> ListLenghtAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return 0;
            }
            
            return await _database.ListLengthAsync(key);
        }
        
        public long GetHashLength(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return 0;
            }
            return _database.HashLength(key);
        }
        
        public async Task<long> GetHashLengthAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return 0;
            }
            return await _database.HashLengthAsync(key);
        }
        
        public long GetListLength(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return 0;
            }
            return _database.ListLength(key);
        }
        
        public async Task<long> GetListLengthAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return 0;
            }
            return await _database.ListLengthAsync(key);
        }
        
        public List<TEntity> ListGet(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return new List<TEntity>();
            }
            
            var vList = _database.ListRange(key);
            if (vList.Length == 0)
            {
                return new List<TEntity>();
            }
            
            return vList.Select(item => GetDeserialize<TEntity>(item)).ToList();
        }
        
        public async Task<List<TEntity>> ListGetAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return new List<TEntity>();
            }
            
            var vList = await _database.ListRangeAsync(key);
            if (vList.Length == 0)
            {
                return new List<TEntity>();
            }
            
            return vList.Select(item => GetDeserialize<TEntity>(item)).ToList();
        }
        
        public Dictionary<string, TEntity> HashSetGetDicAll(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return new Dictionary<string, TEntity>();
            }
            
            var result = new Dictionary<string, TEntity>();
            var arr = _database.HashGetAll(key);
            
            if (arr == null || arr.Length == 0)
            {
                return result;
            }
            
            foreach (var item in arr)
            {
                if (!item.Name.IsNullOrEmpty)
                {
                    var obj = GetDeserialize<TEntity>(item.Value);
                    result.TryAdd(item.Name, obj);
                }
            }
            
            return result;
        }
        
        public TEntity HashSetGetOne(string key, Guid id)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return default;
            }
            
            var item = _database.HashGet(key, id.ToString());
            return item.IsNullOrEmpty ? default : GetDeserialize<TEntity>(item);
        }
        
        public TEntity HashSetGetOne(string key, string id)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(id))
            {
                return default;
            }
            
            var item = _database.HashGet(key, id);
            return item.IsNullOrEmpty ? default : GetDeserialize<TEntity>(item);
        }
        
        public async Task<TEntity> HashSetGetOneAsync(string key, string id)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(id))
            {
                return default;
            }
            
            var item = await _database.HashGetAsync(key, id);
            return item.IsNullOrEmpty ? default : GetDeserialize<TEntity>(item);
        }
        
        public async Task<long> ListLeftPushAsync(string key, TEntity entity)
        {
            if (string.IsNullOrEmpty(key) || _database == null || entity == null)
            {
                return 0;
            }
            
            var s = GetSerializeObjectString(entity);
            
            // Check if list is too large and manage it
            if (await _database.ListLengthAsync(key) > int.MaxValue)
            {
                if (!await _database.KeyDeleteAsync(key))
                {
                    await _database.KeyExpireAsync(key, TimeSpan.FromMilliseconds(10));
                }
            }
            
            return await _database.ListLeftPushAsync(key, s);
        }
        
        public async Task ListRemoveStringAsync(string key, string value, long count = 0)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(value))
            {
                return;
            }
            
            await _database.ListRemoveAsync(key, value, count);
        }
        
        public async Task<TEntity> GetListTailDataAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null || !await KeyExistsAsync(key))
            {
                return default;
            }
            
            var length = await _database.ListLengthAsync(key);
            if (length == 0)
            {
                return default;
            }
            
            var res = await _database.ListGetByIndexAsync(key, length - 1);
            return res.IsNullOrEmpty ? default : GetDeserialize<TEntity>(res);
        }
        
        public List<TEntity> GetAll()
        {
            if (_database == null)
            {
                return new List<TEntity>();
            }
            
            var entities = new List<TEntity>();
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            
            foreach (var item in server.Keys(pattern: "*"))
            {
                if (_database.KeyType(item) == RedisType.String)
                {
                    var str = _database.StringGet(item);
                    if (!str.IsNullOrEmpty)
                    {
                        var entity = GetDeserialize<TEntity>(str);
                        if (entity != null)
                        {
                            entities.Add(entity);
                        }
                    }
                }
            }
            
            return entities;
        }

        public async Task<TEntity> GetOneAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return default;
            }
            
            var str = await _database.StringGetAsync(key);
            return str.IsNull ? default : GetDeserialize<TEntity>(str);
        }

        public async Task<bool> InsertOrUpdateOneAsync(TEntity entity, string key, TimeSpan? ttl = null)
        {
            if (string.IsNullOrEmpty(key) || _database == null || entity == null)
            {
                return false;
            }
            
            var value = GetSerializeObjectString(entity);
            return await _database.StringSetAsync(key, value, ttl);
        }

        private byte[] GetSerializeObjectString(TEntity entity)
        {
            try
            {
                return MessagePackSerializer.Serialize(entity, options);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"MessagePack serialization failed: {ex.Message}");
                // Fallback to JSON serialization if MessagePack fails
                var json = JsonConvert.SerializeObject(entity, jsonSettings);
                return Encoding.UTF8.GetBytes(json);
            }
        }
        
        private T GetDeserialize<T>(byte[] data)
        {
            if (data == null || data.Length == 0)
            {
                return default;
            }
            
            try
            {
                return MessagePackSerializer.Deserialize<T>(data, options);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"MessagePack deserialization failed: {ex.Message}");
                try
                {
                    // Fallback to JSON deserialization
                    string jsonString = Encoding.UTF8.GetString(data);
                    return JsonConvert.DeserializeObject<T>(jsonString, jsonSettings);
                }
                catch (Exception jsonEx)
                {
                    Debug.WriteLine($"JSON deserialization also failed: {jsonEx.Message}");
                    return default;
                }
            }
        }
        
        public async Task DeleteKeyAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return;
            }
            
            const int maxRetries = 5;
            const int delayMs = 10;
            
            for (int i = 0; i < maxRetries; i++)
            {
                if (await _database.KeyDeleteAsync(key))
                {
                    return;
                }
                await Task.Delay(delayMs * (i + 1)); // Exponential backoff
            }
        }

        public async Task<bool> HashSetUpdateOneAsync(string key, string id, TEntity entity)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(id) || entity == null)
            {
                return false;
            }
            
            var s = GetSerializeObjectString(entity);
            return await _database.HashSetAsync(key, id, s);
        }

        public async Task<bool> HashSetUpdateManyAsync(string key, List<Guid> ids, List<TEntity> entities)
        {
            if (string.IsNullOrEmpty(key) || _database == null || ids == null || entities == null || ids.Count != entities.Count || !ids.Any())
            {
                return false;
            }
            
            var hashEntries = new HashEntry[ids.Count];
            for (int i = 0; i < ids.Count; i++)
            {
                hashEntries[i] = new HashEntry(ids[i].ToString(), GetSerializeObjectString(entities[i]));
            }
            
            await _database.HashSetAsync(key, hashEntries);
            return true;
        }
        
        public async Task<bool> HashSetUpdateManyAsync(string key, List<string> ids, List<TEntity> entities)
        {
            if (string.IsNullOrEmpty(key) || _database == null || ids == null || entities == null || ids.Count != entities.Count || !ids.Any())
            {
                return false;
            }
            
            var hashEntries = new HashEntry[ids.Count];
            for (int i = 0; i < ids.Count; i++)
            {
                hashEntries[i] = new HashEntry(ids[i], GetSerializeObjectString(entities[i]));
            }
            
            await _database.HashSetAsync(key, hashEntries);
            return true;
        }
        
        public async Task<List<TEntity>> ListRangeAsync(string key, int start = 0, int stop = -1)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return new List<TEntity>();
            }
            
            var redisValues = await _database.ListRangeAsync(key, start, stop);
            if (redisValues.Length == 0)
            {
                return new List<TEntity>();
            }
            
            return redisValues
                .Select(item => GetDeserialize<TEntity>(item))
                .Where(item => item != null)
                .ToList();
        }
        
        public async Task<bool> KeyExpireAsync(string key, TimeSpan ttl)
        {
            if (string.IsNullOrEmpty(key) || _database == null || ttl <= TimeSpan.Zero)
            {
                return false;
            }
            
            return await _database.KeyExpireAsync(key, ttl);
        }

        public async Task<TEntity> ListRightPopAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return default;
            }
            
            var res = await _database.ListRightPopAsync(key);
            return res.IsNull ? default : GetDeserialize<TEntity>(res);
        }

        public async Task<List<TEntity>> HashSetGetAllAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return new List<TEntity>();
            }
            
            var hashEntries = await _database.HashGetAllAsync(key);
            if (hashEntries.Length == 0)
            {
                return new List<TEntity>();
            }
            
            return hashEntries
                .Select(entry => GetDeserialize<TEntity>(entry.Value))
                .Where(entity => entity != null)
                .ToList();
        }
        
        public async Task<TEntity> HashSetGetAllAsync(string key, string hashkey)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(hashkey))
            {
                return default;
            }
            
            var hashEntry = await _database.HashGetAsync(key, hashkey);
            return hashEntry.IsNullOrEmpty ? default : GetDeserialize<TEntity>(hashEntry);
        }

        public async Task<Dictionary<string, TEntity>> HashSetGetDicAllAsync(string key)
        {
            if (string.IsNullOrEmpty(key) || _database == null)
            {
                return new Dictionary<string, TEntity>();
            }
            
            var result = new Dictionary<string, TEntity>();
            var arr = await _database.HashGetAllAsync(key);
            
            if (arr.Length == 0)
            {
                return result;
            }
            
            foreach (var item in arr)
            {
                if (!item.Name.IsNullOrEmpty && !item.Value.IsNullOrEmpty)
                {
                    var obj = GetDeserialize<TEntity>(item.Value);
                    if (obj != null)
                    {
                        result.TryAdd(item.Name, obj);
                    }
                }
            }
            
            return result;
        }

        public async Task UpdateOneAsync(TEntity entity, string key, TimeSpan? ttl = null)
        {
            if (string.IsNullOrEmpty(key) || _database == null || entity == null)
            {
                return;
            }
            
            TimeSpan? expiry = ttl;
            if (!expiry.HasValue)
            {
                var data = await _database.StringGetWithExpiryAsync(key);
                expiry = data.Expiry;
            }
            
            var s = GetSerializeObjectString(entity);
            await _database.StringSetAsync(key, s, expiry);
        }

        public async Task<bool> KeyExistsAsync(string key)
        {
            return !string.IsNullOrEmpty(key) && _database != null && await _database.KeyExistsAsync(key);
        }

        public async Task<bool> HashSetDeleteOneAsync(string key, string id)
        {
            if (string.IsNullOrEmpty(key) || _database == null || string.IsNullOrEmpty(id))
            {
                return false;
            }
            
            return await _database.HashDeleteAsync(key, id);
        }
    }
}
