﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaVideoPatrolItem
    {
        public string PiId { get; set; } = null!;
        public string PatId { get; set; } = null!;
        public string Piname { get; set; } = null!;
        public int PiseqNo { get; set; }
        public string ExeObjType { get; set; } = null!;
        public string ExeObjId { get; set; } = null!;
        public string? Action { get; set; }
        public string? ActParam { get; set; }
        public int DelayBfExe { get; set; }
        public int DelayAfExe { get; set; }
        public string IsCompareDiff { get; set; } = null!;
        public string? BkimgPaths { get; set; }

        public virtual IaVideoPatrol Pat { get; set; } = null!;
    }
}
