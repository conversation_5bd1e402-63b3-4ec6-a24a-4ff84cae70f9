﻿using Abp;
using Abp.Modules;
using Abp.Reflection.Extensions;
using System;
using System.Collections;
using System.Collections.Generic;
using YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto;
using YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto;
using YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto;
using YunDa.ISAS.Redis.Configuration;
using YunDa.ISAS.Redis.Entities.AlarmCategory;
using YunDa.ISAS.Redis.Entities.CameraAuthCategory;
using YunDa.ISAS.Redis.Entities.DataMonitorCategory;
using YunDa.ISAS.Redis.Entities.InspectionCategory;
using YunDa.ISAS.Redis.Entities.InspectionCategory.SpecialInspection;
using YunDa.ISAS.Redis.Entities.LinkageCategory;
using YunDa.ISAS.Redis.Entities.PatternRecongnition;
using YunDa.ISAS.Redis.Entities.SortDefines;
using YunDa.ISAS.Redis.Factory;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.DataTransferObject.CommonDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionSettingDto;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.OperationReport;
using YunDa.SOMS.DataTransferObject.ThirdPartyData.NJJW;
using YunDa.SOMS.Redis.Entities.AlarmCategory;

namespace YunDa.ISAS.Redis
{
    [DependsOn(typeof(AbpKernelModule))]
    public class ISASRedisModule : AbpModule
    {
        public override void PreInitialize()
        {
            IocManager.Register<IRedisConfiguration, RedisConfiguration>();
            IocManager.Register<IRedisClientFactory, RedisClientFactory>();

        }

        public override void Initialize()
        {
            IocManager.RegisterAssemblyByConvention(typeof(ISASRedisModule).GetAssembly());
            RegisterRepository();
        }
        private void RegisterRepository()
        {
            //保存摄像机权限的位置
            IocManager.Register<IRedisRepository<CameraAuthTimeRedis, string>, RedisRepository<CameraAuthTimeRedis, string>>();
            IocManager.Register<IRedisRepository<PatternRecongnitionCheckRedis, string>, RedisRepository<PatternRecongnitionCheckRedis, string>>();
            IocManager.Register<IRedisRepository<TempInspectionRedis, string>, RedisRepository<TempInspectionRedis, string>>();
            IocManager.Register<IRedisRepository<TemRobotInspectionRedis, string>, RedisRepository<TemRobotInspectionRedis, string>>();
            IocManager.Register<IRedisRepository<InspectionResultRedis, string>, RedisRepository<InspectionResultRedis, string>>();
            IocManager.Register<IRedisRepository<LinkageResultInfoRedis, string>, RedisRepository<LinkageResultInfoRedis, string>>();
            IocManager.Register<IRedisRepository<CallInspectionResultStateRedis, string>, RedisRepository<CallInspectionResultStateRedis, string>>();
            IocManager.Register<IRedisRepository<RuningInspectionCardRedis, string>, RedisRepository<RuningInspectionCardRedis, string>>();
            IocManager.Register<IRedisRepository<RobotInspectionResultRedis, string>, RedisRepository<RobotInspectionResultRedis, string>>();
            IocManager.Register<IRedisRepository<AlarmListRedis, string>, RedisRepository<AlarmListRedis, string>>();
            IocManager.Register<IRedisRepository<List<InspectionPlanTaskInfoOutput>, string>, RedisRepository<List<InspectionPlanTaskInfoOutput>, string>>();
            IocManager.Register<IRedisRepository<TaskRunningState, string>, RedisRepository<TaskRunningState, string>>();
            IocManager.Register<IRedisRepository<EnvironmentTempValue, string>, RedisRepository<EnvironmentTempValue, string>>();
            IocManager.Register<IRedisRepository<Dictionary<Guid, EquipmentDataModel>, string>, RedisRepository<Dictionary<Guid, EquipmentDataModel>, string>>();
            IocManager.Register<IRedisRepository< EquipmentDataModel, string>, RedisRepository<EquipmentDataModel, string>>();

            IocManager.Register<IRedisRepository<object, string>, RedisRepository<object, string>>();
            IocManager.Register<IRedisRepository<HashSet<object>, string>, RedisRepository<HashSet<object>, string>>();
            IocManager.Register<IRedisRepository<HashSet<string>, string>, RedisRepository<HashSet<string>, string>>();

            IocManager.Register<IRedisRepository<Dictionary<object, object>, string>, RedisRepository<Dictionary<object, object>, string>>();
            IocManager.Register<IRedisRepository<Dictionary<string, string>, string>, RedisRepository<Dictionary<string, string>, string>>();
            IocManager.Register<IRedisRepository<Dictionary<string, object>, string>, RedisRepository<Dictionary<string, object>, string>>();

            IocManager.Register<IRedisRepository<string, string>, RedisRepository<string, string>>();
            IocManager.Register<IRedisRepository<int, string>, RedisRepository<int, string>>();
            IocManager.Register<IRedisRepository<int[], string>, RedisRepository<int[], string>>();
            IocManager.Register<IRedisRepository<float[], string>, RedisRepository<float[], string>>();
            IocManager.Register<IRedisRepository<List<int>, string>, RedisRepository<List<int>, string>>();
            IocManager.Register<IRedisRepository<List<string>, string>, RedisRepository<List<string>, string>>();
            IocManager.Register<IRedisRepository<List<object>, string>, RedisRepository<List<object>, string>>();
            IocManager.Register<IRedisRepository<List<float>, string>, RedisRepository<List<float>, string>>();
            IocManager.Register<IRedisRepository<List<double>, string>, RedisRepository<List<double>, string>>();
            IocManager.Register<IRedisRepository<List<long>, string>, RedisRepository<List<long>, string>>();
          

            IocManager.Register<IRedisRepository<float, string>, RedisRepository<float, string>>();
            IocManager.Register<IRedisRepository<ArrayList, string>, RedisRepository<ArrayList, string>>();
            IocManager.Register<IRedisRepository<Hashtable, string>, RedisRepository<Hashtable, string>>();
            IocManager.Register<IRedisRepository<bool, string>, RedisRepository<bool, string>>();
            IocManager.Register<IRedisRepository<byte, string>, RedisRepository<byte, string>>();
            IocManager.Register<IRedisRepository<char, string>, RedisRepository<char, string>>();
            IocManager.Register<IRedisRepository<DateTime, string>, RedisRepository<DateTime, string>>();
            IocManager.Register<IRedisRepository<Guid, string>, RedisRepository<Guid, string>>();
            IocManager.Register<IRedisRepository<HashSet<SearchRecord>, string>, RedisRepository<HashSet<SearchRecord>, string>>();
            IocManager.Register<IRedisRepository<SearchRecord, string>, RedisRepository<SearchRecord, string>>();
            IocManager.Register<IRedisRepository<TelemeteringModel, string>, RedisRepository<TelemeteringModel, string>>();
            IocManager.Register<IRedisRepository<TelesignalisationModel, string>, RedisRepository<TelesignalisationModel, string>>();
            IocManager.Register<IRedisRepository<ImDeviceDzOutput, string>, RedisRepository<ImDeviceDzOutput, string>>();
            IocManager.Register<IRedisRepository<IOState, string>, RedisRepository<IOState, string>>();
            IocManager.Register<IRedisRepository<DeviceStatus, string>, RedisRepository<DeviceStatus, string>>();
            IocManager.Register<IRedisRepository<EquipmentInfoDiagnoseResult, string>, RedisRepository<EquipmentInfoDiagnoseResult, string>>();
            IocManager.Register<IRedisRepository<EquipmentInfoSearchRecord, string>, RedisRepository<EquipmentInfoSearchRecord, string>>();
            /*运维相关*/
            IocManager.Register<IRedisRepository<EquipmentInfoAbnormalComponent, string>, RedisRepository<EquipmentInfoAbnormalComponent, string>>();
            IocManager.Register<IRedisRepository<SecondaryCircuitComponent, string>, RedisRepository<SecondaryCircuitComponent, string>>();
            IocManager.Register<IRedisRepository<EquipmentInfoRemainingLifeAssessment, string>, RedisRepository<EquipmentInfoRemainingLifeAssessment, string>>();
            IocManager.Register<IRedisRepository<DeviceCPUMonitoring, string>, RedisRepository<DeviceCPUMonitoring, string>>();
            IocManager.Register<IRedisRepository<BCodeAndNTP, string>, RedisRepository<BCodeAndNTP, string>>();
            IocManager.Register<IRedisRepository<List<NameIntValueProperty>, string>, RedisRepository<List<NameIntValueProperty>, string>>();
            IocManager.Register<IRedisRepository<List<NameValueProperty>, string>, RedisRepository<List<NameValueProperty>, string>>();
            IocManager.Register<IRedisRepository<DeviceBoardStates, string>, RedisRepository<DeviceBoardStates, string>>();
            IocManager.Register<IRedisRepository<ProtectionDeviceCommInfoOutput, string>, RedisRepository<ProtectionDeviceCommInfoOutput, string>>();

            IocManager.Register<IRedisRepository<AlarmMessage,string>, RedisRepository<AlarmMessage, string>>();
            IocManager.Register<IRedisRepository<VideoElectronicFenceAlarmRedis, string>, RedisRepository<VideoElectronicFenceAlarmRedis, string>>();
            IocManager.Register<IRedisRepository<DiagnoseResultOutPut, string>, RedisRepository<DiagnoseResultOutPut, string>>();
            IocManager.Register<IRedisRepository<CheckDefect, string>, RedisRepository<CheckDefect, string>>();
            
            IocManager.Register<IRedisRepository<DiagnosisImageResult, string>, RedisRepository<DiagnosisImageResult, string>>();

            IocManager.Register<IRedisRepository<VideoDevProperty, string>, RedisRepository<VideoDevProperty, string>>();

            
        }

        public override void Shutdown()
        {
            IocManager.Resolve<IRedisClientFactory>().Dispose();
            base.Shutdown();
        }
    }
}
