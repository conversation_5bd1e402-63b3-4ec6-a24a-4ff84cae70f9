﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Documents;
using System.Windows;
using System.Windows.Controls;

namespace Yunda.SOMS.DataMonitoringServer.DataAnalysis.Helper
{
    public static class RichTextBoxHelper
    {
        public static readonly DependencyProperty DocumentProperty =
            DependencyProperty.RegisterAttached(
                "Document",
                typeof(FlowDocument),
                typeof(RichTextBoxHelper),
                new FrameworkPropertyMetadata(null, OnDocumentChanged));

        public static void SetDocument(RichTextBox richTextBox, FlowDocument value)
        {
            richTextBox.SetValue(DocumentProperty, value);
        }

        public static FlowDocument GetDocument(RichTextBox richTextBox)
        {
            return (FlowDocument)richTextBox.GetValue(DocumentProperty);
        }

        private static void OnDocumentChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is RichTextBox richTextBox)
            {
                if (e.NewValue is FlowDocument document)
                {
                    // 清除旧文档的事件处理器  
                    if (e.OldValue is FlowDocument oldDocument)
                    {
                        // 修复：FlowDocument 没有 TextChanged 事件，因此需要移除此代码  
                        // oldDocument.TextChanged -= OnDocumentTextChanged;  
                    }

                    // 设置新文档  
                    richTextBox.Document = document;

                    // 修复：FlowDocument 没有 TextChanged 事件，因此需要移除此代码  
                    // document.TextChanged += OnDocumentTextChanged;  
                }
            }
        }

        // 修复：FlowDocument 没有 TextChanged 事件，因此需要移除此方法  
        // private static void OnDocumentTextChanged(object sender, EventArgs e)  
        // {  
        //     // 这里可以添加文档变更处理逻辑，如滚动到最新位置等  
        // }  
    }
}
