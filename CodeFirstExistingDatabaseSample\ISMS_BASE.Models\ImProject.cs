﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImProject
    {
        public ImProject()
        {
            IaVideoPatrols = new HashSet<IaVideoPatrol>();
            ImDiagrams = new HashSet<ImDiagram>();
            ImFltDistDevGrps = new HashSet<ImFltDistDevGrp>();
            ImProgControls = new HashSet<ImProgControl>();
            ImStations = new HashSet<ImStation>();
        }

        public string Id { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string ProtCommMgrIp { get; set; } = null!;
        public DateTime CreateTime { get; set; }
        public string? Creator { get; set; }

        public virtual ICollection<IaVideoPatrol> IaVideoPatrols { get; set; }
        public virtual ICollection<ImDiagram> ImDiagrams { get; set; }
        public virtual ICollection<ImFltDistDevGrp> ImFltDistDevGrps { get; set; }
        public virtual ICollection<ImProgControl> ImProgControls { get; set; }
        public virtual ICollection<ImStation> ImStations { get; set; }
    }
}
