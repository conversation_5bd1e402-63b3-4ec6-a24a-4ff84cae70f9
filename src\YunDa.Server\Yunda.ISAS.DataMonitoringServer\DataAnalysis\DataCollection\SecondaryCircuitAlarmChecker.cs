using Abp.Dependency;
using MongoDB.Bson;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Interfaces;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Redis.Entities.AlarmCategory;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.SecondaryCircuitDto;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.OperationReport;
using Z.Expressions;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection
{
    /// <summary>
    /// Checks secondary circuit alarms
    /// </summary>
    public class SecondaryCircuitAlarm<PERSON>hecker : ISecondaryCircuitAlar<PERSON><PERSON><PERSON><PERSON>, ISingletonDependency
    {
        private readonly RedisDataRepository _redisDataRepository;
        private readonly RunningDataCache _runningDataCache;
        private readonly WebApiRequest _webApiRequest;
        private readonly Content _settingModel;
        private readonly DataRepository _dataRepository;

        public SecondaryCircuitAlarmChecker(
            RedisDataRepository redisDataRepository,
            RunningDataCache runningDataCache,
            WebApiRequest webApiRequest,
            Content settingModel,
            DataRepository dataRepository)
        {
            _redisDataRepository = redisDataRepository;
            _runningDataCache = runningDataCache;
            _webApiRequest = webApiRequest;
            _settingModel = settingModel;
            _dataRepository = dataRepository;
        }

        /// <summary>
        /// Checks for secondary circuit alarms
        /// </summary>
        public Task CheckSecondaryCircuitAlarmAsync(string yxycRedisHashKey, TelemeteringModel yc, TelesignalisationModel yx)
        {
            if (_runningDataCache.SecondaryCircuitLogicExpressionDic.ContainsKey(yxycRedisHashKey))
            {
                return Task.Run(() =>
                {
                    lock (_runningDataCache.SecondaryCircuitLogicExpressionDic)
                    {
                        // Get original data
                        var listLogics = _runningDataCache.SecondaryCircuitLogicExpressionDic[yxycRedisHashKey];

                        // Create copy list
                        var copiedLogics = listLogics
                        .Select(logic => new LogicExpressionTelesignalisation
                        {
                            LogicExpression = logic.LogicExpression,
                            TelesignalisationAddr = logic.TelesignalisationAddr,
                            SecondaryCircuitId = logic.SecondaryCircuitId
                        })
                        .ToList();

                        foreach (var listLogic in copiedLogics)
                        {
                            string pattern = @"\{([^}]*)\}";
                            Regex regex = new Regex(pattern);
                            // Extract matching content
                            MatchCollection matches = regex.Matches(listLogic.LogicExpression);
                            foreach (Match match in matches)
                            {
                                var logicHashKey = match.Groups[1].Value;
                                string telemeteringRedisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + 
                                    _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);

                                var ycLiveData = _redisDataRepository.TelemeteringModelListRedis.HashSetGetOne(telemeteringRedisKey, logicHashKey);
                                if (ycLiveData != null)
                                {
                                    listLogic.LogicExpression = listLogic.LogicExpression.Replace(logicHashKey, ycLiveData.ResultValue.ToString());
                                }
                                
                                string telesignalisationRedisKey = _redisDataRepository.TelesignalisationModelListRediskey + "_" + 
                                    _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);

                                var yxLiveData = _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOne(telesignalisationRedisKey, logicHashKey);
                                if (yxLiveData != null)
                                {
                                    if (yxLiveData.RemoteType == RemoteTypeEnum.DoublePoint)
                                    {
                                        yxLiveData.ResultValue = yxLiveData.ResultValue > 2 ? 0 : yxLiveData.ResultValue - 1;
                                    }
                                    else
                                    {
                                        yxLiveData.ResultValue = yxLiveData.ResultValue > 2 ? 0 : yxLiveData.ResultValue;
                                    }
                                    listLogic.LogicExpression = listLogic.LogicExpression.Replace(logicHashKey, yxLiveData.ResultValue.ToString());
                                }
                            }

                            try
                            {
                                listLogic.LogicExpression = listLogic.LogicExpression.Replace("{", "").Replace("}", "");
                                var result = Eval.Execute<bool>(listLogic.LogicExpression);
                                if (result)
                                {
                                    Guid alarmEquipmentInfoId = default;
                                    string equipmentName = default;
                                    string yxycName = default;
                                    
                                    if (yc != null)
                                    {
                                        alarmEquipmentInfoId = yc.EquipmentInfoId.Value;
                                        equipmentName = yc.EquipmentInfo.Name;
                                        yxycName = yc.Name;
                                    }
                                    else if (yx != null)
                                    {
                                        alarmEquipmentInfoId = yx.EquipmentInfoId.Value;
                                        yxycName = yx.Name;
                                    }
                                    
                                    var protectionDevices = _webApiRequest.GetProtectionDevicesBySecondaryCircuitList(listLogic.SecondaryCircuitId);
                                    SecondaryCircuitComponent secondaryCircuitComponent = new SecondaryCircuitComponent()
                                    {
                                        AlarmEquipmentInfoId = alarmEquipmentInfoId,
                                        IncludeEquipmentInfoIds = protectionDevices.Select(t => t.EquipmentInfoId).ToList(),
                                        SecondaryCircuitId = listLogic.SecondaryCircuitId,
                                        ComponentName = equipmentName,
                                        HandlingMeasures = "",
                                        Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                        AbnormalReason = $"{equipmentName}的{yxycName}的状态异常",
                                    };

                                    SendSecondaryCircuitDiagnosticsInfoAsync(secondaryCircuitComponent);
                                    
                                    // Pending
                                    string telesignalisationRedisKey = _redisDataRepository.TelesignalisationModelListRediskey + "_" + 
                                        _settingModel.GetDatacatgoryName("无");
                                    var yxData = _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOne(
                                        telesignalisationRedisKey, 
                                        listLogic.TelesignalisationAddr);
                                        
                                    if (yxData != null)
                                    {
                                        yxData.ResultValue = yxData.RemoteType == RemoteTypeEnum.DoublePoint ? 2 : 1;
                                        _webApiRequest.SendVisualYx(yxData);
                                    }
                                }
                                MonitoringEventBus.LogHandler($"判定结果为{result} 判定表达式{listLogic.LogicExpression}", "二次回路遥信判定");
                            }
                            catch (Exception ex)
                            {
                                MonitoringEventBus.LogHandler(ex.ToString(), "错误信息");
                            }
                        }
                    }
                });
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// Sends secondary circuit diagnostics information
        /// </summary>
        public async Task SendSecondaryCircuitDiagnosticsInfoAsync(SecondaryCircuitComponent secondaryCircuitComponent)
        {
            var alarmMessage = new AlarmMessage
            {
                HandlingMeasures = secondaryCircuitComponent.HandlingMeasures,
                AlarmContent = secondaryCircuitComponent.AbnormalReason,
                AlarmDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                EquipmentInfoId = secondaryCircuitComponent.AlarmEquipmentInfoId,
                Id = $"{secondaryCircuitComponent.AlarmEquipmentInfoId}:{secondaryCircuitComponent.AbnormalReason}"
            };
            
            var alarmMessageList = await _redisDataRepository.AlarmMessageRedis.HashSetGetAllAsync(nameof(AlarmMessage));
            var isExistAlarm = alarmMessageList.Any(t => t.EquipmentInfoId == alarmMessage.EquipmentInfoId
                                                && t.AlarmContent == alarmMessage.AlarmContent
                                                && t.HandlingMeasures == alarmMessage.HandlingMeasures
                                            );
            if (!isExistAlarm)
            {
                _redisDataRepository.AlarmMessageRedis.PublishAsync("alarmMessageChannel", alarmMessage);
                _redisDataRepository.AlarmMessageRedis.HashSetUpdateOneAsync(nameof(AlarmMessage), alarmMessage.Id, alarmMessage);
                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(AlarmMessage) + DateTime.Now.Year;
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(alarmMessage.ToBsonDocument());

                string redisChannel = "secondaryCircuitDiagnosticsChannel";
                await _redisDataRepository.SecondaryCircuitComponentRedis.PublishAsync(redisChannel, secondaryCircuitComponent);

                await _redisDataRepository.SecondaryCircuitComponentRedis.HashSetUpdateOneAsync(
                    nameof(SecondaryCircuitComponent), 
                    secondaryCircuitComponent.SecondaryCircuitId.ToString(), 
                    secondaryCircuitComponent);
            }
        }
    }
}
