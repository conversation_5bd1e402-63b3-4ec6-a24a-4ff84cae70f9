﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaVideoPatrol
    {
        public IaVideoPatrol()
        {
            IaVideoPatrolItems = new HashSet<IaVideoPatrolItem>();
        }

        public string PatId { get; set; } = null!;
        public string PatName { get; set; } = null!;
        public string PrjId { get; set; } = null!;
        public string? PatType { get; set; }
        public int? SeqNo { get; set; }
        public string? Comment { get; set; }
        public DateTime CreTime { get; set; }

        public virtual ImProject Prj { get; set; } = null!;
        public virtual ICollection<IaVideoPatrolItem> IaVideoPatrolItems { get; set; }
    }
}
