﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImGlyphDevDatum
    {
        public string GlyphId { get; set; } = null!;
        public string? MeasureDataId { get; set; }
        public string? MeasureDataName { get; set; }
        public string? ControlDataId { get; set; }
        public string? ControlDataName { get; set; }

        public virtual ImGlyph Glyph { get; set; } = null!;
    }
}
