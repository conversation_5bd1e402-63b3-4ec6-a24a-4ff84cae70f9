﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbBumenlx
    {
        public TbBumenlx()
        {
            TbBumen = new HashSet<TbBumen>();
        }

        public string Bmlxbm { get; set; }
        public string Bmlxmc { get; set; }

        public virtual ICollection<TbBumen> TbBumen { get; set; }
    }
}
