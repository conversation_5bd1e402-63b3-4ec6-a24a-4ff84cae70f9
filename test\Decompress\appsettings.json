﻿{
  "ConnectionStrings": {
    "MongoDBSetting": {
      "Host": "*************",
      "Port": "37017",
      "DatabaseName": "isas_mongodb",
      "IsAuth": "false",
      "UserName": "isasAdmin",
      "PassWord": "123456"
    },
    "DataMonitoring": {
      "url": "ws://*************:9090/DataMonitoring"
    },
    "websocket": {
      "port": 9090,
      "path": "DataMonitoring"
    },
    "RedisSetting": {
      "Host": "*************",
      "Port": "36379",
      "Auth": "",
      "Name": "",
      "ClusterType": null,
      "DefaultDatabaseIndex": "0"
    }
  },
  "ServiceConfig": {
    "SubstationId": null,
    "SubstationName": ""
  },
  "SysBaseConfig": {
    "WebAddrUrl": "http://*************:8090",
    "WebExternBaseUrl": "https://*************:4431"
  }
}