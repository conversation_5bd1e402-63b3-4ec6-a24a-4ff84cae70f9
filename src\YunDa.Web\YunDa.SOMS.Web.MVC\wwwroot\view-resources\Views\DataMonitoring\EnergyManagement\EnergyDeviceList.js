var energyDeviceList = {
    editFormId: "editEnergyDeviceForm",
    mainTableId: "energyDeviceTable",
    mainTableHeight: 0,
    toolBarVueId: "energyDeviceToolBar",
    toolBarVue: null,
    editorModalVueID: "energyDeviceModal",
    editorModalVue: null,
    deviceTypeEnumMap: {
        "1": "牵引变",
        "2": "AT变",
        "3": "馈线",
    },
    statusEnumMap: {
        "0": "停用",
        "1": "正常",
        "2": "故障",
        "3": "维护中"
    },

    // 初始化列表
    initListFunc: function (tHeight) {
        energyDeviceList.mainTableHeight = tHeight;
        energyDeviceList.initToolbarByVue();
    },

    // 初始化工具栏Vue
    initToolbarByVue: function () {
        if (!energyDeviceList.toolBarVueId) return;
        
        energyDeviceList.toolBarVue = new Vue({
            el: "#" + energyDeviceList.toolBarVueId,
            data: {
                itemName: null,
                deviceType: '-1'
            },
            mounted: function () {
                energyDeviceList.initEditModalByVue();
            },
            methods: {
                searchHit: function () {
                    energyDeviceList.refreshTable();
                },
                handleDeviceTypeChange: function (value) {
                    console.log('设备类型已变更为:', value);
                    energyDeviceList.refreshTable();
                },
                addHit: function () {
                    if (substationTree.openNodeStationId === null) {
                        if (substationTree.selectNodeArr.count === 0) {
                            layer.alert("请选择变电所！");
                            return;
                        } else {
                            substationTree.openNodeStationId = substationTree.selectNodeArr[0].id;
                        }
                    }

                    energyDeviceList.initEditModalValues("");
                    $("#" + energyDeviceList.editorModalVueID).modal("show");
                },
                deleteHit: function () {
                    let bar = energyDeviceList.getSelectItem();
                    let arrIds = new Array();
                    bar.forEach(r => arrIds.push(r.id));
                    
                    if (arrIds.length == 0) {
                        layer.alert("请选择要删除的项！");
                        return;
                    }
                    
                    isas.ajax({
                        url: AppServiceUrl.EnergyDevice_DeleteDevices,
                        data: JSON.stringify(arrIds),
                        isHideSuccessMsg: false,
                        confirm: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                energyDeviceList.refreshTable();
                            }
                        }
                    });
                }
            }
        });
    },

    // 获取选中的行
    getSelectItem: () => $("#" + energyDeviceList.mainTableId).bootstrapTable("getSelections"),
    
    // 初始化编辑模态框Vue
    initEditModalByVue: function () {
        if (!energyDeviceList.editorModalVueID) return;
        
        energyDeviceList.editorModalVue = new Vue({
            el: "#" + energyDeviceList.editorModalVueID,
            data: {
                id: null,
                header: "添加能耗设备",
                seqNo: 1,
                deviceType: null,
                deviceTypes: [],
                status: null,
                statusOptions: [
                    { value: "0", text: "停用" },
                    { value: "1", text: "正常" },
                    { value: "2", text: "故障" },
                    { value: "3", text: "维护中" }
                ],
                equipmentInfoId: null,
                equipmentInfos: [],
                isOperating: false,
                lastOperationTime: null,
                monitoringConfig: null,
                isActive: true,
                name:'',
                remark: null
            },
            
            mounted: function () {
                let bar = this;
                // 加载设备类型
                bar.deviceTypes = [
                    { value: "1", text: "牵引变" },
                    { value: "2", text: "AT变" },
                    { value: "3", text: "馈线" },
                ];
                
                // 加载关联设备列表
                this.loadEquipmentInfos();
            },
            
            methods: {
                setDeviceType: function (value) {
                    this.deviceType = value;
                },
                
                setStatus: function (value) {
                    this.status = value;
                },
                
                setEquipmentInfoId: function (value) {
                    this.equipmentInfoId = value;
                },
                
                setLastOperationTime: function (value) {
                    this.lastOperationTime = value;
                },
                
                save: function () {
                    if (!$("#" + energyDeviceList.editFormId).valid()) return;
                    
                    let data = {
                        id: this.id,
                        seqNo: parseInt(this.seqNo),
                        deviceType: parseInt(this.deviceType),
                        status: parseInt(this.status),
                        equipmentInfoId: this.equipmentInfoId ? this.equipmentInfoId : null,
                        isOperating: this.isOperating,
                        lastOperationTime: this.lastOperationTime,
                        monitoringConfig: this.monitoringConfig,
                        isActive: this.isActive,
                        name:this.name,
                        remark: this.remark
                    };
                    
                    isas.ajax({
                        url: AppServiceUrl.EnergyDevice_CreateOrUpdateDevice,
                        data: JSON.stringify(data),
                        isHideSuccessMsg: false,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                $("#" + energyDeviceList.editorModalVueID).modal("hide");
                                energyDeviceList.refreshTable();
                            }
                        }
                    });
                }
                ,
                 // 加载关联设备列表
                loadEquipmentInfos: function () {
                    let bar = this;
                    isas.ajax({
                        url: AppServiceUrl.EquipmentInfo_FindEquipmentInfoForSelect,
                        data: JSON.stringify({
                            transformerSubstationId: substationTree.openNodeStationId ? substationTree.openNodeStationId : null,
                        }),
                        isHideSuccessMsg: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                bar.equipmentInfos = rst.result.resultData;
                            }
                        }
                    });
                },
            }
        });
    },
    
   

    // 初始化编辑模态框各值
    initEditModalValues: function (uniqueId) {
        try {
            if (!energyDeviceList.editorModalVue || !energyDeviceList.mainTableId) return;
            
            var rowData = null;
            if (uniqueId)
                rowData = $("#" + energyDeviceList.mainTableId).bootstrapTable("getRowByUniqueId", uniqueId);
            
            energyDeviceList.resetFormValidate();
            
            if (!rowData) {
                rowData = {
                    id: null,
                    seqNo: 1,
                    deviceType: null,
                    status: "1",
                    equipmentInfoId: null,
                    isOperating: false,
                    lastOperationTime: null,
                    monitoringConfig: null,
                    isActive: true,
                    name:'',
                    remark: null
                };
                
                energyDeviceList.editorModalVue.header = "添加能耗设备";
            }
            
            if (rowData.id) {
                energyDeviceList.editorModalVue.header = "编辑能耗设备";
            }
            
            // 设置记录单各参数
            energyDeviceList.editorModalVue.id = rowData.id;
            energyDeviceList.editorModalVue.seqNo = rowData.seqNo;
            energyDeviceList.editorModalVue.deviceType = rowData.deviceType ? rowData.deviceType.toString() : null;
            energyDeviceList.editorModalVue.status = rowData.status ? rowData.status.toString() : "1";
            energyDeviceList.editorModalVue.equipmentInfoId = rowData.equipmentInfoId;
            energyDeviceList.editorModalVue.isOperating = rowData.isOperating;
            energyDeviceList.editorModalVue.lastOperationTime = rowData.lastOperationTime;
            energyDeviceList.editorModalVue.monitoringConfig = rowData.monitoringConfig;
            energyDeviceList.editorModalVue.isActive = rowData.isActive;
            energyDeviceList.editorModalVue.remark = rowData.remark;
            energyDeviceList.editorModalVue.name = rowData.name;
            
        } catch (e) {
            console.log(e);
        }
    },

    // 表单验证
    initEditFormValidate: function () {
        if (!energyDeviceList.editFormId) return;
        
        $("#" + energyDeviceList.editFormId).validate({
            ignore: ":hidden:not(select)",
            rules: {
                deviceType: {
                    required: true
                },
                status: {
                    required: true
                }
            },
            messages: {
                deviceType: {
                    required: "设备类型不能为空"
                },
                status: {
                    required: "设备状态不能为空"
                }
            }
        });
    },
    
    // 刷新表格
    refreshTable: function () {
        $("#" + energyDeviceList.mainTableId).bootstrapTable("refresh");
    },
    
    // 重置验证
    resetFormValidate: function () {
        $("#" + energyDeviceList.editFormId).validate().resetForm();
        $("#" + energyDeviceList.editFormId).find(".form-group").removeClass("has-success").removeClass("has-error");
    },

    // 初始化表格
    initTable: function () {
        if (!energyDeviceList.mainTableId) return;
        
        isas.bootstrapTable({
            el: "#" + energyDeviceList.mainTableId,
            height: energyDeviceList.mainTableHeight,
            toolBarEl: "#" + energyDeviceList.toolBarVueId,
            url: AppServiceUrl.EnergyDevice_GetDevices,
            isInitData: false,
            singleSelect: false,
            pageList: [14, 25, 50, 100, 200],
            pageSize: 14,
            showColumns: true,
            queryParams: function (params) {
                let c = {
                    pageIndex: params.offset / params.limit + 1,
                    pageSize: params.limit,
                    searchCondition: {
                        name: energyDeviceList.toolBarVue === null ? null : energyDeviceList.toolBarVue.itemName,
                        transformerSubstationId: substationTree.openNodeStationId,
                        deviceType: energyDeviceList.toolBarVue && energyDeviceList.toolBarVue.deviceType != '-1' ? 
                        parseInt(energyDeviceList.toolBarVue.deviceType) : null
                    },
                    sorting: ""
                };
                return c;
            },
            columns: [
                {
                    checkbox: true,
                    align: "center",
                    valign: "middle",
                    class: "checkbox checkbox-primary",
                    width: 20
                },
                {
                    title: "行号",
                    align: "center",
                    valign: "middle",
                    width: 20,
                    formatter: function (value, row, index) {
                        let pageSize = $("#" + energyDeviceList.mainTableId).bootstrapTable("getOptions").pageSize;
                        let pageNumber = $("#" + energyDeviceList.mainTableId).bootstrapTable("getOptions").pageNumber;
                        return (pageNumber - 1) * pageSize + 1 + index;
                    }
                },
                {
                    field: "seqNo",
                    title: "序号",
                    align: "center",
                    valign: "middle",
                    width: 20
                },
                {
                    field: "name",
                    title: "名称",
                    align: "center",
                    valign: "middle",
                },
                {
                    field: "equipmentInfo",
                    title: "关联设备",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        return value ? value.name : '-';
                    }
                },
                {
                    field: "deviceType",
                    title: "设备类型",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        return energyDeviceList.deviceTypeEnumMap[value] || "未知";
                    }
                },
                {
                    field: "status",
                    title: "设备状态",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        return energyDeviceList.statusEnumMap[value] || "未知";
                    }
                },
                {
                    field: "isOperating",
                    title: "是否投运",
                    align: "center",
                    valign: "middle",
                    width: 50,
                    formatter: function (value) {
                        return value ? 
                            "<span class='text-success' style='font-size:16px;'><i class='fa fa-check'></i></span>" :
                            "<span class='text-danger' style='font-size:16px;'><i class='fa fa-close'></i></span>";
                    }
                },
                {
                    field: "lastOperationTime",
                    title: "最后投运时间",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
                    }
                },
                {
                    field: "isActive",
                    title: "是否启用",
                    align: "center",
                    valign: "middle",
                    width: 50,
                    formatter: function (value) {
                        return value ? 
                            "<span class='text-success' style='font-size:16px;'><i class='fa fa-check'></i></span>" :
                            "<span class='text-danger' style='font-size:16px;'><i class='fa fa-close'></i></span>";
                    }
                },
                {
                    field: 'operation',
                    visible: true,
                    title: "操作",
                    align: "center",
                    valign: "middle",
                    width: 60,
                    formatter: function (value, row) {
                        let btnHtml = "<button class='btn-link' title='修改' data-toggle='modal' data-target='#" +
                            energyDeviceList.editorModalVueID +
                            "' onclick='energyDeviceList.initEditModalValues(\"" +
                            row.id +
                            "\")'>";
                        btnHtml += "<i class='fa fa-pencil' style='font-size:18px;'></i>";
                        btnHtml += "</button>";
                        return btnHtml;
                    }
                }
            ]
        });
    }
};
