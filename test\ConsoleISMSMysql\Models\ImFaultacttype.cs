﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImFaultacttype
    {
        public int Actcode { get; set; }
        public int Faultcode { get; set; }
        public string Actname { get; set; }

        public virtual ImFaulttype FaultcodeNavigation { get; set; }
    }
}
