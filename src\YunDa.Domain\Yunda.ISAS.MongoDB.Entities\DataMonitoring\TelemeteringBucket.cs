using Abp.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using Yunda.ISAS.MongoDB.Entities.Helper;

namespace Yunda.SOMS.MongoDB.Entities.DataMonitoring
{
    /// <summary>
    /// 遥测数据分桶存储
    /// </summary>
    public class TelemeteringBucket : Entity<Guid>
    {
        /// <summary>
        /// 关联的遥测ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 遥测名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public virtual string EquipmentInfoName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public virtual string Unit { get; set; }

        /// <summary>
        /// 数据源类型
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual int? DataSourceCategory { get; set; }

        /// <summary>
        /// 桶开始时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime StartTime { get; set; }

        /// <summary>
        /// 桶结束时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime EndTime { get; set; }

        /// <summary>
        /// 数据点数量
        /// </summary>
        public virtual int Count { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public virtual float MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public virtual float MaxValue { get; set; }

        /// <summary>
        /// 平均值
        /// </summary>
        public virtual float AvgValue { get; set; }

        /// <summary>
        /// 总和
        /// </summary>
        public virtual float SumValue { get; set; }

        /// <summary>
        /// 第一个值
        /// </summary>
        public virtual float FirstValue { get; set; }

        /// <summary>
        /// 最后一个值
        /// </summary>
        public virtual float LastValue { get; set; }

        /// <summary>
        /// 标准差
        /// </summary>
        public virtual float StdDeviation { get; set; }

        /// <summary>
        /// 测量数据列表
        /// </summary>
        public virtual List<TelemeteringPoint> Measurements { get; set; } = new List<TelemeteringPoint>();
        public int ModifiedCount { get; set; }
    }

    /// <summary>
    /// 遥测数据点
    /// </summary>
    public class TelemeteringPoint
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public virtual DateTime Timestamp { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public virtual float Value { get; set; }

        /// <summary>
        /// 存储方法 1：周期保存  2：变化保存
        /// </summary>
        public virtual int SaveMethod { get; set; }
    }
} 