using Abp.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using Yunda.ISAS.MongoDB.Entities.Helper;

namespace Yunda.SOMS.MongoDB.Entities.DataMonitoring
{
    /// <summary>
    /// 遥测数据统计结果
    /// </summary>
    public class TelemeteringStatisticsResult : Entity<Guid>
    {
        /// <summary>
        /// 关联的遥测ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid TelemeteringConfigurationId { get; set; }
        
        /// <summary>
        /// 设备名称
        /// </summary>
        public virtual string EquipmentInfoName { get; set; }

        /// <summary>
        /// 遥测名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 结果值
        /// </summary>
        public virtual float ResultValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public virtual string Unit { get; set; }

        /// <summary>
        /// 原始结果时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public virtual DateTime ResultTime { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual StatisticsTypeEnum StatisticsType { get; set; }

        /// <summary>
        /// 统计时间间隔
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual FixedIntervalEnum IntervalType { get; set; }

        /// <summary>
        /// 统计时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime StatisticsDateTime { get; set; }

        /// <summary>
        /// 原始数据数量
        /// </summary>
        public virtual int DataCount { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public virtual DateTime CreationTime { get; set; } = DateTime.Now;
    }
} 