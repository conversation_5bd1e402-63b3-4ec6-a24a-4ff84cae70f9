﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFltDistDevice
    {
        public string DeviceId { get; set; } = null!;
        public string GrpId { get; set; } = null!;
        public int SeqNo { get; set; }
        public string TypeInGrp { get; set; } = null!;

        public virtual ImProtectDevice Device { get; set; } = null!;
        public virtual ImFltDistDevGrp Grp { get; set; } = null!;
    }
}
