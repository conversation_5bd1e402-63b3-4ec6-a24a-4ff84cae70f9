using System;
using System.ComponentModel;
using YunDa.ISAS.DataTransferObject.CommonDto;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsConfigDto
{
    /// <summary>
    /// 遥测统计配置搜索条件
    /// </summary>
    public class TelemeteringStatisticsConfigSearchConditionInput : ISASMySQLSearchInput
    {
        /// <summary>
        /// 关联的遥测配置ID
        /// </summary>
        public virtual Guid? TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public virtual int? StatisticsType { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public virtual int? IntervalType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [DefaultValue(true)]
        public virtual bool? IsActive { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public virtual Guid? EquipmentInfoId { get; set; }
    }
} 