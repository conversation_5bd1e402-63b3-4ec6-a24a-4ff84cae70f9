using Abp.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using Yunda.ISAS.MongoDB.Entities.Helper;

namespace Yunda.SOMS.MongoDB.Entities.DataMonitoring
{
    /// <summary>
    /// 遥测统计数据分桶存储
    /// </summary>
    public class TelemeteringStatisticsBucket : Entity<Guid>
    {
        /// <summary>
        /// 关联的遥测ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 遥测名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public virtual string EquipmentInfoName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public virtual string Unit { get; set; }

        /// <summary>
        /// 数据源类型
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual int? DataSourceCategory { get; set; }

        /// <summary>
        /// 统计时间间隔
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual FixedIntervalEnum IntervalType { get; set; }

        /// <summary>
        /// 桶开始时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime StartTime { get; set; }

        /// <summary>
        /// 桶结束时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime EndTime { get; set; }

        /// <summary>
        /// 统计结果数量
        /// </summary>
        public virtual int Count { get; set; }

        /// <summary>
        /// 年份
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual int Year { get; set; }

        /// <summary>
        /// 月份（1-12）
        /// </summary>
        public virtual int Month { get; set; }

        /// <summary>
        /// 统计结果列表
        /// </summary>
        public virtual List<StatisticsResult> Results { get; set; } = new List<StatisticsResult>();
        public object ModifiedCount { get; set; }
    }

    /// <summary>
    /// 统计结果
    /// </summary>
    public class StatisticsResult
    {
        /// <summary>
        /// 统计类型
        /// </summary>
        public virtual StatisticsTypeEnum StatisticsType { get; set; }

        /// <summary>
        /// 统计时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public virtual DateTime StatisticsDateTime { get; set; }

        /// <summary>
        /// 结果值
        /// </summary>
        public virtual float ResultValue { get; set; }

        /// <summary>
        /// 原始数据数量
        /// </summary>
        public virtual int DataCount { get; set; }

        /// <summary>
        /// 原始结果时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public virtual DateTime ResultTime { get; set; }
    }
} 