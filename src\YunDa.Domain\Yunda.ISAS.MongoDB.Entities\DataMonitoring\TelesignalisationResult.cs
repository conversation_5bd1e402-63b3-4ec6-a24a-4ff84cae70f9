﻿using Abp.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using Yunda.ISAS.MongoDB.Entities.Helper;

namespace Yunda.ISAS.MongoDB.Entities.DataMonitoring
{
    /// <summary>
    /// 遥信实时数据结果
    /// </summary>
    public class TelesignalisationResult : Entity<Guid>
    {
        /// <summary>
        /// 遥信ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid? TelesignalisationConfigurationId { get; set; }

        /// <summary>
        /// 结果值
        /// </summary>
        public virtual int ResultValue { get; set; }

        /// <summary>
        /// 结果时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime ResultTime { get; set; }
        public virtual string EquipmentInfoName { get; set; }
        public virtual string Name { get; set; }

        /// <summary>
        /// 存储方法
        /// 1：周期保存  2：变化保存
        /// </summary>
        public virtual int SaveMethod { get; set; }
    }
}