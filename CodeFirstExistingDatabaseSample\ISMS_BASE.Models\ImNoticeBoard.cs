﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImNoticeBoard
    {
        public string NbId { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string YkId { get; set; } = null!;
        public string? Description { get; set; }
        public string OperUserName { get; set; } = null!;
        public string? OperWholeName { get; set; }
        public DateTime ExecuteTime { get; set; }

        public virtual ImStation StatCodeNavigation { get; set; } = null!;
        public virtual ImDeviceYk Yk { get; set; } = null!;
    }
}
