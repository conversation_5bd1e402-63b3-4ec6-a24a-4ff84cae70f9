﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImProject
    {
        public ImProject()
        {
            ImDiagram = new HashSet<ImDiagram>();
            ImProgcontrol = new HashSet<ImProgcontrol>();
            ImStation = new HashSet<ImStation>();
        }

        public string Id { get; set; }
        public string Name { get; set; }
        public string Protcommmgrip { get; set; }
        public DateTime Createtime { get; set; }
        public string Creator { get; set; }

        public virtual ICollection<ImDiagram> ImDiagram { get; set; }
        public virtual ICollection<ImProgcontrol> ImProgcontrol { get; set; }
        public virtual ICollection<ImStation> ImStation { get; set; }
    }
}
