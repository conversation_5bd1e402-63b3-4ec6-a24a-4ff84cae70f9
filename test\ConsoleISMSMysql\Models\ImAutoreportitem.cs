﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAutoreportitem
    {
        public string Itemid { get; set; }
        public string Rptid { get; set; }
        public int Rowno { get; set; }
        public int Colno { get; set; }
        public string Itemtype { get; set; }
        public string Datatype { get; set; }
        public string Dataid { get; set; }
        public string Dataname { get; set; }
        public string Time1 { get; set; }
        public string Time2 { get; set; }
        public string Timecell { get; set; }
        public string Unit { get; set; }
        public string Showunit { get; set; }

        public virtual ImAutoreport Rpt { get; set; }
    }
}
