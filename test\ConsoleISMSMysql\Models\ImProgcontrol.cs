﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImProgcontrol
    {
        public ImProgcontrol()
        {
            ImProgcontrolitem = new HashSet<ImProgcontrolitem>();
        }

        public string Id { get; set; }
        public string Prjid { get; set; }
        public string Progctlname { get; set; }
        public DateTime Createtime { get; set; }

        public virtual ImProject Prj { get; set; }
        public virtual ICollection<ImProgcontrolitem> ImProgcontrolitem { get; set; }
    }
}
