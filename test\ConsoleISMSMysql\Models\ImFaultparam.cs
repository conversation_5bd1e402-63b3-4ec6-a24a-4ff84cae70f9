﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImFaultparam
    {
        public int Paramcode { get; set; }
        public string Paramname { get; set; }
        public string Paramsym1 { get; set; }
        public double Paramcof1 { get; set; }
        public string Paramsym2 { get; set; }
        public double Paramcof2 { get; set; }
    }
}
