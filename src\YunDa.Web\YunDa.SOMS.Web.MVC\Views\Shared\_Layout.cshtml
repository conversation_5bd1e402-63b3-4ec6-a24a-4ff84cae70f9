﻿<!DOCTYPE html>
<html>

<head style="font-family:'Microsoft YaHei';background-color:#20335d">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>智能变电站辅助系统 -  @ViewData["Title"]</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="~/css/font-awesome.min.css?v=4.7" rel="stylesheet">
    <link href="~/css/bootstrap.min.css?v=3.3.7" rel="stylesheet">
    <!-- Bootstrap table -->
    <link href="~/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">
    @*<link href="~/css/plugins/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css" rel="stylesheet">*@
    <link href="~/css/plugins/bootstrap-checkbox/bootstrap-checkbox-new.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/element-ui/theme-chalk/index.css">
    @RenderSection("styles", required: false)
    <link href="~/css/animate.css" rel="stylesheet">
    <link href="~/css/style.css?v=4.1.0" rel="stylesheet">
</head>
<body class="" style="font-family:'Microsoft YaHei';background-color:#20335d">
    @{
        bool isEdit = false;
        bool.TryParse(ViewData["IsEdit"]?.ToString(), out isEdit);
        //string accessToken = ViewData["AccessToken"]?.ToString();
        //accessToken = string.IsNullOrEmpty(accessToken) ? "" : accessToken;
    }
    <input type="hidden" id="isEditInput" value="@isEdit.ToString().ToLower()" />
    @*<input type="hidden" id="accessTokenInput" value="@accessToken" />*@
    <div class="wrapper wrapper-content full-height" style="padding-top:5px;padding-bottom:5px;">
        @RenderBody()
    </div>
    <script src="~/js/jquery-3.4.1.min.js?v=3.4.1" type="text/javascript"></script>
    <script src="~/js/popper.min.js?v=1.15.0" type="text/javascript"></script>
    <script src="~/js/bootstrap.min.js?v=3.3.7" type="text/javascript"></script>
    <script src="~/js/vue.min.js?v=2.6.10" type="text/javascript"></script>
    <script src="~/js/plugins/layer/layer.min.js" type="text/javascript"></script>
    <!-- Bootstrap table -->
    <script src="~/js/plugins/bootstrap-table/bootstrap-table-new.min.js" type="text/javascript"></script>
    <script src="~/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js" type="text/javascript"></script>

    <!-- jQuery Validation plugin javascript-->
    <script src="~/js/plugins/validate/jquery.validate.min.js" type="text/javascript"></script>
    <script src="~/js/plugins/validate/messages_zh.min.js" type="text/javascript"></script>

    @* xss安全插件 *@
    <script src="~/js/plugins/DOMPurify/dist/purify.js" type="text/javascript"></script>

    <script src="~/js/element-ui/index.js"></script>
    <script src="~/js/axios.js"></script>
    <!-- 自定义项目js-->
    <script src="~/view-resources/isas_const.js" type="text/javascript"></script>
    <script src="~/view-resources/isas.js" type="text/javascript"></script>
    @RenderSection("scripts", required: false)
    <script>
        $(function () {
            authorityManagement("button");
            authorityManagement("input");
            authorityManagement("div");
            authorityManagement("table");
        });
        function authorityManagement(elStr) {
            let isEdit = $("#isEditInput").val() == "true";
            if (isEdit) {
                if (elStr == "table")
                    $(elStr + '[authority-management=true]').bootstrapTable('showColumn', 'operation');
                else
                    $(elStr + "[authority-management=true]").show();

            } else {
                if (elStr == "table")
                    $(elStr + '[authority-management=true]').bootstrapTable('hideColumn', 'operation');
                else
                    $(elStr + "[authority-management=true]").hide();
            }
        }
    </script>
</body>
</html>