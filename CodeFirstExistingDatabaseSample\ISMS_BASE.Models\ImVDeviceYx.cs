﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVDeviceYx
    {
        public string Id { get; set; } = null!;
        public string Yxname { get; set; } = null!;
        public string? YxType { get; set; }
        public string? SwOnStr { get; set; }
        public string? SwOffStr { get; set; }
        public string? SwUncertStr { get; set; }
        public string AlertLevel { get; set; } = null!;
        public string NormalState { get; set; } = null!;
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
    }
}
