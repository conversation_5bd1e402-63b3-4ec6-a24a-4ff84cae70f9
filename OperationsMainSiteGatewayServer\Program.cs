﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Abp;
using Abp.Modules;
using Castle.Core;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Serilog;
using Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.Server;
using Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.TestData;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.DeviceHistoryChange;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.DeviceTcpData;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.OperationReport;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer
{
    public class Program
    {
        private static AbpBootstrapper _bootstrapper;
        private static DotNettyTcpServer _dotNettyTcpServer;
        public static async Task Main(string[] args)
        {
           
            try
            {
                _bootstrapper = InitializeBootstrapper();
                // 初始化 ABP 应用程序
                var configuration = new ConfigurationBuilder()
               .AddJsonFile("appsettings.json")
               .Build();
                ConfigureLogger(configuration);
                Log.Information("程序启动");
              
                await RunApplication(ConfigureServerAddr(configuration));
                Log.Information("程序运行完成");
                using (var cts = new CancellationTokenSource())
                {
                    Log.Information("主程序正在运行...");
                    Log.Information("按 Ctrl+C 停止程序...");
                    Console.CancelKeyPress += (sender, e) =>
                    {
                        e.Cancel = true; // 阻止程序立即退出
                        cts.Cancel();    // 触发取消
                    };

                    try
                    {
                        // 创建一个任务来监听用户输入
                        var inputTask = Task.Run(() =>
                        {
                            while (!cts.Token.IsCancellationRequested)
                            {
                                Console.WriteLine("请输入数字选择操作：");
                                Console.WriteLine("1: 执行装置履历测试");
                                Console.WriteLine("2: 执行设备参数报文测试");
                                Console.WriteLine("3: 执行运行诊断报文测试");
                                Console.WriteLine("4: 执行运行状态报文测试");
                                Console.WriteLine("按 'q' 退出程序");
                                string input = Console.ReadLine();

                                if (input == "1")
                                {

                                    SimulateData.Test1(_dotNettyTcpServer);
                                    // 在这里执行操作1的逻辑
                                }
                                else if (input == "2")
                                {

                                    SimulateData.Test2(_dotNettyTcpServer);
                                    // 在这里执行操作2的逻辑
                                }
                                else if (input == "3")
                                {
                                    SimulateData.Test3(_dotNettyTcpServer);
                                    // 在这里执行操作3的逻辑
                                }
                                else if (input == "4")
                                {
                                    SimulateData.Test4(_dotNettyTcpServer);
                                    // 在这里执行操作3的逻辑
                                }
                                else if (input?.ToLower() == "q")
                                {
                                    Log.Information("退出程序...");
                                    cts.Cancel(); // 用户选择退出时触发取消
                                    break;
                                }
                                else
                                {
                                    Log.Information("无效的输入，请重新选择...");
                                }
                            }
                        });
                        await Task.Delay(Timeout.Infinite, cts.Token); // 无限等待，直到取消
                    }
                    catch (TaskCanceledException)
                    {
                        Log.Information("程序停止...");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "程序发生了致命错误");
            }
            finally
            {
                Cleanup();
            }
        }

        /// <summary>
        /// 配置日志记录器
        /// </summary>
        private static void ConfigureLogger(IConfigurationRoot configuration)
        {
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
        }
        /// <summary>
        /// 配置服务端addr
        /// </summary>
        private static byte ConfigureServerAddr(IConfigurationRoot configuration)
        {
            return byte.Parse(configuration["addr"].ToString());
        }
        /// <summary>
        /// 初始化 ABP 引导程序
        /// </summary>
        private static AbpBootstrapper InitializeBootstrapper()
        {
            var bootstrapper = AbpBootstrapper.Create<OperationsGatewayAppModule>();
            bootstrapper.Initialize();
            return bootstrapper;
        }

        /// <summary>
        /// 运行应用程序逻辑
        /// </summary>
        private static async Task RunApplication(byte addr)
        {
            _dotNettyTcpServer = _bootstrapper.IocManager.Resolve<DotNettyTcpServer>();
            await _dotNettyTcpServer.RunServerAsync(addr);
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private  static async Task Cleanup()
        {
            var service = _bootstrapper.IocManager.Resolve<DotNettyTcpServer>();
            await service.StopSeverAsync();
            _bootstrapper?.Dispose();
            Log.CloseAndFlush();
        }
    }
}

