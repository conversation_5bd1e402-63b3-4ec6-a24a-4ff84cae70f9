﻿using DotNetty.Transport.Channels;
using DotNetty.Buffers;
using System;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using Serilog;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.Server
{
    public class HexDumpHandler : ChannelHandlerAdapter
    {
        public async override Task WriteAsync(IChannelHandlerContext context, object message)
        {
            if (message is IByteBuffer buffer)
            {
                // 将字节缓冲区转换为十六进制字符串
                string hexString = ByteBufferToHex(buffer);
                Debug.WriteLine($"发送的报文: {hexString}");
            }

            // 将消息传递给下一个处理器
           await base.WriteAsync(context, message);
        }

        private string ByteBufferToHex(IByteBuffer buffer)
        {
            StringBuilder hex = new StringBuilder(buffer.ReadableBytes * 2);
            for (int i = buffer.ReaderIndex; i < buffer.WriterIndex; i++)
            {
                byte b = buffer.GetByte(i);
                hex.AppendFormat("{0:X2} ", b);
            }
            return hex.ToString().Trim();
        }
        public override void ChannelRead(IChannelHandlerContext context, object message)
        {
            if (message is DotNetty.Buffers.IByteBuffer buffer)
            {
                try
                {
                    int length = buffer.ReadableBytes;
                    byte[] bytes = new byte[length];
                    buffer.GetBytes(buffer.ReaderIndex, bytes); // 读取字节内容

                    string hexDump = BitConverter.ToString(bytes).Replace("-", " ");
                    Log.Information("收到信息 (HexDump):");
                    Log.Information(hexDump);
                }
                finally
                {
                    buffer.Release(); // 确保释放缓冲区
                }
            }
            else
            {
                // 如果不是 IByteBuffer 类型的数据，直接传递给下一个处理器
                base.ChannelRead(context, message);
            }
        }
    }
}

