﻿using System;
using System.ComponentModel;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition
{
    /// <summary>
    /// 遥信配置查询条件
    /// </summary>
    public class TelesignalisationConfigurationSearchConditionInput : ISASMySQLSearchInput
    {
        /// <summary>
        /// 遥信名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 关联监控设备表
        /// </summary>
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 关联监控设备类型表
        /// </summary>
        public virtual Guid? EquipmentTypeId { get; set; }

        /// <summary>
        /// 变电所
        /// </summary>
        public virtual Guid? TransformerSubstationId { get; set; }

        /// <summary>
        /// 关联监控设备上级类型表
        /// </summary>
        public virtual Guid? EquipmentTypeTypeId { get; set; }

        /// <summary>
        /// 虚拟装置
        /// </summary>
        public virtual bool? IsVirtualDevice { get; set; }
        /// <summary>
        /// 是否有自检策略
        /// </summary>
        public virtual bool? IsHasSelfChecking { get; set; }
        /// <summary>
        /// 是否自检判定值
        /// </summary>
        public virtual bool? IsSelfCheckingValue { get; set; }
        public  virtual DataSourceCategoryEnum? DataSourceCategory { get; set; }
    }
}