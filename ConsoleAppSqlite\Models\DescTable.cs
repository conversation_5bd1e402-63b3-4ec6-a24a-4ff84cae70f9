﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleAppSqlite.Models
{
    public partial class DescTable
    {
        public long Id { get; set; }
        public string TbName { get; set; }
        public string TbDesc { get; set; }
        public string TbType { get; set; }
        public long? RowAd { get; set; }
    }
}
