﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbTable
    {
        public TbTable()
        {
            TbField = new HashSet<TbField>();
            TbVirtualtableBasetable = new HashSet<TbVirtualtable>();
            TbVirtualtableDerivetable = new HashSet<TbVirtualtable>();
        }

        public string Tableid { get; set; }
        public string Tablename { get; set; }
        public string Prcname { get; set; }
        public string Ctgy { get; set; }
        public string Dbconnname { get; set; }
        public string Orderby { get; set; }
        public int Seqno { get; set; }
        public string Isview { get; set; }
        public string Tblname4view { get; set; }

        public virtual ICollection<TbField> TbField { get; set; }
        public virtual ICollection<TbVirtualtable> TbVirtualtableBasetable { get; set; }
        public virtual ICollection<TbVirtualtable> TbVirtualtableDerivetable { get; set; }
    }
}
