﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImCurve
    {
        public ImCurve()
        {
            ImCurveitem = new HashSet<ImCurveitem>();
        }

        public string Id { get; set; }
        public string Curvetype { get; set; }
        public string Curvename { get; set; }
        public int Minutes { get; set; }
        public double Ymax { get; set; }
        public double Ymin { get; set; }
        public int Sampling { get; set; }
        public DateTime Cretime { get; set; }

        public virtual ImCurveType CurvetypeNavigation { get; set; }
        public virtual ICollection<ImCurveitem> ImCurveitem { get; set; }
    }
}
