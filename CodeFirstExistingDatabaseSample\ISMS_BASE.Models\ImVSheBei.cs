﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVSheBei
    {
        public string SheBeiId { get; set; } = null!;
        public int Sblxbm { get; set; }
        public string StatCode { get; set; } = null!;
        public string YunXingBh { get; set; } = null!;
        public string? SheBeiBh { get; set; }
        public string? SheBeiXh { get; set; }
        public string? ZhiZaoChang { get; set; }
        public DateTime? ChuChangRq { get; set; }
        public DateTime? AnZhuangRq { get; set; }
        public DateTime? TouYunRq { get; set; }
        public double? Gu<PERSON>iJiaZ<PERSON> { get; set; }
        public string? GuZiBh { get; set; }
        public string? DaXiuZhouQi { get; set; }
        public string? ZhongXiuZhouQi { get; set; }
        public string? XiaoXiuZhouQi { get; set; }
        public string? BeiZhu { get; set; }
        public string Sblxmc { get; set; } = null!;
        public int? Sblxxh { get; set; }
        public string StatName { get; set; } = null!;
    }
}
