﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImGateway
    {
        public ImGateway()
        {
            ImProtectdevice = new HashSet<ImProtectdevice>();
        }

        public string Gatewayid { get; set; }
        public string Gatewayname { get; set; }
        public int Phyaddr { get; set; }
        public string Statcode { get; set; }
        public string Gatewayip1 { get; set; }
        public int? Gatewayport1 { get; set; }
        public string Gatewayip2 { get; set; }
        public int? Gatewayport2 { get; set; }
        public string Protocol { get; set; }

        public virtual ImStation StatcodeNavigation { get; set; }
        public virtual ICollection<ImProtectdevice> ImProtectdevice { get; set; }
    }
}
