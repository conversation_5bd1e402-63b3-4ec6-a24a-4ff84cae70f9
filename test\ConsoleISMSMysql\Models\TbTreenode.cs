﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbTreenode
    {
        public string Nodeid { get; set; }
        public string Ctgy { get; set; }
        public string Nodename { get; set; }
        public string Parentid { get; set; }
        public int Seqno { get; set; }
        public string Desc { get; set; }

        public virtual TbBumen TbBumen { get; set; }
    }
}
