﻿using System;
using System.Diagnostics;

namespace OneClickPublishWeb
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("开始部署网站 ----2020年11月8号郭睿");
                //实例化一个进程类

                Process cmd = new Process();

                //定义要调用的程序名称

                cmd.StartInfo.FileName = "cmd.exe";

                //定义要传入的参数

                cmd.StartInfo.Arguments = "/user";

                // 标准输入输出重定向到.net

                cmd.StartInfo.UseShellExecute = false;

                cmd.StartInfo.RedirectStandardInput = true;

                cmd.StartInfo.RedirectStandardOutput = true;

                //不显示窗口界面

                //cmd.StartInfo.CreateNoWindow = true;

                //cmd.StartInfo.WindowStyle = ProcessWindowStyle.Normal;

                //启动程序    

                cmd.Start();

                //将dos命令产生的信息打印到窗口， 用一个textBox来接收  
                //向cmd窗口发送输入信息
                cmd.StandardInput.WriteLine("title 自动安装软件运行环境");
                cmd.StandardInput.WriteLine("start / wait %% i / q");
                cmd.StandardInput.WriteLine("exit");
                cmd.StandardInput.AutoFlush = true;
                cmd.WaitForExit();
                cmd.Close();
                Console.WriteLine("运行环境部署完成");

              //  PublishWebHelper.Execute();
                Console.WriteLine("执行成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            Console.ReadLine();
        }
    }
}
