﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDataviewer
    {
        public ImDataviewer()
        {
            ImDataviewerData = new HashSet<ImDataviewerData>();
        }

        public string Id { get; set; }
        public int Colcnt { get; set; }
        public int Valuecolwidth { get; set; }
        public short? Namecharset { get; set; }
        public int? Namecolor { get; set; }
        public string Namefontname { get; set; }
        public short? Namesize { get; set; }
        public short? Namebold { get; set; }
        public short? Nameitalic { get; set; }
        public short? Namestrikeout { get; set; }
        public short? Nameunderline { get; set; }
        public short? Valuecharset { get; set; }
        public int? Valuecolor { get; set; }
        public string Valuefontname { get; set; }
        public short? Valuesize { get; set; }
        public short? Valuebold { get; set; }
        public short? Valueitalic { get; set; }
        public short? Valuestrikeout { get; set; }
        public short? Valueunderline { get; set; }

        public virtual ImGlyph IdNavigation { get; set; }
        public virtual ICollection<ImDataviewerData> ImDataviewerData { get; set; }
    }
}
