﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceDataTmp
    {
        public ImDeviceDataTmp()
        {
            ImDeviceActRuleTmps = new HashSet<ImDeviceActRuleTmp>();
        }

        public string Id { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
        public int? ToScada { get; set; }
        public int? SecAddr101 { get; set; }
        public int? CfgFileBz { get; set; }
        public string? BeiZhu { get; set; }
        public string Domain { get; set; } = null!;

        public virtual ImProtectDeviceTmp Device { get; set; } = null!;
        public virtual ImDeviceVaTmp? ImDeviceVaTmp { get; set; }
        public virtual ImDeviceYcTmp? ImDeviceYcTmp { get; set; }
        public virtual ImDeviceYkTmp? ImDeviceYkTmp { get; set; }
        public virtual ImDeviceYmTmp? ImDeviceYmTmp { get; set; }
        public virtual ImDeviceYxTmp? ImDeviceYxTmp { get; set; }
        public virtual ICollection<ImDeviceActRuleTmp> ImDeviceActRuleTmps { get; set; }
    }
}
