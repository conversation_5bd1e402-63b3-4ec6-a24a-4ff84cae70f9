﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImCtrlworddef
    {
        public int Typeid { get; set; }
        public string TypeComment { get; set; }
        public string Bit1Meanings { get; set; }
        public string Bit2Meanings { get; set; }
        public string Bit3Meanings { get; set; }
        public string Bit4Meanings { get; set; }
        public string Bit5Meanings { get; set; }
        public string Bit6Meanings { get; set; }
        public string Bit7Meanings { get; set; }
        public string Bit8Meanings { get; set; }
        public string Bit9Meanings { get; set; }
        public string Bit10Meanings { get; set; }
        public string Bit11Meanings { get; set; }
        public string Bit12Meanings { get; set; }
        public string Bit13Meanings { get; set; }
        public string Bit14Meanings { get; set; }
        public string Bit15Meanings { get; set; }
        public string Bit16Meanings { get; set; }
        public string Manufacturer { get; set; }
        public string Bit17Meanings { get; set; }
        public string Bit18Meanings { get; set; }
        public string Bit19Meanings { get; set; }
        public string Bit20Meanings { get; set; }
        public string Bit21Meanings { get; set; }
        public string Bit22Meanings { get; set; }
        public string Bit23Meanings { get; set; }
        public string Bit24Meanings { get; set; }
        public string Bit25Meanings { get; set; }
        public string Bit26Meanings { get; set; }
        public string Bit27Meanings { get; set; }
        public string Bit28Meanings { get; set; }
        public string Bit29Meanings { get; set; }
        public string Bit30Meanings { get; set; }
        public string Bit31Meanings { get; set; }
        public string Bit32Meanings { get; set; }

        public virtual ImManufacturer ManufacturerNavigation { get; set; }
    }
}
