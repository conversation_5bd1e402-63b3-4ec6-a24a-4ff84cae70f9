using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsConfigDto
{
    /// <summary>
    /// 遥测统计配置输出DTO
    /// </summary>
    [AutoMapFrom(typeof(TelemeteringStatisticsConfig))]
    public class TelemeteringStatisticsConfigOutput : EntityDto<Guid>
    {
        /// <summary>
        /// 关联的遥测配置ID
        /// </summary>
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 遥测配置信息
        /// </summary>
        public virtual TelemeteringConfigurationOutput TelemeteringConfiguration { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public virtual int StatisticsType { get; set; }

        /// <summary>
        /// 统计类型名称
        /// </summary>
        public virtual string StatisticsTypeName { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public virtual int IntervalType { get; set; }

        /// <summary>
        /// 时间间隔类型名称
        /// </summary>
        public virtual string IntervalTypeName { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public virtual bool IsActive { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public virtual string Description { get; set; }
    }
} 