﻿using MQTTnet.Samples.Client;

namespace MqttConsoleAppClient
{
    using static Client_Connection_Samples;
    internal class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("mqtt客户端启动");
            try
            {
                //Connect_Client_Timeout()
                Connect_Client().Wait(); 

            }
            catch (Exception ex)
            {

                throw ex;
            }
            Console.WriteLine("输入任意字符退出");
            Console.ReadLine();
        }
    }
}