﻿using System;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition
{
    /// <summary>
    /// 遥测配置查询体哦阿健
    /// </summary>
    public class TelemeteringConfigurationSearchConditionInput : ISASMySQLSearchInput
    {
        /// <summary>
        /// 遥测名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 关联监控设备表
        /// </summary>
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 关联监控设备类型表
        /// </summary>
        public virtual Guid? EquipmentTypeId { get; set; }

        /// <summary>
        /// 关联监控设备上级类型表
        /// </summary>
        public virtual Guid? EquipmentTypeTypeId { get; set; }

        /// <summary>
        /// 车站id
        /// </summary>
        public virtual Guid? TransformerSubstationId { get; set; }
        /// <summary>
        /// 是否自检判定值
        /// </summary>
        public virtual bool? IsSelfCheckingValue { get; set; }
        public virtual bool? IsVirtualDevice { get; set; }

        public virtual DataSourceCategoryEnum? DataSourceCategory { get; set; }

    }
}