﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbBusiness
    {
        public TbBusiness()
        {
            TbBusiHistories = new HashSet<TbBusiHistory>();
        }

        public string Id { get; set; } = null!;
        public string WfId { get; set; } = null!;
        public string BusiName { get; set; } = null!;
        public string ApplyUser { get; set; } = null!;
        public DateTime ApplyTime { get; set; }
        public string WfStepId { get; set; } = null!;
        public int IsEnd { get; set; }

        public virtual TbWorkflow Wf { get; set; } = null!;
        public virtual TbWfStep WfStep { get; set; } = null!;
        public virtual ICollection<TbBusiHistory> TbBusiHistories { get; set; }
    }
}
