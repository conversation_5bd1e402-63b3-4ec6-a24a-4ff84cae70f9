﻿using Abp.Dependency;
using Newtonsoft.Json;
using SharpCompress.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using System.Windows;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Threading;
using ToolLibrary;
using VideoSurveillanceAdapter;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Helper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Model;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.Domain;
using Yunda.ISAS.DataMonitoringServer.WPF.Services;
using Yunda.ISAS.DataMonitoringServer.WPF.View;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.WPF.View;
using Yunda.SOMS.DataMonitoringServer.WPF.ViewModel;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.DataTransferObject.System.FunctionDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace Yunda.ISAS.DataMonitoringServer.WPF.ViewModel
{
    public delegate void MsgChangeDelegate(Recoder recoder);

    public class MainViewModel : BaseRaisePropertyChanged, ISingletonDependency
    {
        #region Fields

        private const int MAX_RECORDERS = 800;
        private const string SETTING_PATH = "Content.json";
        private const string CONNECTIONS_PATH = "Connections.json";
        private const int REFRESH_INTERVAL_MS = 1000;

        private readonly Dispatcher _dispatcher;
        private readonly IConfigurationService _configService;
        private readonly IConnectionService _connectionService;
        private readonly ILoggingService _loggingService;
        private readonly IDataService _dataService;
        private readonly IVideoService _videoService;
        private readonly RunningDataCache _runningDataCache;
        private readonly WindowUpdateOne _windowUpdateOne;
        private readonly WindowRegex _windowRegex;
        
        private bool _isRunning = false;
        private bool _isStarted = true;
        private int _iecSettingHeight = 0;
        private bool _isManualSetting = true;
        private SelectModelOutput _transformerSubstation;
        private ConnectionConfig _selectedConnection;

        #endregion

        #region Properties

        public event MsgChangeDelegate MsgChange;
        
        public Content SettingModel => _configService.SettingModel;
        
        public ObservableCollection<Recoder> Recorders => _loggingService.Recorders;
        
        public ObservableCollection<InfoAddrLiveData> InfoAddrLiveDatas { get; } = new ObservableCollection<InfoAddrLiveData>();
        
        public ObservableCollection<SelectModelOutput> TransformerSubstations => _configService.TransformerSubstations;
        public ObservableCollection<ConnectionConfig> Connections => _connectionService.Connections;
        public bool ShowRealTimeData { get; set; }

        public bool IsRunning
        {
            get => _isRunning;
            set
            {
                if (_isRunning != value)
                {
                    _isRunning = value;
                    _runningDataCache.IsRunning = value;
                    RaisePropertyChanged();
                    RaisePropertyChanged(nameof(StatBtnContent)); // 关键补充
                }
            }
        }

        public string StatBtnContent => IsRunning ? "停止服务" : "开始服务";

        public bool IsStarted
        {
            get => _isStarted;
            set
            {
                if (_isStarted != value)
                {
                    _isStarted = value;
                    RaisePropertyChanged();
                }
            }
        }

        public int IecSettingHeight
        {
            get => _iecSettingHeight;
            set
            {
                if (_iecSettingHeight != value)
                {
                    _iecSettingHeight = value;
                    RaisePropertyChanged();
                }
            }
        }

        public bool IsManualSetting
        {
            get => _isManualSetting;
            set
            {
                if (_isManualSetting != value)
                {
                    _isManualSetting = value;
                    SetIec104SettingHeight(value);
                    RaisePropertyChanged();
                }
            }
        }

        public SelectModelOutput TransformerSubstation
        {
            get => _transformerSubstation;
            set
            {
                if (_transformerSubstation != value)
                {
                    _transformerSubstation = value;
                    if (value != null)
                    {
                        _configService.UpdateSubstation(value);
                    }
                    RaisePropertyChanged();
                }
            }
        }

        public ConnectionConfig SelectedConnection
        {
            get => _selectedConnection;
            set
            {
                if (_selectedConnection != value)
                {
                    _selectedConnection = value;
                    RaisePropertyChanged();
                }
            }
        }

        #endregion

        #region Commands

        public ICommand StartCommand { get; private set; }
        public ICommand CleanCommand { get; private set; }
        public ICommand SaveCommand { get; private set; }
        public ICommand SetAutoStartup { get; private set; }
        public ICommand RegMenuCmd { get; private set; }
        public ICommand StopMenuCmd { get; private set; }
        public ICommand StartMenuCmd { get; private set; }
        public ICommand CopyMenuCmd { get; private set; }
        public ICommand UpdateAllMenuCmd { get; private set; }
        public ICommand UpdateOneMenuCmd { get; private set; }
        public ICommand StopCommand { get; private set; }
        public ICommand UpdataLiveDataCommand { get; private set; }
        public ICommand ExportLiveDataCommand { get; private set; }
        public ICommand ClearRedisCommand { get; private set; }
        public ICommand AddConnectionCommand { get; private set; }
        public ICommand RemoveConnectionCommand { get; private set; }
        public ICommand ConnectCommand { get; private set; }
        public ICommand DisconnectCommand { get; private set; }
        public ICommand ReconnectCommand { get; private set; }
        public ICommand ToggleRealTimeDataCommand { get; private set; }

        #endregion
        // ... 原有代码 ...

        public ObservableCollection<ConnectionTabViewModel> ConnectionTabs { get; private set; } = new ObservableCollection<ConnectionTabViewModel>();
        private ConcurrentDictionary<string, ConnectionTabViewModel> _connectionTabsDict = new ConcurrentDictionary<string, ConnectionTabViewModel>();
      
        public MainViewModel(
            IConfigurationService configService,
            IConnectionService connectionService,
            ILoggingService loggingService,
            IDataService dataService,
            IVideoService videoService,
            RunningDataCache runningDataCache,
            WindowUpdateOne windowUpdateOne,
            WindowRegex windowRegex)
        {
            _dispatcher = Dispatcher.CurrentDispatcher;
            _configService = configService;
            _connectionService = connectionService;
            _loggingService = loggingService;
            _dataService = dataService;
            _videoService = videoService;
            _runningDataCache = runningDataCache;
            _windowUpdateOne = windowUpdateOne;
            _windowRegex = windowRegex;
            InitializeCommands();
            InitializeEventHandlers();
            _configService.LoadConfiguration();
            TransformerSubstation = _configService.GetDefaultSubstation();
            _videoService.InitializeVideoSDK();
            _connectionService.Initialize(TransformerSubstations);
            SelectedConnection = Connections.FirstOrDefault();
            _loggingService.StartMessageProcessing();
            AutoStartup();
        }
      
   
       private void AutoStartup()
        {
            // 在所有配置加载完成后，检查是否需要自动启动
            if (SettingModel.IsAutoStartup)
            {
                // 使用延迟执行以确保UI初始化完成
                _dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() =>
                {
                    if (TransformerSubstation != null && !IsRunning)
                    {
                        _loggingService.LogMessage(new Recoder
                        {
                            MsgType = "自动启动",
                            Msg = "检测到自动启动设置为开启状态，正在启动服务...",
                            DateTime = DateTime.Now
                        });
                        StartCommand.Execute(null);
                    }
                    else if (TransformerSubstation == null)
                    {
                        _loggingService.LogMessage(new Recoder
                        {
                            MsgType = "自动启动",
                            Msg = "自动启动失败：未选择变电所亭",
                            DateTime = DateTime.Now
                        });
                    }
                }));
            }
        }


        private void InitializeCommands()
        {
            StartCommand = new AnotherCommandImplementation(ExecuteStartCommand);
            CleanCommand = new AnotherCommandImplementation(_ => _loggingService.ClearRecorders());
            SaveCommand = new AnotherCommandImplementation(ExecuteSaveCommand);
            SetAutoStartup = new AnotherCommandImplementation(_ => SettingModel.IsAutoStartup = !SettingModel.IsAutoStartup);
            RegMenuCmd = new AnotherCommandImplementation(_ => _windowRegex.ShowActivated = true);
            StopMenuCmd = new AnotherCommandImplementation(_ => _loggingService.StopLogRecording());
            StartMenuCmd = new AnotherCommandImplementation(_ => _loggingService.StartLogRecording());
            CopyMenuCmd = new AnotherCommandImplementation(_ => _loggingService.CopyRecordersToClipboard());
            UpdateAllMenuCmd = new AnotherCommandImplementation(_ => _dataService.CallAllData());
            UpdateOneMenuCmd = new AnotherCommandImplementation(_ => _windowUpdateOne.ShowActivated = true);
            StopCommand = new AnotherCommandImplementation(_ => _dataService.DataServiceStop());
            UpdataLiveDataCommand = new AnotherCommandImplementation(ExecuteUpdateLiveData);
            ExportLiveDataCommand = new AnotherCommandImplementation(ExecuteExportLiveData);
            ClearRedisCommand = new AnotherCommandImplementation(ExecuteClearRedis);
            AddConnectionCommand = new AnotherCommandImplementation(ExecuteAddConnectionCommand);
            RemoveConnectionCommand = new AnotherCommandImplementation(_ => _connectionService.RemoveSelectedConnections());
            ConnectCommand = new AnotherCommandImplementation(_ => _connectionService.ConnectSelectedConnections());
            DisconnectCommand = new AnotherCommandImplementation(_ => _connectionService.DisconnectSelectedConnections());
            ReconnectCommand = new AnotherCommandImplementation(_ => _connectionService.ReconnectSelectedConnections());
            ToggleRealTimeDataCommand = new AnotherCommandImplementation(_ =>
            {
                ShowRealTimeData = !ShowRealTimeData;
                if (ShowRealTimeData)
                {
                    UpdataLiveDataCommand.Execute(null);
                }
            });
        }

        private void InitializeEventHandlers()
        {
            _loggingService.InitializeEventHandlers();
            MonitoringEventBus.StateEvent += HandleStateEvent;
        }

        private void HandleStateEvent(StateEventArgs s)
        {
            _loggingService.LogMessage(new Recoder { 
                MsgType = "启动信息", 
                Msg = s.IsStartSucceed ? "成功连接远动机" : "断开连接远动机", 
                DateTime = DateTime.Now 
            });
            
            IsRunning = s.IsStartSucceed;
        }

        private void ExecuteStartCommand(object _)
        {
            if (TransformerSubstation == null)
            {
                MessageBox.Show("请选择所亭", "错误信息");
                return;
            }

            if (!IsRunning)
            {
                try
                {
                    _loggingService.LogMessage(new Recoder { 
                        MsgType = "启动信息", 
                        Msg = "开始连接远动机,请不要重复点击开始按钮,请稍等....", 
                        DateTime = DateTime.Now 
                    });
                    _dataService.InitializeYXYCRedisList(_connectionService.Connections.Select(t=> (DataSourceCategoryEnum)t.DataSourceCategoryName));
                    _dataService.DataServiceStart(SettingModel, _connectionService.Connections);
                    IsRunning = true;
                    UpdataLiveDataCommand.Execute(null);
                }
                catch (Exception ex)
                {
                    _loggingService.LogMessage(new Recoder { 
                        MsgType = "启动信息", 
                        Msg = ex.StackTrace, 
                        DateTime = DateTime.Now 
                    });
                }
            }
            else
            {
                _dataService.DataServiceStop();
                IsRunning = false;
                _loggingService.LogMessage(new Recoder { 
                    MsgType = "启动信息", 
                    Msg = "关闭服务，请不要重复点击停止按钮，请稍等...", 
                    DateTime = DateTime.Now 
                });
            }
            
            RaisePropertyChanged();
        }

        private void ExecuteSaveCommand(object _)
        {
            try
            {
                _configService.SaveConfiguration();
                _connectionService.SaveConnections();
                
                _loggingService.LogMessage(new Recoder { 
                    MsgType = "配置信息", 
                    Msg = "配置已保存", 
                    DateTime = DateTime.Now 
                });
                
                if (SettingModel.IsAutoStartup)
                {
                    StartCommand.Execute(null);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogMessage(new Recoder { 
                    MsgType = "配置信息", 
                    Msg = $"保存配置失败: {ex.Message}", 
                    DateTime = DateTime.Now 
                });
                Debug.WriteLine(ex.StackTrace);
            }
        }

        private void ExecuteUpdateLiveData(object _)
        {
            InfoAddrLiveDatas.Clear();
            UpdateTelesignalisations();
            UpdateTelemeterings();
            _runningDataCache.UpdateEquipmentDataModelDicRedis();
        }

        private void UpdateTelesignalisations()
        {
            var telesignalisations = from equipment in _runningDataCache.EquipmentDataDic
                                     where equipment.Value.Telesignalisations != null
                                     from telesignalisation in equipment.Value.Telesignalisations
                                     select new InfoAddrLiveData
                                     {
                                         DateTime = telesignalisation.ResultTime,
                                         InfoAddr = telesignalisation.DispatcherAddress,
                                         Name = telesignalisation.Name,
                                         Value = $"{telesignalisation.ResultValueStr} ({telesignalisation.ResultValue})",
                                         EquipmentName = equipment.Value.EquipmentInfoName,
                                         TypeName = telesignalisation.RemoteType == RemoteTypeEnum.SinglePoint ? "单点遥信" : "双点遥信"
                                     };

            foreach (var item in telesignalisations.OrderBy(t => t.InfoAddr))
            {
                InfoAddrLiveDatas.Add(item);
            }
        }

        private void UpdateTelemeterings()
        {
            var telemeterings = from equipment in _runningDataCache.EquipmentDataDic
                                where equipment.Value.Telemeterings != null
                                from telemetering in equipment.Value.Telemeterings
                                select new InfoAddrLiveData
                                {
                                    DateTime = telemetering.ResultTime,
                                    InfoAddr = telemetering.DispatcherAddress,
                                    Name = telemetering.Name,
                                    Value = telemetering.ResultValue.ToString("0.00"),
                                    EquipmentName = equipment.Value.EquipmentInfoName,
                                    TypeName = "遥测"
                                };

            foreach (var item in telemeterings.OrderBy(t => t.InfoAddr))
            {
                InfoAddrLiveDatas.Add(item);
            }
        }

        private void ExecuteExportLiveData(object _)
        {
            UpdataLiveDataCommand.Execute(null);
            _dataService.ExportLiveData(InfoAddrLiveDatas);
        }

        private async void ExecuteClearRedis(object _)
        {
            await _dataService.DeleteYXYCRedis(Connections.Select(t=> (DataSourceCategoryEnum)t.DataSourceCategoryName));
            await _dataService.InitializeYXYCRedisList(Connections.Select(t => (DataSourceCategoryEnum)t.DataSourceCategoryName));
        }

        private void SetIec104SettingHeight(bool value)
        {
            IecSettingHeight = value ? 30 : 0;
        }

        private void ExecuteAddConnectionCommand(object _)
        {
            try
            {
                // Get default values
                var defaultSubstation = TransformerSubstations.FirstOrDefault();

                SelectedConnection = _connectionService.AddConnection(defaultSubstation, 0);
                // Log the action
                _loggingService.LogMessage(new Recoder { 
                    MsgType = "连接信息", 
                    Msg = $"已添加新连接: {SelectedConnection.Name}", 
                    DateTime = DateTime.Now 
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogMessage(new Recoder { 
                    MsgType = "连接信息", 
                    Msg = $"添加连接失败: {ex.Message}", 
                    DateTime = DateTime.Now 
                });
                Debug.WriteLine(ex.StackTrace);
            }
        }
    }
}
