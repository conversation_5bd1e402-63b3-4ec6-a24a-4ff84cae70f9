<?xml version="1.0"?>
<doc>
    <assembly>
        <name>YunDa.ISAS.Application</name>
    </assembly>
    <members>
        <member name="T:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.HomePageSettingAppService">
            <summary>
            客户端主页配置
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.HomePageSettingAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            三维配置信息删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.HomePageSettingAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            三维配置信息删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.HomePageSettingAppService.DeleteByNameAsync(System.String,System.Nullable{System.Guid})">
            <summary>
            删除客户端主页配置
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.HomePageSettingAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.HomePageSettingSearchInput})">
            <summary>
            查询配置
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.HomePageSettingAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.EditHomePageSettingInput)">
            <summary>
            创建或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.IHomePageSettingAppService.DeleteByNameAsync(System.String,System.Nullable{System.Guid})">
            <summary>
            根据名称删除客户端主页配置
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.IThreeDimensionAppService.DeleteByModelIdAsync(System.Int32,System.String)">
            <summary>
            根据模型id和场站名称删除模型配置文件
            </summary>
            <param name="modelId"></param>
            <param name="transubstationName"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.IThreeDimensionAppService.CreateOrUpdateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.EditThreeDimensionInput})">
            <summary>
            三维配置信息增加或修改
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.EditThreeDimensionInput)">
            <summary>
            三维配置信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.CreateOrUpdateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.EditThreeDimensionInput})">
            <summary>
            三维配置多个信息增加或修改
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.DeleteByModelIdAsync(System.Int32,System.String)">
            <summary>
            三维配置信息删除
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            三维配置信息删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            三维配置信息删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.ThreeDimensionSearchInput})">
            <summary>
            三维配置信息查询接口
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.FindVidevDatas(YunDa.ISAS.DataTransferObject.ClientConfigruation.ThreeDimension.ThreeDimensionVideoSearchInput)">
            <summary>
            查询三维信息视频设备接口
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.ClientConfigruation.ThreeDimension.ThreeDimensionAppService.FindAllModelsNameAndId(System.Nullable{System.Guid})">
            <summary>
            查询三维信息所有模型名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetAlarmLiveData(System.String,System.Nullable{System.Guid},System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取实时报警数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetAlarmList(System.Guid)">
            <summary>
            获取实时报警列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.DeleteAlarmMessage(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除报警信息
            </summary>
            <param name="guids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetAlarmOverviewAsync(System.String)">
            <summary>
            获取报警详细数据
            </summary>
            <param name="stationName"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetAlarmStatisticsAsync(System.String)">
            <summary>
            获取报警统计数据
            </summary>
            <param name="stationName"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.UploadISMSAlarmMsg(System.Object)">
            <summary>
            上传综自报警信息
            </summary>
            <param name="alarmMsg"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.ChecKAlarm(YunDa.SOMS.DataTransferObject.AlarmDataDto.AlarmISMSInfo,YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo)">
            <summary>
            检查报警是否为缺陷
            </summary>
            <param name="alarm"></param>
            <param name="protect"></param>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetCheckDefectsAsync(System.String)">
            <summary>
            查询缺陷信息
            </summary>
            <param name="category"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetCheckDefectsCountAsync(System.String)">
            <summary>
            查询缺陷信息数目
            </summary>
            <param name="category"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.GetCheckDefectStatistics">
            <summary>
            查询缺陷信息数目统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.AlarmLiveDataAppService.ConfirmCheckDefectsAsync(System.String)">
            <summary>
            确认缺陷
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.IAlarmLiveDataAppService.GetAlarmList(System.Guid)">
            <summary>
            获取实时报警数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.IAlarmLiveDataAppService.DeleteAlarmMessage(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除报警信息
            </summary>
            <param name="guids"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService">
            <summary>
            实时数据报警策略管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.EditDMAlarmCategoryInput)">
            <summary>
            报警策略增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.EditDMAlarmCategoryInput)">
            <summary>
            增加报警策略
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.CheckIsExistSameName(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.EditDMAlarmCategoryInput,YunDa.ISAS.DataTransferObject.RequestResult{YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.DMAlarmCategoryOutput}@)">
            <summary>
            检查是否存在相同的名称
            </summary>
            <param name="input"></param>
            <param name="rst"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.EditDMAlarmCategoryInput)">
            <summary>
            更新报警策略
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个报警策略
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个报警策略
            </summary>
            <param name="ids">报警策略id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.SearchCondition.DMAlarmCategorySearchConditionInput})">
            <summary>
            查询报警策略
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.FindDAlarmCategiorForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.SearchCondition.DMAlarmCategorySearchConditionInput)">
            <summary>
            根据查询条件查询设备类型等级数据（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.DMAlarmCategoryAppService.FindAllAlarmLevelForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.SearchCondition.DMAlarmCategorySearchConditionInput)">
            <summary>
            获取所有的报警等级
            </summary>
            <param name="searchCondition"></param>
            <returns>选择列表</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.IDMAlarmCategoryAppService">
            <summary>
            实时数据报警策略管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.IDMAlarmCategoryAppService.FindAllAlarmLevelForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.DMAlarmCategoryDto.SearchCondition.DMAlarmCategorySearchConditionInput)">
            <summary>
            获取所有的报警等级
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache">
            <summary>
            能耗标准缓存类，用于缓存能耗相关的配置数据，减少数据库访问
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.GetEquipmentIdMapAsync">
            <summary>
            从缓存获取设备ID映射
            </summary>
            <returns>设备名称到ID的映射字典</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.SaveEquipmentIdMapAsync(System.Collections.Generic.Dictionary{System.String,System.Guid})">
            <summary>
            缓存设备ID映射
            </summary>
            <param name="equipmentIdMap">设备名称到ID的映射字典</param>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.GetMainTransformerIdsAsync">
            <summary>
            获取主变设备ID映射
            </summary>
            <returns>主变名称到ID的映射字典</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.SaveMainTransformerIdsAsync(System.Collections.Generic.Dictionary{System.String,System.Guid})">
            <summary>
            缓存主变设备ID映射
            </summary>
            <param name="mainTransformerIds">主变名称到ID的映射字典</param>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.GetOperationCriteriasAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            获取设备投运判断标准
            </summary>
            <param name="equipmentIds">设备ID列表</param>
            <returns>投运判断标准列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.SaveOperationCriteriasAsync(System.Collections.Generic.List{YunDa.SOMS.Entities.DataMonitoring.EnergyOperationCriteria})">
            <summary>
            缓存投运判断标准
            </summary>
            <param name="criterias">投运判断标准列表</param>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.ClearOperationCriteriasCacheAsync">
            <summary>
            清除投运判断标准缓存
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching.EnergyCriteriaCache.GetLastUpdateTimeAsync">
            <summary>
            获取最后更新时间
            </summary>
            <returns>最后更新时间</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService">
            <summary>
            能耗配置服务类，提供对能耗配置的相关操作
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.GetConfigs(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyConfigSearchInput})">
            <summary>
            获取能耗配置列表
            </summary>
            <param name="input">查询条件</param>
            <returns>配置列表结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.GetAllAsync">
            <summary>
            获取所有能耗配置信息
            </summary>
            <returns>能耗配置列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.GetByIdAsync(System.Guid)">
            <summary>
            根据ID获取能耗配置信息
            </summary>
            <param name="id">配置ID</param>
            <returns>能耗配置</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.CreateAsync(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig)">
            <summary>
            创建能耗配置
            </summary>
            <param name="input">能耗配置信息</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.UpdateAsync(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig)">
            <summary>
            更新能耗配置
            </summary>
            <param name="input">能耗配置信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.DeleteAsync(System.Guid)">
            <summary>
            删除能耗配置
            </summary>
            <param name="id">配置ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            批量删除能耗配置
            </summary>
            <param name="ids">配置ID列表</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.CreateOrUpdateAsync(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig)">
            <summary>
            添加或更新能耗配置
            </summary>
            <param name="input">能耗配置信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.GetConfigsForSelect(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyConfigSearchInput)">
            <summary>
            获取能耗配置下拉选择列表
            </summary>
            <param name="input">查询条件</param>
            <returns>配置选择列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionConfigAppService.GetByDeviceIdAsync(System.Guid)">
            <summary>
            根据设备ID获取相关配置
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>配置列表</returns>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService._telemeteringModelListRedis">
            <summary>
            遥测数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService._telesignalisationModelListRedis">
            <summary>
            遥信数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService._mongoRepository">
            <summary>
            MongoDB存储库
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetPowerStatistics(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取功率数据
            </summary>
            <param name="RealTimePowerType"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetRealTimePowerFactor">
            <summary>
            获取实时功率因数数据
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetPeriodStartTime(System.DateTime,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)" -->
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetFeederEnergyDistribution(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取馈线能耗分布
            </summary>
            <param name="interval"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetFeederCumulativeEnergyConsumption(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取馈线累计能耗
            </summary>
            <param name="interval"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GenerateTimePointsByInterval(System.DateTime,System.DateTime,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            根据时间间隔生成时间点列表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.FindClosestTimePointIndex(System.Collections.Generic.List{System.DateTime},System.DateTime)">
            <summary>
            查找最接近指定时间的时间点索引
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetTimeFormatByInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取时间格式字符串
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetStartTimeByInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            根据时间间隔获取起始时间
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetIntervalSuffix(Yunda.SOMS.MongoDB.Entities.DataMonitoring.FixedIntervalEnum)">
            <summary>
            Converts FixedIntervalEnum to a string suffix.
            </summary>
            <param name="interval">The fixed interval.</param>
            <returns>The corresponding string suffix.</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.ConvertToFixedInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            Converts RealTimePowerTypeEnum to FixedIntervalEnum.
            </summary>
            <param name="realTimePowerType">The real-time power type.</param>
            <returns>The corresponding FixedIntervalEnum value.</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyConsumptionOverviewAppService.GetEndTimeSpanInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            根据时间间隔类型获取需要前移的时间偏移量
            为了保持代码兼容性，直接调用TimeAdjustmentHelper.GetEndTimeSpanInterval
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>需要前移的时间偏移量</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService">
            <summary>
            能耗设备服务类，提供对能耗设备的相关操作
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService.GetDevices(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyDeviceSearchInput})">
            <summary>
            获取能耗设备列表
            </summary>
            <param name="input">查询条件</param>
            <returns>设备列表结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService.GetDeviceById(System.Guid)">
            <summary>
            根据ID获取能耗设备
            </summary>
            <param name="id">设备ID</param>
            <returns>设备信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService.CreateOrUpdateDevice(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice)">
            <summary>
            创建或更新能耗设备
            </summary>
            <param name="input">设备信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService.DeleteDevice(System.Guid)">
            <summary>
            删除能耗设备
            </summary>
            <param name="id">设备ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService.DeleteDevices(System.Collections.Generic.List{System.Guid})">
            <summary>
            批量删除能耗设备
            </summary>
            <param name="ids">设备ID列表</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.EnergyDeviceAppService.GetDevicesForSelect(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyDeviceSearchInput)">
            <summary>
            获取能耗设备下拉选择列表
            </summary>
            <param name="input">查询条件</param>
            <returns>设备选择列表</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService">
            <summary>
            馈线监视服务，提供馈线能耗、功率组成及功率因数数据的查询功能
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService._telemeteringModelListRediskey">
            <summary>
            遥测数据实时库键名
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService._telemeteringModelListRedis">
            <summary>
            遥测数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService._equipmentInfoRepository">
            <summary>
            设备信息仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService._energyDeviceRepository">
            <summary>
            能耗设备仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService._configRepository">
            <summary>
            能耗配置仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService._mongoRepository">
            <summary>
            MongoDB存储库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.CACHE_TIMEOUT">
            <summary>
            缓存超时时间（秒）
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.#ctor(YunDa.ISAS.Redis.Repositories.IRedisRepository{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel,System.String},Abp.Domain.Repositories.IRepository{YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo,System.Guid},Abp.Domain.Repositories.IRepository{YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice,System.Guid},Abp.Domain.Repositories.IRepository{YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig,System.Guid},YunDa.ISAS.MongoDB.Repositories.IMongoDbRepository{MongoDB.Bson.BsonDocument,System.Guid})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.FindClosestTimePoint(System.Collections.Generic.List{System.DateTime},System.DateTime)">
            <summary>
            查找最接近指定时间的时间点索引
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.GenerateTimePoints(System.DateTime,System.DateTime,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            生成指定时间范围内的时间点列表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.SampleTimePoints(System.Collections.Generic.List{System.DateTime},System.Int32)">
            <summary>
            对时间点进行采样以控制数据量
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.GenerateSimulatedCurrentData(System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.FeederCurrentSeries},System.Collections.Generic.List{YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice},System.Collections.Generic.List{System.DateTime},YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            生成模拟电流数据
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.GetTimeFormatByInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取指定查询间隔类型的时间格式
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.GetStartTimeByInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取指定查询间隔类型的起始时间
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.ConvertToFixedInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            将RealTimePowerTypeEnum转换为FixedIntervalEnum
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.GetIntervalSuffix(Yunda.SOMS.MongoDB.Entities.DataMonitoring.FixedIntervalEnum)">
            <summary>
            获取时间间隔后缀
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.FeederMonitoringAppService.GenerateVarianceByQueryType(System.Random,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            根据查询类型生成合适的数据波动范围
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService">
            <summary>
            能耗配置服务接口
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.GetConfigs(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyConfigSearchInput})">
            <summary>
            获取能耗配置列表
            </summary>
            <param name="input">查询条件</param>
            <returns>配置列表结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.GetAllAsync">
            <summary>
            获取所有能耗配置信息
            </summary>
            <returns>能耗配置列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.GetByIdAsync(System.Guid)">
            <summary>
            根据ID获取能耗配置信息
            </summary>
            <param name="id">配置ID</param>
            <returns>能耗配置</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.CreateAsync(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig)">
            <summary>
            创建能耗配置
            </summary>
            <param name="input">能耗配置信息</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.UpdateAsync(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig)">
            <summary>
            更新能耗配置
            </summary>
            <param name="input">能耗配置信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.DeleteAsync(System.Guid)">
            <summary>
            删除能耗配置
            </summary>
            <param name="id">配置ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            批量删除能耗配置
            </summary>
            <param name="ids">配置ID列表</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.CreateOrUpdateAsync(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig)">
            <summary>
            添加或更新能耗配置
            </summary>
            <param name="input">能耗配置信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.GetConfigsForSelect(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyConfigSearchInput)">
            <summary>
            获取能耗配置下拉选择列表
            </summary>
            <param name="input">查询条件</param>
            <returns>配置选择列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionConfigService.GetByDeviceIdAsync(System.Guid)">
            <summary>
            根据设备ID获取相关配置
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>配置列表</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionOverviewAppService">
            <summary>
            能耗概览服务接口
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionOverviewAppService.GetPowerStatistics(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取功率数据
            </summary>
            <param name="RealTimePowerType">实时功率类型</param>
            <returns>功率数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionOverviewAppService.GetRealTimePowerFactor">
            <summary>
            获取实时功率因数数据
            </summary>
            <returns>功率因数</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionOverviewAppService.GetFeederEnergyDistribution(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取馈线能耗分布
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>馈线能耗分布数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyConsumptionOverviewAppService.GetFeederCumulativeEnergyConsumption(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取馈线累计能耗
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>馈线累计能耗数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyDeviceService.GetDevices(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyDeviceSearchInput})">
            <summary>
            获取能耗设备列表
            </summary>
            <param name="input">查询条件</param>
            <returns>设备列表结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyDeviceService.GetDeviceById(System.Guid)">
            <summary>
            根据ID获取能耗设备
            </summary>
            <param name="id">设备ID</param>
            <returns>设备信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyDeviceService.CreateOrUpdateDevice(YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice)">
            <summary>
            创建或更新能耗设备
            </summary>
            <param name="input">设备信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyDeviceService.DeleteDevice(System.Guid)">
            <summary>
            删除能耗设备
            </summary>
            <param name="id">设备ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyDeviceService.DeleteDevices(System.Collections.Generic.List{System.Guid})">
            <summary>
            批量删除能耗设备
            </summary>
            <param name="ids">设备ID列表</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IEnergyDeviceService.GetDevicesForSelect(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.EnergyDeviceSearchInput)">
            <summary>
            获取能耗设备下拉选择列表
            </summary>
            <param name="input">查询条件</param>
            <returns>设备选择列表</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IFeederMonitoringAppService">
            <summary>
            馈线监视服务接口，提供馈线能耗、功率组成及功率因数数据查询功能
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IFeederMonitoringAppService.GetFeederEnergyConsumption(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取所有馈线的累计能耗
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>馈线能耗分布数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IFeederMonitoringAppService.GetFeederPowerComposition(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取所有馈线的功率组成对比
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>馈线功率组成数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.IFeederMonitoringAppService.GetFeederPowerFactors(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取所有馈线的功率因数
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>馈线功率因数数据</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.ITransformerMonitoringAppService">
            <summary>
            变压器监视服务接口，提供获取变压器电压、电流等数据的功能
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.ITransformerMonitoringAppService.GetHighSidePhaseVoltage(System.Guid,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取高压侧A、B、C相相电压
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="realTimePowerType">数据时间类型</param>
            <returns>含有时间序列的高压侧相电压数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.ITransformerMonitoringAppService.GetHighSidePhaseCurrent(System.Guid,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取高压侧A、B、C相相电流
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="realTimePowerType">数据时间类型</param>
            <returns>含有时间序列的高压侧相电流数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.ITransformerMonitoringAppService.GetLowSideLineVoltage(System.Guid,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取低压侧F1、F2线电压
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="realTimePowerType">数据时间类型</param>
            <returns>含有时间序列的低压侧线电压数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.ITransformerMonitoringAppService.GetLowSideLineCurrent(System.Guid,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取低压侧F1、F2线电流
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="realTimePowerType">数据时间类型</param>
            <returns>含有时间序列的低压侧线电流数据</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideVoltageData">
            <summary>
            高压侧相电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideVoltageData.PhaseAVoltage">
            <summary>
            A相电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideVoltageData.PhaseBVoltage">
            <summary>
            B相电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideVoltageData.PhaseCVoltage">
            <summary>
            C相电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideVoltageData.TimeIntervalType">
            <summary>
            时间间隔类型
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideCurrentData">
            <summary>
            高压侧相电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideCurrentData.PhaseACurrent">
            <summary>
            A相电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideCurrentData.PhaseBCurrent">
            <summary>
            B相电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideCurrentData.PhaseCCurrent">
            <summary>
            C相电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.HighSideCurrentData.TimeIntervalType">
            <summary>
            时间间隔类型
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideVoltageData">
            <summary>
            低压侧线电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideVoltageData.F1Voltage">
            <summary>
            F1线电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideVoltageData.F2Voltage">
            <summary>
            F2线电压数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideVoltageData.TimeIntervalType">
            <summary>
            时间间隔类型
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideCurrentData">
            <summary>
            低压侧线电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideCurrentData.F1Current">
            <summary>
            F1线电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideCurrentData.F2Current">
            <summary>
            F2线电流数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.LowSideCurrentData.TimeIntervalType">
            <summary>
            时间间隔类型
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TimeAdjustmentHelper">
            <summary>
            时间调整助手类，提供各种查询时间调整的方法
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TimeAdjustmentHelper.GetEndTimeSpanInterval(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            根据时间间隔类型获取需要前移的时间偏移量
            由于数据存储和处理有延迟，需要将查询截止时间前移一段时间，确保查询到的都是有效数据
            不同的时间间隔类型需要不同的偏移量，以保证数据的完整性和准确性
            </summary>
            <param name="interval">时间间隔类型</param>
            <returns>需要前移的时间偏移量</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TimeAdjustmentHelper.GetChineseDayOfWeek(System.DateTime)">
            <summary>
            获取中文星期几
            </summary>
            <param name="date">日期</param>
            <returns>中文星期几</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService">
            <summary>
            变压器监视服务，提供变压器相关电气参数的查询功能
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService._telemeteringModelListRedis">
            <summary>
            遥测数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService._equipmentInfoRepository">
            <summary>
            设备信息仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService._energyDeviceRepository">
            <summary>
            能耗设备仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService._configRepository">
            <summary>
            能耗配置仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService._mongoRepository">
            <summary>
            MongoDB存储库
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GetIntervalAndPointCount(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum,System.TimeSpan@,System.Int32@)">
            <summary>
            获取时间间隔和数据点数量
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GetTimeFormat(YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取时间格式化字符串
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GetFormattedTimeString(System.DateTime,System.String)">
            <summary>
            格式化时间字符串
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GenerateVoltageData(System.Collections.Generic.List{YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig},System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel},System.DateTime,System.Int32,System.TimeSpan,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum,System.Single,System.String,System.Int32)">
            <summary>
            生成电压数据序列
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GenerateCurrentData(System.Collections.Generic.List{YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig},System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel},System.DateTime,System.Int32,System.TimeSpan,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum,System.Single,System.String,System.Int32)">
            <summary>
            生成电流数据序列
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GetPointTime(System.DateTime,System.Int32,System.TimeSpan,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            获取数据点的时间点
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GenerateSimulatedData(System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.FloatTimeOutput},System.DateTime,System.Int32,System.TimeSpan,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum,System.Single,System.String,System.Int32)">
            <summary>
            生成模拟数据
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnergyManagement.TransformerMonitoringAppService.GenerateVarianceByQueryType(System.Random,YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto.RealTimePowerTypeEnum)">
            <summary>
            根据查询类型生成合适的数据波动范围
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.EnvironmentLiveDataAppService.GetEnvironmentLiveData">
            <summary>
            获取实时环境数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.IEnvironmentLiveDataAppService.GetEnvironmentLiveData">
            <summary>
            获取实时环境数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ILinkageConditionAppService">
            <summary>
            联动条件管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageConditionAppService.FindConditionByLinkageId(System.Guid)">
            <summary>
            根据联动Id查询联动条件
            </summary>
            <param name="linkageId">联动Id</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService">
            <summary>
            联动条件管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageConditionDto.EditLinkageConditionInput)">
            <summary>
            联动条件增加或修改
            </summary>
            <param name="input">联动条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageConditionDto.EditLinkageConditionInput)">
            <summary>
            增加联动条件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageConditionDto.EditLinkageConditionInput)">
            <summary>
            更新联动条件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个联动条件
            </summary>
            <param name="id">联动条件id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个联动条件
            </summary>
            <param name="ids">联动条件id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageConditionDto.SearchCondition.LinkageConditionSearchConditionInput})">
            <summary>
            查询联动
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.FindConditionTypeForSelect">
            <summary>
            获取联动条件选择列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.FindCompareTypeEnumForSelect">
            <summary>
            获取比较类型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.FindCLogicalOperatorForSelect">
            <summary>
            获取逻辑操作符选择列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageConditionAppService.FindConditionByLinkageId(System.Guid)">
            <summary>
            根据联动Id查询联动条件
            </summary>
            <param name="linkageId">联动Id</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ILinkageExecuteActivityAppService">
            <summary>
            联动执行活动管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageExecuteActivityAppService.CreateManyVideoActivities(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageExecuteActivityDto.EditLinkageVideoActivityInput)">
            <summary>
            创建多条视频活动
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageExecuteActivityAppService.FindActivityTypeForSelect">
            <summary>
            获取活动类型下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageExecuteActivityAppService.FindActivityByLinkageId(System.Guid)">
            <summary>
            根据联动Id查询联动活动
            </summary>
            <param name="linkageId">联动Id</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService">
            <summary>
            联动执行活动管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.CreateManyVideoActivities(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageExecuteActivityDto.EditLinkageVideoActivityInput)">
            <summary>
            创建多条视频活动
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageExecuteActivityDto.EditLinkageExecuteActivityInput)">
            <summary>
            联动活动增加或修改
            </summary>
            <param name="input">联动活动</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageExecuteActivityDto.EditLinkageExecuteActivityInput)">
            <summary>
            修改联动活动
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个联动活动
            </summary>
            <param name="id">联动活动id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个联动活动
            </summary>
            <param name="ids">联动活动id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageExecuteActivityDto.SearchCondition.LinkageExecuteActivitySearchConditionInput})">
            <summary>
            查询联动活动
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.FindActivityTypeForSelect">
            <summary>
            获取活动类型下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageExecuteActivityAppService.FindActivityByLinkageId(System.Guid)">
            <summary>
            根据联动Id查询联动活动
            </summary>
            <param name="linkageId">联动Id</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ILinkageStrategyAppService">
            <summary>
            联动策略管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageStrategyAppService.GetLinkageStrategyForTreeNode(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.SearchCondition.LinkageStrategySearchConditionInput)">
            <summary>
            根据变电所ID联动策略树节点
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageStrategyAppService.GetMonitoringLinkageStrategies(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.SearchCondition.LinkageStrategySearchConditionInput)">
            <summary>
            为监控服务提供的联动策略
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageStrategyAppService.ExportExcel">
            <summary>
            导出设备类型视口数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageStrategyAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入设备类型视口数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ILinkageStrategyAppService.FindData(System.Guid)">
            <summary>
            查询数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService">
            <summary>
            联动策略管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.EditLinkageStrategyInput)">
            <summary>
            联动策略增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.EditLinkageStrategyInput)">
            <summary>
            增加联动策略
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.EditLinkageStrategyInput)">
            <summary>
            更新联动策略
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个联动策略
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个联动策略
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.SearchCondition.LinkageStrategySearchConditionInput})">
            <summary>
            查询联动策略
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.FindData(System.Guid)">
            <summary>
            查询联动策略
            </summary>
            <param name="id">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.GetLinkageStrategyForTreeNode(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.SearchCondition.LinkageStrategySearchConditionInput)">
            <summary>
            根据变电所ID联动策略树节点
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.GetMonitoringLinkageStrategies(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageStrategyDto.SearchCondition.LinkageStrategySearchConditionInput)">
            <summary>
            为监控服务提供的联动策略
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入联动策略数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.ExportExcel">
            <summary>
            导出联动策略数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.SpawnDefaultData(System.Guid)">
            <summary>
            生成默认联动
            </summary>
            <param name="transSubStationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.LinkageStrategyAppService.DeleteAll(System.Guid)">
            <summary>
            删除所有联动策略
            </summary>
            <param name="transformerSubstationId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ISelfCheckingConfigurationAppService">
            <summary>
            预置点管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelecommandConfigurationAppService">
            <summary>
            遥控配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelecommandConfigurationAppService.FindTelecommandByEquipmentInfoId(System.Guid)">
            <summary>
            根据设备id查询遥控命令
            </summary>
            <param name="id">设备Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelecommandConfigurationAppService.FindDatasNoPageList(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.SearchCondition.TelecommandConfigurationSearchConditionInput)">
            <summary>
            
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelecommandConfigurationAppService.FindAreaTeleCommandDatas(System.Guid)">
            <summary>
            获取区域遥控信息数据
            </summary>
            <param name="stationId"></param>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService">
            <summary>
            遥控配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.EditTelecommandConfigurationInput)">
            <summary>
            遥控数据增加或修改
            </summary>
            <param name="input">遥控数据体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.EditTelecommandConfigurationInput)">
            <summary>
            增加遥控数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.EditTelecommandConfigurationInput)">
            <summary>
            修改遥控数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥控数据
            </summary>
            <param name="id">遥控id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥控数据
            </summary>
            <param name="ids">遥控id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.SearchCondition.TelecommandConfigurationSearchConditionInput})">
            <summary>
            查询遥控数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.FindDatasNoPageList(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.SearchCondition.TelecommandConfigurationSearchConditionInput)">
            <summary>
            根据查询条件查询所有遥控数据
            </summary>
            <param name="searchCondition"></param>
            <returns>遥控数据list</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.FindDatasForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.SearchCondition.TelecommandConfigurationSearchConditionInput)">
            <summary>
            查询遥控数据选择列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.GetTelecommandTypes">
            <summary>
            获取遥控类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.FindTelecommandByEquipmentInfoId(System.Guid)">
            <summary>
            根据设备id查询遥控命令
            </summary>
            <param name="id">设备Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandConfigurationAppService.FindAreaTeleCommandDatas(System.Guid)">
            <summary>
            获取区域遥控信息数据
            </summary>
            <param name="stationId"></param>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService">
            <summary>
            遥控计划项配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanItemDto.EditTeleCommandSettingItemInput)">
            <summary>
            遥控计划项配置增加或修改
            </summary>
            <param name="input">遥控数据体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanItemDto.EditTeleCommandSettingItemInput)">
            <summary>
            增加遥控计划配置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanItemDto.EditTeleCommandSettingItemInput)">
            <summary>
            修改遥控计划配置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥控计划项配置
            </summary>
            <param name="id">遥控id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥控计划配置
            </summary>
            <param name="ids">遥控id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanItem.TelecommandPlanItemAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanItemDto.SearchCondition.TelecommandPlanItemSearchConditionInput})">
            <summary>
            查询遥控计划项配置数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.ITelecommandPlanSettingAppService">
            <summary>
            遥控计划配置接口
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService">
            <summary>
            遥控计划配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanSettingDto.EditTelecommandPlanSettingInput)">
            <summary>
            遥控计划配置增加或修改
            </summary>
            <param name="input">遥控数据体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanSettingDto.EditTelecommandPlanSettingInput)">
            <summary>
            增加遥控计划配置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanSettingDto.EditTelecommandPlanSettingInput)">
            <summary>
            修改遥控计划配置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥控计划配置
            </summary>
            <param name="id">遥控id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥控计划配置
            </summary>
            <param name="ids">遥控id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanSettingDto.SearchCondition.TelecommandPlanSettingSearchConditionInput})">
            <summary>
            查询遥控计划配置数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanSetting.TelecommandPlanSettingAppService.FindTelecomandPlanSettingForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanSettingDto.SearchCondition.TelecommandPlanSettingSearchConditionInput)">
            <summary>
                根据查询条件查询遥控计划数据（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.ITelecommandPlanTimeAppService.FindTelcommandPlanTimeEvents(System.Guid)">
            <summary>
            获取遥控计划时间
            </summary>
            <param name="transsubstationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.ITelecommandPlanTimeAppService.FindTelcommandPlanFreqSelect">
            <summary>
            查询遥控计划执行周期（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.ITelecommandPlanTimeAppService.GetTelecommandPlanDatas(System.Guid)">
            <summary>
            查询遥控计划供数据服务程序使用（使用频率较高）
            </summary>
            <param name="substationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.ITelecommandPlanTimeAppService.UpdateTelecommandSendTime(System.Guid,System.DateTime)">
            <summary>
            更新遥控计划执行时间
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService">
            <summary>
            遥控计划时间管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanTimeDto.EditTeleCommandPlanTimeInput)">
            <summary>
            遥控计划配置增加或修改
            </summary>
            <param name="input">遥控数据体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanTimeDto.EditTeleCommandPlanTimeInput)">
            <summary>
            增加遥控计划配置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanTimeDto.EditTeleCommandPlanTimeInput)">
            <summary>
            修改遥控计划配置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥控计划配置
            </summary>
            <param name="id">遥控id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥控计划配置
            </summary>
            <param name="ids">遥控id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandPlanDto.TelecommandPlanTimeDto.SearchCondition.TelecommandPlanTimeSearchConditionInput})">
            <summary>
            查询遥控计划配置数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.GetTelecommandPlanDatas(System.Guid)">
            <summary>
            获取遥控计划数据
            </summary>
            <param name="substationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.UpdateTelecommandSendTime(System.Guid,System.DateTime)">
            <summary>
            更新遥控计划执行时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandPlan.TelecommandPlanTime.TelecommandPlanTimeAppService.FindTelcommandPlanFreqSelect">
            <summary>
            查询遥控计划执行周期（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelecommandTemplateAppService">
            <summary>
            遥控模板管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService">
            <summary>
            遥控模板管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandTemplateDto.EditTelecommandTemplateInput)">
            <summary>
            遥控模板编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandTemplateDto.EditTelecommandTemplateInput)">
            <summary>
            更新遥控模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandTemplateDto.EditTelecommandTemplateInput)">
            <summary>
            增加遥控模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥控模板
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥控模板
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelecommandTemplateAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandTemplateDto.TelecommandTemplateSearchConditionInput})">
            <summary>
            遥控模板查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelemeteringAlarmStrategyAppService">
            <summary>
            遥测报警管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService">
            <summary>
            遥测报警管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmStrategyDto.EditTelemeteringAlarmStrategyInput)">
            <summary>
            遥测报警数据增加或修改
            </summary>
            <param name="input">遥测报警数据</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmStrategyDto.EditTelemeteringAlarmStrategyInput)">
            <summary>
            新增联动报警策略
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmStrategyDto.EditTelemeteringAlarmStrategyInput)">
            <summary>
            遥测模板更新
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥测报警数据
            </summary>
            <param name="id">遥测报警id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥测报警数据
            </summary>
            <param name="ids">遥测报警id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmStrategyDto.SearchCondition.TelemeteringAlarmStrategySearchConditionInput})">
            <summary>
            查询遥测报警数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmStrategyAppService.FindSimDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmStrategyDto.SearchCondition.TelemeteringAlarmStrategySearchConditionInput})">
            <summary>
            查询遥测报警数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelemeteringAlarmTemplateAppService">
            <summary>
            遥测报警模板管理服务
            </summary>                                                                                                          
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmTemplateAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmTemplateDto.EditTelemeteringAlarmTemplateInput)">
            <summary>
            遥测报警模板编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmTemplateAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmTemplateDto.EditTelemeteringAlarmTemplateInput)">
            <summary>
            更新遥控模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmTemplateAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmTemplateDto.EditTelemeteringAlarmTemplateInput)">
            <summary>
            增加遥控模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmTemplateAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥测模板
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmTemplateAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥测报警模板
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringAlarmTemplateAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmTemplateDto.TelemeteringAlarmTemplateSearchConditionInput})">
            <summary>
            遥测报警模板查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelemeteringConfigurationAppService">
            <summary>
            遥测配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelemeteringConfigurationAppService.FindTelemeteringConfigurationForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.SelectTelemeteringSearchConditionInput)">
            <summary>
            根据条件查询遥测下拉框内容
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelemeteringConfigurationAppService.FindTelesignalisationConfigurationDataStruct(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.TelemeteringConfigurationSearchConditionInput)">
            <summary>
            查询要测通讯数据结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelemeteringConfigurationAppService.GetEnvironmentTempValue(System.Nullable{System.Guid})">
            <summary>
            查询环境温度
            </summary>
            <param name="transubstationId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService">
            <summary>
            遥测配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.EditTelemeteringConfigurationInput)">
            <summary>
            遥测数据增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.EditTelemeteringConfigurationInput)">
            <summary>
            新增遥测
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.EditTelemeteringConfigurationInput)">
            <summary>
            修改遥测
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥测数据
            </summary>
            <param name="id">遥测id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥测数据
            </summary>
            <param name="ids">遥测id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.TelemeteringConfigurationSearchConditionInput})">
            <summary>
            查询遥测数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.FindAllDatas">
            <summary>
            查询所有遥测数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.FindTelesignalisationConfigurationDataStruct(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.TelemeteringConfigurationSearchConditionInput)">
            <summary>
            查询要测通讯数据结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.GetTelemetringConfigurationTreeNode(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.TelemeteringConfigurationSearchConditionInput)">
            <summary>
            遥测数据树形结构获取
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.FindTelemeteringConfigurationForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.SelectTelemeteringSearchConditionInput)">
            <summary>
            根据条件查询遥测下拉框内容
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.GetEnvironmentTempValue(System.Nullable{System.Guid})">
            <summary>
            查询环境温度
            </summary>
            <param name="transubstationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.FindListDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition.TelemeteringConfigurationSearchConditionInput)">
            <summary>
             查询所有遥测数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringConfigurationAppService.PaddingSequence(System.Int32,System.Int32,YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum)">
            <summary>
            填充序列
            </summary>
            <param name="start"></param>
            <param name="count"></param>
            <param name="dataSourceCategory"></param>
            
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelemeteringTemplateAppService">
            <summary>
            遥测模板管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService">
            <summary>
            遥测模板管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringTemplateDto.EditTelemeteringTemplateInput)">
            <summary>
            遥控模板编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringTemplateDto.EditTelemeteringTemplateInput)">
            <summary>
            更新遥控模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringTemplateDto.EditTelemeteringTemplateInput)">
            <summary>
            增加遥控模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥测模板
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥测模板
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelemeteringTemplateAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringTemplateDto.TelemeteringTemplateSearchConditionInput})">
            <summary>
            遥测模板查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelesignalisationConfigurationAppService">
            <summary>
            遥信配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.ITelesignalisationConfigurationAppService.FindTelesignalisationConfigurationForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition.SelectTelesignalisationSearchConditionInput)">
            <summary>
            根据条件查询遥信下拉框内容
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService">
            <summary>
            遥信配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.EditTelesignalisationConfigurationInput)">
            <summary>
            遥信数据增加或修改
            </summary>
            <param name="input">遥信数据体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥信数据
            </summary>
            <param name="id">遥信id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥信数据
            </summary>
            <param name="ids">遥信id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition.TelesignalisationConfigurationSearchConditionInput})">
            <summary>
            查询遥信数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.FindAllDatas">
            <summary>
                查询所有遥信数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.FindListDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition.TelesignalisationConfigurationSearchConditionInput)">
            <summary>
             查询所有遥信数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.FindTelesignalisationConfigurationDataStruct(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition.TelesignalisationConfigurationSearchConditionInput)">
            <summary>
                查询遥信通信数据结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationConfigurationAppService.FindTelesignalisationConfigurationForSelect(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition.SelectTelesignalisationSearchConditionInput)">
            <summary>
            根据条件查询遥信下拉框内容
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.ITelesignalisationTemplateAppService">
            <summary>
            遥信模板管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService">
            <summary>
            遥信模板管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationTemplateDto.EditTelesignalisationTemplateInput)">
            <summary>
            遥信模板编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationTemplateDto.EditTelesignalisationTemplateInput)">
            <summary>
            更新遥信模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationTemplateDto.EditTelesignalisationTemplateInput)">
            <summary>
            增加遥信模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个遥信模板
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个遥信模板9656
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.DataMonitoring.TelesignalisationTemplateAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationTemplateDto.TelesignalisationTemplateSearchConditionInput})">
            <summary>
            遥信模板查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService">
            <summary>
            识别配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.SelfCheckingConfigurationDto.EditSelfCheckingConfigurationInput)">
            <summary>
            识别配置增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.SelfCheckingConfigurationDto.EditSelfCheckingConfigurationInput)">
            <summary>
            修改识别配置信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.SelfCheckingConfigurationDto.EditSelfCheckingConfigurationInput)">
            <summary>
            增加配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个识别配置信息
            </summary>
            <param name="id">识别配置id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个识别配置信息
            </summary>
            <param name="ids">识别配置id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.SelfCheckingConfigurationDto.SelfCheckingConfigurationSearchCondition})">
            <summary>
            识别配置查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.GetTypes(System.Guid)">
            <summary>
            查找种类
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.GetJudgmentModes">
            <summary>
            查找判定类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.GetDataTypes">
            <summary>
            数据类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.SelfCheckingConfigurationAppService.ImportPatternConfiguration">
            <summary>
            导入识别配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.CameraAuthenticationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.EditCameraAuthenticationInput)">
            <summary>
            增加或修改
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.CameraAuthenticationAppService.FindStationLevels">
            <summary>
            获取主站的级别下拉选项
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.CameraAuthenticationAppService.FindCameraAuthForSelect">
            <summary>
            获取摄像机权限的下拉选项
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IInspectionCardAppService">
            <summary>
            巡检记录单管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionCardAppService.SpawnImageRecognitionInspectionItem(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.SearchCondition.InspectionCardSearchConditionInput)">
            <summary>
            生成图像识别任务单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionCardAppService.SpawnIncludeAllPresetItem(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.SearchCondition.InspectionCardSearchConditionInput)">
            <summary>
            生成所有除红外测温点的任务单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionCardAppService.StartCardIdAsync(System.Guid)">
            <summary>
            启动巡检
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionCardAppService.UpdateInspectionServiceCache">
            <summary>
            刷新巡检服务缓存
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService">
            <summary>
            巡检记录单管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.EditInspectionCardInput)">
            <summary>
            巡检任务单更新或者添加
            </summary>
            <param name="input">巡检任务单</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.EditInspectionCardInput)">
            <summary>
            更新巡检任务单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.EditInspectionCardInput)">
            <summary>
            增加巡检任务单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检任务单
            </summary>
            <param name="id">巡检任务单id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.SpawnImageRecognitionInspectionItem(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.SearchCondition.InspectionCardSearchConditionInput)">
            <summary>
            生成识别任务单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.SpawnIncludeAllPresetItem(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.SearchCondition.InspectionCardSearchConditionInput)">
            <summary>
            生成所有预置点任务单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个巡检任务单
            </summary>
            <param name="ids">巡检任务单id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionCardDto.SearchCondition.InspectionCardSearchConditionInput})">
            <summary>
            查询巡检任务单
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.StartCardIdAsync(System.Guid)">
            <summary>
            启动巡检
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionCardAppService.UpdateInspectionServiceCache">
            <summary>
            刷新巡检服务缓存
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IInspectionItemAppService">
            <summary>
            巡检记录单管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionItemAppService.FindProcessActionsForSelect">
            <summary>
            获取可执行的巡视动作下拉框数据
            </summary>
            <returns>返回满足条件的巡视动作</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService">
            <summary>
            巡检记录单管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemDto.EditInspectionItemInput)">
            <summary>
            巡检任务项增加或修改
            </summary>
            <param name="input">巡检任务项</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemDto.EditInspectionItemInput})">
            <summary>
            多个巡检任务项增加或修改
            </summary>
            <param name="inputs">巡检任务项</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemDto.EditInspectionItemInput)">
            <summary>
            巡检任务项修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemDto.EditInspectionItemInput)">
            <summary>
            巡检任务项增加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检任务项
            </summary>
            <param name="id">巡检任务项id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个巡检任务项
            </summary>
            <param name="ids">巡检任务项id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemDto.SearchCondition.InspectionItemSearchConditionInput})">
            <summary>
            查询巡检任务项
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionItemAppService.FindProcessActionsForSelect">
            <summary>
            巡检任务活动选择列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionLiveDataAppService.GetInspectionLiveData(System.Guid)">
            <summary>
            获取巡检实时数据
            </summary>
            <param name="transformerSubstationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionLiveDataAppService.GetInspectionProgressOverview(System.Guid)">
            <summary>
            获取正在巡检总览信息
            </summary>
            <param name="transformerSubstationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionLiveDataAppService.GetInspectionLiveData(System.Guid)">
            <summary>
            获取实时巡检进度数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionLiveDataAppService.GetInspectionProgressOverview(System.Guid)">
            <summary>
            获取正在巡检总览信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService">
            <summary>
            巡检计划管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.IssureInspectionPlan">
            <summary>
            下发巡检计划到子站
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.GetInspectionPlanTaskInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            根据变电所ID获取巡检任务信息
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.GetInspectionTaskPlanInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            根据变电所id获取巡检计划信息-最新
            合并所有重复项 -任务卡片
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.CopyTaskByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            复制数据
            </summary>
            <param name="ids">ID列表</param>
            <returns>返回是否复制成功</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.GetInspectionTaskInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            根据变电所ID获取巡检任务信息（包含临时巡检）
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.FindDatasToday(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.InspectionPlanTaskSearchConditionInput})">
            <summary>
            今日巡检任务计划
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IInspectionPlanTaskAppService.GetLiveRecognizeInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            查询实时识别信息
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService">
            <summary>
            巡检计划管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService._inspectionTaskExtensionAppService">
            <summary>
            巡检任务扩展服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.EditInspectionPlanTaskInput)">
            <summary>
            巡检计划增加或者修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.EditInspectionPlanTaskInput},System.Guid)">
            <summary>
            多条巡检计划增加
            </summary>
            <param name="inputs"></param>
            <param name="transsubstationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.EditInspectionPlanTaskInput)">
            <summary>
            巡检任务计划修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.EditInspectionPlanTaskInput)">
            <summary>
            巡检任务增加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检任务计划
            </summary>
            <param name="id">巡检任务计划id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个巡检任务计划
            </summary>
            <param name="ids">巡检任务计划id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.InspectionPlanTaskSearchConditionInput})">
            <summary>
            巡检任务计划查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.FindDatasCoombineWeek(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.InspectionPlanTaskSearchConditionInput})">
            <summary>
            巡检任务计划合并查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.FindDatasToday(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionPlanTaskDto.InspectionPlanTaskSearchConditionInput})">
            <summary>
            今日巡检任务计划
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.IssureInspectionPlan">
            <summary>
            下发巡检计划到子站
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.GetInspectionPlanTaskInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            查询巡检任务计划  (巡检任务必须是执行时间的)
            </summary>
            <param name="subId">巡检任务计划id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.GetInspectionTaskInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            查询巡检任务计划 （包含临时巡检）
            </summary>
            <param name="subId">巡检任务计划id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.GetInspectionTaskPlanInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            根据变电所id获取巡检计划信息-最新
            合并所有重复项 -任务卡片
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.GetLiveRecognizeInfosBySubId(System.Nullable{System.Guid})">
            <summary>
            查询实时识别信息
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.CopyTaskByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            
            复制删除数据
            </summary>
            <param name="ids">ID列表</param>
            <returns>返回是否复制成功</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.InspectionPlanTaskAppService.RefreshTaskForInspectionService(System.Nullable{System.Guid})">
            <summary>
            刷新巡检任务
            </summary>
            <param name="subId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.ILightingControlAppService">
            <summary>
            巡检灯控服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.ILightingControlAppService.GetLightControlRuleSelectModelOutput">
            <summary>
            获取灯光控制规则枚举
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.LightingControlAppService">
            <summary>
            巡检灯控服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.LightingControlAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检灯控规则
            </summary>
            <param name="id">id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.LightingControlAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个巡检灯控规则
            </summary>
            <param name="ids">id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.LightingControlAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperaturePointDto.SearchCondition.LightingControlSearchConditionInput})">
            <summary>
            巡检灯控规则信息查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.LightingControlAppService.InspectionLightTeleCommand(System.Guid)">
            <summary>
            巡检灯控遥控信息获取
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.LightingControlAppService.GetLightControlRuleSelectModelOutput">
            <summary>
            开灯规则枚举
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IMeasureTemperaturePointAppService">
            <summary>
            测温点管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IMeasureTemperaturePointAppService.FindMeasureTemperatureTypeForSelect">
            <summary>
            检索视频测温点类型下拉框选择信息（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IMeasureTemperaturePointAppService.GetDatasByPresetPointId(System.Guid)">
            <summary>
            根据预置点ID获得测温点信息
            </summary>
            <param name="presetPointId">预置点Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IMeasureTemperaturePointAppService.ImportExcel(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入测温点数据库对应配置项
            </summary>
            <param name="formCollection"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IMeasureTemperaturePointAppService.ExportExcel(System.Guid)">
            <summary>
            导出预置位数据
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService">
            <summary>
            测温点管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperaturePointDto.EditMeasureTemperaturePointInput)">
            <summary>
            修改或添加测温点
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperaturePointDto.EditMeasureTemperaturePointInput)">
            <summary>
            增加测温点
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperaturePointDto.EditMeasureTemperaturePointInput)">
            <summary>
            修改测温点信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个测温点信息
            </summary>
            <param name="id">测温点id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个测温点信息
            </summary>
            <param name="ids">测温点id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperaturePointDto.SearchCondition.MeasureTemperaturePointSearchConditionInput})">
            <summary>
            测温点信息查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.FindMeasureTemperatureTypeForSelect">
            <summary>
            检索视频测温点类型下拉框选择信息（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.GetDatasByPresetPointId(System.Guid)">
            <summary>
            根据预置点ID获得测温点信息
            </summary>
            <param name="presetPointId">预置点Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.ExportExcel(System.Guid)">
            <summary>
            导出预置位测温点数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.ImportExcel(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入测温点数据库对应配置项
            </summary>
            <param name="formCollection"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MeasureTemperaturePointAppService.RelevanceTelemetringData">
            <summary>
            同步遥测信息体地址
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MultidimensionalCheckScheduleAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.MultidimensionalCheckScheduleDto.MultidimensionalCheckScheduleSearchConditionInput})">
            <summary>
            查询数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MultidimensionalCheckAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.MultidimensionalCheckDto.MultidimensionalCheckSearchConditionInput})">
            <summary>
            查询数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.MultidimensionalCheckAppService.SpawCardData">
            <summary>
            自动生成核对数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IPatternRecognitionAppService">
            <summary>
            预置点管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPatternRecognitionAppService.SpawnTelemetering(YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara)">
            <summary>
            生成识别点的遥测点位信息
            </summary>
            <param name="para"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPatternRecognitionAppService.SpawnTelesignalisation(YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara)">
            <summary>
            生成识别点的遥测点位信息
            </summary>
            <param name="para"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPatternRecognitionAppService.GetTypes">
            <summary>
            查找识别种类
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPatternRecognitionAppService.GetPresetRecognizeRemake(System.Guid)">
            <summary>
            根据预置位id获取识别备注信息
            </summary>
            <param name="presetId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService">
            <summary>
            识别配置管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.PatternRecognitionConfigurationDto.EditPatternRecognitionConfigutrationInput)">
            <summary>
            识别配置增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.PatternRecognitionConfigurationDto.EditPatternRecognitionConfigutrationInput)">
            <summary>
            修改识别配置信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.PatternRecognitionConfigurationDto.EditPatternRecognitionConfigutrationInput)">
            <summary>
            增加识别配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个识别配置信息
            </summary>
            <param name="id">识别配置id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个识别配置信息
            </summary>
            <param name="ids">识别配置id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.PatternRecognitionConfigurationDto.PatternRecognitionConfigutrationSearchCondition})">
            <summary>
            识别配置查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.SpawnTelemetering(YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara)">
            <summary>
            生成识别点的遥测点位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.GetTypes">
            <summary>
            查找识别种类
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.SpawnTelesignalisation(YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara)">
            <summary>
            生成识别点的遥信点位信息
            </summary>
            <param name="para"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.GetPresetRecognizeRemake(System.Guid)">
            <summary>
            根据预置位id获取识别备注信息
            </summary>
            <param name="presetId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.ImportPatternConfiguration">
            <summary>
            导入识别配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PatternRecognitionAppService.FindPatternConfigurationForSelect(YunDa.ISAS.DataTransferObject.PatternRecognition.SelectPatternConfigurationInput)">
            <summary>
            获取可以选择的识别配置下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="P:YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara.Ids">
            <summary>
            数据id数组
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara.DispatcherStartAddr">
            <summary>
            调度起始地址
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara.InfoStartAddr">
            <summary>
            信息体起始地址
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.VideoSurveillance.PatternRecognition.SpawnTeleDataPara.DeviceAddress">
            <summary>
            装置地址
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService">
            <summary>
            预置点管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.FindPresetPointsForSelect(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.PresetPointSearchCondition)">
            <summary>
            获取摄像头预置点下拉框数据
            </summary>
            <returns>返回满足条件的数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.ExportExcelData(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.PresetPointInput)">
            <summary>
            导出数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入预置点信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.FindEquipmentVideoPresets(System.Guid)">
            <summary>
            根据设备信息查询摄像头-摄像头NVR-预置位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.FindPresetPointsByViewPointForSelect(System.Guid)">
            <summary>
            根据设备视口Id检索预置点按设备视口分层信息
            </summary>
            <param name="equipmentViewPointId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.GetData(System.Guid)">
            <summary>
            预置信息id查询
            </summary>
            <param name="id">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.GotoPreset(System.String,System.Int32)">
            <summary>
            远程调用预置位
            </summary>
            <param name="cameraName"></param>
            <param name="presetNum"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IPresetPointAppService.GotoPresetById(System.Guid)">
            <summary>
            远程调用预置位
            </summary>
            <param name="presetId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService">
            <summary>
            预置点管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.EditPresetPointInput)">
            <summary>
            预置点增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.Update(System.Object)">
            <summary>
            预置位修改接口
            </summary>
            <param name="content"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.EditPresetPointInput)">
            <summary>
            修改预置点信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.EditPresetPointInput)">
            <summary>
            增加预置点
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个预置点信息
            </summary>
            <param name="id">预置点id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个预置点信息
            </summary>
            <param name="ids">预置点id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.PresetPointSearchCondition})">
            <summary>
            预置信息查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.GetData(System.Guid)">
            <summary>
            预置信息id查询
            </summary>
            <param name="id">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入预置点信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.ExportExcelData(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.PresetPointInput)">
            <summary>
            导出预置位数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.FindPresetPointsForSelect(YunDa.ISAS.DataTransferObject.VideoSurveillance.PresetPointDto.PresetPointSearchCondition)">
            <summary>
            根据查询条件检索预置点信息（供其他模块选择）
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.FindPresetPointsByViewPointForSelect(System.Guid)">
            <summary>
            根据设备视口Id检索预置点按设备视口分层信息（供其他模块选择）
            </summary>
            <param name="equipmentViewPointId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.FindEquipmentVideoPresets(System.Guid)">
            <summary>
            根据设备信息查询摄像头及其预置位信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.PersetRecongnizeSettingInfo">
            <summary>
            预置位识别信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.GetVideoByName(System.String)">
            <summary>
            摄像头信息查询
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.GotoPreset(System.String,System.Int32)">
            <summary>
            调用预置位
            </summary>
            <param name="cameraName"></param>
            <param name="presetNum"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointAppService.GotoPresetById(System.Guid)">
            <summary>
            调用预置位
            </summary>
            <param name="presetId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.PresetPointExAppService.FindEquipmentVideoPresets(System.Guid)">
            <summary>
            根据设备信息查询摄像头及其预置位信息
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService">
            <summary>
            视频设备管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindVideoDevForSelect(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput)">
            <summary>
            获取视频设备下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindDevTypeForSelect">
            <summary>
            检索视频设备类型下拉框选择信息（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindVoiceTypeForSelect">
            <summary>
            检索音频类型下拉框选择信息（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindVideoDevById(System.Guid)">
            <summary>
            根据 Guid ID 检索视频设备信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindCameraAndParentByCameraId(System.Guid)">
            <summary>
            根据摄像头 Guid ID 检索 NVR 设备信息
            </summary>
            <param name="id"></param>
            <returns> NVR 详细信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindVideoAndNvrByCameraId(System.Guid)">
            <summary>
            根据摄像头 Guid ID 检索 NVR 设备信息
            </summary>
            <param name="id"></param>
            <returns> NVR 详细信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindCameraAndParentAndPointByPId(System.Guid)">
            <summary>
            根据预置点ID查询摄像头及其NVR
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindVideoDevAndPresetPointByEquipmentInfoId(System.Guid)">
            <summary>
            根据设备id查询预置点及其父节点设备
            </summary>
            <param name="id">设备Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindInfraredCameras(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.SearchCondition.InfraredCameraSearchConditionInput)">
            <summary>
            查找红外摄像头；若IsNeedMeasureResult=true和EquipmentInfoId.HasValue则可查询对应设备的最近一天内最后一次获取得温度值
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindDevForTreeModelOutput(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput)">
            <summary>
            查找摄像头树形列表信息
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.FindDatasToNopageList(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput)">
            <summary>
            查找摄像头
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.UpdateRedisVideoData">
            <summary>
            初始化或更新摄像头权限信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.ExportExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ImportSubstationInfo)">
            <summary>
            导出摄像头与预置位信息
            </summary>
            <param name="substationInfo"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入摄像头与预置位信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.IVideoDevAppService.GetInvestigationTrailEvent(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoInvestigationTrainEventInput)">
            <summary>
            客户端根据摄像头获取历史报警事件与巡检事件
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService">
            <summary>
            视频设备管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService._mtpResultRepository">
            <summary>
            红外测温结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.UpdateRedisVideoData">
            <summary>
            初始化缓存摄像头权限
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.EditVideoDevInput)">
            <summary>
            视频设备信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.EditVideoDevInput)">
            <summary>
            增加视频设备信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.EditVideoDevInput)">
            <summary>
            更新视频设备
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个视频设备信息
            </summary>
            <param name="id">视频设备id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个视频设备信息
            </summary>
            <param name="ids">视频设备id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput})">
            <summary>
            视频设备信息查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindDevTypeForSelect">
            <summary>
            检索视频设备类型下拉框选择信息（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindVoiceTypeForSelect">
            <summary>
            检索视频设备类型下拉框选择信息（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindDevForTreeModelOutput(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput)">
            <summary>
            根据查询条件查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns>视频设备树形列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindDatasToNopageList(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput)">
            <summary>
            根据查询条件查询视频设备
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns>视频设备信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindVideoDevForSelect(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoDevSearchConditionInput)">
            <summary>
            根据查询条件检索视频设备信息（供其他模块选择）
            </summary>
            <param name="searchCondition"></param>
            <returns>视频设备选择列表</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindVideoDevById(System.Guid)">
            <summary>
            根据 ID 检索视频设备信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindCameraAndParentByCameraId(System.Guid)">
            <summary>
            查询NVR 设备信息
            </summary>
            <param name="id">摄像头id</param>
            <returns> NVR 详细信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindVideoAndNvrByCameraId(System.Guid)">
            <summary>
            获取摄像机及NVR信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindVideoDevAndPresetPointByEquipmentInfoId(System.Guid)">
            <summary>
            根据设备id查询预置点及其父节点设备
            </summary>
            <param name="id">设备Id</param>
            <returns>视频设备信息</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindInfraredCameras(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.SearchCondition.InfraredCameraSearchConditionInput)">
            <summary>
            查找红外摄像头；若IsNeedMeasureResult=true和EquipmentInfoId.HasValue则可查询对应设备的最后一次获取得温度值
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindCameraAndParentAndPointByPId(System.Guid)">
            <summary>
            根据预置点ID查询摄像头及其NVR
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.ImportExcelDataOld(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入摄像头信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.ExportExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ImportSubstationInfo)">
            <summary>
            导出摄像头数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.DeleteCameraNoPrePointAndRepeat">
            <summary>
            纠错摄像机重复添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.GetInvestigationTrailEvent(YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.VideoInvestigationTrainEventInput)">
            <summary>
            客户端根据摄像头获取历史报警事件与巡检事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.UpdateCameraIp(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.VideoSurveillance.VideoDevDto.UpdateCameraIpInput},System.Guid)">
            <summary>
            通过占用通道号修改摄像头ip地址
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoDevAppService.FindCameraCascadeForSelect">
            <summary>
            获取摄像头列表
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.VideoSurveillance.VideoElectronicFenceAppService">
            <summary>
            视频电子围栏服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoElectronicFenceAppService.GetVideoCameraRTSPConfigure(System.String)">
            <summary>
            获取摄像头RTSP地址
            </summary>
            <param name="stationName"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoElectronicFenceAppService.UploadCfg(System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{System.Int32}})">
            <summary>
            上传配置信息
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.VideoSurveillance.VideoElectronicFenceAppService.UploadAlarmMsg(System.String,System.String)">
            <summary>
            上传监测的报警信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentDataCategoryAppService.FindEquipmentDataCategoryBaseForSelect">
            <summary>
            查询设备类型基础
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentDataCategoryAppService.FindEquipmentDataCategoryBase">
            <summary>
            查询设备界面详细类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentDataCategoryAppService.FindDatasByEquipmentId(System.Guid)">
            <summary>
            查询设备类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentIndicatorConfigAppService.CreateOrUpdateAsync(YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentIndicatorConfigDto.EditEquipmentIndicatorConfigInput)">
            <summary>
            指标配置增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService">
            <summary>
                设备信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.EditEquipmentInfoInput)">
            <summary>
            设备信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.SyncTeleTemplate">
            <summary>
            根据设备类型同步更新遥信、遥测、模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.CreateTelemeteringConfigurationByTemplatesAsync(YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo,System.Collections.Generic.List{YunDa.ISAS.Entities.DataMonitoring.TelemeteringTemplate})">
            <summary>
            根据模板创建遥测
            </summary>
            <param name="equipmentInfo"></param>
            <param name="telemeteringTemplates"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.CreateTelesignalisationConfigurationByTemplatesAsync(YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo,System.Collections.Generic.List{YunDa.ISAS.Entities.DataMonitoring.TelesignalisationTemplate})">
            <summary>
            根据模板创建遥信
            </summary>
            <param name="equipmentInfo"></param>
            <param name="telesignalisationTemplates"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.CreateTelecommandConfigurationByTemplatesAsync(YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo,System.Collections.Generic.List{YunDa.ISAS.Entities.DataMonitoring.TelecommandTemplate})">
            <summary>
            根据模板创建遥控
            </summary>
            <param name="equipmentInfo"></param>
            <param name="telecommandTemplates"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个设备信息
            </summary>
            <param name="id">设备id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个设备信息
            </summary>
            <param name="ids">设备id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.FindSimDatas(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.EquipmentInfoSearchConditionInput)">
            <summary>
             获取设备列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.EquipmentInfoSearchConditionInput})">
            <summary>
            查询设备信息
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.FindEquipmentById(System.Guid)">
            <summary>
                根据设备 Guid ID 检索单个设备信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.VerficationExist(YunDa.ISAS.DataTransferObject.RequestResult{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.EquipmentInfoOutput}@,YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.EditEquipmentInfoInput)">
            <summary>
                验证是否存在相同设备
            </summary>
            <param name="rst"></param>
            <param name="input"></param>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.GetEquipmentInfoTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.EquipmentInfoSearchConditionInput)">
            <summary>
            获取设备类型树结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.GetMonitoringEquipmentInfos(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.MonitoringEquipmentInfoSearchConditionInput)">
            <summary>
            获取变电所的所有监测设备
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.FindEquipmentInfoForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.SelectEquipmentInfoSearchConditionInput)">
            <summary>
            获取可以选择的设备下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.FindEquipmentInfoCascadeForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.SelectEquipmentInfoSearchConditionInput)">
            <summary>
            获取可以选择的设备下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.EquipmentsSafetyStateUpdate(System.Collections.Generic.List{System.Guid},System.Int32,YunDa.ISAS.Entities.GeneralInformation.SafetyStateTypeEnum)">
            <summary>
            更新设备布防撤防状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.EquipmenSafetyStateTypetGet(System.Guid)">
            <summary>
            查询设备布防撤防状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.EquipmenSafetyStateTypetGetAll(System.Guid)">
            <summary>
            查询所有设备布防撤防状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.ExportExcel(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.EquipmentInfoExportConditionInput)">
            <summary>
            导出设备数据配置文档
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoAppService.ImportExcel(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入设备数据并修改数据库对应配置项
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService">
            <summary>
             设备信息-扩展类
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService._telemeteringModelListRedis">
            <summary>
            遥测数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService._telesignalisationModelListRedis">
            <summary>
            遥信数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService._telemeteringModelInflectionListRedis">
            <summary>
            遥测数据变位库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService._telesignalisationModelInflectionListRedis">
            <summary>
            遥信数据变位库
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.FindEquipmentSearchRecordAsync(System.Guid)">
            <summary>
            查询设备搜索列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.SeachConfirm(System.Guid)">
            <summary>
            查询确定
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.FindEquipmentTreeData(System.String,YunDa.ISAS.Entities.GeneralInformation.EquipmentTypeLevelEnum)">
            <summary>
            查询设备树型结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.GetSubEquipements(System.Guid)">
            <summary>
            查询子设备
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.GetEquipmentInfoAsset(System.Guid)">
            <summary>
            查询设备资产信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.InitYCListAsync(YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum)">
            <summary>
            初始化遥测队列
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentInfoExAppService.InitYXListAsync(YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum)">
            <summary>
            初始化遥信队列
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.FindEquipmentInfoForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.SelectEquipmentInfoSearchConditionInput)">
            <summary>
            获取可以选择的设备下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.FindEquipmentById(System.Guid)">
            <summary>
            通过Id查找单个数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.GetEquipmentInfoTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.EquipmentInfoSearchConditionInput)">
            <summary>
            获取设备类型树结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.GetMonitoringEquipmentInfos(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.MonitoringEquipmentInfoSearchConditionInput)">
            <summary>
            获取变电所的所有监测设备
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.SyncTeleTemplate">
            <summary>
            根据设备类型同步更新遥信、遥测、模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.ExportExcel(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition.EquipmentInfoExportConditionInput)">
            <summary>
            导出设备数据配置文档
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.ImportExcel(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入设备数据并修改数据库对应配置项
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.EquipmentsSafetyStateUpdate(System.Collections.Generic.List{System.Guid},System.Int32,YunDa.ISAS.Entities.GeneralInformation.SafetyStateTypeEnum)">
            <summary>
            更新布防撤防状态
            </summary>
            <param name="input"></param>
            <param name="seconds"></param>
            <param name="safetyState"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.EquipmenSafetyStateTypetGet(System.Guid)">
            <summary>
            查询布防撤防状态
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoAppService.EquipmenSafetyStateTypetGetAll(System.Guid)">
            <summary>
            查询所内设备布防撤防状态
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoExAppService.GetEquipmentInfoAsset(System.Guid)">
            <summary>
            客户端查询设备资产信息
            </summary>
            <param name="equipementId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoExAppService.InitYCListAsync(YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum)">
            <summary>
            初始化遥测
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentInfoExAppService.InitYXListAsync(YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum)">
            <summary>
            初始化遥信
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.SecondaryElectricalEquipmentInfoAppService">
            <summary>
             二次设备信息
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.SecondaryElectricalEquipmentInfoAppService.index">
            <summary>
            迁移综自的数据到运维系统中 三遥信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.GetPDMonitoringDataAsync(YunDa.ISAS.Redis.Repositories.IRedisRepository{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel,System.String},System.String,System.Guid,YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum,YunDa.ISAS.MongoDB.Repositories.IMongoDbRepository{MongoDB.Bson.BsonDocument,System.Guid})">
            <summary>
            获取设备局部放电监测数据，结合实时数据和历史数据
            </summary>
            <param name="telemeteringModelListRedis">遥测数据Redis仓库</param>
            <param name="telemeteringModelListRediskey">Redis键名</param>
            <param name="equipmentId">设备ID</param>
            <param name="dataSourceCategory">数据源分类</param>
            <returns>结构化的放电监测数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.GenerateHistoricalThreeDData(System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel},System.Guid,YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum,System.DateTime,System.DateTime,YunDa.ISAS.MongoDB.Repositories.IMongoDbRepository{MongoDB.Bson.BsonDocument,System.Guid})">
            <summary>
            从历史数据中生成三维图数据
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.GenerateHistoricalWaveformData(System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel},System.Guid,YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum,System.DateTime,System.DateTime,YunDa.ISAS.MongoDB.Repositories.IMongoDbRepository{MongoDB.Bson.BsonDocument,System.Guid})">
            <summary>
            从历史数据中生成波形数据
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.GenerateHistoricalFrequencyData(System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel},System.Guid,YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum,System.DateTime,System.DateTime,YunDa.ISAS.MongoDB.Repositories.IMongoDbRepository{MongoDB.Bson.BsonDocument,System.Guid})">
            <summary>
            从历史数据中生成频率分析数据
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.GenerateHistoricalHeatmapData(System.Collections.Generic.List{YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto.TelemeteringModel},System.Guid,YunDa.ISAS.Entities.DataMonitoring.DataSourceCategoryEnum,System.DateTime,System.DateTime,YunDa.ISAS.MongoDB.Repositories.IMongoDbRepository{MongoDB.Bson.BsonDocument,System.Guid})">
            <summary>
            从历史数据中生成热图数据
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.EquipmentSpecialDataOutput">
            <summary>
            特殊监测数据输出模型
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.EquipmentSpecialDataOutput.Threed">
            <summary>
            三维数据 [相位, 时间戳, 计数]
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.EquipmentSpecialDataOutput.Waveform">
            <summary>
            波形数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.EquipmentSpecialDataOutput.Freq">
            <summary>
            频率数据
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.EquipmentSpecialDataOutput.Heatmap">
            <summary>
            热图数据
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.WaveformPoint">
            <summary>
            波形数据点
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.FrequencyData">
            <summary>
            频率数据
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveData.ChartsData.EquipmentSpecialMonitoringData.HeatmapData">
            <summary>
            热图数据
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService._telemeteringModelListRedis">
            <summary>
            遥测数据实时库
            </summary>
        </member>
        <member name="F:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService._telesignalisationModelListRedis">
            <summary>
            遥信数据实时库
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetEquipmentLiveStateByEquipmentId(System.Guid)">
            <summary>
            根据设备id获取设备实时运行数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetEquipmentLiveStateByModelId(System.Int32)">
            <summary>
            根据模型id获取设备实时运行数据
            </summary>
            <param name="modelId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetEquipmentOverviewAsync(System.Guid)">
            <summary>
            设备总览信息
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetEquipmentTelemeteringByEquipmentId(System.Guid,System.Int32)">
            <summary>
            根据设备id获取设备遥测实时数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetEquipmentTelesignalisationByEquipmentId(System.Guid,System.Int32)">
            <summary>
            根据设备id获取设备遥信实时数据
            </summary>
            <param name="equipmentId"></param>
            <param name="dataSourceCategory"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.UploadEquipmentDiagnosisAsync(System.Object)">
            <summary>
            九维发送设备诊断信息到后台
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.IsTrueAlarm(System.String)">
            <summary>
            是否为可信报警
            </summary>
            <param name="suggest"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetAlarmLevelStr(System.String)">
            <summary>
            获取报警级别
            </summary>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.NavigateToClientEquipmentDetailAsync(System.String)">
            <summary>
            导航到客户端详细页面
            </summary>
            <param name="equipemntInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.NavigateToClientCameraAsync(System.String)">
            <summary>
            导航到客户端视频页面
            </summary>
            <param name="equipemntInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLiveDataAppService.GetPDMonitoringDataAsync(System.Guid)">
            <summary>
            获取特定设备的局部放电监测数据
            </summary>
            <returns>结构化的局部放电监测数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLiveDataAppService.GetEquipmentLiveStateByModelId(System.Int32)">
            <summary>
            根据模型id获取设备实时运行数据
            </summary>
            <param name="modelId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLiveDataAppService.GetEquipmentLiveStateByEquipmentId(System.Guid)">
            <summary>
            根据设备id获取设备实时运行数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLiveDataAppService.GetEquipmentOverviewAsync(System.Guid)">
            <summary>
            设备总览信息
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLiveDataAppService.GetEquipmentTelemeteringByEquipmentId(System.Guid,System.Int32)">
            <summary>
            获取设备遥测信息
            </summary>
            <param name="equipmentId">设备Id</param>
            <param name="dataSourceCategory">数据种类</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLiveDataAppService.GetEquipmentTelesignalisationByEquipmentId(System.Guid,System.Int32)">
            <summary>
            获取设备实时遥信信息
            </summary>
            <param name="equipmentId"></param>
            <param name="dataSourceCategory"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService">
            <summary>
            设备安装区域管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentLocationDto.EditEquipmentLocationInput)">
            <summary>
            设备安装区域编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentLocationDto.EditEquipmentLocationInput)">
            <summary>
            更新
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.CreateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentLocationDto.EditEquipmentLocationInput)">
            <summary>
            增加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个设备安装区域
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个设备安装区域
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentLocationDto.EquipmentLocationSearchConditionInput})">
            <summary>
            设备安装区域查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.FindEquipmentLocationForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentLocationDto.EquipmentLocationSearchConditionInput)">
            <summary>
            获取可以选择的设备安装区域下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.EquipmentLocationSafetyArmingUpdate(System.String)">
            <summary>
            设备安装区域设备布防
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentLocationAppService.EquipmentLocationSafetyDisarmingUpdate(System.String)">
            <summary>
            设备安装区域设备撤防
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLocationAppService.FindEquipmentLocationForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentLocationDto.EquipmentLocationSearchConditionInput)">
            <summary>
            获取可以选择的设备安装区域下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLocationAppService.EquipmentLocationSafetyDisarmingUpdate(System.String)">
            <summary>
            撤防区域设备
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentLocationAppService.EquipmentLocationSafetyArmingUpdate(System.String)">
            <summary>
            布防区域设备
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService">
            <summary>
            设备类型视口信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeViewPointDto.EditEquipmentTypeViewPointInput)">
            <summary>
            设备类型视口信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个设备类型视口
            </summary>
            <param name="id">设备id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个设备类型视口
            </summary>
            <param name="ids">设备id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeViewPointDto.SearchCondition.EquipmentTypeViewPointSearchConditionInput})">
            <summary>
            查询设备类型视口
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.FindEquipmentTypeLevelForSelect(System.Guid)">
            <summary>
            查询设备类型视口供选择
            </summary>
            <param name="equipmentTypeId">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入设备类型视口数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeViewPointAppService.ExportExcel">
            <summary>
            导出设备类型视口数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeViewPointAppService.FindEquipmentTypeLevelForSelect(System.Guid)">
            <summary>
            查询设备类型视口供选择
            </summary>
            <param name="equipmentTypeId">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeViewPointAppService.ExportExcel">
            <summary>
            导出设备类型视口数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeViewPointAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入设备类型视口数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService">
            <summary>
            设备类型信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.EditEquipmentTypeInput)">
            <summary>
            设备类型增加或修改
            </summary>
            <param name="input">设备类型信息体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.DeleteByIdAsync(System.Guid)">
            <summary>
             删除单个设备类型
            </summary>
            <param name="id">设备类型id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个设备类型
            </summary>
            <param name="ids">设备类型id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeSearchConditionInput})">
            <summary>
            设备类型数据查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.FindSimDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeSearchConditionInput})">
            <summary>
            设备类型数据查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.FindEquipmentTypeLevelForSelect">
            <summary>
                根据查询条件查询设备类型等级数据（供其他模块选择）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.VerficationExistName(YunDa.ISAS.DataTransferObject.RequestResult{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.EquipmentTypeOutput}@,YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.EditEquipmentTypeInput,YunDa.ISAS.Entities.GeneralInformation.EquipmentType)">
            <summary>
                验证名称是否已经存在
            </summary>
            <param name="rst"></param>
            <param name="input"></param>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.GetEquipmentTypeTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeTreeSearchConditionInput)">
            <summary>
            设备类型树节点，按条件获取
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.GetAllTypeTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeTreeSearchConditionInput)">
            <summary>
            设备类型树节点，按条件获取
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.FindEquipmentTypeForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeSearchConditionInput)">
            <summary>
            获取设备类型树节点
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.ExportExcelData">
            <summary>
            导出三遥模板数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入三遥模板数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentTypeExAppService">
            <summary>
            设备类型扩展类
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeAppService.FindEquipmentTypeLevelForSelect">
            <summary>
            获取可以选择的设备类型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeAppService.GetEquipmentTypeTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeTreeSearchConditionInput)">
            <summary>
            设备类型树节点，按条件获取
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeAppService.FindEquipmentTypeForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.SearchCondition.EquipmentTypeSearchConditionInput)">
            <summary>
            获取可以选择的设备类型下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeAppService.ExportExcelData">
            <summary>
            导出模板数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentTypeAppService.ImportExcelData(Microsoft.AspNetCore.Http.IFormCollection)">
            <summary>
            导入三遥模板数据
            </summary>
            <param name="formCollection"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.EquipmentViewPointAppService">
            <summary>
            设备视口信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentViewPointAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentViewPointDto.EditEquipmentViewPointInput)">
            <summary>
            设备视口信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentViewPointAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个设备视口
            </summary>
            <param name="id">设备id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentViewPointAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个设备视口
            </summary>
            <param name="ids">设备id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentViewPointAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentViewPointDto.SearchCondition.EquipmentViewPointSearchConditionInput})">
            <summary>
            查询设备视口
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.EquipmentViewPointAppService.FindViewPointCasecadeForSelect(System.Guid,System.Guid)">
            <summary>
            查询设备视口分层(注意两个参数同时有值时，第一个生效)
            </summary>
            <param name="equipmentInfoId">设备Id</param>
            <param name="cameraId">摄像机Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IEquipmentViewPointAppService.FindViewPointCasecadeForSelect(System.Guid,System.Guid)">
            <summary>
            查询设备视口分层
            </summary>
            <param name="equipmentInfoId"></param>
            <param name="cameraId"></param>
            
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IManufacturerInfoAppService.SoftDeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据ID数组对数据进行软删除
            </summary>
            <param name="ids">ID数组</param>
            <returns>软删除操作结果</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IManufacturerInfoAppService.RecoverByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据ID数组对数据恢复软删除的数据
            </summary>
            <param name="ids">ID数组</param>
            <returns>软恢复操作结果</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService">
            <summary>
            厂商信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.ManufacturerInfoDto.EditManufacturerInfoInput)">
            <summary>
            厂商信息增加或修改
            </summary>
            <param name="input">厂商信息体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个厂商信息
            </summary>
            <param name="id">厂商id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个厂商信息
            </summary>
            <param name="ids">厂商id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.SoftDeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据 Guid ID 软删除厂商信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.ManufacturerInfoDto.ManufacturerInfoSearchConditionInput})">
            <summary>
            厂商信息查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.FindManufacturerInfoForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.ManufacturerInfoDto.ManufacturerInfoSearchConditionInput)">
            <summary>
            根据查询条件检索厂商信息（供其他模块选择）
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ManufacturerInfoAppService.RecoverByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据 Guid ID 恢复软删除的厂商信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.MasterStationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.ManufacturerInfoDto.EditMasterStationInput)">
            <summary>
            增加或修改
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IPowerSupplyLineAppService.GetPowerSupplyLinesAndChildrenAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.PowerSupplyLineSearchConditionInput)">
            <summary>
            查找线路下所有的子节点
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IPowerSupplyLineAppService.GetPowerSupplyLineTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.PowerSupplyLineSearchConditionInput)">
            <summary>
            获取线路树结构
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService">
            <summary>
            线路信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.EditPowerSupplyLineInput)">
            <summary>
            线路数据增加或修改
            </summary>
            <param name="input">线路数据信息体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.EditPowerSupplyLineInput)">
            <summary>
            更新线路数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.CreateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.EditPowerSupplyLineInput)">
            <summary>
            增加线路数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.GetPowerSupplyLinesAndChildrenAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.PowerSupplyLineSearchConditionInput)">
            <summary>
            线路信息及其变电所信息查询【将来可能会废弃】
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.PowerSupplyLineSearchConditionInput})">
            <summary>
            线路信息查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.GetPowerSupplyLineTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.PowerSupplyLineDto.PowerSupplyLineSearchConditionInput)">
            <summary>
            获取线路树节点
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个线路信息
            </summary>
            <param name="id">线路id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个线路信息
            </summary>
            <param name="ids">线路id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.PowerSupplyLineAppService.MeregeLineDataFromISMS">
            <summary>
            从ISMS同步线路数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService">
            <summary>
            保护装置履历信息管理类
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.ScanDeviceQRCode(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.EditBoardCardQrCodeInput)">
            <summary>
            扫码枪输入设备信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.GetBoardCardQrCodeInfo(System.Guid)">
            <summary>
            获取板卡信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除板卡信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.SearchCondition.BoardCardInfoSearchConditionInput})">
            <summary>
            查询保护装置板卡信息
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.FindHistoryDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询板卡历史数据
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.FindHistorySoftVersionByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询板卡历史版本数据
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.GetBoardStateInfo(System.Guid,System.Guid)">
            <summary>
            获取板卡状态
            </summary>
            <param name="equipmentId"></param>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.GetBoardIOState(System.Guid,System.Guid)">
            <summary>
            获取板卡IO信息
            </summary>
            <param name="equipmentId"></param>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.SpwanBoardInfoList">
            <summary>
            生成板卡信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.ModifyBoardInfoList(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionSettingDto.ProtectionDeviceVersionInfo)">
            <summary>
            更新版本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.SpwanBoardMancfactoryJDYDList">
            <summary>
            生成板卡信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.UpdateProtetionInfoForHistoryTest">
            <summary>
            填充出厂编号
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.BoardCardInfoAppService.SpawnBoardHistory">
            <summary>
            生成初始的历史记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IBoardCardInfoAppService.FindHistoryDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询板卡历史记录
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IBoardCardInfoAppService.GetBoardStateInfo(System.Guid,System.Guid)">
            <summary>
            查询板卡在线信息
            </summary>
            <param name="equipmentId"></param>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IBoardCardInfoAppService.ScanDeviceQRCode(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.EditBoardCardQrCodeInput)">
            <summary>
            扫码枪输入设备信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IBoardCardInfoAppService.ModifyBoardInfoList(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionSettingDto.ProtectionDeviceVersionInfo)">
            <summary>
            更新版本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IBoardCardInfoAppService.SpwanBoardInfoList">
            <summary>
            生成板卡信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IBoardCardInfoAppService.GetBoardCardQrCodeInfo(System.Guid)">
            <summary>
            获取板卡二维码信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.FindDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询保护设备信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.FindHistoryDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询历史保护装置信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.FindHistorySoftVersionDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询历史保护装置版本信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.FindProtectionDeviceForSelect(System.Guid)">
            <summary>
            获取所有保护装置
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.GetDeviceEventType(System.Int32,System.Int32)">
            <summary>
            获取装置事件类型
            </summary>
            <param name="deviceAddr"></param>
            <param name="eventCode"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.FindProtectionDeviceCommInfo(System.String)">
            <summary>
            获取装置通信地址
            </summary>
            <param name="stationName"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.IProtectionDeviceAppService.MigreteDeviceEquipementDataFromISMS(System.String)">
            <summary>
            从综自后台迁移装置数据到运维后台
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService">
            <summary>
            保护装置履历信息管理类
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.CreateOrUpdateAsync(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.EditProtectionDeviceInfoInput)">
            <summary>
            插入保护装置数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.ScanDeviceQRCode(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.EditProtectionQrCodeDeviceInfoInput)">
            <summary>
            扫码枪输入设备信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetDeviceQRCode(System.Guid)">
            <summary>
            获取设备二维码信息
            </summary>
            <param name="EquipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetProtectDeviceVersionInfo(System.Guid)">
            <summary>
            获取保护装置版本信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.SearchCondition.ProtectionDeviceInfoSearchConditionInput})">
            <summary>
            查询保护装置信息
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindDatasByCircuit(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.SearchCondition.ProtectionDeviceInfoSearchConditionInput})">
            <summary>
            查询保护装置信息
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询保护装置信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindHistoryDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询历史保护装置信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindHistorySoftVersionDataByEquipmentInfoId(System.Nullable{System.Guid})">
            <summary>
            查询保护装置历史版本信息
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindProtectionDeviceForSelect(System.Guid)">
            <summary>
            获取保护装置列表
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.FindProtectionDeviceCommInfo(System.String)">
            <summary>
            只在所内有效-获取变电所装置的ip地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetProtectionDeviceCommInfo(System.Guid,System.Guid)">
            <summary>
            根据装置id获取装置通信参数
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.MigreteDeviceEquipementDataFromISMS(System.String)">
            <summary>
            从综自后台数据库迁移数据到运维数据库中（保护装置数据）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.MigreteImProtectionDeviceTypeDataFromISMS">
            <summary>
            从综自后台数据库迁移数据到运维数据库中（保护装置类型数据）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.MigreteImGatewayDataFromISMS">
            <summary>
            从综自后台数据库迁移数据到运维数据库中（保护装置网关数据）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetDeviceEventType(System.Int32,System.Int32)">
            <summary>
            获取装置自检事件
            </summary>
            <param name="deviceAddr"></param>
            <param name="eventCode"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetDeviceStatusAsync(System.Guid)">
            <summary>
            获取装置状态
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetTelemeteringByKeywordAsync(System.Guid,System.String)">
            <summary>
            根据关键字获取保护装置的遥信遥测
            </summary>
            <param name="equipmentInfoId"></param>
            <param name="keyword"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.GetDeviceCPUMonitoring(System.Guid)">
            <summary>
            获取装置初始监测状态
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.UpdateProtetionInfoForTest">
            <summary>
            填充出厂编号
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.UpdateProtetionInfoForHistoryTest">
            <summary>
            填充出厂编号
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceAppService.SpawnDeviceinfoHistory">
            <summary>
            生成装置的初始历史记录
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceCV">
            <summary>
            装置履历
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDeviceCV.WriteProtectionDeviceCVXml">
            <summary>
            写装置履历xml文件
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDevice.ProtecttionDeviceRedisAppService.GetEquipmentInfoAbnormalComponentsAsync(System.Guid,System.Guid)">
            <summary>
            获取装置自检信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDevice.ProtecttionDeviceRedisAppService.GetSecondaryCircuitComponentsAsync(System.Guid,System.Guid)">
            <summary>
            获取二次回路诊断信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDevice.ProtecttionDeviceRedisAppService.GetEquipmentInfoRemainingLifeAssessments(System.Guid,System.Guid)">
            <summary>
            获取装置寿命预测信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionDevice.ProtecttionDeviceRedisAppService.GetProtectionDeviceCommInfoAsync(System.Guid)">
            <summary>
            查询保护装置在线情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.IProtectionSettingAppService.FindDZDataByEquipmentInfoId(System.Nullable{System.Guid},System.String)">
            <summary>
            查询定值信息
            </summary>
            <param name="equipmentInfoId"></param>
            <param name="dzType"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.IProtectionSettingAppService.GetDzByDeviceAddr(System.Int32,System.Int32)">
            <summary>
            根据装置获取定值
            </summary>
            <param name="deviceAddr"></param>
            <param name="cpuIndex"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.ISecondaryCircuitAppService.GetCircuitTypes">
            <summary>
            获取回路类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.ISecondaryCircuitAppService.FindProtectionDeviceForSelect(System.Guid)">
            <summary>
            获取回路包含的保护装置供选择
            </summary>
            <param name="secondaryCircuitId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.ISecondaryCircuitAppService.GetProtectionDevices(System.Guid)">
            <summary>
            获取回路包含的保护装置
            </summary>
            <param name="secondaryCircuitId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService">
            <summary>
            二次回路类
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.SearchCondition.SecondaryCircuitSearchConditionInput})">
            <summary>
            获取回路数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService.FindProtectionDeviceForSelect(System.Guid)">
            <summary>
            获取回路关联的保护装置列表
            </summary>
            <param name="secondaryCircuitId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService.GetCircuitTypes">
            <summary>
            获取回路类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService.LinkProtectionDeviceToCircuitAsync(System.Collections.Generic.List{System.Guid},System.Guid)">
            <summary>
            关联保护装置到二次回路
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService.UploaderImg(YunDa.SOMS.DataTransferObject.GeneralInformation.SecondaryCircuitDto.UploaderImgInput)">
            <summary>
            上传base64文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitAppService.GenerateSecondaryCircuitTestData">
            <summary>
            生成10个测试回路信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitLogicExpressionAppService">
            <summary>
            二次回路逻辑表达式类
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitLogicExpressionAppService.CreateOrUpdateAsync(YunDa.SOMS.DataTransferObject.GeneralInformation.SecondaryCircuitDto.EditSecondaryCircuitLogicExpressionInput)">
            <summary>
            添加或者修改二次回路的逻辑表达式
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitLogicExpressionAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除逻辑表达式
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SecondaryCircuitInfo.SecondaryCircuitLogicExpressionAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.SearchCondition.SecondaryCircuitLogicExpressionSearchConditionInput})">
            <summary>
            查询逻辑表达式
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService">
            <summary>
            保护定值
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto.SearchCondition.ProtectionSettingSearchConditionInput})">
            <summary>
            查询保护装置定值信息
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.GetDzByDeviceAddr(System.Int32,System.Int32)">
            <summary>
            使用装置地址从后台数据库中获取定值信息
            </summary>
            <param name="deviceAddr"></param>
            <param name="cpuIndex"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.FindDZDataByEquipmentInfoId(System.Nullable{System.Guid},System.String)">
            <summary>
            查询保护装置定值信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.MigreteProtectSettingDataFromISMS">
            <summary>
            从综自后台数据库迁移数据到运维数据库中（定值数据）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.MigreteProtectSttingTypeDataFromISMS">
            <summary>
            从综自后台数据库迁移数据到运维数据库中（定值类型数据）
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.SpwaProtectDeviceSettingTestData">
            <summary>
            生成模拟数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.GetDeviceSettingEnumPu">
            <summary>
            获取定值枚举数组
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ProtectionSettingInfo.ProtectionSettingAppService.GetDeviceSettingType">
            <summary>
            获取定值类型
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService">
            <summary>
            转发查询信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetDZInfoAsync(System.String)">
            <summary>
            转发定值行信息
            </summary>
            <param name="dz"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetDZValueByCommentsAsync(YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionSettingDto.SerachDZValue)">
            <summary>
            查询定值,根据定值名称
            </summary>
            <param name="serachDZValue"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetFaultRptInfo(System.String)">
            <summary>
            转发故障报告信息
            </summary>
            <param name="faultRpt"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetFaultRptInfoByday(System.DateTime,System.Int32)">
            <summary>
            获取故障报告聚合报告，按照5分钟时间聚合
            </summary>
            <param name="time"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.UpdateAnalysisResult(System.String,YunDa.SOMS.DataTransferObject.MaintenanceAndOperations.SecondaryEquipment.FaultAnalysisReport)">
            <summary>
            上传诊断结果
            </summary>
            <param name="id"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetWaveDataByAlertIDAsync(YunDa.SOMS.DataTransferObject.MaintenanceAndOperations.SecondaryEquipment.FaultReportInput)">
            <summary>
            获取波形数据   客户端缓存接口
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetWaveCfgFile(YunDa.SOMS.DataTransferObject.MaintenanceAndOperations.SecondaryEquipment.FaultReportInput)">
            <summary>
            获取波形数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetWaveDataFile(YunDa.SOMS.DataTransferObject.MaintenanceAndOperations.SecondaryEquipment.FaultReportInput)">
            <summary>
            获取波形文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.ClearDzCacheAsync">
            <summary>
            清空定值缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.SettingAndFaultRpt.TransformInfomationAppService.GetIsmsProtecvtionVersionInfo(System.String)">
            <summary>
            获取保护装置版本信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.GetTransformerSubstationTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.TransformerSubstationSearchConditionInput)">
            <summary>
            获取变电所结构树
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.GetMonitoringInfo(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.MonitoringInfoSearchConditionInput)">
            <summary>
            获取变电所的监测数据配置项
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.FindTransformerSubstationForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.SelectTransformerSubstationSearchConditionInput)">
            <summary>
            根据条件查询变电所下拉框内容
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.FindTransformerSubstationById(System.Guid)">
            <summary>
            根据id查询变电所
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.FindTransformerSubstationByName(System.String)">
            <summary>
            根据id查询变电所
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.ExportTeleConfigurationExcel(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.TeleConfigurationExportConditionInput)">
            <summary>
            导出遥测、遥信、遥控配置文档
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.ImportTeleConfigurationExcelAsync(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入遥测、遥信、遥控配置文档，并修改数据库对应配置项
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.ExportBaseData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            导出基本数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.GetMasterStationType">
            <summary>
             获取当前主站；调用方向：辅助监控系统巡检服务  ->  辅助监控系统
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.UpdateDataMonitoringAddressById(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.EditDataMonitoringAddressInput)">
            <summary>
            修改104实时数据地址
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.UpdateRobotServerAddressById(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.EditRobotServerAddressInput)">
            <summary>
            修改机器人服务地址
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.ITransformerSubstationAppService.FindMastStationTypeForSelect">
            <summary>
            获取主站类型
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService">
            <summary>
            变电所信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.EditTransformerSubstationInput)">
            <summary>
            变电所增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.DeleteByIdAsync(System.Guid)">
            <summary>
             删除单个变电所
            </summary>
            <param name="id">变电所id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个变电所
            </summary>
            <param name="ids">变电所id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.TransformerSubstationSearchConditionInput})">
            <summary>
            查询变电所信息
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.GetTransformerSubstationTreeNode(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.TransformerSubstationSearchConditionInput)">
            <summary>
            获取变电所普通设备层级结构
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.GetMonitoringInfo(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.MonitoringInfoSearchConditionInput)">
            <summary>
            获取变电所的监测数据配置项
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.FindTransformerSubstationForSelect(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.SelectTransformerSubstationSearchConditionInput)">
            <summary>
            根据条件查询变电所下拉框内容
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.FindMastStationTypeForSelect">
            <summary>
            获取主站类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.FindTransformerSubstationById(System.Guid)">
            <summary>
            根据id查询变电所
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.FindTransformerSubstationByName(System.String)">
            <summary>
            根据名称查询变电所
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.ExportTeleConfigurationExcel(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.TeleConfigurationExportConditionInput)">
            <summary>
            导出遥测、遥信、遥控配置文档
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.ImportTeleConfigurationExcelOldAsync(Microsoft.AspNetCore.Http.IFormCollection,System.Nullable{System.Guid})">
            <summary>
            导入遥测、遥信、遥控配置文档，并修改数据库对应配置项
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.ExportBaseData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            下载基础数据文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteInspectionItemExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            10巡检卡片
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteTeleCommandLinkageExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            9.2动环联动点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteVideoLinkageExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            9.1视频联动点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WritePresetExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            8摄像机预置位点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteTeleDebugExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            7遥调点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteTelemeteringExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            6遥测点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteTeleSignalExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            5遥信点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteTeleCommandExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            4遥控点表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteEquipmentExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            3.2非视频设备配置表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteVideoDevEquipmentSettingExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            3.1视频设备配置表
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteVideoServiceEquipmentSettingExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            2视频服务器配置信息
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.WriteStationExcelData(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.ExportBaseDataConditionInput)">
            <summary>
            被控站基本情况
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.GetMasterStationType">
            <summary>
             获取当前主站；调用方向：辅助监控系统巡检服务  ->  辅助监控系统
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.UpdateDataMonitoringAddressById(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.EditDataMonitoringAddressInput)">
            <summary>
            修改104实时数据地址
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationAppService.UpdateRobotServerAddressById(YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto.EditRobotServerAddressInput)">
            <summary>
            修改机器人服务地址
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetTransformerSubstationHealthStatusAsync(System.Nullable{System.Guid})">
            <summary>
            获取所内评分
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetSecondaryEquipmentHealthStatusAsync(System.Nullable{System.Guid})">
            <summary>
            获取二次设备健康状态
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetPrimaryEquipmentHealthStatusAsync(System.Nullable{System.Guid})">
            <summary>
            获取一次设备健康状态
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetPrimaryEquipmentStatus">
            <summary>
            查询一次设备评分
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetOnlineEquipmentStatusAsync">
            <summary>
            查询在线监测信息诊断
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetSecondaryEquipmentStatus">
            <summary>
            查询二次设备评分
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetAuxiliaryEquipmentStatus">
            <summary>
            查询辅助监控设备评分
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetRobotEquipmentStatus">
            <summary>
            获取机器人在线状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetDefectCategoryCountAsync(System.Guid)">
            <summary>
            获取缺陷统计
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.MeregeStationDataFromISMS">
            <summary>
            从ISMS库中合并变电站数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetAuxiliaryMonitoringAlarmAsync">
            <summary>
            获取辅助监测报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetComprehensiveAlarmAsync">
            <summary>
            获取综自报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.GetFaultReportAsync">
            <summary>
            获取故障报告
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.ConfirmFaultReportAsync(System.String)">
            <summary>
            确认故障报告
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformerSubstationExAppService.ConfirmAlarmAsync(System.String)">
            <summary>
            确认报警
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.GeneralInformation.TransformSubstationExportDataAppService.ExportEquipmentEntityPDF(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto.EquipmentTypesForChecksProperty})">
            <summary>
            变电所增加或修改
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MaintenanceAndOperations.MaintenanceSystem.MaintenanceSystemInfoAppService.GetVersionInfo(System.Guid)">
            <summary>
            获取装置版本信息
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.MaintenanceAndOperations.SecondaryEquipment.SecondaryEquipmentVersionManagementAppService">
            <summary>
            二次设备信息管理
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MaintenanceAndOperations.SecondaryEquipment.SecondaryEquipmentVersionManagementAppService.GetVersionInfo(System.Guid)">
            <summary>
            获取装置版本信息
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MaintenanceAndOperations.SecondaryEquipment.SecondaryEquipmentVersionManagementAppService.GetVersionInfoHistory(System.Guid)">
            <summary>
            获取装置版本历史记录
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.MobileSurveillance.IRobotDeviceInfoAppService">
            <summary>
            机器人点位管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotDeviceInfoAppService.RobotDeviceInfoSyncByRobotDevId(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotDeviceInfoDto.EditRobotDeviceInfoByRobotInfoIdInput)">
            <summary>
            根据机器人id同步设备点位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotDeviceInfoAppService.GetRobotDeviceInfoTreeNode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotDeviceInfoDto.RobotDeviceInfoSearchConditionInput)">
            <summary>
            获取机器人设备点位结构树
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService">
            <summary>
            机器人设备点位管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService.RobotDeviceInfoSyncByRobotDevId(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotDeviceInfoDto.EditRobotDeviceInfoByRobotInfoIdInput)">
            <summary>
            根据机器人Id同步多个机器人设备点位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotDeviceInfoDto.EditRobotDeviceInfoInput)">
            <summary>
            机器人设备点位信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个机器人设备点位信息
            </summary>
            <param name="id">设备点位信息id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个机器人设备点位信息
            </summary>
            <param name="ids">设备点位信息id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotDeviceInfoDto.RobotDeviceInfoSearchConditionInput})">
            <summary>
            机器人预置点信息查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotDeviceInfoAppService.GetRobotDeviceInfoTreeNode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotDeviceInfoDto.RobotDeviceInfoSearchConditionInput)">
            <summary>
            获取机器人设备点位结构树
            </summary>
            <param name="searchCondition"></param>
        </member>
        <member name="T:YunDa.ISAS.Application.MobileSurveillance.IRobotInfoAppService">
            <summary>
            机器人管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotInfoAppService.FindRobotTypeForSelect">
            <summary>
            获取机器人类型下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotInfoAppService.GetRobotTreeNode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotInfoDto.RobotInfoSearchConditionInput)">
            <summary>
            获取机器人结构树
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotInfoAppService.GetCameraByRobotInfoId(System.Guid)">
            <summary>
            根据机器人Id获取摄像头信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotInfoAppService.GetRobotFTPDirectory">
            <summary>
            获取机器人巡检报表保存目录
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotInfoAppService.FindData(System.Guid)">
            <summary>
            机器人信息查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService">
            <summary>
            机器人管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotInfoDto.EditRobotInfoInput)">
            <summary>
            机器人信息增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.TestIsExistSameRobotCode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotInfoDto.EditRobotInfoInput)">
            <summary>
            检测是否存在相同名称的机器人
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个机器人信息
            </summary>
            <param name="id">机器人id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个机器人信息
            </summary>
            <param name="ids">机器人id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotInfoDto.RobotInfoSearchConditionInput})">
            <summary>
            机器人信息查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.FindData(System.Guid)">
            <summary>
            机器人信息查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.FindRobotTypeForSelect">
            <summary>
            获取机器人类型下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.GetRobotTreeNode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotInfoDto.RobotInfoSearchConditionInput)">
            <summary>
            获取机器人结构树
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.GetCameraByRobotInfoId(System.Guid)">
            <summary>
            根据机器人Id获取摄像头信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotInfoAppService.GetRobotFTPDirectory">
            <summary>
            获取机器人巡检报表FTP保存目录
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.MobileSurveillance.IRobotTaskAppService">
            <summary>
            机器人任务管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotTaskAppService.GetRobotTaskTreeNode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskDto.RobotTaskSearchConditionInput)">
            <summary>
            获取机器人任务结构树
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotTaskAppService.FindRobotTaskTypeForSelect">
            <summary>
            获取机器人任务类型下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotTaskAppService.FindRobotTaskPriorityForSelect">
            <summary>
            获取机器人任务优先权类型下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotTaskAppService.FindT5RobotData(System.String,System.String)">
            <summary>
            天创机器人查询任务接口
            </summary>
            <param name="tempId"></param>
            <param name="taskId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.IRobotTaskAppService.UpdateRobotTaskTempIdTaskIdAsync(System.Nullable{System.Guid},System.String,System.String,System.String)">
            <summary>
            天创机器人修改任务模板id和任务计划id
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService">
            <summary>
            机器人任务管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskDto.EditRobotTaskInput)">
            <summary>
            机器人任务增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.CreateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskDto.EditRobotTaskInput)">
            <summary>
            创建任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.UpdateRobotTaskTempIdTaskIdAsync(System.Nullable{System.Guid},System.String,System.String,System.String)">
            <summary>
            天创机器人修改任务模板id和任务计划id
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个机器人任务
            </summary>
            <param name="id">机器人任务id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个机器人任务
            </summary>
            <param name="ids">机器人任务id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskDto.RobotTaskSearchConditionInput})">
            <summary>
            机器人任务查询
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.GetRobotTaskTreeNode(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskDto.RobotTaskSearchConditionInput)">
            <summary>
            获取机器人任务结构树
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.FindRobotTaskTypeForSelect">
            <summary>
            获取机器人任务类型下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.FindRobotTaskPriorityForSelect">
            <summary>
            获取机器人任务优先权类型下拉框数据
            </summary>
            <returns>返回下拉框数据</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.MobileSurveillance.RobotTaskAppService.FindT5RobotData(System.String,System.String)">
            <summary>
            天创机器人查询任务接口
            </summary>
            <param name="tempId"></param>
            <param name="taskId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.AppUrlConfigurationService.DatabaseConfigurationProviderAppService.CreateOrUpdateAsync(YunDa.SOMS.Entities.System.AppUrlConfiguration)">
            <summary>
            添加或更新URL配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.AppUrlConfigurationService.DatabaseConfigurationProviderAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个URL配置
            </summary>
            <param name="id">配置ID</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.AppUrlConfigurationService.DatabaseConfigurationProviderAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个URL配置
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.AppUrlConfigurationService.DatabaseConfigurationProviderAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.SOMS.Entities.System.AppUrlConfiguration})">
            <summary>
            查询URL配置信息
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.AppUrlConfigurationService.DatabaseConfigurationProviderAppService.FindAllDatas">
            <summary>
            获取所有URL配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.AppUrlConfigurationService.DatabaseConfigurationProviderAppService.GenerateDescriptionAsync(System.String,System.String)">
            <summary>
            使用DeepSeek生成配置描述
            </summary>
            <param name="name">配置名称</param>
            <param name="value">配置值</param>
            <returns>包含生成结果的请求响应</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.ConfigurationAppService">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.GetValueByKeys(System.String[])">
            <summary>
            根据key获取配置文件中的信息 嵌套key须依次排列
            </summary>
            <param name="keys"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.GetValueByKey(System.String)">
            <summary>
            根据key获取配置文件中的信息
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.GetSysConfongiguration(System.String,System.Guid)">
            <summary>
            获取系统配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.GetSysConfongigurations(System.String,System.Guid,System.Nullable{YunDa.ISAS.Entities.System.SysConfigurationTypeEnum},System.String)">
            <summary>
            获取系统配置列表
            </summary>
            <param name="name"></param>
            <param name="stationId"></param>
            <param name="type"></param>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.SetSysConfongiguration(YunDa.ISAS.DataTransferObject.System.FunctionDto.EditSysConfigurationInput)">
            <summary>
            提交系统配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.UpdateSysConfongigurationContentAsync(Microsoft.AspNetCore.Http.IFormFile,System.Guid)">
            <summary>
            使用文件修改配置内容
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除配置
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.FindForSelect(YunDa.ISAS.DataTransferObject.System.FunctionDto.ConfigurationSearchConditionInput)">
            <summary>
            获取可以选择的设备下拉列表
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.GetPanoramaConfig(System.String)">
            <summary>
            获取场站全景配置
            </summary>
            <param name="stationName"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.ConfigurationAppService.SavePanoramaConfig(YunDa.SOMS.DataTransferObject.System.ConfigurationDto.HotSpot,System.String,System.String)">
            <summary>
            保存场站全景配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.IConfigurationAppService.GetSysConfongigurations(System.String,System.Guid,System.Nullable{YunDa.ISAS.Entities.System.SysConfigurationTypeEnum},System.String)">
            <summary>
            获取配置信息
            </summary>
            <param name="name"></param>
            <param name="stationId"></param>
            <param name="type"></param>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.FunctionAppService">
            <summary>
            系统功能
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.System.FunctionDto.EditFunctionInput)">
            <summary>
            系统功能信息添加或删除
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除系统功能单个信息
            </summary>
            <param name="id">功能id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除系统功能多个信息
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.System.FunctionDto.FunctionSearchConditionInput})">
            <summary>
            查询系统功能信息
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.FindDatasForFunctionList">
            <summary>
            查询所有功能供
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.FindFunctionTypeForTree">
            <summary>
            获取系统功能树形结构数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.GetListTreeModelOutput">
            <summary>
            获取树形结构
            </summary>
            <returns>客户端和浏览器端的树形结构</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.GetFunctionTypesValue">
            <summary>
            获取系统功能类型初始数据
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.FunctionAppService.FindTypesForSelect">
            <summary>
            获取系统功能数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.IFunctionAppService.FindFunctionTypeForTree">
            <summary>
            获取可执行的巡视动作下拉框数据
            </summary>
            <returns>返回满足条件的巡视动作</returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.IFunctionAppService.FindTypesForSelect">
            <summary>
            获取供选择的所有功能类别
            </summary>
            <returns>返回所有功能类别</returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.IRoleFunctionAppService">
            <summary>
            角色功能关联服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.IRoleFunctionAppService.UpdateAll(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.System.RoleFunctionDto.EditRoleFunctionInput})">
            <summary>
            角色功能全部更新
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.RoleFunctionAppService">
            <summary>
            角色功能功能关联服务
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.System.RoleFunctionAppService.CurrentUser">
            <summary>
            当前登录用户
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.UpdateAll(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.System.RoleFunctionDto.EditRoleFunctionInput})">
            <summary>
            角色功能全部更新
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.System.RoleFunctionDto.EditRoleFunctionInput)">
            <summary>
            角色功能添加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.System.RoleFunctionDto.EditRoleFunctionInput)">
            <summary>
            修改角色功能
            </summary>
            <param name="input">角色功能实体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.CreateAsync(YunDa.ISAS.DataTransferObject.System.RoleFunctionDto.EditRoleFunctionInput)">
            <summary>
            新增角色功能
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个角色功能
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个角色功能
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleFunctionAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.System.RoleFunctionDto.RoleFunctionSearchConditionInput})">
            <summary>
            角色功能功能查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.IRoleUserAppService">
            <summary>
            角色用户关联服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.System.RoleUserAppService">
            <summary>
            角色用户关联服务
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.System.RoleUserAppService.CurrentUser">
            <summary>
            当前登录用户
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.System.RoleUserDto.EditRoleUserInput)">
            <summary>
            角色用户添加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.System.RoleUserDto.EditRoleUserInput})">
            <summary>
            多个角色用户添加
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.System.RoleUserDto.EditRoleUserInput)">
            <summary>
            修改角色
            </summary>
            <param name="input">角色实体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.CreateAsync(YunDa.ISAS.DataTransferObject.System.RoleUserDto.EditRoleUserInput)">
            <summary>
            新增角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个角色
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个角色
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleUserAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.System.RoleUserDto.RoleUserSearchConditionInput})">
            <summary>
            角色查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.IRoleAppService">
            <summary>
            角色管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.System.RoleAppService">
            <summary>
            角色管理服务
            </summary>
        </member>
        <member name="P:YunDa.ISAS.Application.System.RoleAppService.CurrentUser">
            <summary>
            当前登录用户
            </summary>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.System.RoleDto.EditRoleInput)">
            <summary>
            角色添加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.System.RoleDto.EditRoleInput)">
            <summary>
            修改角色
            </summary>
            <param name="input">角色实体</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleAppService.CreateAsync(YunDa.ISAS.DataTransferObject.System.RoleDto.EditRoleInput)">
            <summary>
            新增角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个角色
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个角色
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.Application.System.RoleAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.System.RoleDto.SearchCondition.RoleSearchConditionInput})">
            <summary>
            角色查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.Application.System.IUserAppService">
            <summary>
            系统用户管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.Application.System.UserAppService">
            <summary>
            系统用户管理服务
            </summary>
        </member>
    </members>
</doc>
