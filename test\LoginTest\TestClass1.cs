﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LoginTest
{
    internal class TestClass1
    {
    }
    public class T5TaskRecvBase<T> where T : class
    {
        public string DataType { get; set; }
        public T Data { get; set; }
    }
    public class TaskStatusData
    {
        /// <summary>
        /// 执行记录 id
        /// </summary>
        public int ExecId { get; set; }
        /// <summary>
        /// 机器人 id（V2.2.4版本新增）
        /// </summary>
        public int RobotId { get; set; }
        /// <summary>
        /// 任务 id
        /// </summary>
        public int TaskId { get; set; }
        /// <summary>
        /// 任务进度状态（3-正在执行 4-中途暂停 5-执行完成 6-中途终止 7-识别中 8-任务超时终止）
        /// </summary>
        public int ProgressStatus { get; set; }
        /// <summary>
        /// 巡检点id
        /// </summary>
        public int ViewPointId { get; set; }
        /// <summary>
        /// 设备 id
        /// </summary>
        public int DeviceId { get; set; }
        /// <summary>
        /// 模板id
        /// </summary>
        public int TemplatedId { get; set; }
        public string RobotSn { get; set; } = "第三方id";
        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }
        /// <summary>
        /// 任务类型
        /// </summary>
        public string TaskType { get; set; }
        /// <summary>
        /// 任务开始时间（2022-12-30 11:21:00）
        /// </summary>
        public DateTime? BeginTime { get; set; }
        /// <summary>
        /// 任务剩余时间（秒）
        /// </summary>
        public int LeftTime { get; set; }
        /// <summary>
        /// 任务进度（%）
        /// </summary>
        public int TaskTotalProgress { get; set; }
    }
}
