﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbPlanTask
    {
        public TbPlanTask()
        {
            TbPlanTaskItems = new HashSet<TbPlanTaskItem>();
        }

        public string BusiObjId { get; set; } = null!;
        public string BusiObjName { get; set; } = null!;
        public string BusiObjType { get; set; } = null!;
        public string State { get; set; } = null!;
        public DateTime CreateTime { get; set; }

        public virtual ICollection<TbPlanTaskItem> TbPlanTaskItems { get; set; }
    }
}
