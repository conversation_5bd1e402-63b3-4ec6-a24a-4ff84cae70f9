﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImCurveItem
    {
        public string CurveId { get; set; } = null!;
        public string DataId { get; set; } = null!;
        public int? LineColor { get; set; }
        public int? LineWidth { get; set; }

        public virtual ImCurve Curve { get; set; } = null!;
        public virtual ImDeviceDatum Data { get; set; } = null!;
    }
}
