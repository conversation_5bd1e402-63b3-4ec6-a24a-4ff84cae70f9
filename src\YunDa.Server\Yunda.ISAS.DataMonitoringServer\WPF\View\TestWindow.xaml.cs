﻿using Abp.Dependency;
using FluentFTP;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Yunda.SOMS.DataMonitoringServer.FTPHandle;
using Yunda.SOMS.DataMonitoringServer.ProtectionDeviceHandle;
using Yunda.SOMS.DataMonitoringServer.SQLiteData;
using Yunda.SOMS.DataMonitoringServer.TcpSocket.Server;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.DeviceTcpData;
using Button = System.Windows.Controls.Button;
using ComboBox = System.Windows.Controls.ComboBox;
using MessageBox = System.Windows.MessageBox;
using Orientation = System.Windows.Controls.Orientation;
using Path = System.IO.Path;
using TextBox = System.Windows.Controls.TextBox;

namespace Yunda.SOMS.DataMonitoringServer.WPF.View
{
    /// <summary>
    /// TestWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TestWindow : Window, ISingletonDependency
    {
        FtpFile _ftpFile;
        private readonly Dictionary<byte, ClientControls> _clientControls = new Dictionary<byte, ClientControls>();
        DotNettyTcpServer _dotNettyTcpServer;
        ProtectionDeviceDataCenter _protectionDeviceDataCenter;
        ProtectionDeviceIOInfoHandle _protectionDeviceIOInfoHandle;
        public TestWindow(FtpFile ftpFile, DotNettyTcpServer dotNettyTcpServer
            , ProtectionDeviceDataCenter protectionDeviceDataCenter, ProtectionDeviceIOInfoHandle protectionDeviceIOInfoHandle)
        {
            _ftpFile = ftpFile;
            _dotNettyTcpServer = dotNettyTcpServer;
            _dotNettyTcpServer.MessageReceived += OnMessageReceived; // 订阅事件
            _protectionDeviceDataCenter = protectionDeviceDataCenter;
            InitializeComponent();
            _protectionDeviceIOInfoHandle = protectionDeviceIOInfoHandle;
            this.Closing += (s, e) =>
            {
                e.Cancel = true; // 取消关闭
                this.Hide();     // 隐藏窗口}
            };
        }
        private void OnMessageReceived( byte address, byte[] message, byte description)
        {
            Dispatcher.Invoke(() =>
            {
                if (!_clientControls.ContainsKey(address))
                {
                    AddClientControls(address);
                }
                // 将数据转换为字符串以便传递给订阅者
                string messageStr = BitConverter.ToString(message);
                // 更新对应客户端的消息显示
                _clientControls[address].ReceivedMessageBox.Text +=
                    $"装置地址: {address}, 功能码: {description}, 消息: {messageStr}\n";
            });
        }
        private void AddClientControls(byte addr)
        {
            // 创建控件组
            var clientPanel = new StackPanel { Orientation = Orientation.Vertical, Margin = new Thickness(5) };
            var header = new TextBlock { Text = $"客户端 地址: {addr}", FontWeight = FontWeights.Bold };

            // 消息接收框
            var receivedMessageBox = new TextBox
            {
                Height = 100,
                TextWrapping = TextWrapping.Wrap,
                IsReadOnly = true,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto
            };

            // Address输入框
            var addressBox = new TextBox { Height = 30, Margin = new Thickness(0, 5, 0, 5) };//请输入Address

            // ControlWord输入框
            var controlWordBox = new TextBox { Height = 30, Margin = new Thickness(0, 5, 0, 5) };//请输入ControlWord

            // 功能码选择框
            var functionCodeBox = new ComboBox { Height = 30, Margin = new Thickness(0, 5, 0, 5) };
            functionCodeBox.ItemsSource = new List<ComboBoxItem>
            {
                new ComboBoxItem { Content = "0 - 确认报文", Tag = (byte)0 },
                new ComboBoxItem { Content = "1 - 装置用户定值信息报文", Tag = (byte)1 },
                new ComboBoxItem { Content = "2 - 装置厂家定值信息报文", Tag = (byte)2 },
                new ComboBoxItem { Content = "3 - 装置版本信息报文", Tag = (byte)3 },
                new ComboBoxItem { Content = "4 - 装置自检信息报文", Tag = (byte)4 },
                new ComboBoxItem { Content = "5 - 装置开入开出数据信息报文", Tag = (byte)5 },
                new ComboBoxItem { Content = "6 - 装置板卡通信状态信息报文", Tag = (byte)6 },
                new ComboBoxItem { Content = "7 - 装置变位上送B码对时状态", Tag = (byte)7 }
            };

            // 发送消息输入框
            var sendMessageBox = new TextBox { Height = 30, Margin = new Thickness(0, 5, 0, 5) };//发送消息

            // 发送按钮
            var sendButton = new Button
            {
                Content = "发送",
                Width = 50,
                Margin = new Thickness(0, 0, 0, 10)
            };
            sendButton.Click += (s, e) => SendMessage(addressBox.Text, controlWordBox.Text, functionCodeBox, sendMessageBox.Text);

            // 将控件添加到 panel 中
            clientPanel.Children.Add(header);
            clientPanel.Children.Add(receivedMessageBox);
            clientPanel.Children.Add(new TextBlock { Text = "装置地址:" });
            clientPanel.Children.Add(addressBox);
            clientPanel.Children.Add(new TextBlock { Text = "控制字:" });
            clientPanel.Children.Add(controlWordBox);
            clientPanel.Children.Add(new TextBlock { Text = "功能码:" });
            clientPanel.Children.Add(functionCodeBox);
            clientPanel.Children.Add(new TextBlock { Text = "发送消息:" });
            clientPanel.Children.Add(sendMessageBox);
            clientPanel.Children.Add(sendButton);

            // 将 panel 添加到主容器
            ClientPanel.Children.Add(clientPanel);

            // 将控件组存储到字典中
            _clientControls[addr] = new ClientControls
            {
                ReceivedMessageBox = receivedMessageBox,
                AddressBox = addressBox,
                ControlWordBox = controlWordBox,
                FunctionCodeBox = functionCodeBox,
                SendMessageBox = sendMessageBox
            };
        }

        private void SendMessage(string addressText, string controlWordText, ComboBox functionCodeBox, string message)
        {
            if (!byte.TryParse(addressText, out byte address))
            {
                MessageBox.Show("请输入有效的 Address");
                return;
            }

            if (!byte.TryParse(controlWordText, out byte controlWord))
            {
                MessageBox.Show("请输入有效的 ControlWord");
                return;
            }

            if (functionCodeBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is byte functionCode)
            {
                if (string.IsNullOrEmpty(message)) message = "0";
                _dotNettyTcpServer.SendMessageByIp(address, controlWord, functionCode, byte.Parse(message));
                _clientControls[address].SendMessageBox.Clear();
            }
            else
            {
                MessageBox.Show("请选择功能码");
            }
        }
        private void Button_Click(object sender, RoutedEventArgs e)
        {
           
        
        }
        private void Button_Click_GetIOInfo(object sender, RoutedEventArgs e)
        {
            // 打开文件选择对话框
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "SQL3 files (*.sql3)|*.sql3"; // 过滤器，确保只选择 .sql3 文件
            openFileDialog.Multiselect = false; // 只允许选择一个文件

            if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK) // 如果用户选定了文件
            {
                string selectedFilePath = openFileDialog.FileName;
                fileName.Text = selectedFilePath; // 在 fileName TextBox 中显示选中的文件路径
                // 显示选中的文件名
                info.Text = Path.GetFileName(selectedFilePath);
                IODataHandle commDataHandle = new IODataHandle();
                var data = commDataHandle.GetIODataDic(selectedFilePath);
                //DisplayData(dictionary:data.Item2);
            }
            else
            {
                info.Text = "未选择文件";
            }
           
        }

        private void Button_Click_GetNetworkInfo(object sender, RoutedEventArgs e)
        {
            // 打开文件选择对话框
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.Filter = "SQL3 files (*.sql3)|*.sql3"; // 过滤器，确保只选择 .sql3 文件
            openFileDialog.Multiselect = false; // 只允许选择一个文件

            if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK) // 如果用户选定了文件
            {
                string selectedFilePath = openFileDialog.FileName;
                fileName.Text = selectedFilePath; // 在 fileName TextBox 中显示选中的文件路径
                // 显示选中的文件名
                info.Text = Path.GetFileName(selectedFilePath);
                CommDataHandle commDataHandle = new CommDataHandle();
                var data = commDataHandle.GetCommunicationParameters(selectedFilePath);
                DisplayData(data);

            }
            else
            {
                info.Text = "未选择文件";
            }
        }
        private void DisplayData(CommunicationParameters parameters=null, Dictionary<string, string> dictionary = null)
        {
            var displayList = new List<KeyValuePair<string, string>>();

            // 将 CommunicationParameters 数据转换为键值对
            if (parameters != null)
            {
                displayList.Add(new KeyValuePair<string, string>("网口1-IP", parameters.IpPort1));
                displayList.Add(new KeyValuePair<string, string>("网口1-掩码", parameters.MaskPort1));
                displayList.Add(new KeyValuePair<string, string>("网口2-IP", parameters.IpPort2));
                displayList.Add(new KeyValuePair<string, string>("网口2-掩码", parameters.MaskPort2));
                displayList.Add(new KeyValuePair<string, string>("网口3-IP", parameters.IpPort3));
                displayList.Add(new KeyValuePair<string, string>("网口3-掩码", parameters.MaskPort3));
                displayList.Add(new KeyValuePair<string, string>("通信网关", parameters.Gateway));
                displayList.Add(new KeyValuePair<string, string>("SNTP服务器1IP", parameters.SntpServer1Ip));
                displayList.Add(new KeyValuePair<string, string>("SNTP服务器2IP", parameters.SntpServer2Ip));
            }

            // 将 Dictionary<string, string> 数据添加到显示列表
            if (dictionary != null)
            {
                foreach (var kvp in dictionary)
                {
                    displayList.Add(new KeyValuePair<string, string>(kvp.Key, kvp.Value));
                }
            }

            // 将数据绑定到 ListView
            DataListView.ItemsSource = displayList;
        }

        private void Button_Click_GetFTPIOInfo(object sender, RoutedEventArgs e)
        {
            string ftpBaseUrl = "ftp://*************";  // FTP 服务器基础路径
            string ftpUsername = "root"; // FTP 用户名
            string ftpPassword = "root"; // FTP 密码
            string filepath = "/nor/root/commcpu/cfg/";
            // 调用函数并传入文件名
            string fileName = "iodb_tp1.sql3"; // 传入的文件名
            string localFile = _ftpFile. GetFileFromFtp(ftpBaseUrl, ftpUsername, ftpPassword, "1000",filepath, fileName);
            IODataHandle commDataHandle = new IODataHandle();
            var data = commDataHandle.GetIODataDic(localFile);
            //DisplayData(dictionary: data.Item2  );
        }

        private void Button_Click_GetFTPNetworkInfo(object sender, RoutedEventArgs e)
        {
            string ftpBaseUrl = "ftp://*************";  // FTP 服务器基础路径
            string ftpUsername = "root"; // FTP 用户名
            string ftpPassword = "root"; // FTP 密码
            string filepath = "/nor/root/commcpu/cfg/";
            // 调用函数并传入文件名
            string fileName = "commdb.sql3"; // 传入的文件名
            string localFile = _ftpFile.GetFileFromFtp(ftpBaseUrl, ftpUsername, ftpPassword, filepath, fileName);
            CommDataHandle commDataHandle = new CommDataHandle();
            var data = commDataHandle.GetCommunicationParameters(localFile);
            DisplayData(data);
        }
       

        private void Button_Click_SendTcpMessage(object sender, RoutedEventArgs e)
        {
            //if (FunctionCodeComboBox.SelectedItem is ComboBoxItem selectedItem && byte.TryParse(selectedItem.Tag.ToString(), out byte functionCode))
            //{
            //    string message = SendMessageBox.Text;
            //    byte address = 0x01;
            //    byte controlWord = 0x00;

            //    // 转换消息内容为字节数组
            //    byte[] data = Encoding.ASCII.GetBytes(message);

            //    // 调用发送方法
            //    _dotNettyTcpServer.SendCustomMessage(ip, address, controlWord, functionCode, data);
            //    MessageBox.Show("消息已发送", "发送成功", MessageBoxButton.OK, MessageBoxImage.Information);
            //}
            //else
            //{
            //    MessageBox.Show("请选择功能码", "发送失败", MessageBoxButton.OK, MessageBoxImage.Warning);
            //}
        }

        private void Click_GetProtectionCommInfos(object sender, RoutedEventArgs e)
        {
            //_protectionDeviceDataCenter.InitProtectionDeviceComms();
        }

        private void Click_GetGetIOInfo(object sender, RoutedEventArgs e)
        {
            //_protectionDeviceIOInfoHandle.GetIOInfo();
        }
    }

    // 用于存储每个客户端的控件组
    public class ClientControls
    {
        public TextBox ReceivedMessageBox { get; set; }
        public TextBox AddressBox { get; set; }
        public TextBox ControlWordBox { get; set; }
        public ComboBox FunctionCodeBox { get; set; }
        public TextBox SendMessageBox { get; set; }
    }
}
