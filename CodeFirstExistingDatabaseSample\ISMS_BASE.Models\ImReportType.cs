﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImReportType
    {
        public ImReportType()
        {
            ImReportCfgs = new HashSet<ImReportCfg>();
        }

        public string RptTypeCode { get; set; } = null!;
        public string RptTypeName { get; set; } = null!;

        public virtual ICollection<ImReportCfg> ImReportCfgs { get; set; }
    }
}
