﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleAppSqlite.Models
{
    public partial class Caniopara
    {
        public long Id { get; set; }
        public long? ParaIndex { get; set; }
        public string ParaGroup { get; set; }
        public string ParaName { get; set; }
        public string ParaValue { get; set; }
        public long? ValMin { get; set; }
        public long? ValMax { get; set; }
        public string Unit { get; set; }
        public long? ValType { get; set; }
        public long? IsEnum { get; set; }
        public long? StepL2 { get; set; }
        public long? StepL1 { get; set; }
    }
}
