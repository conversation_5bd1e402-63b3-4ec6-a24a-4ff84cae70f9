﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImLinkage
    {
        public string Lnkid { get; set; }
        public string <PERSON><PERSON>bjid { get; set; }
        public string Srcobjtype { get; set; }
        public string Lnktype { get; set; }
        public string Resource { get; set; }
        public string Action { get; set; }
        public string Actparam { get; set; }
        public string State { get; set; }
    }
}
