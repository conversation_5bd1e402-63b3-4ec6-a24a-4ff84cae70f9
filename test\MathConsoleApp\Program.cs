﻿using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace MathConsoleApp
{
    internal class Program
    {
        private static void Main(string[] args)
        {
            Console.WriteLine((string)null);
            //for (int i = 0; i < 10; i++)
            //{
            //    Task.Run(()=> {
            //        while (true)
            //        {
            //            Thread.Sleep(200);
            //            Test_ScriptEngine();

            //        }
            //    });
            //}
            //SearchInsert(new int[] { 1, 3,5,6 }, 7);
            Console.ReadLine();
            //int x = 141;
            //byte v = (byte)x;
            //Console.WriteLine(v);
            //Console.WriteLine("Test0: {0}", MyEvaluator.EvaluateToInteger("(30 + 4) * 2"));
            //Console.WriteLine("Test1: {0}", MyEvaluator.EvaluateToString("\"Hello \" + \"There \""));
            //Console.WriteLine("Test2: {0}", MyEvaluator.EvaluateToBool("30 == 40"));
            //Console.WriteLine("Test3: {0}", MyEvaluator.EvaluateToObject("new DataSet()"));
            //Console.WriteLine("Test4: {0}", MyEvaluator.EvaluateToDouble("(12.4****)*0.4"));
            //Console.WriteLine("Test5: {0}", MyEvaluator.EvaluateToLong("(12+1000)*1000"));
            //Console.WriteLine("Test6: {0}", MyEvaluator.EvaluateToString("\"double max ==\"+double.MaxValue"));//decimal.MaxValue+/",/"+

            //EvaluatorItem[] items = {
            //                        new EvaluatorItem(typeof(int), "(30 + 4) * 2", "GetNumber"),
            //                        new EvaluatorItem(typeof(string), "\"Hello \" + \"There\"", "GetString"),
            //                        new EvaluatorItem(typeof(bool), "30 == 40", "GetBool"),
            //                        new EvaluatorItem(typeof(object), "new DataSet()", "GetDataSet")
            //                        };

            //MyEvaluator eval = new MyEvaluator(items);
            //Console.WriteLine("TestStatic0: {0}", eval.EvaluateInt("GetNumber"));
            //Console.WriteLine("TestStatic1: {0}", eval.EvaluateString("GetString"));
            //Console.WriteLine("TestStatic2: {0}", eval.EvaluateBool("GetBool"));
            //Console.WriteLine("TestStatic3: {0}", eval.Evaluate("GetDataSet"));
            //Console.ReadLine();
        }
        public async static void Test_2()
        {
            try
            {
                //DateTime startTime = new DateTime();
                string str = string.Format("((30 + 4) * 2{0}2)||{1}", new string[] { "<", true.ToString().ToLower() });
                CancellationTokenSource cts = new CancellationTokenSource();
                var script = CSharpScript.Create(code: str);
                ScriptState<object> obj = await script.RunAsync(cancellationToken: cts.Token);
                bool isLinkage = false;
                bool.TryParse(obj.ReturnValue.ToString(), out isLinkage);
                Console.WriteLine("TestStatic2: {0}", isLinkage);
                cts.Cancel();
                cts.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine("报错", ex.ToString());
            }

        }
        public static bool IsValid(string s)
        {
            var char1 = '(';
            var char2 = ')';
            var char3 = '[';
            var char4 = ']';
            var char5 = '{';
            var char6 = '}';

            var arr = s.ToArray();
            for (int i = 0; i < arr.Length; i++)
            {
                if (arr[i] == char1)
                {
                    var flag = false;
                    for (int j = i + 1; j < arr.Length; j++)
                    {
                        if (arr[j] == char2)
                        {

                        }
                    }
                }

            }
            return true;
        }
        public static int SearchInsert(int[] nums, int target)
        {
            //for (int i = 0; i < nums.Length; i++)
            //{

            //}
            for (int i = 0; i < nums.Length; i++)
            {
                if (nums[0] > target)
                {
                    return 0;
                }
                else if (nums[nums.Length - 1] < target)
                {
                    return nums.Length;
                }
                else if (nums[i] == target)
                {
                    return i;
                }
                else if (i >= 1 && nums[i] > target && nums[i - 1] < target)
                {
                    return i;
                }



            }
            return 0;
        }
        public static void Test()
        {
            try
            {
                //DateTime startTime = new DateTime();
                string str = string.Format("((30 + 4) * 2{0}2)||{1}", new string[] { "<", true.ToString().ToLower() }.ToList().ToArray());
                // str = "if(1==2||true){return 1+1;}else{return 2+1;}";
                Task<object> task = CSharpScript.EvaluateAsync<object>(str);
                object obj = task.GetAwaiter().GetResult();
                bool isLinkage = false;
                //bool.TryParse(obj.ToString(), out isLinkage);
                Console.WriteLine("TestStatic1.2: {0}", isLinkage);
                task.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine("报错", ex.ToString());
            }

        }

        public static void Test_ScriptEngine()
        {
            try
            {
                using (var engine = new JavaScriptEngineSwitcher.ChakraCore.ChakraCoreJsEngine())
                {
                    //DateTime startTime = new DateTime();
                    string str = string.Format("((30 + 4) * 2{0}2.0089567)||{1}", new string[] { "<", true.ToString().ToLower() }.ToList().ToArray());
                    // str = "if(1==2||true){return 1+1;}else{return 2+1;}";
                    object obj = engine.Evaluate(str);
                    bool isLinkage = false;
                    bool.TryParse(obj.ToString(), out isLinkage);
                    Console.WriteLine("TestStatic1.4: {0}", isLinkage);
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine("报错", ex.ToString());
            }

        }
    }
}