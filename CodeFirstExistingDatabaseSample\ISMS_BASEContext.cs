﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using CodeFirstExistingDatabaseSample.ISMS_BASE.Models;
using Abp.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE
{
    public partial class ISMS_BASEContext : AbpDbContext
    {
        public ISMS_BASEContext(DbContextOptions<ISMS_BASEContext> options) : base(options)
        {
        }
        #region 实体类

        public virtual DbSet<IaFuncNameConst> IaFuncNameConsts { get; set; } = null!;
        public virtual DbSet<IaMaChannel> IaMaChannels { get; set; } = null!;
        public virtual DbSet<IaModule> IaModules { get; set; } = null!;
        public virtual DbSet<IaModuleDir> IaModuleDirs { get; set; } = null!;
        public virtual DbSet<IaMonitorArea> IaMonitorAreas { get; set; } = null!;
        public virtual DbSet<IaStatAccCtler> IaStatAccCtlers { get; set; } = null!;
        public virtual DbSet<IaStatAidVideoDev> IaStatAidVideoDevs { get; set; } = null!;
        public virtual DbSet<IaVAllChannel> IaVAllChannels { get; set; } = null!;
        public virtual DbSet<IaVAllFunc4Module> IaVAllFunc4Modules { get; set; } = null!;
        public virtual DbSet<IaVMaChannel> IaVMaChannels { get; set; } = null!;
        public virtual DbSet<IaVModule> IaVModules { get; set; } = null!;
        public virtual DbSet<IaVStatAidVideoDev> IaVStatAidVideoDevs { get; set; } = null!;
        public virtual DbSet<IaVVideoPatrolItemExecObj> IaVVideoPatrolItemExecObjs { get; set; } = null!;
        public virtual DbSet<IaVideoPatrol> IaVideoPatrols { get; set; } = null!;
        public virtual DbSet<IaVideoPatrolHi> IaVideoPatrolHis { get; set; } = null!;
        public virtual DbSet<IaVideoPatrolItem> IaVideoPatrolItems { get; set; } = null!;
        public virtual DbSet<IaVideoPatrolItemHi> IaVideoPatrolItemHis { get; set; } = null!;
        public virtual DbSet<Im3posSwitch> Im3posSwitches { get; set; } = null!;
        public virtual DbSet<ImAlert201310> ImAlert201310s { get; set; } = null!;
        public virtual DbSet<ImAlertLevel> ImAlertLevels { get; set; } = null!;
        public virtual DbSet<ImAlertState> ImAlertStates { get; set; } = null!;
        public virtual DbSet<ImAlertType> ImAlertTypes { get; set; } = null!;
        public virtual DbSet<ImAnalogData2010> ImAnalogData2010s { get; set; } = null!;
        public virtual DbSet<ImAnalogData20102> ImAnalogData20102s { get; set; } = null!;
        public virtual DbSet<ImAsdu140FltActType> ImAsdu140FltActTypes { get; set; } = null!;
        public virtual DbSet<ImAsdu140FltCurrInfo> ImAsdu140FltCurrInfos { get; set; } = null!;
        public virtual DbSet<ImAsdu142info> ImAsdu142infos { get; set; } = null!;
        public virtual DbSet<ImAutoReport> ImAutoReports { get; set; } = null!;
        public virtual DbSet<ImAutoReportItem> ImAutoReportItems { get; set; } = null!;
        public virtual DbSet<ImBreakerNoEnum> ImBreakerNoEnums { get; set; } = null!;
        public virtual DbSet<ImCtrlWordDef> ImCtrlWordDefs { get; set; } = null!;
        public virtual DbSet<ImCurve> ImCurves { get; set; } = null!;
        public virtual DbSet<ImCurveItem> ImCurveItems { get; set; } = null!;
        public virtual DbSet<ImCurveType> ImCurveTypes { get; set; } = null!;
        public virtual DbSet<ImDataViewer> ImDataViewers { get; set; } = null!;
        public virtual DbSet<ImDataViewerDatum> ImDataViewerData { get; set; } = null!;
        public virtual DbSet<ImDevCtgy> ImDevCtgies { get; set; } = null!;
        public virtual DbSet<ImDeviceActRule> ImDeviceActRules { get; set; } = null!;
        public virtual DbSet<ImDeviceActRuleTmp> ImDeviceActRuleTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceDataTmp> ImDeviceDataTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceDatum> ImDeviceData { get; set; } = null!;
        public virtual DbSet<ImDeviceDz> ImDeviceDzs { get; set; } = null!;
        public virtual DbSet<ImDeviceDzTmp> ImDeviceDzTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceDzenum> ImDeviceDzenums { get; set; } = null!;
        public virtual DbSet<ImDeviceDzenum2> ImDeviceDzenum2s { get; set; } = null!;
        public virtual DbSet<ImDeviceDzenumPu> ImDeviceDzenumPus { get; set; } = null!;
        public virtual DbSet<ImDeviceVa> ImDeviceVas { get; set; } = null!;
        public virtual DbSet<ImDeviceVaTmp> ImDeviceVaTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceYc> ImDeviceYcs { get; set; } = null!;
        public virtual DbSet<ImDeviceYcTmp> ImDeviceYcTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceYk> ImDeviceYks { get; set; } = null!;
        public virtual DbSet<ImDeviceYkTmp> ImDeviceYkTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceYm> ImDeviceYms { get; set; } = null!;
        public virtual DbSet<ImDeviceYmTmp> ImDeviceYmTmps { get; set; } = null!;
        public virtual DbSet<ImDeviceYx> ImDeviceYxes { get; set; } = null!;
        public virtual DbSet<ImDeviceYxSrcDevice> ImDeviceYxSrcDevices { get; set; } = null!;
        public virtual DbSet<ImDeviceYxTmp> ImDeviceYxTmps { get; set; } = null!;
        public virtual DbSet<ImDiagram> ImDiagrams { get; set; } = null!;
        public virtual DbSet<ImDztype> ImDztypes { get; set; } = null!;
        public virtual DbSet<ImEventFlag> ImEventFlags { get; set; } = null!;
        public virtual DbSet<ImEventParam> ImEventParams { get; set; } = null!;
        public virtual DbSet<ImEventType> ImEventTypes { get; set; } = null!;
        public virtual DbSet<ImEventType2010> ImEventType2010s { get; set; } = null!;
        public virtual DbSet<ImEventType2010DeviceYktmp> ImEventType2010DeviceYktmps { get; set; } = null!;
        public virtual DbSet<ImFaultActType> ImFaultActTypes { get; set; } = null!;
        public virtual DbSet<ImFaultActType2010> ImFaultActType2010s { get; set; } = null!;
        public virtual DbSet<ImFaultActType20102> ImFaultActType20102s { get; set; } = null!;
        public virtual DbSet<ImFaultActTypePd> ImFaultActTypePds { get; set; } = null!;
        public virtual DbSet<ImFaultParam> ImFaultParams { get; set; } = null!;
        public virtual DbSet<ImFaultParamPd> ImFaultParamPds { get; set; } = null!;
        public virtual DbSet<ImFaultReportItem> ImFaultReportItems { get; set; } = null!;
        public virtual DbSet<ImFaultType> ImFaultTypes { get; set; } = null!;
        public virtual DbSet<ImFaultType2010> ImFaultType2010s { get; set; } = null!;
        public virtual DbSet<ImFaultTypePd> ImFaultTypePds { get; set; } = null!;
        public virtual DbSet<ImFltDistDevGrp> ImFltDistDevGrps { get; set; } = null!;
        public virtual DbSet<ImFltDistDevice> ImFltDistDevices { get; set; } = null!;
        public virtual DbSet<ImFltDistParam> ImFltDistParams { get; set; } = null!;
        public virtual DbSet<ImFxcurrAbnormalSet> ImFxcurrAbnormalSets { get; set; } = null!;
        public virtual DbSet<ImGateWay> ImGateWays { get; set; } = null!;
        public virtual DbSet<ImGlyph> ImGlyphs { get; set; } = null!;
        public virtual DbSet<ImGlyphDefProp> ImGlyphDefProps { get; set; } = null!;
        public virtual DbSet<ImGlyphDevDatum> ImGlyphDevData { get; set; } = null!;
        public virtual DbSet<ImGlyphHot> ImGlyphHots { get; set; } = null!;
        public virtual DbSet<ImGrid> ImGrids { get; set; } = null!;
        public virtual DbSet<ImLabel> ImLabels { get; set; } = null!;
        public virtual DbSet<ImLinkage> ImLinkages { get; set; } = null!;
        public virtual DbSet<ImManufacturer> ImManufacturers { get; set; } = null!;
        public virtual DbSet<ImNoticeBoard> ImNoticeBoards { get; set; } = null!;
        public virtual DbSet<ImNotifyTask> ImNotifyTasks { get; set; } = null!;
        public virtual DbSet<ImNotifyTaskAlertObj> ImNotifyTaskAlertObjs { get; set; } = null!;
        public virtual DbSet<ImNotifyTaskPhone> ImNotifyTaskPhones { get; set; } = null!;
        public virtual DbSet<ImNotifyTaskTemplate> ImNotifyTaskTemplates { get; set; } = null!;
        public virtual DbSet<ImOnlineAnalysis> ImOnlineAnalyses { get; set; } = null!;
        public virtual DbSet<ImProgControl> ImProgControls { get; set; } = null!;
        public virtual DbSet<ImProgControlItem> ImProgControlItems { get; set; } = null!;
        public virtual DbSet<ImProject> ImProjects { get; set; } = null!;
        public virtual DbSet<ImProtectDevice> ImProtectDevices { get; set; } = null!;
        public virtual DbSet<ImProtectDeviceTmp> ImProtectDeviceTmps { get; set; } = null!;
        public virtual DbSet<ImPuCtgy> ImPuCtgies { get; set; } = null!;
        public virtual DbSet<ImPuUpdateLog> ImPuUpdateLogs { get; set; } = null!;
        public virtual DbSet<ImPuWaveChl> ImPuWaveChls { get; set; } = null!;
        public virtual DbSet<ImPuctgyFaultType> ImPuctgyFaultTypes { get; set; } = null!;
        public virtual DbSet<ImPuctgyFltRptItem> ImPuctgyFltRptItems { get; set; } = null!;
        public virtual DbSet<ImReportCfg> ImReportCfgs { get; set; } = null!;
        public virtual DbSet<ImReportCfgDatum> ImReportCfgData { get; set; } = null!;
        public virtual DbSet<ImReportType> ImReportTypes { get; set; } = null!;
        public virtual DbSet<ImSanBiZhiFaultDefine> ImSanBiZhiFaultDefines { get; set; } = null!;
        public virtual DbSet<ImSanBiZhiFaultType> ImSanBiZhiFaultTypes { get; set; } = null!;
        public virtual DbSet<ImSheBei> ImSheBeis { get; set; } = null!;
        public virtual DbSet<ImSheBeiDeviceDatum> ImSheBeiDeviceData { get; set; } = null!;
        public virtual DbSet<ImSheBeiLx> ImSheBeiLxes { get; set; } = null!;
        public virtual DbSet<ImSheBeiProtDevice> ImSheBeiProtDevices { get; set; } = null!;
        public virtual DbSet<ImSheBeiZt> ImSheBeiZts { get; set; } = null!;
        public virtual DbSet<ImStation> ImStations { get; set; } = null!;
        public virtual DbSet<ImSwitchState> ImSwitchStates { get; set; } = null!;
        public virtual DbSet<ImTimeSrc> ImTimeSrcs { get; set; } = null!;
        public virtual DbSet<ImVDevDataLinkage> ImVDevDataLinkages { get; set; } = null!;
        public virtual DbSet<ImVDeviceDatum> ImVDeviceData { get; set; } = null!;
        public virtual DbSet<ImVDeviceDz> ImVDeviceDzs { get; set; } = null!;
        public virtual DbSet<ImVDeviceYc> ImVDeviceYcs { get; set; } = null!;
        public virtual DbSet<ImVDeviceYk> ImVDeviceYks { get; set; } = null!;
        public virtual DbSet<ImVDeviceYm> ImVDeviceYms { get; set; } = null!;
        public virtual DbSet<ImVDeviceYx> ImVDeviceYxes { get; set; } = null!;
        public virtual DbSet<ImVDeviceYxSrcDevice> ImVDeviceYxSrcDevices { get; set; } = null!;
        public virtual DbSet<ImVDzdataDetail> ImVDzdataDetails { get; set; } = null!;
        public virtual DbSet<ImVDzdataMain> ImVDzdataMains { get; set; } = null!;
        public virtual DbSet<ImVEventType2010DeviceYktmp> ImVEventType2010DeviceYktmps { get; set; } = null!;
        public virtual DbSet<ImVFaultActType2010> ImVFaultActType2010s { get; set; } = null!;
        public virtual DbSet<ImVNotifyTask> ImVNotifyTasks { get; set; } = null!;
        public virtual DbSet<ImVNotifyTaskPhone> ImVNotifyTaskPhones { get; set; } = null!;
        public virtual DbSet<ImVOnlineAnalysis> ImVOnlineAnalyses { get; set; } = null!;
        public virtual DbSet<ImVProtectDevice> ImVProtectDevices { get; set; } = null!;
        public virtual DbSet<ImVProtectDeviceTmp> ImVProtectDeviceTmps { get; set; } = null!;
        public virtual DbSet<ImVSheBei> ImVSheBeis { get; set; } = null!;
        public virtual DbSet<ImVSheBeiDeviceDatum> ImVSheBeiDeviceData { get; set; } = null!;
        public virtual DbSet<ImVSheBeiProtDevice> ImVSheBeiProtDevices { get; set; } = null!;
        public virtual DbSet<ImVStation4SheBei> ImVStation4SheBeis { get; set; } = null!;
        public virtual DbSet<ImVariantBool> ImVariantBools { get; set; } = null!;
        public virtual DbSet<ImVariantBoolState> ImVariantBoolStates { get; set; } = null!;
        public virtual DbSet<ImVersion> ImVersions { get; set; } = null!;
        public virtual DbSet<ImVersionTypeEnum2010> ImVersionTypeEnum2010s { get; set; } = null!;
        public virtual DbSet<ImWatchdog> ImWatchdogs { get; set; } = null!;
        public virtual DbSet<ImYkType> ImYkTypes { get; set; } = null!;
        public virtual DbSet<ImYxType> ImYxTypes { get; set; } = null!;
        public virtual DbSet<TbAccessController> TbAccessControllers { get; set; } = null!;
        public virtual DbSet<TbAidPlatform> TbAidPlatforms { get; set; } = null!;
        public virtual DbSet<TbAidVideoChannel> TbAidVideoChannels { get; set; } = null!;
        public virtual DbSet<TbAidVideoDev> TbAidVideoDevs { get; set; } = null!;
        public virtual DbSet<TbAttachment> TbAttachments { get; set; } = null!;
        public virtual DbSet<TbBuMan> TbBuMen { get; set; } = null!;
        public virtual DbSet<TbBuMenLx> TbBuMenLxes { get; set; } = null!;
        public virtual DbSet<TbBusiHistory> TbBusiHistories { get; set; } = null!;
        public virtual DbSet<TbBusiness> TbBusinesses { get; set; } = null!;
        public virtual DbSet<TbDaTask> TbDaTasks { get; set; } = null!;
        public virtual DbSet<TbDataModiHi> TbDataModiHis { get; set; } = null!;
        public virtual DbSet<TbDataViewer> TbDataViewers { get; set; } = null!;
        public virtual DbSet<TbDataViewerBusiObj> TbDataViewerBusiObjs { get; set; } = null!;
        public virtual DbSet<TbDframeRevision> TbDframeRevisions { get; set; } = null!;
        public virtual DbSet<TbDiagram> TbDiagrams { get; set; } = null!;
        public virtual DbSet<TbEnvVar> TbEnvVars { get; set; } = null!;
        public virtual DbSet<TbExportTemplate> TbExportTemplates { get; set; } = null!;
        public virtual DbSet<TbExportTemplateCol> TbExportTemplateCols { get; set; } = null!;
        public virtual DbSet<TbField> TbFields { get; set; } = null!;
        public virtual DbSet<TbGlyph> TbGlyphs { get; set; } = null!;
        public virtual DbSet<TbGlyphHot> TbGlyphHots { get; set; } = null!;
        public virtual DbSet<TbGlyphLinkObj> TbGlyphLinkObjs { get; set; } = null!;
        public virtual DbSet<TbGrid> TbGrids { get; set; } = null!;
        public virtual DbSet<TbImage> TbImages { get; set; } = null!;
        public virtual DbSet<TbLabel> TbLabels { get; set; } = null!;
        public virtual DbSet<TbNotifySent> TbNotifySents { get; set; } = null!;
        public virtual DbSet<TbNotifyTask> TbNotifyTasks { get; set; } = null!;
        public virtual DbSet<TbPlanTask> TbPlanTasks { get; set; } = null!;
        public virtual DbSet<TbPlanTaskItem> TbPlanTaskItems { get; set; } = null!;
        public virtual DbSet<TbPresetPoint> TbPresetPoints { get; set; } = null!;
        public virtual DbSet<TbQuanXian> TbQuanXians { get; set; } = null!;
        public virtual DbSet<TbSession> TbSessions { get; set; } = null!;
        public virtual DbSet<TbSm> TbSms { get; set; } = null!;
        public virtual DbSet<TbSmsSent> TbSmsSents { get; set; } = null!;
        public virtual DbSet<TbTable> TbTables { get; set; } = null!;
        public virtual DbSet<TbTableDataDup> TbTableDataDups { get; set; } = null!;
        public virtual DbSet<TbTableMap> TbTableMaps { get; set; } = null!;
        public virtual DbSet<TbTreeNode> TbTreeNodes { get; set; } = null!;
        public virtual DbSet<TbUserCondition> TbUserConditions { get; set; } = null!;
        public virtual DbSet<TbVideoChannelRight> TbVideoChannelRights { get; set; } = null!;
        public virtual DbSet<TbVirtualTable> TbVirtualTables { get; set; } = null!;
        public virtual DbSet<TbWfStep> TbWfSteps { get; set; } = null!;
        public virtual DbSet<TbWfStepUser> TbWfStepUsers { get; set; } = null!;
        public virtual DbSet<TbWorkflow> TbWorkflows { get; set; } = null!;
        public virtual DbSet<TbXiTongMoKuai> TbXiTongMoKuais { get; set; } = null!;
        public virtual DbSet<TbXiTongRiZhi> TbXiTongRiZhis { get; set; } = null!;
        public virtual DbSet<TbYongHu> TbYongHus { get; set; } = null!;
        public virtual DbSet<VwAidVideoChannel> VwAidVideoChannels { get; set; } = null!;
        public virtual DbSet<VwPresetPoint> VwPresetPoints { get; set; } = null!;
        public virtual DbSet<VwTableDataDup> VwTableDataDups { get; set; } = null!;
        public virtual DbSet<VwTableMap> VwTableMaps { get; set; } = null!;
        public virtual DbSet<VwVideoChannelRight> VwVideoChannelRights { get; set; } = null!;
        #endregion

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder
             .EnableSensitiveDataLogging(true);
            base.OnConfiguring(optionsBuilder);
            //if (!optionsBuilder.IsConfigured)
            //{
            //    optionsBuilder.UseSqlServer("Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_BASE;Trusted_Connection=False;");
            //}
        }

    

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<IaFuncNameConst>(entity =>
            {
                entity.HasKey(e => e.FnameId)
                    .HasName("PK_FuncNameConst");

                entity.ToTable("ia_FuncNameConst");

                entity.HasIndex(e => e.Fname, "UK_FuncNameConst_FName")
                    .IsUnique();

                entity.Property(e => e.FnameId)
                    .HasMaxLength(2)
                    .IsUnicode(false)
                    .HasColumnName("FNameID");

                entity.Property(e => e.Fname)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("FName");
            });

            modelBuilder.Entity<IaMaChannel>(entity =>
            {
                entity.HasKey(e => e.RecId)
                    .HasName("PK_MA_Channel");

                entity.ToTable("ia_MA_Channel");

                entity.HasIndex(e => new { e.MaId, e.ChanId }, "UK_MA_Channel")
                    .IsUnique();

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.MaId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("MA_ID");

                entity.HasOne(d => d.Ma)
                    .WithMany(p => p.IaMaChannels)
                    .HasForeignKey(d => d.MaId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MAChan_MA");
            });

            modelBuilder.Entity<IaModule>(entity =>
            {
                entity.HasKey(e => e.ModuleId)
                    .HasName("PK_Module");

                entity.ToTable("ia_Module");

                entity.HasIndex(e => new { e.DirId, e.ModuleName }, "UK_Module_Name")
                    .IsUnique();

                entity.Property(e => e.ModuleId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ModuleID");

                entity.Property(e => e.DirId)
                    .HasMaxLength(2)
                    .IsUnicode(false)
                    .HasColumnName("DirID");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("FuncID");

                entity.Property(e => e.ImgName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ModuleName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ModuleType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("State_");

                entity.HasOne(d => d.Dir)
                    .WithMany(p => p.IaModules)
                    .HasForeignKey(d => d.DirId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Module_Dir");
            });

            modelBuilder.Entity<IaModuleDir>(entity =>
            {
                entity.HasKey(e => e.DirId)
                    .HasName("PK_ModuleDir");

                entity.ToTable("ia_ModuleDir");

                entity.HasIndex(e => e.DirName, "UK_ModuleDir_Name")
                    .IsUnique();

                entity.Property(e => e.DirId)
                    .HasMaxLength(2)
                    .IsUnicode(false)
                    .HasColumnName("DirID");

                entity.Property(e => e.DirName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IaMonitorArea>(entity =>
            {
                entity.HasKey(e => e.MaId)
                    .HasName("PK_MonitorArea");

                entity.ToTable("ia_MonitorArea");

                entity.HasIndex(e => new { e.Maname, e.StatCode }, "UK_MonitorArea")
                    .IsUnique();

                entity.Property(e => e.MaId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("MA_ID");

                entity.Property(e => e.Maname)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("MAName");

                entity.Property(e => e.ParentId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("Parent_ID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.IaMonitorAreas)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MonitorArea_Station");
            });

            modelBuilder.Entity<IaStatAccCtler>(entity =>
            {
                entity.HasKey(e => e.CtlerId)
                    .HasName("PK_Stat_AccCtler");

                entity.ToTable("ia_Stat_AccCtler");

                entity.Property(e => e.CtlerId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("CtlerID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.HasOne(d => d.Ctler)
                    .WithOne(p => p.IaStatAccCtler)
                    .HasForeignKey<IaStatAccCtler>(d => d.CtlerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StatAccCtler_Ctler");

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.IaStatAccCtlers)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StatAccCtler_Stat");
            });

            modelBuilder.Entity<IaStatAidVideoDev>(entity =>
            {
                entity.HasKey(e => e.RecId)
                    .HasName("PK_Stat_VideoDev");

                entity.ToTable("ia_Stat_AidVideoDev");

                entity.HasIndex(e => new { e.StatCode, e.DevId }, "UK_Stat_VideoDev")
                    .IsUnique();

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.HasOne(d => d.Dev)
                    .WithMany(p => p.IaStatAidVideoDevs)
                    .HasForeignKey(d => d.DevId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StatVideoDev_Dev");

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.IaStatAidVideoDevs)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StatVideoDev_Stat");
            });

            modelBuilder.Entity<IaVAllChannel>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ia_v_AllChannel");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.ChanName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ChanType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IaVAllFunc4Module>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ia_v_AllFunc4Module");

                entity.Property(e => e.FuncId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("FuncID");

                entity.Property(e => e.FuncName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.FuncType)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.OrderFld)
                    .HasMaxLength(293)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IaVMaChannel>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ia_v_MA_Channel");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.ChanName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ChanType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.MaId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("MA_ID");

                entity.Property(e => e.Maname)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("MAName");

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IaVModule>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ia_v_Module");

                entity.Property(e => e.DirId)
                    .HasMaxLength(2)
                    .IsUnicode(false)
                    .HasColumnName("DirID");

                entity.Property(e => e.DirName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.FuncId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("FuncID");

                entity.Property(e => e.FuncName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.FuncType)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.ImgName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ModuleId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ModuleID");

                entity.Property(e => e.ModuleName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ModuleType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("State_");
            });

            modelBuilder.Entity<IaVStatAidVideoDev>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ia_v_Stat_AidVideoDev");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Ip)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("IP");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PassEnc)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PlatId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PlatID");

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IaVVideoPatrolItemExecObj>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ia_v_VideoPatrolItemExecObj");

                entity.Property(e => e.ExeObjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ExeObjID");

                entity.Property(e => e.ExeObjName)
                    .HasMaxLength(357)
                    .IsUnicode(false);

                entity.Property(e => e.ExeObjType)
                    .HasMaxLength(8)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<IaVideoPatrol>(entity =>
            {
                entity.HasKey(e => e.PatId)
                    .HasName("PK_VideoPatrol");

                entity.ToTable("ia_VideoPatrol");

                entity.HasIndex(e => e.PatName, "UK_VideoPatrol_Name")
                    .IsUnique();

                entity.Property(e => e.PatId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PatID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CreTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.PatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.PatType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PrjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PrjID");

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.IaVideoPatrols)
                    .HasForeignKey(d => d.PrjId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VideoPatrol_Project");
            });

            modelBuilder.Entity<IaVideoPatrolHi>(entity =>
            {
                entity.HasKey(e => e.PatHisId)
                    .HasName("PK_VideoPatrolHis");

                entity.ToTable("ia_VideoPatrolHis");

                entity.Property(e => e.PatHisId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PatHisID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.EndTime).HasColumnType("datetime");

                entity.Property(e => e.PatId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PatID");

                entity.Property(e => e.PatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.PatType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PatUser)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Result)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.StartTime).HasColumnType("datetime");
            });

            modelBuilder.Entity<IaVideoPatrolItem>(entity =>
            {
                entity.HasKey(e => e.PiId)
                    .HasName("PK_VPItem");

                entity.ToTable("ia_VideoPatrolItem");

                entity.HasIndex(e => new { e.PatId, e.PiseqNo }, "UK_VPItem_SeqNo")
                    .IsUnique();

                entity.Property(e => e.PiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PI_ID");

                entity.Property(e => e.ActParam)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Action)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.BkimgPaths)
                    .HasMaxLength(1000)
                    .IsUnicode(false)
                    .HasColumnName("BKImgPaths");

                entity.Property(e => e.ExeObjId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("ExeObjID");

                entity.Property(e => e.ExeObjType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.IsCompareDiff)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.PatId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PatID");

                entity.Property(e => e.Piname)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("PIName");

                entity.Property(e => e.PiseqNo).HasColumnName("PISeqNo");

                entity.HasOne(d => d.Pat)
                    .WithMany(p => p.IaVideoPatrolItems)
                    .HasForeignKey(d => d.PatId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VPItem_VP");
            });

            modelBuilder.Entity<IaVideoPatrolItemHi>(entity =>
            {
                entity.HasKey(e => e.PatItemHisId)
                    .HasName("PK_VideoPatrolItemHis");

                entity.ToTable("ia_VideoPatrolItemHis");

                entity.Property(e => e.PatItemHisId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PatItemHisID");

                entity.Property(e => e.CompareMsg)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CompareRet)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('尚未比较')");

                entity.Property(e => e.Description)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.EndTime).HasColumnType("datetime");

                entity.Property(e => e.PatHisId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PatHisID");

                entity.Property(e => e.PiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PI_ID");

                entity.Property(e => e.Piname)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("PIName");

                entity.Property(e => e.PiseqNo).HasColumnName("PISeqNo");

                entity.Property(e => e.StartTime).HasColumnType("datetime");

                entity.Property(e => e.State)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.PatHis)
                    .WithMany(p => p.IaVideoPatrolItemHis)
                    .HasForeignKey(d => d.PatHisId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VPHItem_VPH");
            });

            modelBuilder.Entity<Im3posSwitch>(entity =>
            {
                entity.ToTable("im_3PosSwitch");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.GndykdataId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("GNDYKDataID");

                entity.Property(e => e.GndykdataName)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("GNDYKDataName");

                entity.Property(e => e.GndyxdataId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("GNDYXDataID");

                entity.Property(e => e.GndyxdataName)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("GNDYXDataName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.Im3posSwitch)
                    .HasForeignKey<Im3posSwitch>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_3PosSwitch_Glyph");
            });

            modelBuilder.Entity<ImAlert201310>(entity =>
            {
                entity.ToTable("im_Alert201310");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.AlertTime)
                    .HasMaxLength(25)
                    .IsUnicode(false);

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Alerter)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Content)
                    .HasMaxLength(2500)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.RecTime).HasColumnType("datetime");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TimeSrc)
                    .HasMaxLength(8)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImAlertLevel>(entity =>
            {
                entity.HasKey(e => e.AlertLevelCode)
                    .HasName("pk_AlertLevel");

                entity.ToTable("im_AlertLevel");

                entity.Property(e => e.AlertLevelCode)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.AlertLevelName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImAlertState>(entity =>
            {
                entity.HasKey(e => e.StateCode)
                    .HasName("pk_AlertState");

                entity.ToTable("im_AlertState");

                entity.Property(e => e.StateCode)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.StateName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImAlertType>(entity =>
            {
                entity.HasKey(e => e.AlertType)
                    .HasName("PK_AlertType");

                entity.ToTable("im_AlertType");

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.AlertTypeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.AlertLevelNavigation)
                    .WithMany(p => p.ImAlertTypes)
                    .HasForeignKey(d => d.AlertLevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AltType_Level");
            });

            modelBuilder.Entity<ImAnalogData2010>(entity =>
            {
                entity.HasKey(e => new { e.DeviceCtgy, e.DataCode })
                    .HasName("PK_AnalogData_2010");

                entity.ToTable("im_AnalogData_2010");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Cof1).HasDefaultValueSql("((1))");

                entity.Property(e => e.Cof2).HasDefaultValueSql("((1))");

                entity.Property(e => e.DataName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.EnumStr)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Precise1).HasDefaultValueSql("((2))");

                entity.Property(e => e.Precise2).HasDefaultValueSql("((2))");

                entity.Property(e => e.Sym1)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Sym2)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.DeviceCtgyNavigation)
                    .WithMany(p => p.ImAnalogData2010s)
                    .HasForeignKey(d => d.DeviceCtgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AnalogData2010_DevCtgy");
            });

            modelBuilder.Entity<ImAnalogData20102>(entity =>
            {
                entity.HasKey(e => new { e.PuctgyCode, e.DataCode })
                    .HasName("PK_AnalogData_2010_2");

                entity.ToTable("im_AnalogData_2010_2");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.Cof1).HasDefaultValueSql("((1))");

                entity.Property(e => e.Cof2).HasDefaultValueSql("((1))");

                entity.Property(e => e.DataName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.EnumStr)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Precise1).HasDefaultValueSql("((2))");

                entity.Property(e => e.Precise2).HasDefaultValueSql("((2))");

                entity.Property(e => e.Sym1)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Sym2)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImAnalogData20102s)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AnalogData2_PUCtgy");
            });

            modelBuilder.Entity<ImAsdu140FltActType>(entity =>
            {
                entity.HasKey(e => new { e.PuctgyCode, e.ActCode })
                    .HasName("PK_ASDU140FltActType");

                entity.ToTable("im_ASDU140_FltActType");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.ActName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CompanyName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.CompanyNameNavigation)
                    .WithMany(p => p.ImAsdu140FltActTypes)
                    .HasForeignKey(d => d.CompanyName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ASDU140FltActType_Manu");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImAsdu140FltActTypes)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ASDU140FltActType");
            });

            modelBuilder.Entity<ImAsdu140FltCurrInfo>(entity =>
            {
                entity.HasKey(e => new { e.CompanyName, e.CurrIndCode })
                    .HasName("PK_ASDU140FltCurr");

                entity.ToTable("im_ASDU140_FltCurrInfo");

                entity.Property(e => e.CompanyName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Coeff).HasDefaultValueSql("((1))");

                entity.Property(e => e.CurrName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Precise).HasDefaultValueSql("((2))");

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.CompanyNameNavigation)
                    .WithMany(p => p.ImAsdu140FltCurrInfos)
                    .HasForeignKey(d => d.CompanyName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ASDU140FltCurr_Manu");
            });

            modelBuilder.Entity<ImAsdu142info>(entity =>
            {
                entity.HasKey(e => new { e.CompanyName, e.ValueType, e.ValueIndex })
                    .HasName("PK_ASDU142Info");

                entity.ToTable("im_ASDU142Info");

                entity.Property(e => e.CompanyName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ValueType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Coeff).HasDefaultValueSql("((1))");

                entity.Property(e => e.ParseFormat)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('一般数字')");

                entity.Property(e => e.Precise).HasDefaultValueSql("((2))");

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.ValueName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.CompanyNameNavigation)
                    .WithMany(p => p.ImAsdu142infos)
                    .HasForeignKey(d => d.CompanyName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ASDU142Info_Manu");
            });

            modelBuilder.Entity<ImAutoReport>(entity =>
            {
                entity.HasKey(e => e.RptId)
                    .HasName("PK_AutoReport");

                entity.ToTable("im_AutoReport");

                entity.HasIndex(e => e.RptName, "UK_AutoReport")
                    .IsUnique();

                entity.Property(e => e.RptId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RptID");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.RptName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RptType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.SavePath)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("State_")
                    .HasDefaultValueSql("('在用')");

                entity.Property(e => e.TempFileObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("TempFileObjID");
            });

            modelBuilder.Entity<ImAutoReportItem>(entity =>
            {
                entity.HasKey(e => e.ItemId)
                    .HasName("PK_AutoReportItem");

                entity.ToTable("im_AutoReportItem");

                entity.HasIndex(e => new { e.RptId, e.RowNo, e.ColNo }, "UK_AutoReportItem")
                    .IsUnique();

                entity.Property(e => e.ItemId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ItemID");

                entity.Property(e => e.DataId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.DataName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ItemType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.RptId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RptID");

                entity.Property(e => e.ShowUnit)
                    .HasMaxLength(5)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.Time1)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Time2)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.TimeCell)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.Rpt)
                    .WithMany(p => p.ImAutoReportItems)
                    .HasForeignKey(d => d.RptId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AutoReportItem_Report");
            });

            modelBuilder.Entity<ImBreakerNoEnum>(entity =>
            {
                entity.HasKey(e => new { e.PuctgyCode, e.EnumIndex })
                    .HasName("PK_BreakerNoEnum");

                entity.ToTable("im_BreakerNoEnum");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.EnumName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImBreakerNoEnums)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BreakerNoEnum");
            });

            modelBuilder.Entity<ImCtrlWordDef>(entity =>
            {
                entity.HasKey(e => e.TypeId)
                    .HasName("PK_CtrlWordDef");

                entity.ToTable("im_CtrlWordDef");

                entity.Property(e => e.TypeId).ValueGeneratedNever();

                entity.Property(e => e.Bit10Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit10_Meanings");

                entity.Property(e => e.Bit11Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit11_Meanings");

                entity.Property(e => e.Bit12Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit12_Meanings");

                entity.Property(e => e.Bit13Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit13_Meanings");

                entity.Property(e => e.Bit14Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit14_Meanings");

                entity.Property(e => e.Bit15Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit15_Meanings");

                entity.Property(e => e.Bit16Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit16_Meanings");

                entity.Property(e => e.Bit17Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit17_Meanings");

                entity.Property(e => e.Bit18Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit18_Meanings");

                entity.Property(e => e.Bit19Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit19_Meanings");

                entity.Property(e => e.Bit1Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit1_Meanings");

                entity.Property(e => e.Bit20Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit20_Meanings");

                entity.Property(e => e.Bit21Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit21_Meanings");

                entity.Property(e => e.Bit22Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit22_Meanings");

                entity.Property(e => e.Bit23Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit23_Meanings");

                entity.Property(e => e.Bit24Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit24_Meanings");

                entity.Property(e => e.Bit25Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit25_Meanings");

                entity.Property(e => e.Bit26Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit26_Meanings");

                entity.Property(e => e.Bit27Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit27_Meanings");

                entity.Property(e => e.Bit28Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit28_Meanings");

                entity.Property(e => e.Bit29Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit29_Meanings");

                entity.Property(e => e.Bit2Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit2_Meanings");

                entity.Property(e => e.Bit30Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit30_Meanings");

                entity.Property(e => e.Bit31Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit31_Meanings");

                entity.Property(e => e.Bit32Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit32_Meanings");

                entity.Property(e => e.Bit3Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit3_Meanings");

                entity.Property(e => e.Bit4Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit4_Meanings");

                entity.Property(e => e.Bit5Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit5_Meanings");

                entity.Property(e => e.Bit6Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit6_Meanings");

                entity.Property(e => e.Bit7Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit7_Meanings");

                entity.Property(e => e.Bit8Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit8_Meanings");

                entity.Property(e => e.Bit9Meanings)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Bit9_Meanings");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('交大运达电气')");

                entity.Property(e => e.TypeComment)
                    .HasMaxLength(180)
                    .IsUnicode(false)
                    .HasColumnName("Type_Comment");

                entity.HasOne(d => d.ManufacturerNavigation)
                    .WithMany(p => p.ImCtrlWordDefs)
                    .HasForeignKey(d => d.Manufacturer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CtrlWordDef_Manu");
            });

            modelBuilder.Entity<ImCurve>(entity =>
            {
                entity.ToTable("im_Curve");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreTime).HasColumnType("datetime");

                entity.Property(e => e.CurveName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CurveType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Ymax).HasColumnName("YMax");

                entity.Property(e => e.Ymin).HasColumnName("YMin");

                entity.HasOne(d => d.CurveTypeNavigation)
                    .WithMany(p => p.ImCurves)
                    .HasForeignKey(d => d.CurveType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Curve_Type");
            });

            modelBuilder.Entity<ImCurveItem>(entity =>
            {
                entity.HasKey(e => new { e.CurveId, e.DataId })
                    .HasName("PK_CurveItem");

                entity.ToTable("im_CurveItem");

                entity.Property(e => e.CurveId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("CurveID");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.HasOne(d => d.Curve)
                    .WithMany(p => p.ImCurveItems)
                    .HasForeignKey(d => d.CurveId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CurveItem_Curve");

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImCurveItems)
                    .HasForeignKey(d => d.DataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CurveItem_DeviceData");
            });

            modelBuilder.Entity<ImCurveType>(entity =>
            {
                entity.HasKey(e => e.CveTypeCode)
                    .HasName("pk_curve_type");

                entity.ToTable("im_Curve_Type");

                entity.Property(e => e.CveTypeCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CveTypeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImDataViewer>(entity =>
            {
                entity.ToTable("im_DataViewer");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.NameFontName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ValueFontName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDataViewer)
                    .HasForeignKey<ImDataViewer>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DataViewer_Glyph");
            });

            modelBuilder.Entity<ImDataViewerDatum>(entity =>
            {
                entity.HasKey(e => new { e.DataViewerId, e.DataId })
                    .HasName("PK_DataViewerData");

                entity.ToTable("im_DataViewer_Data");

                entity.Property(e => e.DataViewerId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DataViewerID");

                entity.Property(e => e.DataId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.HasOne(d => d.DataViewer)
                    .WithMany(p => p.ImDataViewerData)
                    .HasForeignKey(d => d.DataViewerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DVD_DV");
            });

            modelBuilder.Entity<ImDevCtgy>(entity =>
            {
                entity.HasKey(e => e.DevCtgyCode)
                    .HasName("pk_DevCtgy");

                entity.ToTable("im_DevCtgy");

                entity.Property(e => e.DevCtgyCode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.DevCtgyName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImDeviceActRule>(entity =>
            {
                entity.HasKey(e => e.RuleId)
                    .HasName("PK_DevActRule");

                entity.ToTable("im_DeviceActRule");

                entity.Property(e => e.RuleId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RuleID");

                entity.Property(e => e.ActParam)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ActType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.LogicExpr)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LogicValue)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImDeviceActRules)
                    .HasForeignKey(d => d.DataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DevActRule_DeviceData");
            });

            modelBuilder.Entity<ImDeviceActRuleTmp>(entity =>
            {
                entity.HasKey(e => e.RuleId)
                    .HasName("PK_DevActRule_Tmp");

                entity.ToTable("im_DeviceActRule_Tmp");

                entity.Property(e => e.RuleId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RuleID");

                entity.Property(e => e.ActParam)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ActType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.DataId)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.LogicExpr)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LogicValue)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImDeviceActRuleTmps)
                    .HasForeignKey(d => d.DataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DevActRuleTmp_DeviceDataTmp");
            });

            modelBuilder.Entity<ImDeviceDataTmp>(entity =>
            {
                entity.ToTable("im_DeviceData_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CfgFileBz)
                    .HasColumnName("CfgFile_BZ")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.Domain)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('综自')");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.SecAddr101).HasDefaultValueSql("((1))");

                entity.Property(e => e.ToScada)
                    .HasColumnName("ToSCADA")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.Visible).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDeviceDataTmps)
                    .HasForeignKey(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceDataTmp_DeviceTmp");
            });

            modelBuilder.Entity<ImDeviceDatum>(entity =>
            {
                entity.ToTable("im_DeviceData");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.Domain)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('综自')");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.SrcTempDataId)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("SrcTempDataID");

                entity.Property(e => e.Visible).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDeviceData)
                    .HasForeignKey(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceData_ProtDevice");
            });

            modelBuilder.Entity<ImDeviceDz>(entity =>
            {
                entity.ToTable("im_DeviceDZ");

                entity.Property(e => e.Id)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.CtrlWordTypeId).HasColumnName("CtrlWord_TypeId");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DzCoeff).HasColumnName("DZ_Coeff");

                entity.Property(e => e.DzCoeff1).HasColumnName("DZ_Coeff_1");

                entity.Property(e => e.DzComment)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Comment");

                entity.Property(e => e.DzIndex).HasColumnName("DZ_Index");

                entity.Property(e => e.DzMax).HasColumnName("DZ_Max");

                entity.Property(e => e.DzMin).HasColumnName("DZ_Min");

                entity.Property(e => e.DzName)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Name");

                entity.Property(e => e.DzPrecise).HasColumnName("DZ_Precise");

                entity.Property(e => e.DzPrecise1).HasColumnName("DZ_Precise_1");

                entity.Property(e => e.DzRange)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Range");

                entity.Property(e => e.DzType).HasColumnName("DZ_Type");

                entity.Property(e => e.DzUnit)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit");

                entity.Property(e => e.DzUnit1)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit_1");

                entity.Property(e => e.DzUnitCvtCoeff).HasColumnName("DZ_UnitCvt_Coeff");

                entity.Property(e => e.EnumTypeId).HasColumnName("Enum_TypeId");

                entity.Property(e => e.Hidden)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.RelateCtId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("RelateCT_ID");

                entity.Property(e => e.RelatePtId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("RelatePT_ID");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDeviceDzs)
                    .HasForeignKey(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceDZ_ProtDevice");

                entity.HasOne(d => d.DzTypeNavigation)
                    .WithMany(p => p.ImDeviceDzs)
                    .HasForeignKey(d => d.DzType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceDZ_DZType");
            });

            modelBuilder.Entity<ImDeviceDzTmp>(entity =>
            {
                entity.ToTable("im_DeviceDZ_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.CtrlWordTypeId).HasColumnName("CtrlWord_TypeId");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DzCoeff).HasColumnName("DZ_Coeff");

                entity.Property(e => e.DzCoeff1).HasColumnName("DZ_Coeff_1");

                entity.Property(e => e.DzComment)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Comment");

                entity.Property(e => e.DzIndex).HasColumnName("DZ_Index");

                entity.Property(e => e.DzMax).HasColumnName("DZ_Max");

                entity.Property(e => e.DzMin).HasColumnName("DZ_Min");

                entity.Property(e => e.DzName)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Name");

                entity.Property(e => e.DzPrecise).HasColumnName("DZ_Precise");

                entity.Property(e => e.DzPrecise1).HasColumnName("DZ_Precise_1");

                entity.Property(e => e.DzRange)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Range");

                entity.Property(e => e.DzType).HasColumnName("DZ_Type");

                entity.Property(e => e.DzUnit)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit");

                entity.Property(e => e.DzUnit1)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit_1");

                entity.Property(e => e.DzUnitCvtCoeff).HasColumnName("DZ_UnitCvt_Coeff");

                entity.Property(e => e.EnumTypeId).HasColumnName("Enum_TypeId");

                entity.Property(e => e.Hidden)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.RelateCtId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("RelateCT_ID");

                entity.Property(e => e.RelatePtId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("RelatePT_ID");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDeviceDzTmps)
                    .HasForeignKey(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceDZTmp_ProtDeviceTmp");

                entity.HasOne(d => d.DzTypeNavigation)
                    .WithMany(p => p.ImDeviceDzTmps)
                    .HasForeignKey(d => d.DzType)
                    .HasConstraintName("FK_DeviceDZTmp_DZType");
            });

            modelBuilder.Entity<ImDeviceDzenum>(entity =>
            {
                entity.HasKey(e => new { e.EnumTypeId, e.EnumIndex })
                    .HasName("PK_DeviceDZEnum");

                entity.ToTable("im_DeviceDZEnum");

                entity.HasIndex(e => new { e.EnumTypeId, e.EnumComment }, "UK_DeviceDZEnum")
                    .IsUnique();

                entity.Property(e => e.EnumTypeId).HasColumnName("Enum_TypeId");

                entity.Property(e => e.EnumIndex).HasColumnName("Enum_Index");

                entity.Property(e => e.EnumComment)
                    .HasMaxLength(200)
                    .IsUnicode(false)
                    .HasColumnName("Enum_Comment");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('交大运达电气')");

                entity.HasOne(d => d.ManufacturerNavigation)
                    .WithMany(p => p.ImDeviceDzenums)
                    .HasForeignKey(d => d.Manufacturer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceDZEnum_Manu");
            });

            modelBuilder.Entity<ImDeviceDzenum2>(entity =>
            {
                entity.HasKey(e => new { e.EnumTypeId, e.EnumByte, e.EnumIndex })
                    .HasName("PK_DeviceDZEnum2");

                entity.ToTable("im_DeviceDZEnum2");

                entity.HasIndex(e => new { e.EnumTypeId, e.EnumByte, e.EnumComment }, "UK_DeviceDZEnum2")
                    .IsUnique();

                entity.Property(e => e.EnumTypeId).HasColumnName("Enum_TypeId");

                entity.Property(e => e.EnumByte)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("Enum_Byte");

                entity.Property(e => e.EnumIndex).HasColumnName("Enum_Index");

                entity.Property(e => e.EnumComment)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("Enum_Comment");
            });

            modelBuilder.Entity<ImDeviceDzenumPu>(entity =>
            {
                entity.HasKey(e => new { e.PuctgyCode, e.EnumTypeId, e.EnumIndex })
                    .HasName("PK_DeviceDZEnumPU");

                entity.ToTable("im_DeviceDZEnumPU");

                entity.HasIndex(e => new { e.PuctgyCode, e.EnumTypeId, e.EnumComment }, "UK_DeviceDZEnumPU")
                    .IsUnique();

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.EnumTypeId).HasColumnName("Enum_TypeId");

                entity.Property(e => e.EnumIndex).HasColumnName("Enum_Index");

                entity.Property(e => e.EnumComment)
                    .HasMaxLength(200)
                    .IsUnicode(false)
                    .HasColumnName("Enum_Comment");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImDeviceDzenumPus)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceDZEnumPU_PUCtgy");
            });

            modelBuilder.Entity<ImDeviceVa>(entity =>
            {
                entity.ToTable("im_DeviceVA");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Expr)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Vaname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("VAName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceVa)
                    .HasForeignKey<ImDeviceVa>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceVA_DeviceData");
            });

            modelBuilder.Entity<ImDeviceVaTmp>(entity =>
            {
                entity.ToTable("im_DeviceVA_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Expr)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Vaname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("VAName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceVaTmp)
                    .HasForeignKey<ImDeviceVaTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceVATmp_DeviceDataTmp");
            });

            modelBuilder.Entity<ImDeviceYc>(entity =>
            {
                entity.ToTable("im_DeviceYC");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Intl4Save).HasDefaultValueSql("((5))");

                entity.Property(e => e.Precise).HasDefaultValueSql("((2))");

                entity.Property(e => e.Precise2).HasDefaultValueSql("((2))");

                entity.Property(e => e.SaveMode)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('绝对值变化')");

                entity.Property(e => e.Unit)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Unit2)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YCName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYc)
                    .HasForeignKey<ImDeviceYc>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYC_DeviceData");
            });

            modelBuilder.Entity<ImDeviceYcTmp>(entity =>
            {
                entity.ToTable("im_DeviceYC_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Intl4Save).HasDefaultValueSql("((5))");

                entity.Property(e => e.Precise).HasDefaultValueSql("((2))");

                entity.Property(e => e.Precise2).HasDefaultValueSql("((2))");

                entity.Property(e => e.SaveMode)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('绝对值变化')");

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Unit2)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YCName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYcTmp)
                    .HasForeignKey<ImDeviceYcTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYCTmp_DeviceDataTmp");
            });

            modelBuilder.Entity<ImDeviceYk>(entity =>
            {
                entity.ToTable("im_DeviceYK");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.IsResetCmd)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.LockMode)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('按遥信状态')");

                entity.Property(e => e.LockModeOff)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("LockMode_Off")
                    .HasDefaultValueSql("('按遥信状态')");

                entity.Property(e => e.PreState4Yk).HasColumnName("PreState4YK");

                entity.Property(e => e.PreState4YkOff).HasColumnName("PreState4YK_Off");

                entity.Property(e => e.PreYxId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("PreYX_ID");

                entity.Property(e => e.PreYxIdOff)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("PreYX_ID_Off");

                entity.Property(e => e.RelatedYxId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("RelatedYX_ID");

                entity.Property(e => e.SwOffStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwOnStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwUncertStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YkType)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("YK_Type");

                entity.Property(e => e.Ykname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YKName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYk)
                    .HasForeignKey<ImDeviceYk>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYK_DeviceData");
            });

            modelBuilder.Entity<ImDeviceYkTmp>(entity =>
            {
                entity.ToTable("im_DeviceYK_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.IsResetCmd)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.LockMode)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('按遥信状态')");

                entity.Property(e => e.LockModeOff)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("LockMode_Off")
                    .HasDefaultValueSql("('按遥信状态')");

                entity.Property(e => e.PreState4Yk).HasColumnName("PreState4YK");

                entity.Property(e => e.PreState4YkOff).HasColumnName("PreState4YK_Off");

                entity.Property(e => e.PreYxId)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("PreYX_ID");

                entity.Property(e => e.PreYxIdOff)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("PreYX_ID_Off");

                entity.Property(e => e.RelatedYxId)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("RelatedYX_ID");

                entity.Property(e => e.SwOffStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwOnStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwUncertStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YkType)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("YK_Type");

                entity.Property(e => e.Ykname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YKName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYkTmp)
                    .HasForeignKey<ImDeviceYkTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYKTmp_DeviceDataTmp");
            });

            modelBuilder.Entity<ImDeviceYm>(entity =>
            {
                entity.ToTable("im_DeviceYM");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Ymname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YMName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYm)
                    .HasForeignKey<ImDeviceYm>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYM_DeviceData");
            });

            modelBuilder.Entity<ImDeviceYmTmp>(entity =>
            {
                entity.ToTable("im_DeviceYM_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Ymname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YMName");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYmTmp)
                    .HasForeignKey<ImDeviceYmTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYMTmp_DeviceDataTmp");
            });

            modelBuilder.Entity<ImDeviceYx>(entity =>
            {
                entity.ToTable("im_DeviceYX");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.NormalState)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('忽略')");

                entity.Property(e => e.SwOffStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwOnStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwUncertStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YxType)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("YX_Type");

                entity.Property(e => e.Yxname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YXName");

                entity.HasOne(d => d.AlertLevelNavigation)
                    .WithMany(p => p.ImDeviceYxes)
                    .HasForeignKey(d => d.AlertLevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYX_Level");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYx)
                    .HasForeignKey<ImDeviceYx>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYX_DeviceData");
            });

            modelBuilder.Entity<ImDeviceYxSrcDevice>(entity =>
            {
                entity.ToTable("im_DeviceYX_SrcDevice");

                entity.HasIndex(e => new { e.YxdataId, e.SrcDevId }, "UK_DeviceYXSrcDevice")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.SrcDevId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("SrcDevID");

                entity.Property(e => e.YxdataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("YXDataID");
            });

            modelBuilder.Entity<ImDeviceYxTmp>(entity =>
            {
                entity.ToTable("im_DeviceYX_Tmp");

                entity.Property(e => e.Id)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.NormalState)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('忽略')");

                entity.Property(e => e.SwOffStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwOnStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwUncertStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YxType)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("YX_Type");

                entity.Property(e => e.Yxname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YXName");

                entity.HasOne(d => d.AlertLevelNavigation)
                    .WithMany(p => p.ImDeviceYxTmps)
                    .HasForeignKey(d => d.AlertLevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYXTmp_Level");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceYxTmp)
                    .HasForeignKey<ImDeviceYxTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DeviceYXTmp_DeviceDataTmp");
            });

            modelBuilder.Entity<ImDiagram>(entity =>
            {
                entity.ToTable("im_Diagram");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.Creator)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.PrjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PrjID");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDiagram)
                    .HasForeignKey<ImDiagram>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_imDiagram_Glyph");

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImDiagrams)
                    .HasForeignKey(d => d.PrjId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_imDiagram_Project");
            });

            modelBuilder.Entity<ImDztype>(entity =>
            {
                entity.HasKey(e => e.DztypeId)
                    .HasName("PK_DZType");

                entity.ToTable("im_DZType");

                entity.Property(e => e.DztypeId)
                    .ValueGeneratedNever()
                    .HasColumnName("DZTypeID");

                entity.Property(e => e.Dztype)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("DZType");
            });

            modelBuilder.Entity<ImEventFlag>(entity =>
            {
                entity.HasKey(e => new { e.DeviceCtgy, e.EvtFlagCode })
                    .HasName("PK_EventFlag");

                entity.ToTable("im_EventFlag");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Param1Mean)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Param1Sym)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Param1ValueType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Param2Mean)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Param2Sym)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Param2ValueType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Param3Mean)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Param3Sym)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Param3ValueType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.DeviceCtgyNavigation)
                    .WithMany(p => p.ImEventFlags)
                    .HasForeignKey(d => d.DeviceCtgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EvengFlag_DevCtgy");
            });

            modelBuilder.Entity<ImEventParam>(entity =>
            {
                entity.HasKey(e => new { e.DeviceCtgy, e.ParamValue })
                    .HasName("PK_EventParam");

                entity.ToTable("im_EventParam");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ParamDesc)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.DeviceCtgyNavigation)
                    .WithMany(p => p.ImEventParams)
                    .HasForeignKey(d => d.DeviceCtgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EventParam_DevCtgy");
            });

            modelBuilder.Entity<ImEventType>(entity =>
            {
                entity.HasKey(e => new { e.EvtCode, e.PuctgyCode })
                    .HasName("PK_EventType");

                entity.ToTable("im_EventType");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('级别1')");

                entity.Property(e => e.EvtName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.IsYiCiSb)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasColumnName("IsYiCiSB")
                    .HasDefaultValueSql("('否')");

                entity.HasOne(d => d.AlertLevelNavigation)
                    .WithMany(p => p.ImEventTypes)
                    .HasForeignKey(d => d.AlertLevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EventType_Level");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImEventTypes)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EventType_PUCtgy");
            });

            modelBuilder.Entity<ImEventType2010>(entity =>
            {
                entity.HasKey(e => new { e.DeviceCtgy, e.EvtCode })
                    .HasName("PK_EventType_2010");

                entity.ToTable("im_EventType_2010");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('级别1')");

                entity.Property(e => e.EvtName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.HasOne(d => d.AlertLevelNavigation)
                    .WithMany(p => p.ImEventType2010s)
                    .HasForeignKey(d => d.AlertLevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EventType2010_Level");

                entity.HasOne(d => d.DeviceCtgyNavigation)
                    .WithMany(p => p.ImEventType2010s)
                    .HasForeignKey(d => d.DeviceCtgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EventType2010_DevCtgy");
            });

            modelBuilder.Entity<ImEventType2010DeviceYktmp>(entity =>
            {
                entity.HasKey(e => e.RecId)
                    .HasName("PK_EventType2010_DeviceYKTmp");

                entity.ToTable("im_EventType2010_DeviceYKTmp");

                entity.HasIndex(e => new { e.DeviceCtgy, e.EvtCode, e.YktmpId }, "UK_EventType2010_DeviceYKTmp")
                    .IsUnique();

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.YktmpId)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("YKTmp_ID");
            });

            modelBuilder.Entity<ImFaultActType>(entity =>
            {
                entity.HasKey(e => new { e.ActCode, e.FaultCode })
                    .HasName("PK_FaultActType");

                entity.ToTable("im_FaultActType");

                entity.Property(e => e.ActName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.FaultCodeNavigation)
                    .WithMany(p => p.ImFaultActTypes)
                    .HasForeignKey(d => d.FaultCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FaultActType_FaultType");
            });

            modelBuilder.Entity<ImFaultActType2010>(entity =>
            {
                entity.HasKey(e => new { e.FltTypeId, e.ActCode })
                    .HasName("PK_FaultActType2010");

                entity.ToTable("im_FaultActType_2010");

                entity.Property(e => e.FltTypeId)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("FltTypeID");

                entity.Property(e => e.ActName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.FltType)
                    .WithMany(p => p.ImFaultActType2010s)
                    .HasForeignKey(d => d.FltTypeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FaultActType_FaultType2010");
            });

            modelBuilder.Entity<ImFaultActType20102>(entity =>
            {
                entity.HasKey(e => new { e.PuctgyCode, e.ActCode })
                    .HasName("PK_FaultActType2010_2");

                entity.ToTable("im_FaultActType_2010_2");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.ActName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImFaultActType20102s)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FaultActType2010_2_PUCtgy");
            });

            modelBuilder.Entity<ImFaultActTypePd>(entity =>
            {
                entity.HasKey(e => new { e.ActCode, e.FaultCode })
                    .HasName("PK_FaultActTypePD");

                entity.ToTable("im_FaultActType_PD");

                entity.Property(e => e.ActName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.FaultCodeNavigation)
                    .WithMany(p => p.ImFaultActTypePds)
                    .HasForeignKey(d => d.FaultCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FaultActTypePD_FaultTypePD");
            });

            modelBuilder.Entity<ImFaultParam>(entity =>
            {
                entity.HasKey(e => e.ParamCode)
                    .HasName("PK_FaultParam");

                entity.ToTable("im_FaultParam");

                entity.Property(e => e.ParamCode).ValueGeneratedNever();

                entity.Property(e => e.ParamName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ParamSym1)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.ParamSym2)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImFaultParamPd>(entity =>
            {
                entity.HasKey(e => new { e.ParamCode, e.FaultCode })
                    .HasName("PK_FaultParam_PD");

                entity.ToTable("im_FaultParam_PD");

                entity.Property(e => e.ParamName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ParamSym1)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.ParamSym2)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.FaultCodeNavigation)
                    .WithMany(p => p.ImFaultParamPds)
                    .HasForeignKey(d => d.FaultCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FaultParamPD_FaultTypePD");
            });

            modelBuilder.Entity<ImFaultReportItem>(entity =>
            {
                entity.HasKey(e => e.ItemName)
                    .HasName("PK_FaultReportItem");

                entity.ToTable("im_FaultReportItem");

                entity.Property(e => e.ItemName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImFaultType>(entity =>
            {
                entity.HasKey(e => e.FaultCode)
                    .HasName("PK_FaultType");

                entity.ToTable("im_FaultType");

                entity.Property(e => e.FaultCode).ValueGeneratedNever();

                entity.Property(e => e.FaultName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImFaultType2010>(entity =>
            {
                entity.ToTable("im_FaultType_2010");

                entity.HasIndex(e => new { e.DeviceCtgy, e.FaultCode }, "UK_FaultType_2010_1")
                    .IsUnique();

                entity.HasIndex(e => new { e.DeviceCtgy, e.FaultName }, "UK_FaultType_2010_2")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.FaultName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.DeviceCtgyNavigation)
                    .WithMany(p => p.ImFaultType2010s)
                    .HasForeignKey(d => d.DeviceCtgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FaultType_2010_DevCtgy");
            });

            modelBuilder.Entity<ImFaultTypePd>(entity =>
            {
                entity.HasKey(e => e.FaultCode)
                    .HasName("PK_FaultType_PD");

                entity.ToTable("im_FaultType_PD");

                entity.Property(e => e.FaultCode).ValueGeneratedNever();

                entity.Property(e => e.FaultName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImFltDistDevGrp>(entity =>
            {
                entity.ToTable("im_FltDistDevGrp");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.GrpName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PrjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PrjID");

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImFltDistDevGrps)
                    .HasForeignKey(d => d.PrjId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FltDistDevGrp_Project");
            });

            modelBuilder.Entity<ImFltDistDevice>(entity =>
            {
                entity.HasKey(e => e.DeviceId)
                    .HasName("PK_FltDistDev");

                entity.ToTable("im_FltDistDevice");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.GrpId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("GrpID");

                entity.Property(e => e.TypeInGrp)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('测距装置')");

                entity.HasOne(d => d.Device)
                    .WithOne(p => p.ImFltDistDevice)
                    .HasForeignKey<ImFltDistDevice>(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FltDistDev_ProtDevice");

                entity.HasOne(d => d.Grp)
                    .WithMany(p => p.ImFltDistDevices)
                    .HasForeignKey(d => d.GrpId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FltDistDev_DevGroup");
            });

            modelBuilder.Entity<ImFltDistParam>(entity =>
            {
                entity.ToTable("im_FltDistParam");

                entity.HasIndex(e => new { e.GrpId, e.ParamType, e.SectionNo, e.NodeNo }, "UK_FltDistParam")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.GrpId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("GrpID");

                entity.Property(e => e.ParamType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.Grp)
                    .WithMany(p => p.ImFltDistParams)
                    .HasForeignKey(d => d.GrpId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FltDistParam_DevGroup");
            });

            modelBuilder.Entity<ImFxcurrAbnormalSet>(entity =>
            {
                entity.HasKey(e => new { e.YcdataId1, e.YcdataId2 })
                    .HasName("PK_FXCurrAbnormal_Set");

                entity.ToTable("im_FXCurrAbnormal_Set");

                entity.Property(e => e.YcdataId1)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("YCDataID1");

                entity.Property(e => e.YcdataId2)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("YCDataID2");

                entity.Property(e => e.DeltaTofYc).HasColumnName("DeltaTOfYC");
            });

            modelBuilder.Entity<ImGateWay>(entity =>
            {
                entity.HasKey(e => e.GateWayId)
                    .HasName("PK_GateWay");

                entity.ToTable("im_GateWay");

                entity.HasIndex(e => new { e.PhyAddr, e.StatCode }, "UK_GateWay")
                    .IsUnique();

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.GateWayName)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.GatewayIp1)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("GatewayIP1");

                entity.Property(e => e.GatewayIp2)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("GatewayIP2");

                entity.Property(e => e.Protocol)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.ImGateWays)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GateWay_Station");
            });

            modelBuilder.Entity<ImGlyph>(entity =>
            {
                entity.ToTable("im_Glyph");

                entity.HasIndex(e => e.DiagId, "IX_imGlyph_DiagID");

                entity.HasIndex(e => e.ParentId, "IX_imGlyph_ParentID");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.DiagId)
                    .HasMaxLength(38)
                    .IsUnicode(false);

                entity.Property(e => e.LinkDiagId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("LinkDiagID");

                entity.Property(e => e.Locked).HasDefaultValueSql("((0))");

                entity.Property(e => e.Name)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ParentId)
                    .HasMaxLength(38)
                    .IsUnicode(false);

                entity.Property(e => e.PwrOnThreshold).HasDefaultValueSql("((0))");

                entity.Property(e => e.Reserved1).IsUnicode(false);

                entity.Property(e => e.Reserved2).IsUnicode(false);

                entity.Property(e => e.ZOrder).HasColumnName("Z_order");
            });

            modelBuilder.Entity<ImGlyphDefProp>(entity =>
            {
                entity.HasKey(e => e.GlyphType)
                    .HasName("PK_imGlyphDefProp");

                entity.ToTable("im_GlyphDefProp");

                entity.Property(e => e.GlyphType).ValueGeneratedNever();

                entity.Property(e => e.GlyphName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImGlyphDevDatum>(entity =>
            {
                entity.HasKey(e => e.GlyphId)
                    .HasName("PK_GlyphDevData");

                entity.ToTable("im_GlyphDevData");

                entity.Property(e => e.GlyphId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("GlyphID");

                entity.Property(e => e.ControlDataId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("ControlDataID");

                entity.Property(e => e.ControlDataName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.MeasureDataId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("MeasureDataID");

                entity.Property(e => e.MeasureDataName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.HasOne(d => d.Glyph)
                    .WithOne(p => p.ImGlyphDevDatum)
                    .HasForeignKey<ImGlyphDevDatum>(d => d.GlyphId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GlyphDevData_Glyph");
            });

            modelBuilder.Entity<ImGlyphHot>(entity =>
            {
                entity.HasKey(e => new { e.GlyphId, e.HotIndex })
                    .HasName("PK_imGlyphHots");

                entity.ToTable("im_GlyphHots");

                entity.Property(e => e.GlyphId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("GlyphID");

                entity.Property(e => e.ConnectedGlyph)
                    .HasMaxLength(38)
                    .IsUnicode(false);

                entity.HasOne(d => d.Glyph)
                    .WithMany(p => p.ImGlyphHots)
                    .HasForeignKey(d => d.GlyphId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_imGlyphHots_Glyph");
            });

            modelBuilder.Entity<ImGrid>(entity =>
            {
                entity.ToTable("im_Grid");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImGrid)
                    .HasForeignKey<ImGrid>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_imGrid_Glyph");
            });

            modelBuilder.Entity<ImLabel>(entity =>
            {
                entity.ToTable("im_Label");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Caption)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.FBold).HasColumnName("F_Bold");

                entity.Property(e => e.FCharSet).HasColumnName("F_CharSet");

                entity.Property(e => e.FColor).HasColumnName("F_Color");

                entity.Property(e => e.FItalic).HasColumnName("F_Italic");

                entity.Property(e => e.FName)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("F_Name");

                entity.Property(e => e.FSize).HasColumnName("F_Size");

                entity.Property(e => e.FStrikeout).HasColumnName("F_Strikeout");

                entity.Property(e => e.FUnderLine).HasColumnName("F_UnderLine");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImLabel)
                    .HasForeignKey<ImLabel>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_imLabel_Glyph");
            });

            modelBuilder.Entity<ImLinkage>(entity =>
            {
                entity.HasKey(e => e.LnkId)
                    .HasName("PK_Linkage");

                entity.ToTable("im_Linkage");

                entity.HasIndex(e => new { e.SrcObjId, e.LnkType }, "UK_Linkage")
                    .IsUnique();

                entity.Property(e => e.LnkId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("LnkID");

                entity.Property(e => e.ActParam)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Action)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.LnkType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Resource)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.SrcObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SrcObjID");

                entity.Property(e => e.SrcObjType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('在用')");
            });

            modelBuilder.Entity<ImManufacturer>(entity =>
            {
                entity.HasKey(e => e.ManuCode)
                    .HasName("PK_Manufacturer");

                entity.ToTable("im_Manufacturer");

                entity.Property(e => e.ManuCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ManuName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImNoticeBoard>(entity =>
            {
                entity.HasKey(e => e.NbId)
                    .HasName("PK_NoticeBoard");

                entity.ToTable("im_NoticeBoard");

                entity.HasIndex(e => e.YkId, "UK_NoticeBoard_YK")
                    .IsUnique();

                entity.Property(e => e.NbId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("NB_ID");

                entity.Property(e => e.Description)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ExecuteTime).HasColumnType("datetime");

                entity.Property(e => e.OperUserName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.OperWholeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.YkId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("YK_ID");

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.ImNoticeBoards)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_NB_Station");

                entity.HasOne(d => d.Yk)
                    .WithOne(p => p.ImNoticeBoard)
                    .HasForeignKey<ImNoticeBoard>(d => d.YkId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_NB_DeviceYK");
            });

            modelBuilder.Entity<ImNotifyTask>(entity =>
            {
                entity.HasKey(e => e.TaskId);

                entity.ToTable("im_NotifyTask");

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.TaskDesc)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.HasOne(d => d.Task)
                    .WithOne(p => p.ImNotifyTask)
                    .HasForeignKey<ImNotifyTask>(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_IMNotify_DFrameNotify");
            });

            modelBuilder.Entity<ImNotifyTaskAlertObj>(entity =>
            {
                entity.ToTable("im_NotifyTaskAlertObj");

                entity.Property(e => e.Id)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.AlertObjId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("AlertObjID");

                entity.Property(e => e.AlertObjName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.HasOne(d => d.Task)
                    .WithMany(p => p.ImNotifyTaskAlertObjs)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AlertObj_Notify");
            });

            modelBuilder.Entity<ImNotifyTaskPhone>(entity =>
            {
                entity.ToTable("im_NotifyTaskPhone");

                entity.Property(e => e.Id)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.FullName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.PhoneNo)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.HasOne(d => d.Task)
                    .WithMany(p => p.ImNotifyTaskPhones)
                    .HasForeignKey(d => d.TaskId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Phone_Notify");
            });

            modelBuilder.Entity<ImNotifyTaskTemplate>(entity =>
            {
                entity.HasKey(e => e.AlertType)
                    .HasName("PK_NotifyTaskTemplate");

                entity.ToTable("im_NotifyTaskTemplate");

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.BusiTabName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PkfldName)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("PKFldName");

                entity.Property(e => e.Sql4msg)
                    .HasMaxLength(1000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Msg");

                entity.Property(e => e.Sql4notify)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Notify");

                entity.Property(e => e.Sql4phoneNo)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4PhoneNo");
            });

            modelBuilder.Entity<ImOnlineAnalysis>(entity =>
            {
                entity.ToTable("im_OnlineAnalysis");

                entity.HasIndex(e => new { e.Method, e.DataIdofC2h2, e.DataIdofC2h4, e.DataIdofC2h6, e.DataIdofCh4, e.DataIdofH2 }, "UK_OnlineAnalysis")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.DataIdofC2h2)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofC2H2");

                entity.Property(e => e.DataIdofC2h4)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofC2H4");

                entity.Property(e => e.DataIdofC2h6)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofC2H6");

                entity.Property(e => e.DataIdofCh4)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofCH4");

                entity.Property(e => e.DataIdofH2)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofH2");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.IsAlert)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.Method)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('在用')");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImOnlineAnalyses)
                    .HasForeignKey(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OnlineAnalysis_Device");
            });

            modelBuilder.Entity<ImProgControl>(entity =>
            {
                entity.ToTable("im_ProgControl");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.PrjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PrjID");

                entity.Property(e => e.ProgCtlName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImProgControls)
                    .HasForeignKey(d => d.PrjId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProgControl_Project");
            });

            modelBuilder.Entity<ImProgControlItem>(entity =>
            {
                entity.ToTable("im_ProgControlItem");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.ProgCtlId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ProgCtlID");

                entity.Property(e => e.WaitYxtimeout).HasColumnName("WaitYXTimeout");

                entity.Property(e => e.YkdataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("YKDataID");

                entity.Property(e => e.Ykstate).HasColumnName("YKState");

                entity.HasOne(d => d.ProgCtl)
                    .WithMany(p => p.ImProgControlItems)
                    .HasForeignKey(d => d.ProgCtlId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PCItem_ProgControl");

                entity.HasOne(d => d.Ykdata)
                    .WithMany(p => p.ImProgControlItems)
                    .HasForeignKey(d => d.YkdataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PCItem_DeviceYK");
            });

            modelBuilder.Entity<ImProject>(entity =>
            {
                entity.ToTable("im_Project");

                entity.HasIndex(e => e.Name, "IX_Project_Name")
                    .IsUnique();

                entity.HasIndex(e => e.Name, "UK_Project")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.Creator)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Name)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ProtCommMgrIp)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasColumnName("ProtCommMgrIP");
            });

            modelBuilder.Entity<ImProtectDevice>(entity =>
            {
                entity.HasKey(e => e.DeviceId)
                    .HasName("PK_ProtDevice");

                entity.ToTable("im_ProtectDevice");

                entity.HasIndex(e => new { e.DeviceAddr, e.GateWayId }, "UK_ProtDevice")
                    .IsUnique();

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.BayName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CanSwDzzone).HasColumnName("CanSwDZZone");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceType)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('综自设备')");

                entity.Property(e => e.EndOfDkjl).HasColumnName("EndOfDKJL");

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.StartOfDkjl).HasColumnName("StartOfDKJL");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.Support12yc).HasColumnName("Support1_2YC");

                entity.Property(e => e.SupportDkjl).HasColumnName("SupportDKJL");

                entity.HasOne(d => d.GateWay)
                    .WithMany(p => p.ImProtectDevices)
                    .HasForeignKey(d => d.GateWayId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProtDevice_GateWay");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImProtectDevices)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProtDevice_PUCtgy");

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.ImProtectDevices)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProtDevice_Station");
            });

            modelBuilder.Entity<ImProtectDeviceTmp>(entity =>
            {
                entity.ToTable("im_ProtectDevice_Tmp");

                entity.HasIndex(e => e.PuctgyCode, "UK_ProtDevice_Tmp")
                    .IsUnique();

                entity.HasIndex(e => e.DeviceName, "UK_ProtDevice_Tmp_Name")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithOne(p => p.ImProtectDeviceTmp)
                    .HasForeignKey<ImProtectDeviceTmp>(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProtDeviceTmp_PUCtgy");
            });

            modelBuilder.Entity<ImPuCtgy>(entity =>
            {
                entity.HasKey(e => e.PuctgyCode)
                    .HasName("PK_PU_ctgy");

                entity.ToTable("im_PU_Ctgy");

                entity.HasIndex(e => e.PuctgyName, "UK_PU_Ctgy_Name")
                    .IsUnique();

                entity.Property(e => e.PuctgyCode)
                    .ValueGeneratedNever()
                    .HasColumnName("PUCtgyCode");

                entity.Property(e => e.AnalogParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('按装置大类')");

                entity.Property(e => e.CanSwDzzone).HasColumnName("CanSwDZZone");

                entity.Property(e => e.DevCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('牵引')");

                entity.Property(e => e.DzreadOnly).HasColumnName("DZReadOnly");

                entity.Property(e => e.DzzoneCount)
                    .HasColumnName("DZZoneCount")
                    .HasDefaultValueSql("((4))");

                entity.Property(e => e.EndOfDkjl).HasColumnName("EndOfDKJL");

                entity.Property(e => e.EventParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('按装置大类')");

                entity.Property(e => e.Generation)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.IsCrcc)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasColumnName("IsCRCC")
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PuctgyName)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("PUCtgyName");

                entity.Property(e => e.StartOfDkjl).HasColumnName("StartOfDKJL");

                entity.Property(e => e.Support12yc).HasColumnName("Support1_2YC");

                entity.Property(e => e.SupportDkjl).HasColumnName("SupportDKJL");

                entity.Property(e => e.SupportDz)
                    .HasColumnName("SupportDZ")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.SupportFhluBo).HasColumnName("SupportFHLuBo");

                entity.Property(e => e.SupportGuZhangBg)
                    .HasColumnName("SupportGuZhangBG")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.SupportLuBoWj)
                    .HasColumnName("SupportLuBoWJ")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.SupportShiJianBg)
                    .HasColumnName("SupportShiJianBG")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.SupportZiJianBg)
                    .HasColumnName("SupportZiJianBG")
                    .HasDefaultValueSql("((1))");

                entity.HasOne(d => d.DevCtgyNavigation)
                    .WithMany(p => p.ImPuCtgies)
                    .HasForeignKey(d => d.DevCtgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PUCtgy_DevCtgy");

                entity.HasOne(d => d.ManufacturerNavigation)
                    .WithMany(p => p.ImPuCtgies)
                    .HasForeignKey(d => d.Manufacturer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PUCtgy_Manu");
            });

            modelBuilder.Entity<ImPuUpdateLog>(entity =>
            {
                entity.ToTable("im_PU_UpdateLog");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Editor)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.UpdDate)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.UpdDesc).IsUnicode(false);
            });

            modelBuilder.Entity<ImPuWaveChl>(entity =>
            {
                entity.ToTable("im_PU_WaveChl");

                entity.HasIndex(e => new { e.PuctgyCode, e.ChlType, e.ChlNum }, "UK_im_PU_WaveChl")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.ChlName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ChlType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.ChlUnit1)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.ChlUnit2)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImPuWaveChls)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PUWaveChl_PUCtgy");
            });

            modelBuilder.Entity<ImPuctgyFaultType>(entity =>
            {
                entity.ToTable("im_PUCtgy_FaultType");

                entity.HasIndex(e => new { e.PuctgyCode, e.FaultTypeId }, "UK_PUCtgy_FaultType")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.FaultTypeId)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("FaultTypeID");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");
            });

            modelBuilder.Entity<ImPuctgyFltRptItem>(entity =>
            {
                entity.HasKey(e => new { e.PuctgyCode, e.ItemName })
                    .HasName("PK_PUCtgy_FltRptItem");

                entity.ToTable("im_PUCtgy_FltRptItem");

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.ItemName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ShowLabel)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.ItemNameNavigation)
                    .WithMany(p => p.ImPuctgyFltRptItems)
                    .HasForeignKey(d => d.ItemName)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PUCtgy_FltRptItem2");

                entity.HasOne(d => d.PuctgyCodeNavigation)
                    .WithMany(p => p.ImPuctgyFltRptItems)
                    .HasForeignKey(d => d.PuctgyCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PUCtgy_FltRptItem1");
            });

            modelBuilder.Entity<ImReportCfg>(entity =>
            {
                entity.ToTable("im_ReportCfg");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.ExpStartCol).HasDefaultValueSql("((-1))");

                entity.Property(e => e.ExpStartRow).HasDefaultValueSql("((-1))");

                entity.Property(e => e.RptName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.RptType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TemplateName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.RptTypeNavigation)
                    .WithMany(p => p.ImReportCfgs)
                    .HasForeignKey(d => d.RptType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ReportCfg_RptType");
            });

            modelBuilder.Entity<ImReportCfgDatum>(entity =>
            {
                entity.ToTable("im_ReportCfgData");

                entity.HasIndex(e => new { e.RptCfgId, e.DataId }, "UK_ReportCfgData")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.Reserved).IsUnicode(false);

                entity.Property(e => e.RptCfgId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RptCfgID");

                entity.Property(e => e.ShowIntl).HasDefaultValueSql("((0))");

                entity.Property(e => e.Title)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImReportCfgData)
                    .HasForeignKey(d => d.DataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ReportCfgData_DeviceData");

                entity.HasOne(d => d.RptCfg)
                    .WithMany(p => p.ImReportCfgData)
                    .HasForeignKey(d => d.RptCfgId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ReportCfgData_ReportCfg");
            });

            modelBuilder.Entity<ImReportType>(entity =>
            {
                entity.HasKey(e => e.RptTypeCode)
                    .HasName("pk_report_type");

                entity.ToTable("im_Report_Type");

                entity.Property(e => e.RptTypeCode)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RptTypeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImSanBiZhiFaultDefine>(entity =>
            {
                entity.ToTable("im_SanBiZhi_FaultDefine");

                entity.HasIndex(e => new { e.C2h2C2h4, e.Ch4H2, e.C2h4C2h6 }, "UK_FltDefine")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.C2h2C2h4).HasColumnName("C2H2_C2H4");

                entity.Property(e => e.C2h4C2h6).HasColumnName("C2H4_C2H6");

                entity.Property(e => e.Ch4H2).HasColumnName("CH4_H2");

                entity.HasOne(d => d.FltTypeCodeNavigation)
                    .WithMany(p => p.ImSanBiZhiFaultDefines)
                    .HasForeignKey(d => d.FltTypeCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FltDefine_FltType");
            });

            modelBuilder.Entity<ImSanBiZhiFaultType>(entity =>
            {
                entity.HasKey(e => e.FltTypeCode)
                    .HasName("PK_SanBiZhi_FaultType");

                entity.ToTable("im_SanBiZhi_FaultType");

                entity.Property(e => e.FltTypeCode).ValueGeneratedNever();

                entity.Property(e => e.FltTypeExam).IsUnicode(false);

                entity.Property(e => e.FltTypeName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImSheBei>(entity =>
            {
                entity.HasKey(e => e.SheBeiId)
                    .HasName("PK_BianDianSB");

                entity.ToTable("im_SheBei");

                entity.HasIndex(e => e.YunXingBh, "UK_BianDianSB")
                    .IsUnique();

                entity.Property(e => e.SheBeiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiID");

                entity.Property(e => e.AnZhuangRq)
                    .HasColumnType("datetime")
                    .HasColumnName("AnZhuangRQ");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ChuChangRq)
                    .HasColumnType("datetime")
                    .HasColumnName("ChuChangRQ");

                entity.Property(e => e.DaXiuZhouQi)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.GuZiBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GuZiBH");

                entity.Property(e => e.Sblxbm).HasColumnName("SBLXBM");

                entity.Property(e => e.SheBeiBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiBH");

                entity.Property(e => e.SheBeiXh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiXH");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.TouYunRq)
                    .HasColumnType("datetime")
                    .HasColumnName("TouYunRQ");

                entity.Property(e => e.XiaoXiuZhouQi)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.YunXingBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("YunXingBH");

                entity.Property(e => e.ZhiZaoChang)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ZhongXiuZhouQi)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.SblxbmNavigation)
                    .WithMany(p => p.ImSheBeis)
                    .HasForeignKey(d => d.Sblxbm)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BianDianSB_SBLX");

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.ImSheBeis)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BianDianSB_Station");
            });

            modelBuilder.Entity<ImSheBeiDeviceDatum>(entity =>
            {
                entity.HasKey(e => e.RecId)
                    .HasName("PK_SheBeiDeviceData");

                entity.ToTable("im_SheBei_DeviceData");

                entity.HasIndex(e => e.DataId, "UK_SheBeiDeviceData")
                    .IsUnique();

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.SheBeiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiID");

                entity.HasOne(d => d.Data)
                    .WithOne(p => p.ImSheBeiDeviceDatum)
                    .HasForeignKey<ImSheBeiDeviceDatum>(d => d.DataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SBDevData_DevData");

                entity.HasOne(d => d.SheBei)
                    .WithMany(p => p.ImSheBeiDeviceData)
                    .HasForeignKey(d => d.SheBeiId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SBDevData_SheBei");
            });

            modelBuilder.Entity<ImSheBeiLx>(entity =>
            {
                entity.HasKey(e => e.Sblxbm)
                    .HasName("PK_SheBeiLX");

                entity.ToTable("im_SheBeiLX");

                entity.Property(e => e.Sblxbm)
                    .ValueGeneratedNever()
                    .HasColumnName("SBLXBM");

                entity.Property(e => e.Sblxmc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SBLXMC");

                entity.Property(e => e.Sblxxh).HasColumnName("SBLXXH");
            });

            modelBuilder.Entity<ImSheBeiProtDevice>(entity =>
            {
                entity.HasKey(e => e.RecId)
                    .HasName("PK_SheBeiProtDevice");

                entity.ToTable("im_SheBei_ProtDevice");

                entity.HasIndex(e => e.DeviceId, "UK_SheBeiProtDevice")
                    .IsUnique();

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.SheBeiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiID");

                entity.HasOne(d => d.Device)
                    .WithOne(p => p.ImSheBeiProtDevice)
                    .HasForeignKey<ImSheBeiProtDevice>(d => d.DeviceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SBDevice_Device");

                entity.HasOne(d => d.SheBei)
                    .WithMany(p => p.ImSheBeiProtDevices)
                    .HasForeignKey(d => d.SheBeiId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SBDevice_SheBei");
            });

            modelBuilder.Entity<ImSheBeiZt>(entity =>
            {
                entity.HasKey(e => e.Sbztbm)
                    .HasName("PK_SheBeiZT");

                entity.ToTable("im_SheBeiZT");

                entity.Property(e => e.Sbztbm)
                    .ValueGeneratedNever()
                    .HasColumnName("SBZTBM");

                entity.Property(e => e.Sbztmc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SBZTMC");

                entity.Property(e => e.Sbztxh).HasColumnName("SBZTXH");
            });

            modelBuilder.Entity<ImStation>(entity =>
            {
                entity.HasKey(e => e.StatCode)
                    .HasName("PK_Station");

                entity.ToTable("im_Station");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.PrjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PrjID");

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImStations)
                    .HasForeignKey(d => d.PrjId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Station_Project");
            });

            modelBuilder.Entity<ImSwitchState>(entity =>
            {
                entity.HasKey(e => e.SwStateCode)
                    .HasName("PK_SwitchState");

                entity.ToTable("im_SwitchState");

                entity.Property(e => e.SwStateCode).ValueGeneratedNever();

                entity.Property(e => e.SwStateStr)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImTimeSrc>(entity =>
            {
                entity.HasKey(e => e.TimeSrcCode)
                    .HasName("pk_TimeSrc");

                entity.ToTable("im_TimeSrc");

                entity.Property(e => e.TimeSrcCode)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.TimeSrcName)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVDevDataLinkage>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DevDataLinkage");

                entity.Property(e => e.ActParam)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Action)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Domain)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.LnkId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("LnkID");

                entity.Property(e => e.LnkType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Resource)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.SrcObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SrcObjID");

                entity.Property(e => e.SrcObjType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SrcTempDataId)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("SrcTempDataID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(6)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVDeviceDatum>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceData");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Domain)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.SrcTempDataId)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("SrcTempDataID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVDeviceDz>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceDZ");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.CtrlWordTypeId).HasColumnName("CtrlWord_TypeId");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DzCoeff).HasColumnName("DZ_Coeff");

                entity.Property(e => e.DzCoeff1).HasColumnName("DZ_Coeff_1");

                entity.Property(e => e.DzComment)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Comment");

                entity.Property(e => e.DzIndex).HasColumnName("DZ_Index");

                entity.Property(e => e.DzMax).HasColumnName("DZ_Max");

                entity.Property(e => e.DzMin).HasColumnName("DZ_Min");

                entity.Property(e => e.DzName)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Name");

                entity.Property(e => e.DzPrecise).HasColumnName("DZ_Precise");

                entity.Property(e => e.DzPrecise1).HasColumnName("DZ_Precise_1");

                entity.Property(e => e.DzRange)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Range");

                entity.Property(e => e.DzType).HasColumnName("DZ_Type");

                entity.Property(e => e.DzUnit)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit");

                entity.Property(e => e.DzUnit1)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit_1");

                entity.Property(e => e.DzUnitCvtCoeff).HasColumnName("DZ_UnitCvt_Coeff");

                entity.Property(e => e.DztypeName)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("DZTypeName");

                entity.Property(e => e.EnumTypeId).HasColumnName("Enum_TypeId");

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Hidden)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Id)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.RelateCtId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("RelateCT_ID");

                entity.Property(e => e.RelatePtId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("RelatePT_ID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVDeviceYc>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceYC");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.SaveMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Unit2)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YCName");
            });

            modelBuilder.Entity<ImVDeviceYk>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceYK");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.IsResetCmd)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.LockMode)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.LockModeOff)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("LockMode_Off");

                entity.Property(e => e.PreState4Yk).HasColumnName("PreState4YK");

                entity.Property(e => e.PreState4YkOff).HasColumnName("PreState4YK_Off");

                entity.Property(e => e.PreYxId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("PreYX_ID");

                entity.Property(e => e.PreYxIdOff)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("PreYX_ID_Off");

                entity.Property(e => e.RelatedYxId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("RelatedYX_ID");

                entity.Property(e => e.RelatedYxName)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("RelatedYX_Name");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.SwOffStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwOnStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwUncertStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YkType)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("YK_Type");

                entity.Property(e => e.Ykname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YKName");
            });

            modelBuilder.Entity<ImVDeviceYm>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceYM");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Ymname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YMName");
            });

            modelBuilder.Entity<ImVDeviceYx>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceYX");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.Id)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.InfoAddr).HasColumnName("Info_Addr");

                entity.Property(e => e.NormalState)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.SwOffStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwOnStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SwUncertStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YxType)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("YX_Type");

                entity.Property(e => e.Yxname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YXName");
            });

            modelBuilder.Entity<ImVDeviceYxSrcDevice>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DeviceYX_SrcDevice");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevNameOfYx)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("DevNameOfYX");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.SrcDevId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("SrcDevID");

                entity.Property(e => e.SrcDevName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.YxdataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("YXDataID");
            });

            modelBuilder.Entity<ImVDzdataDetail>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DZData_Detail");

                entity.Property(e => e.DzComment)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Comment");

                entity.Property(e => e.DzId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_ID");

                entity.Property(e => e.DzIndex).HasColumnName("DZ_Index");

                entity.Property(e => e.DzUnit)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit");

                entity.Property(e => e.DzValue)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Value");

                entity.Property(e => e.DzdataDetailId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DZDataDetailID");

                entity.Property(e => e.DzdataMainId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DZDataMainID");
            });

            modelBuilder.Entity<ImVDzdataMain>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_DZData_Main");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.DataType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DzdataMainId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DZDataMainID");

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVEventType2010DeviceYktmp>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_EventType2010_DeviceYKTmp");

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.EvtName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.Ykname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("YKName");

                entity.Property(e => e.YktmpId)
                    .HasMaxLength(8)
                    .IsUnicode(false)
                    .HasColumnName("YKTmp_ID");
            });

            modelBuilder.Entity<ImVFaultActType2010>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_FaultActType_2010");

                entity.Property(e => e.ActName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.FaultName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.FltTypeId)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("FltTypeID");
            });

            modelBuilder.Entity<ImVNotifyTask>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_NotifyTask");

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.BusiTabName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.EndTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.PkfldName)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("PKFldName");

                entity.Property(e => e.Sql4msg)
                    .HasMaxLength(1000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Msg");

                entity.Property(e => e.Sql4notify)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Notify");

                entity.Property(e => e.Sql4phoneNo)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4PhoneNo");

                entity.Property(e => e.StartTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TaskDesc)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.Property(e => e.TaskName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVNotifyTaskPhone>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_NotifyTaskPhone");

                entity.Property(e => e.FullName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Id)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.PhoneNo)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.Property(e => e.TaskName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVOnlineAnalysis>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_OnlineAnalysis");

                entity.Property(e => e.C2h2Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("c2h2_YCName");

                entity.Property(e => e.C2h4Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("c2h4_YCName");

                entity.Property(e => e.C2h6Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("c2h6_YCName");

                entity.Property(e => e.Ch4Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("ch4_YCName");

                entity.Property(e => e.DataIdofC2h2)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofC2H2");

                entity.Property(e => e.DataIdofC2h4)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofC2H4");

                entity.Property(e => e.DataIdofC2h6)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofC2H6");

                entity.Property(e => e.DataIdofCh4)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofCH4");

                entity.Property(e => e.DataIdofH2)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DataIDofH2");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.H2Ycname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("h2_YCName");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.IsAlert)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Method)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVProtectDevice>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_ProtectDevice");

                entity.Property(e => e.AnalogParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.BayName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CanSwDzzone).HasColumnName("CanSwDZZone");

                entity.Property(e => e.DevCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.DzreadOnly).HasColumnName("DZReadOnly");

                entity.Property(e => e.DzzoneCount).HasColumnName("DZZoneCount");

                entity.Property(e => e.EndOfDkjl).HasColumnName("EndOfDKJL");

                entity.Property(e => e.EventParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.GateWayId)
                    .HasMaxLength(6)
                    .IsUnicode(false)
                    .HasColumnName("GateWayID");

                entity.Property(e => e.GateWayName)
                    .HasMaxLength(200)
                    .IsUnicode(false);

                entity.Property(e => e.GatewayIp1)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("GatewayIP1");

                entity.Property(e => e.GatewayIp2)
                    .HasMaxLength(15)
                    .IsUnicode(false)
                    .HasColumnName("GatewayIP2");

                entity.Property(e => e.Generation)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.StartOfDkjl).HasColumnName("StartOfDKJL");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Support12yc).HasColumnName("Support1_2YC");

                entity.Property(e => e.SupportDkjl).HasColumnName("SupportDKJL");

                entity.Property(e => e.SupportDz).HasColumnName("SupportDZ");

                entity.Property(e => e.SupportFhluBo).HasColumnName("SupportFHLuBo");

                entity.Property(e => e.SupportGuZhangBg).HasColumnName("SupportGuZhangBG");

                entity.Property(e => e.SupportLuBoWj).HasColumnName("SupportLuBoWJ");

                entity.Property(e => e.SupportShiJianBg).HasColumnName("SupportShiJianBG");

                entity.Property(e => e.SupportZiJianBg).HasColumnName("SupportZiJianBG");
            });

            modelBuilder.Entity<ImVProtectDeviceTmp>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_ProtectDevice_Tmp");

                entity.Property(e => e.AnalogParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.CanSwDzzone).HasColumnName("CanSwDZZone");

                entity.Property(e => e.DevCtgy)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.DzreadOnly).HasColumnName("DZReadOnly");

                entity.Property(e => e.DzzoneCount).HasColumnName("DZZoneCount");

                entity.Property(e => e.EndOfDkjl).HasColumnName("EndOfDKJL");

                entity.Property(e => e.EventParseMode)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Generation)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Id)
                    .HasMaxLength(3)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PuctgyCode).HasColumnName("PUCtgyCode");

                entity.Property(e => e.PuctgyName)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("PUCtgyName");

                entity.Property(e => e.StartOfDkjl).HasColumnName("StartOfDKJL");

                entity.Property(e => e.Support12yc).HasColumnName("Support1_2YC");

                entity.Property(e => e.SupportDkjl).HasColumnName("SupportDKJL");

                entity.Property(e => e.SupportDz).HasColumnName("SupportDZ");

                entity.Property(e => e.SupportFhluBo).HasColumnName("SupportFHLuBo");

                entity.Property(e => e.SupportGuZhangBg).HasColumnName("SupportGuZhangBG");

                entity.Property(e => e.SupportLuBoWj).HasColumnName("SupportLuBoWJ");

                entity.Property(e => e.SupportShiJianBg).HasColumnName("SupportShiJianBG");

                entity.Property(e => e.SupportZiJianBg).HasColumnName("SupportZiJianBG");
            });

            modelBuilder.Entity<ImVSheBei>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_SheBei");

                entity.Property(e => e.AnZhuangRq)
                    .HasColumnType("datetime")
                    .HasColumnName("AnZhuangRQ");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.ChuChangRq)
                    .HasColumnType("datetime")
                    .HasColumnName("ChuChangRQ");

                entity.Property(e => e.DaXiuZhouQi)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.GuZiBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("GuZiBH");

                entity.Property(e => e.Sblxbm).HasColumnName("SBLXBM");

                entity.Property(e => e.Sblxmc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SBLXMC");

                entity.Property(e => e.Sblxxh).HasColumnName("SBLXXH");

                entity.Property(e => e.SheBeiBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiBH");

                entity.Property(e => e.SheBeiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiID");

                entity.Property(e => e.SheBeiXh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiXH");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.TouYunRq)
                    .HasColumnType("datetime")
                    .HasColumnName("TouYunRQ");

                entity.Property(e => e.XiaoXiuZhouQi)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.YunXingBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("YunXingBH");

                entity.Property(e => e.ZhiZaoChang)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ZhongXiuZhouQi)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVSheBeiDeviceDatum>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_SheBei_DeviceData");

                entity.Property(e => e.DataFullName)
                    .HasMaxLength(319)
                    .IsUnicode(false);

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.DataName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DataType)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.SheBeiFullName)
                    .HasMaxLength(152)
                    .IsUnicode(false);

                entity.Property(e => e.SheBeiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiID");

                entity.Property(e => e.SheBeiStatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.YunXingBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("YunXingBH");
            });

            modelBuilder.Entity<ImVSheBeiProtDevice>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_SheBei_ProtDevice");

                entity.Property(e => e.DeviceFullName)
                    .HasMaxLength(152)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.DeviceName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceStatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.SheBeiFullName)
                    .HasMaxLength(152)
                    .IsUnicode(false);

                entity.Property(e => e.SheBeiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("SheBeiID");

                entity.Property(e => e.SheBeiStatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.YunXingBh)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("YunXingBH");
            });

            modelBuilder.Entity<ImVStation4SheBei>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_Station4SheBei");

                entity.Property(e => e.PrjId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PrjID");

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.StatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVariantBool>(entity =>
            {
                entity.HasKey(e => e.VaId)
                    .HasName("PK_Variant_Bool");

                entity.ToTable("im_Variant_Bool");

                entity.HasIndex(e => e.Expr, "UK_Variant_Bool_Expr")
                    .IsUnique();

                entity.HasIndex(e => new { e.StatCode, e.Vaname }, "UK_Variant_Bool_Name")
                    .IsUnique();

                entity.Property(e => e.VaId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("VA_ID");

                entity.Property(e => e.AlertMsg)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('不报警')");

                entity.Property(e => e.Expr)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.ExprDataIds)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("ExprDataIDs");

                entity.Property(e => e.ExprDesc)
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.FalseStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.IsFibre)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.Memo)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(4)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(6)
                    .IsUnicode(false);

                entity.Property(e => e.TrueStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Vaname)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("VAName");

                entity.HasOne(d => d.StatCodeNavigation)
                    .WithMany(p => p.ImVariantBools)
                    .HasForeignKey(d => d.StatCode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VABool_Station");
            });

            modelBuilder.Entity<ImVariantBoolState>(entity =>
            {
                entity.HasKey(e => e.VarBoolStateCode)
                    .HasName("PK_VariantBoolState");

                entity.ToTable("im_VariantBoolState");

                entity.Property(e => e.VarBoolStateCode).ValueGeneratedNever();

                entity.Property(e => e.VarBoolStateStr)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVersion>(entity =>
            {
                entity.HasKey(e => e.LastModified);

                entity.ToTable("im_Version");

                entity.Property(e => e.LastModified)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Desc)
                    .HasMaxLength(255)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImVersionTypeEnum2010>(entity =>
            {
                entity.HasKey(e => e.EnumIndex)
                    .HasName("PK_VersionTypeEnum2010");

                entity.ToTable("im_VersionTypeEnum_2010");

                entity.Property(e => e.EnumIndex).ValueGeneratedNever();

                entity.Property(e => e.EnumName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImWatchdog>(entity =>
            {
                entity.HasKey(e => e.ExeName)
                    .HasName("PK_Watchdog");

                entity.ToTable("im_Watchdog");

                entity.Property(e => e.ExeName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LastTime).HasColumnType("datetime");
            });

            modelBuilder.Entity<ImYkType>(entity =>
            {
                entity.HasKey(e => e.TypeCode)
                    .HasName("pk_YK_Type");

                entity.ToTable("im_YK_Type");

                entity.Property(e => e.TypeCode)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TypeName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImYxType>(entity =>
            {
                entity.HasKey(e => e.TypeCode)
                    .HasName("pk_YXtype");

                entity.ToTable("im_YX_Type");

                entity.Property(e => e.TypeCode)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TypeName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbAccessController>(entity =>
            {
                entity.HasKey(e => e.CtlerId)
                    .HasName("PK_AccessCtler");

                entity.ToTable("tb_AccessController");

                entity.Property(e => e.CtlerId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("CtlerID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.CtlerName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.CtlerType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.DoorNames)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.NetAddr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Passowrd)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbAidPlatform>(entity =>
            {
                entity.HasKey(e => e.PlatId)
                    .HasName("PK_AidPlatform");

                entity.ToTable("tb_AidPlatform");

                entity.HasIndex(e => e.PlatName, "PK_AidPlatform_Name")
                    .IsUnique();

                entity.Property(e => e.PlatId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PlatID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Ip)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("IP");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PassEnc)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PlatImpl)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PlatName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.PlatType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbAidVideoChannel>(entity =>
            {
                entity.HasKey(e => e.ChanId)
                    .HasName("PK_AidVideoChan");

                entity.ToTable("tb_AidVideoChannel");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.ChanName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ChanType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.IdinPlat)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("IDinPlat");

                entity.Property(e => e.IsPtz)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("IsPTZ");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.Dev)
                    .WithMany(p => p.TbAidVideoChannels)
                    .HasForeignKey(d => d.DevId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AidVideoChan_Dev");
            });

            modelBuilder.Entity<TbAidVideoDev>(entity =>
            {
                entity.HasKey(e => e.DevId)
                    .HasName("PK_AidVideoDev");

                entity.ToTable("tb_AidVideoDev");

                entity.HasIndex(e => e.DevName, "UK_AidVideoDev_Name")
                    .IsUnique();

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Ip)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("IP");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PassEnc)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PlatId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PlatID");

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.HasOne(d => d.Plat)
                    .WithMany(p => p.TbAidVideoDevs)
                    .HasForeignKey(d => d.PlatId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AidVideoDev_Plat");
            });

            modelBuilder.Entity<TbAttachment>(entity =>
            {
                entity.ToTable("tb_Attachment");

                entity.HasIndex(e => e.DuiXiangBm, "IND_Attachment_DXBM");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DuiXiangBm)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("DuiXiangBM");

                entity.Property(e => e.FaBuRen)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.FaBuSj)
                    .HasColumnType("datetime")
                    .HasColumnName("FaBuSJ");

                entity.Property(e => e.FuJianMc)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("FuJianMC");

                entity.Property(e => e.WenJianCd).HasColumnName("WenJianCD");

                entity.Property(e => e.WenJianLj)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("WenJianLJ");

                entity.Property(e => e.WenJianLx)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasColumnName("WenJianLX");
            });

            modelBuilder.Entity<TbBuMan>(entity =>
            {
                entity.ToTable("tb_BuMen");

                entity.HasIndex(e => e.BuMenMc, "UK_BuMen")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Bmlxbm)
                    .HasMaxLength(2)
                    .IsUnicode(false)
                    .HasColumnName("BMLXBM");

                entity.Property(e => e.BuMenBm)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BuMenBM");

                entity.Property(e => e.BuMenMc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BuMenMC");

                entity.HasOne(d => d.BmlxbmNavigation)
                    .WithMany(p => p.TbBuMen)
                    .HasForeignKey(d => d.Bmlxbm)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BM_BMLX");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.TbBuMan)
                    .HasForeignKey<TbBuMan>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BM_TreeNode");
            });

            modelBuilder.Entity<TbBuMenLx>(entity =>
            {
                entity.HasKey(e => e.Bmlxbm)
                    .HasName("PK_BuMenLX");

                entity.ToTable("tb_BuMenLX");

                entity.HasIndex(e => e.Bmlxmc, "UK_BuMenLX")
                    .IsUnique();

                entity.Property(e => e.Bmlxbm)
                    .HasMaxLength(2)
                    .IsUnicode(false)
                    .HasColumnName("BMLXBM");

                entity.Property(e => e.Bmlxmc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BMLXMC");
            });

            modelBuilder.Entity<TbBusiHistory>(entity =>
            {
                entity.ToTable("tb_BusiHistory");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.BusiId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("BusiID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.OperDesc)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.OperTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.OperUser)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WfStepId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("WfStepID");

                entity.HasOne(d => d.Busi)
                    .WithMany(p => p.TbBusiHistories)
                    .HasForeignKey(d => d.BusiId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BusiHis_Busi");

                entity.HasOne(d => d.WfStep)
                    .WithMany(p => p.TbBusiHistories)
                    .HasForeignKey(d => d.WfStepId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BusiHis_WfStep");
            });

            modelBuilder.Entity<TbBusiness>(entity =>
            {
                entity.ToTable("tb_Business");

                entity.HasIndex(e => new { e.WfId, e.BusiName }, "UK_Business")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.ApplyTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ApplyUser)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BusiName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.WfId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("WfID");

                entity.Property(e => e.WfStepId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("WfStepID");

                entity.HasOne(d => d.Wf)
                    .WithMany(p => p.TbBusinesses)
                    .HasForeignKey(d => d.WfId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Business_Wf");

                entity.HasOne(d => d.WfStep)
                    .WithMany(p => p.TbBusinesses)
                    .HasForeignKey(d => d.WfStepId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Business_WfStep");
            });

            modelBuilder.Entity<TbDaTask>(entity =>
            {
                entity.HasKey(e => e.TaskId)
                    .HasName("PK_DA_Task");

                entity.ToTable("tb_DA_Task");

                entity.HasIndex(e => e.TaskName, "UK_DATaskName")
                    .IsUnique();

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EndTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.Memo)
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.Sql4acquisition)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Acquisition");

                entity.Property(e => e.Sql4record)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Record");

                entity.Property(e => e.SrcDatabase)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SrcServer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SrcTable)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StartTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TaskName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbDataModiHi>(entity =>
            {
                entity.ToTable("tb_DataModiHis");

                entity.HasIndex(e => e.DstObjId, "Ind_DataModiHis");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Content)
                    .HasMaxLength(4000)
                    .IsUnicode(false);

                entity.Property(e => e.DstObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("DstObjID");

                entity.Property(e => e.DstObjName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DstTable)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.EditTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Editor)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Type)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbDataViewer>(entity =>
            {
                entity.ToTable("tb_DataViewer");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.NameFontName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ValueFontName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.TbDataViewer)
                    .HasForeignKey<TbDataViewer>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_tb_DataViewer_Glyph");
            });

            modelBuilder.Entity<TbDataViewerBusiObj>(entity =>
            {
                entity.HasKey(e => new { e.DataViewerId, e.BusiObjId })
                    .HasName("PK_DataViewerBusiObj");

                entity.ToTable("tb_DataViewer_BusiObj");

                entity.Property(e => e.DataViewerId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DataViewerID");

                entity.Property(e => e.BusiObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BusiObjID");

                entity.Property(e => e.BusiObjName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.HasOne(d => d.DataViewer)
                    .WithMany(p => p.TbDataViewerBusiObjs)
                    .HasForeignKey(d => d.DataViewerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DataViewerBusiObj_DataViewer");
            });

            modelBuilder.Entity<TbDframeRevision>(entity =>
            {
                entity.HasKey(e => e.OccurTime);

                entity.ToTable("tb_DFrameRevision");

                entity.Property(e => e.OccurTime).HasColumnType("datetime");

                entity.Property(e => e.Content)
                    .HasMaxLength(255)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbDiagram>(entity =>
            {
                entity.ToTable("tb_Diagram");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.Creator)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.TbDiagram)
                    .HasForeignKey<TbDiagram>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Diagram_Glyph");
            });

            modelBuilder.Entity<TbEnvVar>(entity =>
            {
                entity.HasKey(e => e.EnvVarName)
                    .HasName("PK_EnvVar");

                entity.ToTable("tb_EnvVar");

                entity.Property(e => e.EnvVarName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.EnvVarUnit)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.EnvVarValue)
                    .HasMaxLength(255)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbExportTemplate>(entity =>
            {
                entity.HasKey(e => e.TempId)
                    .HasName("PK_ExportTemplate");

                entity.ToTable("tb_ExportTemplate");

                entity.HasIndex(e => e.TempFileName, "UK_ExportTemplate")
                    .IsUnique();

                entity.Property(e => e.TempId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("TempID");

                entity.Property(e => e.DataTag4Filter)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DefFileName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.FilterCond)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.FilterValue)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.FilterValueType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.RelateDir)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.TempFileName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbExportTemplateCol>(entity =>
            {
                entity.ToTable("tb_ExportTemplateCol");

                entity.HasIndex(e => new { e.TempId, e.ColSeqNo }, "UK_ExpTempCol")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.DataTag)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Expression)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.TempId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("TempID");

                entity.HasOne(d => d.Temp)
                    .WithMany(p => p.TbExportTemplateCols)
                    .HasForeignKey(d => d.TempId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ExpTempCol_Temp");
            });

            modelBuilder.Entity<TbField>(entity =>
            {
                entity.HasKey(e => e.FieldId)
                    .HasName("PK_Field");

                entity.ToTable("tb_Field");

                entity.HasIndex(e => new { e.FieldName, e.TableId }, "UK_Field")
                    .IsUnique();

                entity.Property(e => e.FieldId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("FieldID");

                entity.Property(e => e.CheckValue)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ColorMode)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DefaultValue)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.FieldName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.FieldType)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('Str')");

                entity.Property(e => e.FormatStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.GroupName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Hint)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Prcname)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("PRCName");

                entity.Property(e => e.ReadOnly).HasDefaultValueSql("((0))");

                entity.Property(e => e.RefCodeField)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.RefNameField)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.RefTable)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.RefTableAlias)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ShowOnAdd).HasDefaultValueSql("((1))");

                entity.Property(e => e.ShowOnBrw).HasDefaultValueSql("((1))");

                entity.Property(e => e.ShowOnEdit).HasDefaultValueSql("((1))");

                entity.Property(e => e.ShowOnQry).HasDefaultValueSql("((1))");

                entity.Property(e => e.TableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TableID");

                entity.HasOne(d => d.Table)
                    .WithMany(p => p.TbFields)
                    .HasForeignKey(d => d.TableId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Field_Table");
            });

            modelBuilder.Entity<TbGlyph>(entity =>
            {
                entity.ToTable("tb_Glyph");

                entity.HasIndex(e => e.DiagId, "IX_Glyph_DiagID");

                entity.HasIndex(e => e.ParentId, "IX_Glyph_ParentID");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.BusiObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BusiObjID");

                entity.Property(e => e.BusiObjName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.DiagId)
                    .HasMaxLength(38)
                    .IsUnicode(false);

                entity.Property(e => e.Hide).HasDefaultValueSql("((0))");

                entity.Property(e => e.Locked).HasDefaultValueSql("((0))");

                entity.Property(e => e.Name)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ParentId)
                    .HasMaxLength(38)
                    .IsUnicode(false);

                entity.Property(e => e.Reserved1)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Reserved2)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ZOrder).HasColumnName("Z_order");
            });

            modelBuilder.Entity<TbGlyphHot>(entity =>
            {
                entity.HasKey(e => new { e.GlyphId, e.HotIndex })
                    .HasName("PK_GlyphHots");

                entity.ToTable("tb_GlyphHots");

                entity.Property(e => e.GlyphId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("GlyphID");

                entity.Property(e => e.ConnectedGlyph)
                    .HasMaxLength(38)
                    .IsUnicode(false);

                entity.HasOne(d => d.Glyph)
                    .WithMany(p => p.TbGlyphHots)
                    .HasForeignKey(d => d.GlyphId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GlyphHots_Glyph");
            });

            modelBuilder.Entity<TbGlyphLinkObj>(entity =>
            {
                entity.ToTable("tb_GlyphLinkObj");

                entity.HasIndex(e => new { e.GlyphId, e.LinkObjName, e.LinkObjType }, "UK_GlyphLinkObj")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.GlyphId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("GlyphID");

                entity.Property(e => e.LinkObjName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.LinkObjParam)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.LinkObjPortal)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.LinkObjPos)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.LinkObjType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.HasOne(d => d.Glyph)
                    .WithMany(p => p.TbGlyphLinkObjs)
                    .HasForeignKey(d => d.GlyphId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GlyphLinkObj_Glyph");
            });

            modelBuilder.Entity<TbGrid>(entity =>
            {
                entity.ToTable("tb_Grid");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.TbGrid)
                    .HasForeignKey<TbGrid>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Grid_Glyph");
            });

            modelBuilder.Entity<TbImage>(entity =>
            {
                entity.HasKey(e => e.ObjId)
                    .HasName("PK_Image");

                entity.ToTable("tb_Image");

                entity.Property(e => e.ObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("ObjID");

                entity.Property(e => e.Data).HasColumnType("image");

                entity.Property(e => e.Format)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("Format_");
            });

            modelBuilder.Entity<TbLabel>(entity =>
            {
                entity.ToTable("tb_Label");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Caption)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.FBold).HasColumnName("F_Bold");

                entity.Property(e => e.FCharSet).HasColumnName("F_CharSet");

                entity.Property(e => e.FColor).HasColumnName("F_Color");

                entity.Property(e => e.FItalic).HasColumnName("F_Italic");

                entity.Property(e => e.FName)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("F_Name");

                entity.Property(e => e.FSize).HasColumnName("F_Size");

                entity.Property(e => e.FStrikeout).HasColumnName("F_Strikeout");

                entity.Property(e => e.FUnderLine).HasColumnName("F_UnderLine");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.TbLabel)
                    .HasForeignKey<TbLabel>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Label_Glyph");
            });

            modelBuilder.Entity<TbNotifySent>(entity =>
            {
                entity.HasKey(e => new { e.BusiTabName, e.PkfldValue })
                    .HasName("PK_NotifySent");

                entity.ToTable("tb_NotifySent");

                entity.Property(e => e.BusiTabName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PkfldValue)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("PKFldValue");

                entity.Property(e => e.SentTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<TbNotifyTask>(entity =>
            {
                entity.HasKey(e => e.TaskId)
                    .HasName("PK_Notify");

                entity.ToTable("tb_NotifyTask");

                entity.HasIndex(e => e.TaskName, "UK_Notify_Name")
                    .IsUnique();

                entity.Property(e => e.TaskId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TaskID");

                entity.Property(e => e.BusiTabName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.EndTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.PkfldName)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("PKFldName");

                entity.Property(e => e.Sql4msg)
                    .HasMaxLength(1000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Msg");

                entity.Property(e => e.Sql4notify)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4Notify");

                entity.Property(e => e.Sql4phoneNo)
                    .HasMaxLength(2000)
                    .IsUnicode(false)
                    .HasColumnName("SQL4PhoneNo");

                entity.Property(e => e.StartTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TaskName)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbPlanTask>(entity =>
            {
                entity.HasKey(e => e.BusiObjId)
                    .HasName("PK_PlanTask");

                entity.ToTable("tb_PlanTask");

                entity.HasIndex(e => new { e.BusiObjType, e.BusiObjName }, "UK_PlanTask_BusiObjName")
                    .IsUnique();

                entity.Property(e => e.BusiObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BusiObjID");

                entity.Property(e => e.BusiObjName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.BusiObjType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbPlanTaskItem>(entity =>
            {
                entity.ToTable("tb_PlanTaskItem");

                entity.HasIndex(e => new { e.BusiObjId, e.DayOfWeek, e.ExecTime }, "UK_PlanTaskItem")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.BusiObjId)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("BusiObjID");

                entity.Property(e => e.DayOfWeek)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasColumnName("DayOfWeek_");

                entity.Property(e => e.ExecTime)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasOne(d => d.BusiObj)
                    .WithMany(p => p.TbPlanTaskItems)
                    .HasForeignKey(d => d.BusiObjId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Item_PlanTask");
            });

            modelBuilder.Entity<TbPresetPoint>(entity =>
            {
                entity.HasKey(e => e.PointId)
                    .HasName("PK_PresetPoint");

                entity.ToTable("tb_PresetPoint");

                entity.HasIndex(e => new { e.DevId, e.Number }, "UK_Point_Number")
                    .IsUnique();

                entity.Property(e => e.PointId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PointID");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.HasOne(d => d.Chan)
                    .WithMany(p => p.TbPresetPoints)
                    .HasForeignKey(d => d.ChanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Point_Chan");

                entity.HasOne(d => d.Dev)
                    .WithMany(p => p.TbPresetPoints)
                    .HasForeignKey(d => d.DevId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Point_Dev");
            });

            modelBuilder.Entity<TbQuanXian>(entity =>
            {
                entity.HasKey(e => new { e.YongHuMing, e.MoKuaiBianHao })
                    .HasName("PK_QuanXian");

                entity.ToTable("tb_QuanXian");

                entity.Property(e => e.YongHuMing)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.MoKuaiBianHao)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.HasOne(d => d.YongHuMingNavigation)
                    .WithMany(p => p.TbQuanXians)
                    .HasForeignKey(d => d.YongHuMing)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_QuanXian_YongHu");
            });

            modelBuilder.Entity<TbSession>(entity =>
            {
                entity.HasKey(e => e.SessionId)
                    .HasName("PK_Session");

                entity.ToTable("tb_Session");

                entity.Property(e => e.SessionId)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ClientIp)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("ClientIP");

                entity.Property(e => e.LastTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.SoftInfo)
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbSm>(entity =>
            {
                entity.HasKey(e => e.MsgId)
                    .HasName("PK_SMS");

                entity.ToTable("tb_SMS");

                entity.Property(e => e.MsgId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("MsgID");

                entity.Property(e => e.Content)
                    .HasMaxLength(1000)
                    .IsUnicode(false);

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");
            });

            modelBuilder.Entity<TbSmsSent>(entity =>
            {
                entity.ToTable("tb_SMS_Sent");

                entity.Property(e => e.Id)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("ID")
                    .HasDefaultValueSql("(newid())");

                entity.Property(e => e.FullName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.IsSent)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.MsgId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("MsgID");

                entity.Property(e => e.PhoneNo)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.SentTime).HasColumnType("datetime");

                entity.HasOne(d => d.Msg)
                    .WithMany(p => p.TbSmsSents)
                    .HasForeignKey(d => d.MsgId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SMSSent_SMS");
            });

            modelBuilder.Entity<TbTable>(entity =>
            {
                entity.HasKey(e => e.TableId)
                    .HasName("PK_Table");

                entity.ToTable("tb_Table");

                entity.HasIndex(e => e.TableName, "UK_Table")
                    .IsUnique();

                entity.Property(e => e.TableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TableID");

                entity.Property(e => e.Ctgy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DbconnName)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DBConnName");

                entity.Property(e => e.IsView)
                    .HasMaxLength(4)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('否')");

                entity.Property(e => e.OrderBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Prcname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("PRCName");

                entity.Property(e => e.TableName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.TblName4View)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbTableDataDup>(entity =>
            {
                entity.HasKey(e => e.DataDupId)
                    .HasName("PK_TableDataDup");

                entity.ToTable("tb_TableDataDup");

                entity.HasIndex(e => new { e.DataDupCtgy, e.TableMapId }, "UK_TableDataDup")
                    .IsUnique();

                entity.Property(e => e.DataDupId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DataDupID");

                entity.Property(e => e.DataDupCtgy)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SrcWhereClause)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.TableMapId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TableMapID");

                entity.HasOne(d => d.TableMap)
                    .WithMany(p => p.TbTableDataDups)
                    .HasForeignKey(d => d.TableMapId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DataDup_TableMap");
            });

            modelBuilder.Entity<TbTableMap>(entity =>
            {
                entity.HasKey(e => e.TableMapId)
                    .HasName("PK_TableMap");

                entity.ToTable("tb_TableMap");

                entity.HasIndex(e => new { e.SrcTableId, e.DstTableId, e.MapCtgy }, "UK_TableMap")
                    .IsUnique();

                entity.Property(e => e.TableMapId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TableMapID");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DstTableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DstTableID");

                entity.Property(e => e.MapCtgy)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SrcTableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("SrcTableID");

                entity.HasOne(d => d.DstTable)
                    .WithMany(p => p.TbTableMapDstTables)
                    .HasForeignKey(d => d.DstTableId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DstTable_Table");

                entity.HasOne(d => d.SrcTable)
                    .WithMany(p => p.TbTableMapSrcTables)
                    .HasForeignKey(d => d.SrcTableId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SrcTable_Table");
            });

            modelBuilder.Entity<TbTreeNode>(entity =>
            {
                entity.HasKey(e => e.NodeId)
                    .HasName("PK_TreeNode");

                entity.ToTable("tb_TreeNode");

                entity.Property(e => e.NodeId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("NodeID");

                entity.Property(e => e.Ctgy)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.Desc)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("Desc_");

                entity.Property(e => e.NodeName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ParentId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ParentID");
            });

            modelBuilder.Entity<TbUserCondition>(entity =>
            {
                entity.ToTable("tb_UserCondition");

                entity.HasIndex(e => new { e.YongHuMing, e.TiaoJianLx, e.TiaoJianMc }, "UK_UserCondition")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.MiaoShu)
                    .HasMaxLength(3000)
                    .IsUnicode(false);

                entity.Property(e => e.ShiJian).HasColumnType("datetime");

                entity.Property(e => e.TiaoJian)
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.TiaoJianLx)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("TiaoJianLX");

                entity.Property(e => e.TiaoJianMc)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("TiaoJianMC");

                entity.Property(e => e.YongHuMing)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbVideoChannelRight>(entity =>
            {
                entity.HasKey(e => e.RecId)
                    .HasName("PK_VideoChannelRight");

                entity.ToTable("tb_VideoChannelRight");

                entity.HasIndex(e => new { e.YongHuMing, e.ChanId, e.RightName }, "UK_VideoChannelRight")
                    .IsUnique();

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.RightName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YongHuMing)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.Chan)
                    .WithMany(p => p.TbVideoChannelRights)
                    .HasForeignKey(d => d.ChanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChanRight_Chan");

                entity.HasOne(d => d.YongHuMingNavigation)
                    .WithMany(p => p.TbVideoChannelRights)
                    .HasForeignKey(d => d.YongHuMing)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChanRight_YongHu");
            });

            modelBuilder.Entity<TbVirtualTable>(entity =>
            {
                entity.HasKey(e => e.TableName)
                    .HasName("PK_VirtualTable");

                entity.ToTable("tb_VirtualTable");

                entity.HasIndex(e => new { e.BaseTableId, e.DeriveTableId }, "UK_VTable")
                    .IsUnique();

                entity.Property(e => e.TableName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.BaseTableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("BaseTableID");

                entity.Property(e => e.CtgyFldInBaseTable)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.CtgyInBaseTable)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.DeriveTableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DeriveTableID");

                entity.Property(e => e.OrderBy)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.HasOne(d => d.BaseTable)
                    .WithMany(p => p.TbVirtualTableBaseTables)
                    .HasForeignKey(d => d.BaseTableId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VirtualTable_Table1");

                entity.HasOne(d => d.DeriveTable)
                    .WithMany(p => p.TbVirtualTableDeriveTables)
                    .HasForeignKey(d => d.DeriveTableId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_VirtualTable_Table2");
            });

            modelBuilder.Entity<TbWfStep>(entity =>
            {
                entity.ToTable("tb_WfStep");

                entity.HasIndex(e => new { e.WfId, e.StepName }, "UK_WfStep1")
                    .IsUnique();

                entity.HasIndex(e => new { e.WfId, e.SeqNo }, "UK_WfStep2")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Dllfunc)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DLLFunc");

                entity.Property(e => e.Dllname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DLLName");

                entity.Property(e => e.StatName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.StepName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.WfId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("WfID");

                entity.HasOne(d => d.Wf)
                    .WithMany(p => p.TbWfSteps)
                    .HasForeignKey(d => d.WfId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WfStep_Wf");
            });

            modelBuilder.Entity<TbWfStepUser>(entity =>
            {
                entity.HasKey(e => e.WfStepId)
                    .HasName("PK_WfStepUser");

                entity.ToTable("tb_WfStepUser");

                entity.Property(e => e.WfStepId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("WfStepID");

                entity.Property(e => e.UserName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.HasOne(d => d.WfStep)
                    .WithOne(p => p.TbWfStepUser)
                    .HasForeignKey<TbWfStepUser>(d => d.WfStepId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WfStepUser_WfStep");
            });

            modelBuilder.Entity<TbWorkflow>(entity =>
            {
                entity.ToTable("tb_Workflow");

                entity.HasIndex(e => e.Name, "UK_Workflow")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.Name)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbXiTongMoKuai>(entity =>
            {
                entity.HasKey(e => e.MoKuaiBianHao)
                    .HasName("PK_XiTongMoKuai");

                entity.ToTable("tb_XiTongMoKuai");

                entity.HasIndex(e => new { e.FuMoKuai, e.MoKuaiXuHao }, "UK_FoMoKuai_MoKuaiXuHao")
                    .IsUnique();

                entity.Property(e => e.MoKuaiBianHao)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.FuMoKuai)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.MoKuaiLeiXing)
                    .HasMaxLength(1)
                    .IsUnicode(false);

                entity.Property(e => e.MoKuaiLuJing)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.MoKuaiMc)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("MoKuaiMC");

                entity.Property(e => e.MoKuaiMiaoShu)
                    .HasMaxLength(100)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbXiTongRiZhi>(entity =>
            {
                entity.ToTable("tb_XiTongRiZhi");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.FaShengRiQi)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.FaShengShiJian)
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.MiaoShu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.YongHu)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbYongHu>(entity =>
            {
                entity.HasKey(e => e.YongHuMing)
                    .HasName("PK_YongHu");

                entity.ToTable("tb_YongHu");

                entity.Property(e => e.YongHuMing)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.BuMenId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("BuMenID");

                entity.Property(e => e.ChuangJianSj)
                    .HasColumnType("datetime")
                    .HasColumnName("ChuangJianSJ")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.KouLing)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.LastErrTime).HasColumnType("datetime");

                entity.Property(e => e.LeiXing)
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .IsFixedLength();

                entity.Property(e => e.QuanMing)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.YongHuMiaoShu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ZhuYe)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ZhuangTai)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.HasMany(d => d.YongHuZus)
                    .WithMany(p => p.YongHus)
                    .UsingEntity<Dictionary<string, object>>(
                        "TbYongHuGuanXi",
                        l => l.HasOne<TbYongHu>().WithMany().HasForeignKey("YongHuZu").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_YongHuGuanXi1_YongHu"),
                        r => r.HasOne<TbYongHu>().WithMany().HasForeignKey("YongHu").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_YongHuGuanXi2_YongHu"),
                        j =>
                        {
                            j.HasKey("YongHuZu", "YongHu").HasName("PK_YongHuGuanXi");

                            j.ToTable("tb_YongHuGuanXi");

                            j.IndexerProperty<string>("YongHuZu").HasMaxLength(50).IsUnicode(false);

                            j.IndexerProperty<string>("YongHu").HasMaxLength(50).IsUnicode(false);
                        });

                entity.HasMany(d => d.YongHus)
                    .WithMany(p => p.YongHuZus)
                    .UsingEntity<Dictionary<string, object>>(
                        "TbYongHuGuanXi",
                        l => l.HasOne<TbYongHu>().WithMany().HasForeignKey("YongHu").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_YongHuGuanXi2_YongHu"),
                        r => r.HasOne<TbYongHu>().WithMany().HasForeignKey("YongHuZu").OnDelete(DeleteBehavior.ClientSetNull).HasConstraintName("FK_YongHuGuanXi1_YongHu"),
                        j =>
                        {
                            j.HasKey("YongHuZu", "YongHu").HasName("PK_YongHuGuanXi");

                            j.ToTable("tb_YongHuGuanXi");

                            j.IndexerProperty<string>("YongHuZu").HasMaxLength(50).IsUnicode(false);

                            j.IndexerProperty<string>("YongHu").HasMaxLength(50).IsUnicode(false);
                        });
            });

            modelBuilder.Entity<VwAidVideoChannel>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_AidVideoChannel");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.ChanName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.ChanType)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.IdinPlat)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("IDinPlat");

                entity.Property(e => e.Ip)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("IP");

                entity.Property(e => e.IsPtz)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("IsPTZ");

                entity.Property(e => e.Manufacturer)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Model)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.UseState)
                    .HasMaxLength(10)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<VwPresetPoint>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_PresetPoint");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.ChanName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Comment)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DevId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("DevID");

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Ip)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("IP");

                entity.Property(e => e.PointId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("PointID");
            });

            modelBuilder.Entity<VwTableDataDup>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_TableDataDup");

                entity.Property(e => e.DataDupCtgy)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.DataDupId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DataDupID");

                entity.Property(e => e.DstFullName)
                    .HasMaxLength(408)
                    .IsUnicode(false);

                entity.Property(e => e.DstTableName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.MapBeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.SrcFullName)
                    .HasMaxLength(408)
                    .IsUnicode(false);

                entity.Property(e => e.SrcTableName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.SrcWhereClause)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.TableMapId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TableMapID");
            });

            modelBuilder.Entity<VwTableMap>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_TableMap");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DstFullName)
                    .HasMaxLength(408)
                    .IsUnicode(false);

                entity.Property(e => e.DstPrcname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DstPRCName");

                entity.Property(e => e.DstTableCtgy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.DstTableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DstTableID");

                entity.Property(e => e.DstTableName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.MapCtgy)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.SrcFullName)
                    .HasMaxLength(408)
                    .IsUnicode(false);

                entity.Property(e => e.SrcPrcname)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("SrcPRCName");

                entity.Property(e => e.SrcTableCtgy)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SrcTableId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("SrcTableID");

                entity.Property(e => e.SrcTableName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.TableMapId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("TableMapID");
            });

            modelBuilder.Entity<VwVideoChannelRight>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("vw_VideoChannelRight");

                entity.Property(e => e.ChanId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ChanID");

                entity.Property(e => e.ChanName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.DevName)
                    .HasMaxLength(100)
                    .IsUnicode(false);

                entity.Property(e => e.Ip)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("IP");

                entity.Property(e => e.RecId)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("RecID");

                entity.Property(e => e.RightName)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.YongHuMing)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            OnModelCreatingPartial(modelBuilder);
        }
    }
}
