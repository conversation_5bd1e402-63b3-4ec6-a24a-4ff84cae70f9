﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class Role
    {
        public Role()
        {
            PolicyUserRoles = new HashSet<PolicyUserRole>();
        }

        public Guid RoleId { get; set; }
        public string RoleName { get; set; } = null!;
        public string? Description { get; set; }
        public string TaskMask { get; set; } = null!;
        public byte RoleFlags { get; set; }

        public virtual ICollection<PolicyUserRole> PolicyUserRoles { get; set; }
    }
}
