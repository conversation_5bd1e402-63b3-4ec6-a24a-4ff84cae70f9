﻿<Window x:Class="MqttWpfApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MqttWpfApp"
        mc:Ignorable="d"
        Title="MainWindow" Height="1000" Width="800">
    <Canvas>
        <Button Canvas.Left="47" Canvas.Top="29" Click="Button_Click" x:Name="start">开启服务器</Button>
        <Button Canvas.Left="170" Canvas.Top="29" Click="store_Click" x:Name="store" HorizontalAlignment="Left" VerticalAlignment="Center">信息存储</Button>
        <Label Canvas.Top="59" HorizontalAlignment="Left" VerticalAlignment="Center">所有数据</Label>
        <ListBox  x:Name="list" Height="400" Width="800" Canvas.Left="0" Canvas.Top="89" HorizontalAlignment="Left" VerticalAlignment="Center" Background="#FFCCDEF4" d:ItemsSource="{d:SampleData ItemCount=5}"/>
        <Label Canvas.Top="495" HorizontalAlignment="Center" VerticalAlignment="Top">有效数据</Label>
        <ListBox  x:Name="list1" Height="400" Width="800" Canvas.Left="0" Canvas.Top="520" HorizontalAlignment="Left" VerticalAlignment="Center" Background="#FFCCDEF4" d:ItemsSource="{d:SampleData ItemCount=5}"/>

    </Canvas>
</Window>
