﻿using System;
using System.Collections.Generic;
using CodeFirstExistingDatabaseSample.ReportServer.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace CodeFirstExistingDatabaseSample
{
    public partial class ReportServerContext : DbContext
    {
        public ReportServerContext()
        {
        }

        public ReportServerContext(DbContextOptions<ReportServerContext> options)
            : base(options)
        {
        }

        public virtual DbSet<ActiveSubscription> ActiveSubscriptions { get; set; } = null!;
        public virtual DbSet<Batch> Batches { get; set; } = null!;
        public virtual DbSet<CachePolicy> CachePolicies { get; set; } = null!;
        public virtual DbSet<Catalog> Catalogs { get; set; } = null!;
        public virtual DbSet<ChunkDatum> ChunkData { get; set; } = null!;
        public virtual DbSet<ChunkSegmentMapping> ChunkSegmentMappings { get; set; } = null!;
        public virtual DbSet<ConfigurationInfo> ConfigurationInfos { get; set; } = null!;
        public virtual DbSet<DataSet> DataSets { get; set; } = null!;
        public virtual DbSet<DataSource> DataSources { get; set; } = null!;
        public virtual DbSet<DbupgradeHistory> DbupgradeHistories { get; set; } = null!;
        public virtual DbSet<Event> Events { get; set; } = null!;
        public virtual DbSet<ExecutionLog> ExecutionLogs { get; set; } = null!;
        public virtual DbSet<ExecutionLog2> ExecutionLog2s { get; set; } = null!;
        public virtual DbSet<ExecutionLog3> ExecutionLog3s { get; set; } = null!;
        public virtual DbSet<ExecutionLogStorage> ExecutionLogStorages { get; set; } = null!;
        public virtual DbSet<ExtendedDataSet> ExtendedDataSets { get; set; } = null!;
        public virtual DbSet<ExtendedDataSource> ExtendedDataSources { get; set; } = null!;
        public virtual DbSet<History> Histories { get; set; } = null!;
        public virtual DbSet<Key> Keys { get; set; } = null!;
        public virtual DbSet<ModelDrill> ModelDrills { get; set; } = null!;
        public virtual DbSet<ModelItemPolicy> ModelItemPolicies { get; set; } = null!;
        public virtual DbSet<ModelPerspective> ModelPerspectives { get; set; } = null!;
        public virtual DbSet<Notification> Notifications { get; set; } = null!;
        public virtual DbSet<Policy> Policies { get; set; } = null!;
        public virtual DbSet<PolicyUserRole> PolicyUserRoles { get; set; } = null!;
        public virtual DbSet<ReportSchedule> ReportSchedules { get; set; } = null!;
        public virtual DbSet<Role> Roles { get; set; } = null!;
        public virtual DbSet<RunningJob> RunningJobs { get; set; } = null!;
        public virtual DbSet<Schedule> Schedules { get; set; } = null!;
        public virtual DbSet<SecDatum> SecData { get; set; } = null!;
        public virtual DbSet<Segment> Segments { get; set; } = null!;
        public virtual DbSet<SegmentedChunk> SegmentedChunks { get; set; } = null!;
        public virtual DbSet<ServerParametersInstance> ServerParametersInstances { get; set; } = null!;
        public virtual DbSet<ServerUpgradeHistory> ServerUpgradeHistories { get; set; } = null!;
        public virtual DbSet<SnapshotDatum> SnapshotData { get; set; } = null!;
        public virtual DbSet<Subscription> Subscriptions { get; set; } = null!;
        public virtual DbSet<SubscriptionsBeingDeleted> SubscriptionsBeingDeleteds { get; set; } = null!;
        public virtual DbSet<UpgradeInfo> UpgradeInfos { get; set; } = null!;
        public virtual DbSet<User> Users { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {

                optionsBuilder.UseSqlServer("Server=***************;User ID=**;Password=**;Database=ReportServer;Trusted_Connection=False;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("Latin1_General_CI_AS_KS_WS");

            modelBuilder.Entity<ActiveSubscription>(entity =>
            {
                entity.HasKey(e => e.ActiveId);

                entity.Property(e => e.ActiveId)
                    .ValueGeneratedNever()
                    .HasColumnName("ActiveID");

                entity.Property(e => e.SubscriptionId).HasColumnName("SubscriptionID");

                entity.HasOne(d => d.Subscription)
                    .WithMany(p => p.ActiveSubscriptions)
                    .HasForeignKey(d => d.SubscriptionId)
                    .HasConstraintName("FK_ActiveSubscriptions_Subscriptions");
            });

            modelBuilder.Entity<Batch>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("Batch");

                entity.HasIndex(e => new { e.BatchId, e.AddedOn }, "IX_Batch")
                    .IsClustered();

                entity.HasIndex(e => e.AddedOn, "IX_Batch_1");

                entity.Property(e => e.Action)
                    .HasMaxLength(32)
                    .IsUnicode(false);

                entity.Property(e => e.AddedOn).HasColumnType("datetime");

                entity.Property(e => e.BatchId).HasColumnName("BatchID");

                entity.Property(e => e.Content).HasColumnType("image");

                entity.Property(e => e.Item).HasMaxLength(425);

                entity.Property(e => e.Param).HasMaxLength(425);

                entity.Property(e => e.Parent).HasMaxLength(425);

                entity.Property(e => e.Properties).HasColumnType("ntext");
            });

            modelBuilder.Entity<CachePolicy>(entity =>
            {
                entity.HasKey(e => e.CachePolicyId)
                    .IsClustered(false);

                entity.ToTable("CachePolicy");

                entity.HasIndex(e => e.ReportId, "IX_CachePolicyReportID")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.CachePolicyId)
                    .ValueGeneratedNever()
                    .HasColumnName("CachePolicyID");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.HasOne(d => d.Report)
                    .WithOne(p => p.CachePolicy)
                    .HasForeignKey<CachePolicy>(d => d.ReportId)
                    .HasConstraintName("FK_CachePolicyReportID");
            });

            modelBuilder.Entity<Catalog>(entity =>
            {
                entity.HasKey(e => e.ItemId)
                    .IsClustered(false);

                entity.ToTable("Catalog");

                entity.HasIndex(e => e.Path, "IX_Catalog")
                    .IsUnique()
                    .IsClustered();

                entity.HasIndex(e => new { e.Type, e.ComponentId }, "IX_ComponentLookup");

                entity.HasIndex(e => e.LinkSourceId, "IX_Link");

                entity.HasIndex(e => e.ParentId, "IX_Parent");

                entity.HasIndex(e => e.SnapshotDataId, "IX_SnapshotDataId");

                entity.Property(e => e.ItemId)
                    .ValueGeneratedNever()
                    .HasColumnName("ItemID");

                entity.Property(e => e.ComponentId).HasColumnName("ComponentID");

                entity.Property(e => e.Content).HasColumnType("image");

                entity.Property(e => e.CreatedById).HasColumnName("CreatedByID");

                entity.Property(e => e.CreationDate).HasColumnType("datetime");

                entity.Property(e => e.Description).HasMaxLength(512);

                entity.Property(e => e.ExecutionTime).HasColumnType("datetime");

                entity.Property(e => e.LinkSourceId).HasColumnName("LinkSourceID");

                entity.Property(e => e.MimeType).HasMaxLength(260);

                entity.Property(e => e.ModifiedById).HasColumnName("ModifiedByID");

                entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

                entity.Property(e => e.Name).HasMaxLength(425);

                entity.Property(e => e.Parameter).HasColumnType("ntext");

                entity.Property(e => e.ParentId).HasColumnName("ParentID");

                entity.Property(e => e.Path).HasMaxLength(425);

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");

                entity.Property(e => e.Property).HasColumnType("ntext");

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");

                entity.Property(e => e.SubType).HasMaxLength(128);

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.CatalogCreatedBies)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Catalog_CreatedByID");

                entity.HasOne(d => d.LinkSource)
                    .WithMany(p => p.InverseLinkSource)
                    .HasForeignKey(d => d.LinkSourceId)
                    .HasConstraintName("FK_Catalog_LinkSourceID");

                entity.HasOne(d => d.ModifiedBy)
                    .WithMany(p => p.CatalogModifiedBies)
                    .HasForeignKey(d => d.ModifiedById)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Catalog_ModifiedByID");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_Catalog_ParentID");

                entity.HasOne(d => d.Policy)
                    .WithMany(p => p.Catalogs)
                    .HasForeignKey(d => d.PolicyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Catalog_Policy");
            });

            modelBuilder.Entity<ChunkDatum>(entity =>
            {
                entity.HasKey(e => e.ChunkId)
                    .IsClustered(false);

                entity.HasIndex(e => new { e.SnapshotDataId, e.ChunkType, e.ChunkName }, "IX_ChunkData")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.ChunkId)
                    .ValueGeneratedNever()
                    .HasColumnName("ChunkID");

                entity.Property(e => e.ChunkName).HasMaxLength(260);

                entity.Property(e => e.Content).HasColumnType("image");

                entity.Property(e => e.MimeType).HasMaxLength(260);

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");
            });

            modelBuilder.Entity<ChunkSegmentMapping>(entity =>
            {
                entity.HasKey(e => new { e.ChunkId, e.SegmentId });

                entity.ToTable("ChunkSegmentMapping");

                entity.HasIndex(e => e.SegmentId, "IX_ChunkSegmentMapping_SegmentId");

                entity.HasIndex(e => new { e.ChunkId, e.StartByte }, "UNIQ_ChunkId_StartByte")
                    .IsUnique();
            });

            modelBuilder.Entity<ConfigurationInfo>(entity =>
            {
                entity.HasKey(e => e.ConfigInfoId)
                    .IsClustered(false);

                entity.ToTable("ConfigurationInfo");

                entity.HasIndex(e => e.Name, "IX_ConfigurationInfo")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.ConfigInfoId)
                    .ValueGeneratedNever()
                    .HasColumnName("ConfigInfoID");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.Property(e => e.Value).HasColumnType("ntext");
            });

            modelBuilder.Entity<DataSet>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .HasName("PK_DataSet")
                    .IsClustered(false);

                entity.HasIndex(e => e.LinkId, "IX_DataSetLinkID");

                entity.HasIndex(e => new { e.ItemId, e.Name }, "IX_DataSet_ItemID_Name")
                    .IsClustered();

                entity.Property(e => e.Id)
                    .ValueGeneratedNever()
                    .HasColumnName("ID");

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.LinkId).HasColumnName("LinkID");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.DataSetItems)
                    .HasForeignKey(d => d.ItemId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DataSetItemID");

                entity.HasOne(d => d.Link)
                    .WithMany(p => p.DataSetLinks)
                    .HasForeignKey(d => d.LinkId)
                    .HasConstraintName("FK_DataSetLinkID");
            });

            modelBuilder.Entity<DataSource>(entity =>
            {
                entity.HasKey(e => e.Dsid);

                entity.ToTable("DataSource");

                entity.HasIndex(e => e.ItemId, "IX_DataSourceItemID");

                entity.HasIndex(e => e.SubscriptionId, "IX_DataSourceSubscriptionID");

                entity.Property(e => e.Dsid)
                    .ValueGeneratedNever()
                    .HasColumnName("DSID");

                entity.Property(e => e.ConnectionString).HasColumnType("image");

                entity.Property(e => e.Extension).HasMaxLength(260);

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.Property(e => e.OriginalConnectionString).HasColumnType("image");

                entity.Property(e => e.Password).HasColumnType("image");

                entity.Property(e => e.Prompt).HasColumnType("ntext");

                entity.Property(e => e.SubscriptionId).HasColumnName("SubscriptionID");

                entity.Property(e => e.UserName).HasColumnType("image");

                entity.HasOne(d => d.Item)
                    .WithMany(p => p.DataSources)
                    .HasForeignKey(d => d.ItemId)
                    .HasConstraintName("FK_DataSourceItemID");
            });

            modelBuilder.Entity<DbupgradeHistory>(entity =>
            {
                entity.HasKey(e => e.UpgradeId);

                entity.ToTable("DBUpgradeHistory");

                entity.Property(e => e.UpgradeId).HasColumnName("UpgradeID");

                entity.Property(e => e.DateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DbVersion).HasMaxLength(25);

                entity.Property(e => e.User)
                    .HasMaxLength(128)
                    .HasDefaultValueSql("(suser_sname())");
            });

            modelBuilder.Entity<Event>(entity =>
            {
                entity.ToTable("Event");

                entity.HasIndex(e => e.ProcessStart, "IX_Event2");

                entity.HasIndex(e => e.TimeEntered, "IX_Event_TimeEntered");

                entity.Property(e => e.EventId)
                    .ValueGeneratedNever()
                    .HasColumnName("EventID");

                entity.Property(e => e.BatchId).HasColumnName("BatchID");

                entity.Property(e => e.EventData).HasMaxLength(260);

                entity.Property(e => e.EventType).HasMaxLength(260);

                entity.Property(e => e.ProcessHeartbeat).HasColumnType("datetime");

                entity.Property(e => e.ProcessStart).HasColumnType("datetime");

                entity.Property(e => e.TimeEntered).HasColumnType("datetime");
            });

            modelBuilder.Entity<ExecutionLog>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ExecutionLog");

                entity.Property(e => e.Format).HasMaxLength(26);

                entity.Property(e => e.InstanceName).HasMaxLength(38);

                entity.Property(e => e.Parameters).HasColumnType("ntext");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.Property(e => e.Status).HasMaxLength(40);

                entity.Property(e => e.TimeEnd).HasColumnType("datetime");

                entity.Property(e => e.TimeStart).HasColumnType("datetime");

                entity.Property(e => e.UserName).HasMaxLength(260);
            });

            modelBuilder.Entity<ExecutionLog2>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ExecutionLog2");

                entity.Property(e => e.AdditionalInfo).HasColumnType("xml");

                entity.Property(e => e.ExecutionId).HasMaxLength(64);

                entity.Property(e => e.Format).HasMaxLength(26);

                entity.Property(e => e.InstanceName).HasMaxLength(38);

                entity.Property(e => e.Parameters).HasColumnType("ntext");

                entity.Property(e => e.ReportAction)
                    .HasMaxLength(21)
                    .IsUnicode(false);

                entity.Property(e => e.ReportPath).HasMaxLength(425);

                entity.Property(e => e.RequestType)
                    .HasMaxLength(12)
                    .IsUnicode(false);

                entity.Property(e => e.Source)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.Status).HasMaxLength(40);

                entity.Property(e => e.TimeEnd).HasColumnType("datetime");

                entity.Property(e => e.TimeStart).HasColumnType("datetime");

                entity.Property(e => e.UserName).HasMaxLength(260);
            });

            modelBuilder.Entity<ExecutionLog3>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ExecutionLog3");

                entity.Property(e => e.AdditionalInfo).HasColumnType("xml");

                entity.Property(e => e.ExecutionId).HasMaxLength(64);

                entity.Property(e => e.Format).HasMaxLength(26);

                entity.Property(e => e.InstanceName).HasMaxLength(38);

                entity.Property(e => e.ItemAction)
                    .HasMaxLength(21)
                    .IsUnicode(false);

                entity.Property(e => e.ItemPath).HasMaxLength(425);

                entity.Property(e => e.Parameters).HasColumnType("ntext");

                entity.Property(e => e.RequestType)
                    .HasMaxLength(13)
                    .IsUnicode(false);

                entity.Property(e => e.Source)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.Status).HasMaxLength(40);

                entity.Property(e => e.TimeEnd).HasColumnType("datetime");

                entity.Property(e => e.TimeStart).HasColumnType("datetime");

                entity.Property(e => e.UserName).HasMaxLength(260);
            });

            modelBuilder.Entity<ExecutionLogStorage>(entity =>
            {
                entity.HasKey(e => e.LogEntryId)
                    .HasName("PK__Executio__05F5D745E1B95E21");

                entity.ToTable("ExecutionLogStorage");

                entity.HasIndex(e => new { e.TimeStart, e.LogEntryId }, "IX_ExecutionLog");

                entity.Property(e => e.AdditionalInfo).HasColumnType("xml");

                entity.Property(e => e.ExecutionId).HasMaxLength(64);

                entity.Property(e => e.Format).HasMaxLength(26);

                entity.Property(e => e.InstanceName).HasMaxLength(38);

                entity.Property(e => e.Parameters).HasColumnType("ntext");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.Property(e => e.Status).HasMaxLength(40);

                entity.Property(e => e.TimeEnd).HasColumnType("datetime");

                entity.Property(e => e.TimeStart).HasColumnType("datetime");

                entity.Property(e => e.UserName).HasMaxLength(260);
            });

            modelBuilder.Entity<ExtendedDataSet>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ExtendedDataSets");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.LinkId).HasColumnName("LinkID");

                entity.Property(e => e.Name).HasMaxLength(260);
            });

            modelBuilder.Entity<ExtendedDataSource>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("ExtendedDataSources");

                entity.Property(e => e.ConnectionString).HasColumnType("image");

                entity.Property(e => e.Dsid).HasColumnName("DSID");

                entity.Property(e => e.Extension).HasMaxLength(260);

                entity.Property(e => e.ItemId).HasColumnName("ItemID");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.Property(e => e.OriginalConnectionString).HasColumnType("image");

                entity.Property(e => e.Password).HasColumnType("image");

                entity.Property(e => e.Prompt).HasColumnType("ntext");

                entity.Property(e => e.SubscriptionId).HasColumnName("SubscriptionID");

                entity.Property(e => e.UserName).HasColumnType("image");
            });

            modelBuilder.Entity<History>(entity =>
            {
                entity.HasKey(e => e.HistoryId)
                    .IsClustered(false);

                entity.ToTable("History");

                entity.HasIndex(e => new { e.ReportId, e.SnapshotDate }, "IX_History")
                    .IsUnique()
                    .IsClustered();

                entity.HasIndex(e => e.SnapshotDataId, "IX_SnapshotDataID");

                entity.Property(e => e.HistoryId)
                    .ValueGeneratedNever()
                    .HasColumnName("HistoryID");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.Property(e => e.SnapshotDataId).HasColumnName("SnapshotDataID");

                entity.Property(e => e.SnapshotDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<Key>(entity =>
            {
                entity.HasKey(e => new { e.InstallationId, e.Client });

                entity.Property(e => e.InstallationId).HasColumnName("InstallationID");

                entity.Property(e => e.InstanceName).HasMaxLength(32);

                entity.Property(e => e.MachineName).HasMaxLength(256);

                entity.Property(e => e.PublicKey).HasColumnType("image");

                entity.Property(e => e.SymmetricKey).HasColumnType("image");
            });

            modelBuilder.Entity<ModelDrill>(entity =>
            {
                entity.HasKey(e => e.ModelDrillId)
                    .IsClustered(false);

                entity.ToTable("ModelDrill");

                entity.HasIndex(e => new { e.ModelId, e.ReportId, e.ModelDrillId }, "IX_ModelDrillModelID")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.ModelDrillId)
                    .ValueGeneratedNever()
                    .HasColumnName("ModelDrillID");

                entity.Property(e => e.ModelId).HasColumnName("ModelID");

                entity.Property(e => e.ModelItemId)
                    .HasMaxLength(425)
                    .HasColumnName("ModelItemID");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.HasOne(d => d.Model)
                    .WithMany(p => p.ModelDrillModels)
                    .HasForeignKey(d => d.ModelId)
                    .HasConstraintName("FK_ModelDrillModel");

                entity.HasOne(d => d.Report)
                    .WithMany(p => p.ModelDrillReports)
                    .HasForeignKey(d => d.ReportId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ModelDrillReport");
            });

            modelBuilder.Entity<ModelItemPolicy>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false);

                entity.ToTable("ModelItemPolicy");

                entity.HasIndex(e => new { e.CatalogItemId, e.ModelItemId }, "IX_ModelItemPolicy")
                    .IsClustered();

                entity.Property(e => e.Id)
                    .ValueGeneratedNever()
                    .HasColumnName("ID");

                entity.Property(e => e.CatalogItemId).HasColumnName("CatalogItemID");

                entity.Property(e => e.ModelItemId)
                    .HasMaxLength(425)
                    .HasColumnName("ModelItemID");

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");

                entity.HasOne(d => d.Policy)
                    .WithMany(p => p.ModelItemPolicies)
                    .HasForeignKey(d => d.PolicyId)
                    .HasConstraintName("FK_PoliciesPolicyID");
            });

            modelBuilder.Entity<ModelPerspective>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ModelPerspective");

                entity.HasIndex(e => e.ModelId, "IX_ModelPerspective")
                    .IsClustered();

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.ModelId).HasColumnName("ModelID");

                entity.Property(e => e.PerspectiveDescription).HasColumnType("ntext");

                entity.Property(e => e.PerspectiveId)
                    .HasColumnType("ntext")
                    .HasColumnName("PerspectiveID");

                entity.Property(e => e.PerspectiveName).HasColumnType("ntext");

                entity.HasOne(d => d.Model)
                    .WithMany()
                    .HasForeignKey(d => d.ModelId)
                    .HasConstraintName("FK_ModelPerspectiveModel");
            });

            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasIndex(e => e.ProcessAfter, "IX_Notifications");

                entity.HasIndex(e => e.ProcessStart, "IX_Notifications2");

                entity.HasIndex(e => e.NotificationEntered, "IX_Notifications3");

                entity.Property(e => e.NotificationId)
                    .ValueGeneratedNever()
                    .HasColumnName("NotificationID");

                entity.Property(e => e.ActivationId).HasColumnName("ActivationID");

                entity.Property(e => e.BatchId).HasColumnName("BatchID");

                entity.Property(e => e.DeliveryExtension).HasMaxLength(260);

                entity.Property(e => e.ExtensionSettings).HasColumnType("ntext");

                entity.Property(e => e.Locale).HasMaxLength(128);

                entity.Property(e => e.NotificationEntered).HasColumnType("datetime");

                entity.Property(e => e.Parameters).HasColumnType("ntext");

                entity.Property(e => e.ProcessAfter).HasColumnType("datetime");

                entity.Property(e => e.ProcessHeartbeat).HasColumnType("datetime");

                entity.Property(e => e.ProcessStart).HasColumnType("datetime");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.Property(e => e.SnapShotDate).HasColumnType("datetime");

                entity.Property(e => e.SubscriptionId).HasColumnName("SubscriptionID");

                entity.Property(e => e.SubscriptionLastRunTime).HasColumnType("datetime");

                entity.Property(e => e.SubscriptionOwnerId).HasColumnName("SubscriptionOwnerID");

                entity.HasOne(d => d.Subscription)
                    .WithMany(p => p.Notifications)
                    .HasForeignKey(d => d.SubscriptionId)
                    .HasConstraintName("FK_Notifications_Subscriptions");
            });

            modelBuilder.Entity<Policy>(entity =>
            {
                entity.Property(e => e.PolicyId)
                    .ValueGeneratedNever()
                    .HasColumnName("PolicyID");
            });

            modelBuilder.Entity<PolicyUserRole>(entity =>
            {
                entity.HasKey(e => e.Id)
                    .IsClustered(false);

                entity.ToTable("PolicyUserRole");

                entity.HasIndex(e => new { e.RoleId, e.UserId, e.PolicyId }, "IX_PolicyUserRole")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.Id)
                    .ValueGeneratedNever()
                    .HasColumnName("ID");

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");

                entity.Property(e => e.RoleId).HasColumnName("RoleID");

                entity.Property(e => e.UserId).HasColumnName("UserID");

                entity.HasOne(d => d.Policy)
                    .WithMany(p => p.PolicyUserRoles)
                    .HasForeignKey(d => d.PolicyId)
                    .HasConstraintName("FK_PolicyUserRole_Policy");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.PolicyUserRoles)
                    .HasForeignKey(d => d.RoleId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PolicyUserRole_Role");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.PolicyUserRoles)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PolicyUserRole_User");
            });

            modelBuilder.Entity<ReportSchedule>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ReportSchedule");

                entity.HasIndex(e => e.ReportId, "IX_ReportSchedule_ReportID");

                entity.HasIndex(e => e.ScheduleId, "IX_ReportSchedule_ScheduleID");

                entity.HasIndex(e => e.SubscriptionId, "IX_ReportSchedule_SubscriptionID");

                entity.Property(e => e.ReportId).HasColumnName("ReportID");

                entity.Property(e => e.ScheduleId).HasColumnName("ScheduleID");

                entity.Property(e => e.SubscriptionId).HasColumnName("SubscriptionID");

                entity.HasOne(d => d.Report)
                    .WithMany()
                    .HasForeignKey(d => d.ReportId)
                    .HasConstraintName("FK_ReportSchedule_Report");

                entity.HasOne(d => d.Schedule)
                    .WithMany()
                    .HasForeignKey(d => d.ScheduleId)
                    .HasConstraintName("FK_ReportSchedule_Schedule");

                entity.HasOne(d => d.Subscription)
                    .WithMany()
                    .HasForeignKey(d => d.SubscriptionId)
                    .HasConstraintName("FK_ReportSchedule_Subscriptions");
            });

            modelBuilder.Entity<Role>(entity =>
            {
                entity.HasKey(e => e.RoleId)
                    .IsClustered(false);

                entity.HasIndex(e => e.RoleName, "IX_Roles")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.RoleId)
                    .ValueGeneratedNever()
                    .HasColumnName("RoleID");

                entity.Property(e => e.Description).HasMaxLength(512);

                entity.Property(e => e.RoleName).HasMaxLength(260);

                entity.Property(e => e.TaskMask).HasMaxLength(32);
            });

            modelBuilder.Entity<RunningJob>(entity =>
            {
                entity.HasKey(e => e.JobId);

                entity.HasIndex(e => new { e.ComputerName, e.JobType }, "IX_RunningJobsStatus");

                entity.Property(e => e.JobId)
                    .HasMaxLength(32)
                    .HasColumnName("JobID");

                entity.Property(e => e.ComputerName).HasMaxLength(32);

                entity.Property(e => e.Description).HasColumnType("ntext");

                entity.Property(e => e.RequestName).HasMaxLength(425);

                entity.Property(e => e.RequestPath).HasMaxLength(425);

                entity.Property(e => e.StartDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<Schedule>(entity =>
            {
                entity.ToTable("Schedule");

                entity.HasIndex(e => new { e.Name, e.Path }, "IX_Schedule")
                    .IsUnique();

                entity.Property(e => e.ScheduleId)
                    .ValueGeneratedNever()
                    .HasColumnName("ScheduleID");

                entity.Property(e => e.ConsistancyCheck).HasColumnType("datetime");

                entity.Property(e => e.EndDate).HasColumnType("datetime");

                entity.Property(e => e.EventData).HasMaxLength(260);

                entity.Property(e => e.EventType).HasMaxLength(260);

                entity.Property(e => e.LastRunStatus).HasMaxLength(260);

                entity.Property(e => e.LastRunTime).HasColumnType("datetime");

                entity.Property(e => e.Name).HasMaxLength(260);

                entity.Property(e => e.NextRunTime).HasColumnType("datetime");

                entity.Property(e => e.Path).HasMaxLength(260);

                entity.Property(e => e.StartDate).HasColumnType("datetime");

                entity.HasOne(d => d.CreatedBy)
                    .WithMany(p => p.Schedules)
                    .HasForeignKey(d => d.CreatedById)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Schedule_Users");
            });

            modelBuilder.Entity<SecDatum>(entity =>
            {
                entity.HasKey(e => e.SecDataId)
                    .IsClustered(false);

                entity.HasIndex(e => new { e.PolicyId, e.AuthType }, "IX_SecData")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.SecDataId)
                    .ValueGeneratedNever()
                    .HasColumnName("SecDataID");

                entity.Property(e => e.NtSecDescPrimary).HasColumnType("image");

                entity.Property(e => e.NtSecDescSecondary).HasColumnType("ntext");

                entity.Property(e => e.PolicyId).HasColumnName("PolicyID");

                entity.Property(e => e.XmlDescription).HasColumnType("ntext");

                entity.HasOne(d => d.Policy)
                    .WithMany(p => p.SecData)
                    .HasForeignKey(d => d.PolicyId)
                    .HasConstraintName("FK_SecDataPolicyID");
            });

            modelBuilder.Entity<Segment>(entity =>
            {
                entity.ToTable("Segment");

                entity.HasIndex(e => e.SegmentId, "IX_SegmentMetadata")
                    .IsUnique();

                entity.Property(e => e.SegmentId).HasDefaultValueSql("(newsequentialid())");
            });

            modelBuilder.Entity<SegmentedChunk>(entity =>
            {
                entity.ToTable("SegmentedChunk");

                entity.HasIndex(e => new { e.ChunkId, e.SnapshotDataId }, "IX_ChunkId_SnapshotDataId");

                entity.HasIndex(e => new { e.SnapshotDataId, e.ChunkType, e.ChunkName }, "UNIQ_SnapshotChunkMapping")
                    .IsUnique();

                entity.Property(e => e.ChunkId).HasDefaultValueSql("(newsequentialid())");

                entity.Property(e => e.ChunkName).HasMaxLength(260);

                entity.Property(e => e.MimeType).HasMaxLength(260);
            });

            modelBuilder.Entity<ServerParametersInstance>(entity =>
            {
                entity.HasKey(e => e.ServerParametersId);

                entity.ToTable("ServerParametersInstance");

                entity.HasIndex(e => e.Expiration, "IX_ServerParametersInstanceExpiration");

                entity.Property(e => e.ServerParametersId)
                    .HasMaxLength(32)
                    .HasColumnName("ServerParametersID");

                entity.Property(e => e.CreateDate).HasColumnType("datetime");

                entity.Property(e => e.Expiration).HasColumnType("datetime");

                entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

                entity.Property(e => e.ParametersValues).HasColumnType("image");

                entity.Property(e => e.ParentId)
                    .HasMaxLength(32)
                    .HasColumnName("ParentID");

                entity.Property(e => e.Path).HasMaxLength(425);
            });

            modelBuilder.Entity<ServerUpgradeHistory>(entity =>
            {
                entity.HasKey(e => e.UpgradeId);

                entity.ToTable("ServerUpgradeHistory");

                entity.Property(e => e.UpgradeId).HasColumnName("UpgradeID");

                entity.Property(e => e.DateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.ServerVersion).HasMaxLength(25);

                entity.Property(e => e.User)
                    .HasMaxLength(128)
                    .HasDefaultValueSql("(suser_sname())");
            });

            modelBuilder.Entity<SnapshotDatum>(entity =>
            {
                entity.HasKey(e => e.SnapshotDataId);

                entity.HasIndex(e => e.PermanentRefcount, "IX_SnapshotCleaning");

                entity.Property(e => e.SnapshotDataId)
                    .ValueGeneratedNever()
                    .HasColumnName("SnapshotDataID");

                entity.Property(e => e.CreatedDate).HasColumnType("datetime");

                entity.Property(e => e.Description).HasMaxLength(512);

                entity.Property(e => e.EffectiveParams).HasColumnType("ntext");

                entity.Property(e => e.ExpirationDate).HasColumnType("datetime");

                entity.Property(e => e.QueryParams).HasColumnType("ntext");
            });

            modelBuilder.Entity<Subscription>(entity =>
            {
                entity.Property(e => e.SubscriptionId)
                    .ValueGeneratedNever()
                    .HasColumnName("SubscriptionID");

                entity.Property(e => e.DataSettings).HasColumnType("ntext");

                entity.Property(e => e.DeliveryExtension).HasMaxLength(260);

                entity.Property(e => e.Description).HasMaxLength(512);

                entity.Property(e => e.EventType).HasMaxLength(260);

                entity.Property(e => e.ExtensionSettings).HasColumnType("ntext");

                entity.Property(e => e.LastRunTime).HasColumnType("datetime");

                entity.Property(e => e.LastStatus).HasMaxLength(260);

                entity.Property(e => e.Locale).HasMaxLength(128);

                entity.Property(e => e.MatchData).HasColumnType("ntext");

                entity.Property(e => e.ModifiedById).HasColumnName("ModifiedByID");

                entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

                entity.Property(e => e.OwnerId).HasColumnName("OwnerID");

                entity.Property(e => e.Parameters).HasColumnType("ntext");

                entity.Property(e => e.ReportOid).HasColumnName("Report_OID");

                entity.HasOne(d => d.ModifiedBy)
                    .WithMany(p => p.SubscriptionModifiedBies)
                    .HasForeignKey(d => d.ModifiedById)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Subscriptions_ModifiedBy");

                entity.HasOne(d => d.Owner)
                    .WithMany(p => p.SubscriptionOwners)
                    .HasForeignKey(d => d.OwnerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Subscriptions_Owner");

                entity.HasOne(d => d.ReportO)
                    .WithMany(p => p.Subscriptions)
                    .HasForeignKey(d => d.ReportOid)
                    .HasConstraintName("FK_Subscriptions_Catalog");
            });

            modelBuilder.Entity<SubscriptionsBeingDeleted>(entity =>
            {
                entity.HasKey(e => e.SubscriptionId);

                entity.ToTable("SubscriptionsBeingDeleted");

                entity.Property(e => e.SubscriptionId)
                    .ValueGeneratedNever()
                    .HasColumnName("SubscriptionID");

                entity.Property(e => e.CreationDate).HasColumnType("datetime");
            });

            modelBuilder.Entity<UpgradeInfo>(entity =>
            {
                entity.HasKey(e => e.Item);

                entity.ToTable("UpgradeInfo");

                entity.Property(e => e.Item).HasMaxLength(260);

                entity.Property(e => e.Status).HasMaxLength(512);
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId)
                    .IsClustered(false);

                entity.HasIndex(e => new { e.Sid, e.UserName, e.AuthType }, "IX_Users")
                    .IsUnique()
                    .IsClustered();

                entity.Property(e => e.UserId)
                    .ValueGeneratedNever()
                    .HasColumnName("UserID");

                entity.Property(e => e.Sid).HasMaxLength(85);

                entity.Property(e => e.UserName).HasMaxLength(260);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
