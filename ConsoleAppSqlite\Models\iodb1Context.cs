﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleAppSqlite.Models
{
    public partial class iodb1Context : DbContext
    {
        public iodb1Context()
        {
        }
        public iodb1Context(string sqlpath)
           : base(new DbContextOptionsBuilder<iodb1Context>().UseSqlite("Data Source=" + sqlpath).Options)
        {

        }
        public iodb1Context(DbContextOptions<iodb1Context> options)
            : base(options)
        {
        }

        public virtual DbSet<Caniopara> Caniopara { get; set; }
        public virtual DbSet<DescField> DescField { get; set; }
        public virtual DbSet<DescTable> DescTable { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
//            if (!optionsBuilder.IsConfigured)
//            {
//#warning To protect potentially sensitive information in your connection string, you should move it out of source code. See http://go.microsoft.com/fwlink/?LinkId=723263 for guidance on storing connection strings.
//                optionsBuilder.UseSqlite("Data Source=D:\\Project\\SOMS\\server\\src\\YunDa.Server\\Yunda.ISAS.DataMonitoringServer\\bin\\Debug\\netcoreapp3.1\\SaveProtection\\files\\3\\iodb1.sql3");
//            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Caniopara>(entity =>
            {
                entity.ToTable("caniopara");

                entity.HasIndex(e => e.Id)
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .ValueGeneratedNever();

                entity.Property(e => e.IsEnum).HasColumnName("isEnum");

                entity.Property(e => e.ParaGroup).HasColumnName("paraGroup");

                entity.Property(e => e.ParaIndex).HasColumnName("paraIndex");

                entity.Property(e => e.ParaName).HasColumnName("paraName");

                entity.Property(e => e.ParaValue).HasColumnName("paraValue");

                entity.Property(e => e.StepL1).HasColumnName("stepL1");

                entity.Property(e => e.StepL2).HasColumnName("stepL2");

                entity.Property(e => e.Unit).HasColumnName("unit");

                entity.Property(e => e.ValMax).HasColumnName("valMax");

                entity.Property(e => e.ValMin).HasColumnName("valMin");

                entity.Property(e => e.ValType).HasColumnName("valType");
            });

            modelBuilder.Entity<DescField>(entity =>
            {
                entity.ToTable("desc_field");

                entity.HasIndex(e => e.Id)
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .ValueGeneratedNever();

                entity.Property(e => e.DataDesc).HasColumnName("dataDesc");

                entity.Property(e => e.DataType).HasColumnName("dataType");

                entity.Property(e => e.DefaultV).HasColumnName("defaultV");

                entity.Property(e => e.FieldDesc).HasColumnName("fieldDesc");

                entity.Property(e => e.FieldName).HasColumnName("fieldName");

                entity.Property(e => e.IsModify).HasColumnName("isModify");

                entity.Property(e => e.TbId).HasColumnName("tbID");
            });

            modelBuilder.Entity<DescTable>(entity =>
            {
                entity.ToTable("desc_table");

                entity.HasIndex(e => e.Id)
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .ValueGeneratedNever();

                entity.Property(e => e.RowAd).HasColumnName("rowAD");

                entity.Property(e => e.TbDesc).HasColumnName("tbDesc");

                entity.Property(e => e.TbName).HasColumnName("tbName");

                entity.Property(e => e.TbType).HasColumnName("tbType");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
