﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Documents;
using Yunda.ISAS.DataMonitoringServer;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;

namespace Yunda.SOMS.DataMonitoringServer.WPF.ViewModel
{
    public class ConnectionTabViewModel : INotifyPropertyChanged
    {
        private FlowDocument _logDocument;
        private string _header;
        private int _maxEntries = 500; // 限制最大条目数

        public string Header
        {
            get => _header;
            set
            {
                if (_header != value)
                {
                    _header = value;
                    OnPropertyChanged();
                }
            }
        }

        public FlowDocument LogDocument
        {
            get => _logDocument;
            set
            {
                if (_logDocument != value)
                {
                    _logDocument = value;
                    OnPropertyChanged();
                }
            }
        }

        public ConnectionTabViewModel(ConnectionConfig connection)
        {
            Header = $"{connection.Name} ({connection.DataSourceCategoryDisplay})";
            LogDocument = new FlowDocument()
            {
                Background = System.Windows.Media.Brushes.Black
            };
        }

        public void AddLogEntry(string message, string type, DateTime time)
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                // 根据类型设置不同颜色
                System.Windows.Media.SolidColorBrush color;
                switch (type)
                {
                    case string t when t.Contains("遥测"):
                        color = System.Windows.Media.Brushes.Green;
                        break;
                    case string t when t.Contains("遥信"):
                        color = System.Windows.Media.Brushes.LightBlue;
                        break;
                    case string t when t.Contains("告警"):
                        color = System.Windows.Media.Brushes.Red;
                        break;
                    default:
                        color = System.Windows.Media.Brushes.White;
                        break;
                }

                // 创建富文本段落
                var timeText = new System.Windows.Documents.Run($"[{time.ToString("HH:mm:ss.fff")}] ");
                timeText.Foreground = System.Windows.Media.Brushes.Yellow;

                var typeText = new System.Windows.Documents.Run($"[{type}] ");
                typeText.Foreground = color;

                var messageText = new System.Windows.Documents.Run(message);
                messageText.Foreground = color;

                var paragraph = new Paragraph();
                paragraph.Inlines.Add(timeText);
                paragraph.Inlines.Add(typeText);
                paragraph.Inlines.Add(messageText);

                // 添加到文档
                LogDocument.Blocks.Add(paragraph);

                // 处理溢出问题
                while (LogDocument.Blocks.Count > _maxEntries)
                {
                    LogDocument.Blocks.Remove(LogDocument.Blocks.FirstBlock);
                }
            });
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

}
