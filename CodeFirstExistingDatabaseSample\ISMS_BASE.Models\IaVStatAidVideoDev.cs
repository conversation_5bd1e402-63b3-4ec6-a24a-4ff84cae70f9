﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaVStatAidVideoDev
    {
        public string RecId { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string StatName { get; set; } = null!;
        public string DevId { get; set; } = null!;
        public string PlatId { get; set; } = null!;
        public string DevName { get; set; } = null!;
        public string? Ip { get; set; }
        public int? Port { get; set; }
        public string? UserName { get; set; }
        public string? PassEnc { get; set; }
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public string UseState { get; set; } = null!;
        public int? SeqNo { get; set; }
        public string? Comment { get; set; }
    }
}
