﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbXitongmokuai
    {
        public string <PERSON><PERSON><PERSON><PERSON>hao { get; set; }
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string Mokuaileixing { get; set; }
        public string Mokuailujing { get; set; }
        public string Mokuaimc { get; set; }
        public string Mokuaimiaoshu { get; set; }
        public int Mokuaicengci { get; set; }
        public int Mokuaixuhao { get; set; }
        public int Zidongyunxing { get; set; }
    }
}
