﻿using Abp.Dependency;
using MongoDB.Bson;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.ISAS.MongoDB.Entities.DataMonitoring;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.MongoDB.Repositories;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave
{
    public class TelesignalizationResultSaveTask : ISingletonDependency
    {
        private readonly IMongoDbRepository<BsonDocument, Guid> _telesignalizationRepository;
        private readonly ConcurrentDictionary<string, List<TelesignalisationModel>> _cache = new ConcurrentDictionary<string, List<TelesignalisationModel>>();
        private readonly object _timerLock = new object();
        private Timer _cacheSaveTimer;
        private readonly RunningDataCache _runningDataCache;

        public TelesignalizationResultSaveTask(IMongoDbRepository<BsonDocument, Guid> telesignalizationRepository, 
            RunningDataCache runningDataCache)
        {
            _telesignalizationRepository = telesignalizationRepository;
            _runningDataCache = runningDataCache;
        }

        public void SaveWithCache(TelesignalisationModel model, ConnectionConfig connectionConfig)
        {
            if (model == null || connectionConfig == null)
            {
                Log4Helper.Warn(this.GetType(), "[SaveWithCache] 参数为空，model 或 connectionConfig 为 null");
                return;
            }

            string category;
            try
            {
                category = ((DataSourceCategoryEnum)connectionConfig.DataSourceCategoryName).ToString();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[SaveWithCache] 数据源分类转换失败: {connectionConfig.DataSourceCategoryName}", ex);
                return;
            }

            Log4Helper.Debug(this.GetType(), $"[SaveWithCache] 添加模型到缓存，分类: {category}, 模型ID: {model.Id}");

            _cache.AddOrUpdate(category,
                _ => new List<TelesignalisationModel> { model },
                (_, list) =>
                {
                    lock (list)
                    {
                        list.Add(model);
                    }
                    return list;
                });

            if (_cacheSaveTimer == null)
            {
                lock (_timerLock)
                {
                    if (_cacheSaveTimer == null)
                    {
                        Log4Helper.Info(this.GetType(), "[SaveWithCache] 启动缓存保存定时器");
                        _cacheSaveTimer = new Timer(async _ =>
                        {
                            try
                            {
                                Log4Helper.Debug(this.GetType(), "[CacheSaveTimer] 开始处理缓存");

                                foreach (var key in _cache.Keys)
                                {
                                    List<TelesignalisationModel> batch;

                                    lock (_cache[key])
                                    {
                                        if (_cache[key].Count == 0)
                                            continue;

                                        batch = new List<TelesignalisationModel>(_cache[key]);
                                        _cache[key].Clear();
                                    }

                                    if (batch.Count > 0)
                                    {
                                        Log4Helper.Info(this.GetType(), $"[CacheSaveTimer] 准备保存 {batch.Count} 条数据，分类: {key}");

                                        _telesignalizationRepository.CollectionName = $"{nameof(TelesignalisationModel)}_{key}{DateTime.Now:yyyyMMdd}";

                                        var documents = new List<BsonDocument>();
                                        foreach (var t in batch)
                                        {
                                            if (t.EquipmentInfoId == null || !_runningDataCache.EquipmentInfoSimDic.TryGetValue(t.EquipmentInfoId.Value, out var equipment))
                                            {
                                                Log4Helper.Warn(this.GetType(), $"[CacheSaveTimer] 找不到设备信息，EquipmentInfoId: {t.EquipmentInfoId}");
                                                continue;
                                            }

                                            var result = new TelesignalisationResult
                                            {
                                                EquipmentInfoName = equipment.Name,
                                                TelesignalisationConfigurationId = t.Id,
                                                Name = t.Name,
                                                ResultTime = t.ResultTime,
                                                ResultValue = t.ResultValue,
                                                SaveMethod = 2,
                                            };

                                            documents.Add(result.ToBsonDocument());
                                        }

                                        if (documents.Count > 0)
                                        {
                                            await _telesignalizationRepository.InsertManyAsync(documents);
                                            Log4Helper.Info(this.GetType(), $"[CacheSaveTimer] 成功保存 {documents.Count} 条记录到集合: {_telesignalizationRepository.CollectionName}");
                                        }
                                        else
                                        {
                                            Log4Helper.Warn(this.GetType(), $"[CacheSaveTimer] 没有可保存的文档，分类: {key}");
                                        }
                                    }
                                }

                                Log4Helper.Debug(this.GetType(), "[CacheSaveTimer] 缓存处理完成");
                            }
                            catch (Exception ex)
                            {
                                Log4Helper.Error(this.GetType(), "[CacheSaveTimer] 缓存保存过程中发生异常", ex);
                            }
                        }, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
                    }
                }
            }
        }
    }
}