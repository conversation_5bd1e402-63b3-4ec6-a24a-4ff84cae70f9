var energyConfigList = {
    editFormId: "editEnergyConfigForm",
    mainTableId: "energyConfigTable",
    mainTableHeight: 0,
    toolBarVueId: "energyConfigToolBar",
    toolBarVue: null,
    editorModalVueID: "energyConfigModal",
    editorModalVue: null,
    configTypeEnumMap: {
        "1": "阈值配置",
        "2": "计算参数",
        "3": "显示设置",
        "4": "报警规则"
    },
    valueTypeEnumMap: {
        "1": "字符串",
        "2": "整数",
        "3": "浮点数",
        "4": "布尔值",
        "5": "日期",
        "6": "JSON"
    },

    // 初始化列表
    initListFunc: function (tHeight) {
        energyConfigList.mainTableHeight = tHeight;
        energyConfigList.initToolbarByVue();
    },

    // 初始化工具栏Vue
    initToolbarByVue: function () {
        if (!energyConfigList.toolBarVueId) return;
        
        energyConfigList.toolBarVue = new Vue({
            el: "#" + energyConfigList.toolBarVueId,
            data: {
                name: null,
                configType: '-1',
                energyDeviceId: '-1',
                energyDevices: []
            },
            mounted: function () {
                energyConfigList.initEditModalByVue();
                this.loadEnergyDevices();
            },
            methods: {
                searchHit: function () {
                    energyConfigList.refreshTable();
                },
                handleConfigTypeChange: function (value) {
                    console.log('配置类型已变更为:', value);
                    energyConfigList.refreshTable();
                },
                handleDeviceChange: function (value) {
                    console.log('设备已变更为:', value);
                    energyConfigList.refreshTable();
                },
                addHit: function () {
                    if (substationTree.openNodeStationId === null) {
                        if (substationTree.selectNodeArr.count === 0) {
                            layer.alert("请选择变电所！");
                            return;
                        } else {
                            substationTree.openNodeStationId = substationTree.selectNodeArr[0].id;
                        }
                    }

                    energyConfigList.initEditModalValues("");
                    $("#" + energyConfigList.editorModalVueID).modal("show");
                },
                deleteHit: function () {
                    let bar = energyConfigList.getSelectItem();
                    let arrIds = new Array();
                    bar.forEach(r => arrIds.push(r.id));
                    
                    if (arrIds.length == 0) {
                        layer.alert("请选择要删除的项！");
                        return;
                    }
                    
                    isas.ajax({
                        url: AppServiceUrl.EnergyConsumptionConfig_DeleteByIds,
                        data: JSON.stringify(arrIds),
                        isHideSuccessMsg: false,
                        confirm: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                energyConfigList.refreshTable();
                            }
                        }
                    });
                },
                // 加载能耗设备
                loadEnergyDevices: function() {
                    let bar = this;
                    isas.ajax({
                        url: AppServiceUrl.EnergyDevice_GetDevicesForSelect,
                        data: JSON.stringify({
                            transformerSubstationId: substationTree.openNodeStationId ? substationTree.openNodeStationId : null
                        }),
                        isHideSuccessMsg: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                bar.energyDevices = [{ value: '-1', text: '全部' }].concat(rst.result.resultData);
                            }
                        }
                    });
                }
            }
        });
    },

    // 获取选中的行
    getSelectItem: () => $("#" + energyConfigList.mainTableId).bootstrapTable("getSelections"),
    
    // 初始化编辑模态框Vue
    initEditModalByVue: function () {
        if (!energyConfigList.editorModalVueID) return;
        
        energyConfigList.editorModalVue = new Vue({
            el: "#" + energyConfigList.editorModalVueID,
            data: {
                id: null,
                header: "添加能耗配置",
                seqNo: 1,
                name: null,
                configType: null,
                configTypes: [
                    { value: "1", text: "阈值配置" },
                    { value: "2", text: "计算参数" },
                    { value: "3", text: "显示设置" },
                    { value: "4", text: "报警规则" }
                ],
                valueType: null,
                valueTypes: [
                    { value: "1", text: "字符串" },
                    { value: "2", text: "整数" },
                    { value: "3", text: "浮点数" },
                    { value: "4", text: "布尔值" },
                    { value: "5", text: "日期" },
                    { value: "6", text: "JSON" }
                ],
                energyConsumptionDeviceId: null,
                energyDevices: [],

                equipmentInfoId: null,
                equipmentInfos: [],
                telemeteringId: null,
                telemeterOptions: [],
                telesignalisationId: null,
                telesignalOptions: [],
                configValue: null,
                description: null,
                isActive: true,
                remark: null
            },
            
            mounted: function () {
                this.loadEquipmentInfos();
                this.loadEnergyDevices();
                this.loadTelemeterOptions();
                this.loadTelesignalOptions();
            },
            
            methods: {
                setConfigType: function (value) {
                    this.configType = value;
                },
                
                setValueType: function (value) {
                    this.valueType = value;
                },
                
                setEnergyDeviceId: function (value) {
                    this.energyConsumptionDeviceId = value;
                    // 当设备ID变更时，重新加载遥测和遥信选项
                },
                setEquipmentInfoId: function (value) {
                    this.equipmentInfoId = value;
                    this.loadTelemeterOptions();
                    this.loadTelesignalOptions();
                },
                setTelemeteringId: function (value) {
                    this.telemeteringId = value;
                },
                
                setTelesignalisationId: function (value) {
                    this.telesignalisationId = value;
                },
                
                // 保存配置
                save: function () {
                    if (!$("#" + energyConfigList.editFormId).valid()) return;
                    
                    let data = {
                        id: this.id,
                        seqNo: parseInt(this.seqNo),
                        name: this.name,
                        configType: parseInt(this.configType),
                        valueType: parseInt(this.valueType),
                        energyConsumptionDeviceId: this.energyConsumptionDeviceId ? this.energyConsumptionDeviceId : null,
                        telemeteringId: this.telemeteringId ? this.telemeteringId : null,
                        telesignalisationId: this.telesignalisationId ? this.telesignalisationId : null,
                        configValue: this.configValue,
                        description: this.description,
                        isActive: this.isActive,
                        remark: this.remark
                    };
                    
                    isas.ajax({
                        url: AppServiceUrl.EnergyConsumptionConfig_CreateOrUpdate,
                        data: JSON.stringify(data),
                        isHideSuccessMsg: false,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                //$("#" + energyConfigList.editorModalVueID).modal("hide");
                                energyConfigList.refreshTable();
                            }
                        }
                    });
                },
                
                // 加载能耗设备列表
                loadEnergyDevices: function () {
                    let bar = this;
                    isas.ajax({
                        type:'get',
                        url: AppServiceUrl.EnergyDevice_GetDevicesForSelect,
                        data: JSON.stringify({
                            transformerSubstationId: substationTree.openNodeStationId ? substationTree.openNodeStationId : null,
                        }),
                        isHideSuccessMsg: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                bar.energyDevices = rst.result.resultData;
                            }
                        }
                    });
                },
                // 加载关联设备列表
                loadEquipmentInfos: function () {
                    let bar = this;
                    isas.ajax({

                        url: AppServiceUrl.EquipmentInfo_FindEquipmentInfoForSelect,
                        data: JSON.stringify({
                            transformerSubstationId: substationTree.openNodeStationId ? substationTree.openNodeStationId : null,
                        }),
                        isHideSuccessMsg: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                bar.equipmentInfos = rst.result.resultData;
                            }
                        }
                    });
                },
                // 加载遥测列表
                loadTelemeterOptions: function() {
                    let bar = this;
                    isas.ajax({
                        url: AppServiceUrl.TelemeteringConfiguration_FindTelemeteringConfigurationForSelect,
                        data: JSON.stringify({
                            transformerSubstationId: substationTree.openNodeStationId ? substationTree.openNodeStationId : null,
                            equipmentInfoId: bar.equipmentInfoId ? bar.equipmentInfoId : null
                        }),
                        isHideSuccessMsg: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                bar.telemeterOptions = rst.result.resultData;
                                // 如果当前选中的遥测不在筛选结果中，则重置选择
                                if (bar.telemeteringId && !bar.telemeterOptions.some(x => x.value === bar.telemeteringId)) {
                                    bar.telemeteringId = null;
                                }
                            }
                        }
                    });
                },
                
                // 加载遥信列表
                loadTelesignalOptions: function() {
                    let bar = this;
                    isas.ajax({
                        url: AppServiceUrl.TelesignalisationConfiguration_FindTelesignalisationConfigurationForSelect,
                        data: JSON.stringify({
                            transformerSubstationId: substationTree.openNodeStationId ? substationTree.openNodeStationId : null,
                            equipmentInfoId: bar.equipmentInfoId ? bar.equipmentInfoId : null
                        }),
                        isHideSuccessMsg: true,
                        success: function (rst) {
                            if (rst.result && rst.result.flag) {
                                bar.telesignalOptions = rst.result.resultData;
                                // 如果当前选中的遥信不在筛选结果中，则重置选择
                                if (bar.telesignalisationId && !bar.telesignalOptions.some(x => x.value === bar.telesignalisationId)) {
                                    bar.telesignalisationId = null;
                                }
                            }
                        }
                    });
                }
            }
        });
    },

    // 初始化编辑模态框各值
    initEditModalValues: function (uniqueId) {
        try {
            if (!energyConfigList.editorModalVue || !energyConfigList.mainTableId) return;
            
            var rowData = null;
            if (uniqueId)
                rowData = $("#" + energyConfigList.mainTableId).bootstrapTable("getRowByUniqueId", uniqueId);
            
            energyConfigList.resetFormValidate();
            
            if (!rowData) {
                rowData = {
                    id: null,
                    seqNo: 1,
                    name: null,
                    configType: null,
                    valueType: null,
                    energyConsumptionDeviceId: null,
                    telemeteringId: null,
                    telesignalisationId: null,
                    configValue: null,
                    description: null,
                    isActive: true,
                    remark: null
                };
                
                energyConfigList.editorModalVue.header = "添加能耗配置";
            }
            
            if (rowData.id) {
                energyConfigList.editorModalVue.header = "编辑能耗配置";
            }
            
            // 设置记录单各参数
            energyConfigList.editorModalVue.id = rowData.id;
            energyConfigList.editorModalVue.seqNo = rowData.seqNo;
            energyConfigList.editorModalVue.name = rowData.name;
            energyConfigList.editorModalVue.configType = rowData.configType ? rowData.configType.toString() : null;
            energyConfigList.editorModalVue.valueType = rowData.valueType ? rowData.valueType.toString() : null;
            energyConfigList.editorModalVue.energyConsumptionDeviceId = rowData.energyConsumptionDeviceId;
            energyConfigList.editorModalVue.telemeteringId = rowData.telemeteringId;
            energyConfigList.editorModalVue.telesignalisationId = rowData.telesignalisationId;
            energyConfigList.editorModalVue.configValue = rowData.configValue;
            energyConfigList.editorModalVue.description = rowData.description;
            energyConfigList.editorModalVue.isActive = rowData.isActive;
            energyConfigList.editorModalVue.remark = rowData.remark;
            
        } catch (e) {
            console.log(e);
        }
    },

    // 表单验证
    initEditFormValidate: function () {
        if (!energyConfigList.editFormId) return;
        
        $("#" + energyConfigList.editFormId).validate({
            ignore: ":hidden:not(select)",
            rules: {
                name: {
                    required: true
                },
                configType: {
                    required: true
                },
                valueType: {
                    required: true
                }
            },
            messages: {
                name: {
                    required: "配置名称不能为空"
                },
                configType: {
                    required: "配置类型不能为空"
                },
                valueType: {
                    required: "值类型不能为空"
                }
            }
        });
    },
    
    // 刷新表格
    refreshTable: function () {
        $("#" + energyConfigList.mainTableId).bootstrapTable("refresh");
    },
    
    // 重置验证
    resetFormValidate: function () {
        $("#" + energyConfigList.editFormId).validate().resetForm();
        $("#" + energyConfigList.editFormId).find(".form-group").removeClass("has-success").removeClass("has-error");
    },

    // 初始化表格
    initTable: function () {
        if (!energyConfigList.mainTableId) return;
        
        isas.bootstrapTable({
            el: "#" + energyConfigList.mainTableId,
            height: energyConfigList.mainTableHeight,
            toolBarEl: "#" + energyConfigList.toolBarVueId,
            url: AppServiceUrl.EnergyConsumptionConfig_GetConfigs,
            isInitData: false,
            singleSelect: false,
            pageList: [15, 25, 50, 100, 200],
            pageSize: 15,
            showColumns: true,
            queryParams: function (params) {
                let c = {
                    pageIndex: params.offset / params.limit + 1,
                    pageSize: params.limit,
                    searchCondition: {
                        name: energyConfigList.toolBarVue === null ? null : energyConfigList.toolBarVue.configName,
                        configType: energyConfigList.toolBarVue && energyConfigList.toolBarVue.configType != '-1' ? 
                                    parseInt(energyConfigList.toolBarVue.configType) : null,
                        energyConsumptionDeviceId: energyConfigList.toolBarVue && energyConfigList.toolBarVue.energyDeviceId != '-1' ?
                                    energyConfigList.toolBarVue.energyDeviceId : null
                    },
                    sorting: ""
                };
                return c;
            },
            columns: [
                {
                    checkbox: true,
                    align: "center",
                    valign: "middle",
                    class: "checkbox checkbox-primary",
                    width: 20
                },
                {
                    title: "行号",
                    align: "center",
                    valign: "middle",
                    width: 20,
                    formatter: function (value, row, index) {
                        let pageSize = $("#" + energyConfigList.mainTableId).bootstrapTable("getOptions").pageSize;
                        let pageNumber = $("#" + energyConfigList.mainTableId).bootstrapTable("getOptions").pageNumber;
                        return (pageNumber - 1) * pageSize + 1 + index;
                    }
                },
                {
                    field: "seqNo",
                    title: "序号",
                    align: "center",
                    valign: "middle",
                    width: 20
                },
                {
                    field: "name",
                    title: "配置名称",
                    align: "center",
                    valign: "middle",
                },
                {
                    field: "energyConsumptionDevice",
                    title: "关联设备",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        if (value && value.equipmentInfo) {
                            return value.equipmentInfo.name;
                        }
                        return value ? value.name : '-';
                    }
                },
                {
                    field: "telemeteringConfiguration",
                    title: "相关遥测",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        if (value && value.name) {
                            return value.name;
                        }
                        return value ? value.name : '-';
                    }
                },
                {
                    field: "configType",
                    title: "配置类型",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        return energyConfigList.configTypeEnumMap[value] || "未知";
                    }
                },
                {
                    field: "valueType",
                    title: "值类型",
                    align: "center",
                    valign: "middle",
                    formatter: function (value) {
                        return energyConfigList.valueTypeEnumMap[value] || "未知";
                    }
                },
                {
                    field: "configValue",
                    title: "配置值",
                    align: "center",
                    valign: "middle"
                },
                {
                    field: "description",
                    title: "描述",
                    align: "center",
                    valign: "middle"
                },
                {
                    field: "isActive",
                    title: "是否启用",
                    align: "center",
                    valign: "middle",
                    width: 50,
                    formatter: function (value) {
                        return value ? 
                            "<span class='text-success' style='font-size:16px;'><i class='fa fa-check'></i></span>" :
                            "<span class='text-danger' style='font-size:16px;'><i class='fa fa-close'></i></span>";
                    }
                },
                {
                    field: 'operation',
                    visible: true,
                    title: "操作",
                    align: "center",
                    valign: "middle",
                    width: 60,
                    formatter: function (value, row) {
                        let btnHtml = "<button class='btn-link' title='修改' data-toggle='modal' data-target='#" +
                            energyConfigList.editorModalVueID +
                            "' onclick='energyConfigList.initEditModalValues(\"" +
                            row.id +
                            "\")'>";
                        btnHtml += "<i class='fa fa-pencil' style='font-size:18px;'></i>";
                        btnHtml += "</button>";
                        return btnHtml;
                    }
                }
            ]
        });
    }
};

