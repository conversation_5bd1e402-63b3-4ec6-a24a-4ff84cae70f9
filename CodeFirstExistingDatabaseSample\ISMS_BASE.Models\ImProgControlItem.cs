﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImProgControlItem
    {
        public string Id { get; set; } = null!;
        public string ProgCtlId { get; set; } = null!;
        public string YkdataId { get; set; } = null!;
        public int Ykstate { get; set; }
        public int SeqNo { get; set; }
        public int WaitYxtimeout { get; set; }
        public int DelayTime { get; set; }

        public virtual ImProgControl ProgCtl { get; set; } = null!;
        public virtual ImDeviceYk Ykdata { get; set; } = null!;
    }
}
