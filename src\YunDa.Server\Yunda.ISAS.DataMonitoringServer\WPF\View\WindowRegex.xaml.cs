﻿using Abp.Dependency;
using System.Windows;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection;

namespace Yunda.ISAS.DataMonitoringServer.WPF.View
{
    /// <summary>
    /// WindowRegex.xaml 的交互逻辑
    /// </summary>
    public partial class WindowRegex : Window, ISingletonDependency
    {
        private readonly DataSendTask _dataSendTaskInstance;


        public WindowRegex(DataSendTask dataSendTaskInstance)
        {
            _dataSendTaskInstance = dataSendTaskInstance;
            InitializeComponent();
            this.Closing += (s, e) => { e.Cancel = true; this.Hide(); };
            //this.startInfo.Text = dataSendTaskInstance.startInfoAddr.ToString();
            //this.endInfo.Text = dataSendTaskInstance.endInfoAddr.ToString();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            //int.TryParse(this.startInfo.Text, out _dataSendTaskInstance.startInfoAddr);
            //int.TryParse(this.endInfo.Text, out _dataSendTaskInstance.endInfoAddr);
            this.Close();
        }

        private void Cancek_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
