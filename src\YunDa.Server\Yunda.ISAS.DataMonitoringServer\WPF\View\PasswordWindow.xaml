﻿<Window x:Class="Yunda.SOMS.DataMonitoringServer.WPF.View.PasswordWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:Yunda.SOMS.DataMonitoringServer.WPF.View"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="安全验证" 
        Height="250" 
        Width="400"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="微软雅黑"
        WindowStyle="ToolWindow"
        ResizeMode="NoResize"
        KeyDown="Window_KeyDown">
        
    <materialDesign:Card Margin="16" Padding="8" UniformCornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 标题部分 -->
            <StackPanel Grid.Row="0" Margin="0,0,0,16">
                <TextBlock 
                    Text="确认密码" 
                    Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                    HorizontalAlignment="Center"
                    Margin="0,0,0,8"/>
                <Separator/>
            </StackPanel>
            
            <!-- 密码提示 -->
            <StackPanel Grid.Row="1" Margin="0,0,0,16">
                <TextBlock 
                    Text="请输入确认密码" 
                    Style="{StaticResource MaterialDesignBody1TextBlock}" 
                    HorizontalAlignment="Center"
                    Margin="0,0,0,8"/>
                <TextBlock 
                    Text="密码提示：1" 
                    Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                    HorizontalAlignment="Center"
                    Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
            
            <!-- 密码输入框 -->
            <PasswordBox 
                Grid.Row="2"
                Name="PasswordBox" 
                Margin="24,0" 
                Style="{StaticResource MaterialDesignPasswordBox}"
                materialDesign:HintAssist.Hint="请输入密码"
                materialDesign:TextFieldAssist.HasClearButton="True"
                materialDesign:TextFieldAssist.UnderlineBrush="{DynamicResource PrimaryHueMidBrush}"
                FontSize="16"
                PasswordChanged="PasswordBox_PasswordChanged"/>
                
            <!-- 错误提示 - 默认隐藏 -->
            <TextBlock 
                Grid.Row="2"
                x:Name="ErrorMessage"
                Text="密码错误，请重试"
                Foreground="Red"
                Style="{StaticResource MaterialDesignCaptionTextBlock}" 
                HorizontalAlignment="Center"
                Margin="0,36,0,0"
                Visibility="Collapsed"/>
                
            <!-- 按钮区域 -->
            <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,24,0,0">
                <Button 
                    Name="CancelButton" 
                    Content="取消" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100" 
                    Margin="8,0"
                    Click="CancelButton_Click"/>
                    
                <Button 
                    Name="ConfirmButton" 
                    Content="确认" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="100" 
                    Margin="8,0"
                    Click="ConfirmButton_Click"
                    IsDefault="True"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</Window>
