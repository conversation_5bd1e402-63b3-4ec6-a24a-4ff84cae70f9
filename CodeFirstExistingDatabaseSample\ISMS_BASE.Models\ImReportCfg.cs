﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImReportCfg
    {
        public ImReportCfg()
        {
            ImReportCfgData = new HashSet<ImReportCfgDatum>();
        }

        public string Id { get; set; } = null!;
        public string RptName { get; set; } = null!;
        public string RptType { get; set; } = null!;
        public DateTime CreateTime { get; set; }
        public string? TemplateName { get; set; }
        public int? ExpStartRow { get; set; }
        public int? ExpStartCol { get; set; }

        public virtual ImReportType RptTypeNavigation { get; set; } = null!;
        public virtual ICollection<ImReportCfgDatum> ImReportCfgData { get; set; }
    }
}
