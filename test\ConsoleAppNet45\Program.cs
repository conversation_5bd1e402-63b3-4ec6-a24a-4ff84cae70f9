﻿using MathConsoleApp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace ConsoleAppNet45
{
    internal class Program
    {
        private static void Main(string[] args)
        {
            //ActionBlock<bool> ExcuteBlock = new ActionBlock<bool>(x => { Console.WriteLine(x); }); ;
            //Task<bool> task = new Task<bool>(() => 
            //{
            //    for (int i = 0; i < 100; i++)
            //    {
            //        Task.Delay(50).Wait();
            //        ExcuteBlock.Post(false);
            //    }
            //    return false;
            //});
            // task.Start();
            //while (true)
            //{
            //    Task.Delay(500).Wait();
            //    var res = ExcuteBlock.Completion.IsCompleted;
            //    Console.WriteLine(res+ "Completion");
            //}
            DateTime date = default;
            Console.WriteLine(date.ToLongTimeString());
            //ExcuteBlock.
            //    var res = task.Result;
            //var res =   ExcuteBlock.AsObserver();
            //res.OnCompleted();
            //Debug.WriteLine();
            Console.ReadLine();

        }
    }
}