﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFaultTypePd
    {
        public ImFaultTypePd()
        {
            ImFaultActTypePds = new HashSet<ImFaultActTypePd>();
            ImFaultParamPds = new HashSet<ImFaultParamPd>();
        }

        public int FaultCode { get; set; }
        public string FaultName { get; set; } = null!;

        public virtual ICollection<ImFaultActTypePd> ImFaultActTypePds { get; set; }
        public virtual ICollection<ImFaultParamPd> ImFaultParamPds { get; set; }
    }
}
