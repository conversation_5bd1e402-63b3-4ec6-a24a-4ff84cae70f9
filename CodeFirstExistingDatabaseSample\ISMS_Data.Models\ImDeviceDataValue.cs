﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_Data.Models
{
    public partial class ImDeviceDataValue
    {
        public string DataId { get; set; } = null!;
        public DateTime OccTime { get; set; }
        public string TimeSrc { get; set; } = null!;
        public string? ValueStr { get; set; }
        public double RealValue { get; set; }
        public string? Unit { get; set; }
    }
}
