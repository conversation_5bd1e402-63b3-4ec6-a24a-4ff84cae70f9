﻿using Quartz;
using Quartz.Impl;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace QuartzNETTest
{
    class Program
    {
        public static List<Action> actions = new List<Action>();

        static async Task Main(string[] args)
        {
            //var ret = ConnectionMultiplexer.Connect("127.0.0.1:6379,allowadmin=true");
            //IDatabase database = ret.GetDatabase(0);
            //ISubscriber subscriber = ret.GetSubscriber();
            //subscriber.Subscribe("__keyevent@0__:expired", (channel, notificationType) =>
            //{
            //    Console.WriteLine(channel + "|" + notificationType);
            //});
            //Console.ReadKey();
            StdSchedulerFactory factory = new StdSchedulerFactory();

            // get a scheduler
            IScheduler scheduler = await factory.GetScheduler();
            await scheduler.Start();

            // define the job and tie it to our HelloJob class
            Guid guid = Guid.NewGuid();
            IJobDetail job = JobBuilder.Create<HelloJob>()
                .WithIdentity(guid.ToString())
                .Build();
            guid = Guid.NewGuid();
            // Trigger the job to run now, and then every 40 seconds
            ITrigger trigger = TriggerBuilder.Create()
                .WithIdentity(guid.ToString())
                .StartNow()
                .WithSimpleSchedule(x => x
                    .WithIntervalInSeconds(1)
                    .RepeatForever())
            .Build();
            await scheduler.ScheduleJob(job, trigger);
        rwer: var read = Console.ReadLine();
            switch (read)
            {
                case "1": await scheduler.PauseAll(); break;
                case "2": await scheduler.ResumeAll(); break;
            }
            goto rwer;

        }
    }

    public class HelloJob : IJob
    {

        public async Task Execute(IJobExecutionContext context)
        {
            Console.WriteLine(DateTime.Now.ToLongTimeString());
            //Program. actions.Add(() => {  });
            //foreach (var item in Program.actions)
            //{
            //    item();
            //}

        }
    }
}
