﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImCurveitem
    {
        public string Curveid { get; set; }
        public string Dataid { get; set; }
        public int? Linecolor { get; set; }
        public int? Linewidth { get; set; }

        public virtual ImCurve Curve { get; set; }
        public virtual ImDevicedata Data { get; set; }
    }
}
