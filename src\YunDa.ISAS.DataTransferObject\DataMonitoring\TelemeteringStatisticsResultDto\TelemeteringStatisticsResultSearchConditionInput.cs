using System;
using System.Collections.Generic;
using YunDa.ISAS.DataTransferObject.CommonDto;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsResultDto
{
    /// <summary>
    /// 遥测统计结果搜索条件
    /// </summary>
    public class TelemeteringStatisticsResultSearchConditionInput : ISASMongoSearchInput
    {
        /// <summary>
        /// 关联的遥测ID列表
        /// </summary>
        public virtual List<Guid> TelemeteringConfigurationIds { get; set; }

        /// <summary>
        /// 统计类型列表
        /// </summary>
        public virtual List<StatisticsTypeEnum> StatisticsTypes { get; set; }

        /// <summary>
        /// 统计时间间隔
        /// </summary>
        public virtual FixedIntervalEnum? IntervalType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public virtual DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public virtual DateTime? EndTime { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 变电站ID
        /// </summary>
        public virtual Guid? TransformerSubstationId { get; set; }
    }
} 