﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImGrid
    {
        public string Id { get; set; }
        public int Rowcnt { get; set; }
        public int Colcnt { get; set; }

        public virtual ImGlyph IdNavigation { get; set; }
    }
}
