using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using MongoDB.Bson.Serialization.Attributes;
using System;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsResultDto
{
    /// <summary>
    /// 遥测统计结果输出DTO
    /// </summary>
    [AutoMapFrom(typeof(TelemeteringStatisticsResult))]
    public class TelemeteringStatisticsResultOutput : EntityDto<Guid>
    {
        /// <summary>
        /// 关联的遥测ID
        /// </summary>
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public virtual string EquipmentInfoName { get; set; }

        /// <summary>
        /// 遥测名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 结果值
        /// </summary>
        public virtual float ResultValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public virtual string Unit { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public virtual StatisticsTypeEnum StatisticsType { get; set; }

        /// <summary>
        /// 统计类型名称
        /// </summary>
        public virtual string StatisticsTypeName { get; set; }

        /// <summary>
        /// 统计时间间隔
        /// </summary>
        public virtual FixedIntervalEnum IntervalType { get; set; }

        /// <summary>
        /// 统计时间间隔名称
        /// </summary>
        public virtual string IntervalTypeName { get; set; }

        /// <summary>
        /// 统计时间
        /// </summary>
        public virtual DateTime StatisticsDateTime { get; set; }

        /// <summary>
        /// 原始数据数量
        /// </summary>
        public virtual int DataCount { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public virtual DateTime CreationTime { get; set; }
    }
} 