﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

class Program
{
    static void Main()
    {
        // 提示用户程序开始
        Console.WriteLine("开始测试 CPU 性能，请稍等...");

        // 获取当前时间戳
        var stopwatch = new Stopwatch();

        // 单核性能测试
        Console.WriteLine("\n开始单核性能测试...");
        stopwatch.Start();
        TestSingleCorePerformance();
        stopwatch.Stop();
        Console.WriteLine($"单核测试耗时：{stopwatch.ElapsedMilliseconds} 毫秒");

        // 多核性能测试
        Console.WriteLine("\n开始多核性能测试...");
        stopwatch.Restart();
        TestMultiCorePerformance();
        stopwatch.Stop();
        Console.WriteLine($"多核测试耗时：{stopwatch.ElapsedMilliseconds} 毫秒");

        // 暂停程序，等待用户查看结果
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    // 单核性能测试：在一个线程上执行计算
    static void TestSingleCorePerformance()
    {
        int iterations = 10000000;  // 迭代次数，用于增加计算量

        // 1. 递归斐波那契数列
        Console.WriteLine("计算递归斐波那契数列...");
        var fibResult = Fibonacci(40);  // 递归计算斐波那契数列
        Console.WriteLine($"斐波那契结果：{fibResult}");

        // 2. 数值积分
        Console.WriteLine("计算数值积分...");
        double integralResult = TrapezoidalRule(0, Math.PI, iterations, Math.Sin);
        Console.WriteLine($"积分结果：{integralResult}");

        // 3. 素数筛选
        Console.WriteLine("筛选素数...");
        var primes = SieveOfEratosthenes(iterations);  // 返回 int[] 数组
        Console.WriteLine($"找到的素数个数：{primes.Length}");

        // 4. 矩阵乘法
        Console.WriteLine("进行矩阵乘法...");
        var matrixResult = MatrixMultiplication(5000);
        Console.WriteLine($"矩阵乘法结果第一项：{matrixResult[0, 0]}");
    }

    // 多核性能测试：使用 Parallel.For 来并行执行任务
    static void TestMultiCorePerformance()
    {
        long iterationsPerCore = 25000000;  // 每个核心的计算迭代次数
        int coreCount = Environment.ProcessorCount; // 获取当前计算机的核心数
        double[] results = new double[coreCount];  // 存储每个核心的计算结果

        // 使用 Parallel.For 来并行计算
        Parallel.For(0, coreCount, coreIndex =>
        {
            // 1. 数值积分
            double result = TrapezoidalRule(0, Math.PI, 1000000, Math.Sin);

            // 2. 素数筛选
            var primes = SieveOfEratosthenes(1000000);

            // 3. 矩阵乘法
            var matrixResult = MatrixMultiplication(500);
            result += matrixResult[0, 0];

            results[coreIndex] = result;  // 存储每个核心的计算结果
        });

        // 输出每个核心的计算结果（仅作为占位符）
        for (int i = 0; i < coreCount; i++)
        {
            Console.WriteLine($"核心 {i + 1} 的计算结果：{results[i]}");
        }
    }

    // 递归计算斐波那契数列
    static long Fibonacci(int n)
    {
        if (n <= 1)
            return n;
        return Fibonacci(n - 1) + Fibonacci(n - 2);
    }

    // 数值积分：梯形法则
    static double TrapezoidalRule(double a, double b, int n, Func<double, double> f)
    {
        double h = (b - a) / n;
        double sum = (f(a) + f(b)) / 2.0;

        for (int i = 1; i < n; i++)
        {
            sum += f(a + i * h);
        }

        return sum * h;
    }

    // 埃拉托斯特尼筛法：筛选素数
    static int[] SieveOfEratosthenes(int limit)
    {
        // 创建布尔数组，true表示该位置是素数，false表示不是素数
        bool[] sieve = new bool[limit + 1];
        for (int i = 2; i <= limit; i++)
        {
            sieve[i] = true;
        }

        // 筛选出素数
        for (int i = 2; i * i <= limit; i++)
        {
            if (sieve[i])
            {
                for (int j = i * i; j <= limit; j += i)
                {
                    sieve[j] = false;
                }
            }
        }

        // 将布尔数组中的素数转化为整数数组
        var primesList = new System.Collections.Generic.List<int>();
        for (int i = 2; i <= limit; i++)
        {
            if (sieve[i])
            {
                primesList.Add(i);
            }
        }

        return primesList.ToArray();  // 返回素数数组
    }

    // 矩阵乘法：两个大矩阵的乘法
    static double[,] MatrixMultiplication(int size)
    {
        double[,] matrixA = new double[size, size];
        double[,] matrixB = new double[size, size];
        double[,] result = new double[size, size];

        // 初始化矩阵A和矩阵B
        Random rand = new Random();
        for (int i = 0; i < size; i++)
        {
            for (int j = 0; j < size; j++)
            {
                matrixA[i, j] = rand.NextDouble();
                matrixB[i, j] = rand.NextDouble();
            }
        }

        // 矩阵相乘
        for (int i = 0; i < size; i++)
        {
            for (int j = 0; j < size; j++)
            {
                for (int k = 0; k < size; k++)
                {
                    result[i, j] += matrixA[i, k] * matrixB[k, j];
                }
            }
        }

        return result;
    }
}
