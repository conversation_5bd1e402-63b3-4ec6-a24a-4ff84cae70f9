﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbAidVideoChannel
    {
        public TbAidVideoChannel()
        {
            TbPresetPoints = new HashSet<TbPresetPoint>();
            TbVideoChannelRights = new HashSet<TbVideoChannelRight>();
        }

        public string ChanId { get; set; } = null!;
        public string DevId { get; set; } = null!;
        public string ChanName { get; set; } = null!;
        public string? ChanType { get; set; }
        public int ChanNo { get; set; }
        public string? IdinPlat { get; set; }
        public string IsPtz { get; set; } = null!;
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public string UseState { get; set; } = null!;
        public int? SeqNo { get; set; }
        public string? Comment { get; set; }

        public virtual TbAidVideoDev Dev { get; set; } = null!;
        public virtual ICollection<TbPresetPoint> TbPresetPoints { get; set; }
        public virtual ICollection<TbVideoChannelRight> TbVideoChannelRights { get; set; }
    }
}
