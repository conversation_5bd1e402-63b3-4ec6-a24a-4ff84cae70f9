﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImProgControl
    {
        public ImProgControl()
        {
            ImProgControlItems = new HashSet<ImProgControlItem>();
        }

        public string Id { get; set; } = null!;
        public string PrjId { get; set; } = null!;
        public string ProgCtlName { get; set; } = null!;
        public DateTime CreateTime { get; set; }

        public virtual ImProject Prj { get; set; } = null!;
        public virtual ICollection<ImProgControlItem> ImProgControlItems { get; set; }
    }
}
