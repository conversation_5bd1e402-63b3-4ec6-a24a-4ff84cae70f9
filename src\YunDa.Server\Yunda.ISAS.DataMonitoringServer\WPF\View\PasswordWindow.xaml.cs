﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace Yunda.SOMS.DataMonitoringServer.WPF.View
{
    /// <summary>
    /// PasswordWindow.xaml 的交互逻辑
    /// </summary>
    public partial class PasswordWindow : Window
    {
        private bool _isPasswordCorrect = false;

        public PasswordWindow()
        {
            InitializeComponent();
            PasswordBox.Focus();
        }

        // 确认按钮点击事件
        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            ValidatePassword();
        }

        // 取消按钮点击事件
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // 密码框内容变化时事件
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            // 如果之前显示了错误信息，当用户开始输入新密码时隐藏错误信息
            if (ErrorMessage.Visibility == Visibility.Visible)
            {
                ErrorMessage.Visibility = Visibility.Collapsed;
            }
        }

        // 窗口键盘按键事件
        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                DialogResult = false;
                Close();
            }
        }

        // 密码验证方法
        private void ValidatePassword()
        {
            _isPasswordCorrect = PasswordBox.Password == "1";

            if (_isPasswordCorrect)
            {
                DialogResult = true;
                Close();
            }
            else
            {
                // 显示密码错误信息
                ErrorMessage.Visibility = Visibility.Visible;

                // 创建震动动画以提供视觉反馈
                DoubleAnimation shakeAnimation = new DoubleAnimation
                {
                    From = -5,
                    To = 5,
                    Duration = TimeSpan.FromMilliseconds(50),
                    AutoReverse = true,
                    RepeatBehavior = new RepeatBehavior(3)
                };

                // 应用到密码框使其轻微摇晃
                PasswordBox.RenderTransform = new TranslateTransform();
                PasswordBox.RenderTransform.BeginAnimation(TranslateTransform.XProperty, shakeAnimation);

                // 清空密码框并重新获取焦点
                PasswordBox.Password = string.Empty;
                PasswordBox.Focus();
            }
        }
    }
}
