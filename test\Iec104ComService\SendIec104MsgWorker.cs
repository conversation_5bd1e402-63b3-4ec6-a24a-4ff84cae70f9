﻿using Microsoft.Extensions.Hosting;
using NetMQ.Sockets;
using System.Threading;
using System.Threading.Tasks;

namespace Iec104ComService
{
    class SendIec104MsgWorker : BackgroundService
    {
        private readonly SubscriberSocket _subscriberSocket;
        public SendIec104MsgWorker(SubscriberSocket subscriberSocket)
        {
            _subscriberSocket = subscriberSocket;
            _subscriberSocket.Subscribe("");
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                //string messageTopicReceived = _subscriberSocket.ReceiveFrameString();
                //string messageReceived = _subscriberSocket.ReceiveFrameString();
                //Log.Information(messageReceived);
                //Log.Information("SendIec104MsgWorker");
                await Task.Delay(1000);
            }

        }
    }
}
