﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImShebeilx
    {
        public ImShebeilx()
        {
            ImShebei = new HashSet<ImShebei>();
        }

        public int Sblxbm { get; set; }
        public string Sblxmc { get; set; }
        public int? Sblxxh { get; set; }

        public virtual ICollection<ImShebei> ImShebei { get; set; }
    }
}
