﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImPuctgyFltrptitem
    {
        public int Puctgycode { get; set; }
        public string Itemname { get; set; }
        public string Showlabel { get; set; }

        public virtual ImFaultreportitem ItemnameNavigation { get; set; }
        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
    }
}
