﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbYongHu
    {
        public TbYongHu()
        {
            TbQuanXians = new HashSet<TbQuanXian>();
            TbVideoChannelRights = new HashSet<TbVideoChannelRight>();
            YongHuZus = new HashSet<TbYongHu>();
            YongHus = new HashSet<TbYongHu>();
        }

        public string YongHuMing { get; set; } = null!;
        public string LeiXing { get; set; } = null!;
        public string? QuanMing { get; set; }
        public string? YongHuMiaoShu { get; set; }
        public DateTime ChuangJianSj { get; set; }
        public string? KouLing { get; set; }
        public string? ZhuangTai { get; set; }
        public string? ZhuYe { get; set; }
        public string? BuMenId { get; set; }
        public int ErrCount { get; set; }
        public DateTime? LastErrTime { get; set; }

        public virtual ICollection<TbQuanXian> TbQuanXians { get; set; }
        public virtual ICollection<TbVideoChannelRight> TbVideoChannelRights { get; set; }

        public virtual ICollection<TbYongHu> YongHuZus { get; set; }
        public virtual ICollection<TbYongHu> YongHus { get; set; }
    }
}
