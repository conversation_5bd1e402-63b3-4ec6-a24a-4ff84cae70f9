﻿using System;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.SearchCondition
{
    /// <summary>
    /// 遥控配置查询条件
    /// </summary>
    public class TelecommandConfigurationSearchConditionInput : ISASMySQLSearchInput
    {
        /// <summary>
        /// 遥控名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 关联监控设备表
        /// </summary>
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 关联监控设备类型表
        /// </summary>
        public virtual Guid? EquipmentTypeId { get; set; }
        /// <summary>
        /// 调度地址
        /// </summary>  
        public virtual int? DispatcherAddress { get; set; }
        /// <summary>
        /// 变电所
        /// </summary>
        public virtual Guid? TransformerSubstationId { get; set; }
        /// <summary>
        /// 数据来源
        /// </summary>
        public virtual DataSourceCategoryEnum? DataSourceCategory { get; set; }
    }
}