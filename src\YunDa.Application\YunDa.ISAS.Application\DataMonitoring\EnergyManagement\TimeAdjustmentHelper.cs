using System;
using System.Globalization;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    /// <summary>
    /// 时间调整助手类，提供各种查询时间调整的方法
    /// </summary>
    public static class TimeAdjustmentHelper
    {
        /// <summary>
        /// 根据时间间隔类型获取需要前移的时间偏移量
        /// 由于数据存储和处理有延迟，需要将查询截止时间前移一段时间，确保查询到的都是有效数据
        /// 不同的时间间隔类型需要不同的偏移量，以保证数据的完整性和准确性
        /// </summary>
        /// <param name="interval">时间间隔类型</param>
        /// <returns>需要前移的时间偏移量</returns>
        public static TimeSpan GetEndTimeSpanInterval(RealTimePowerTypeEnum interval)
        {
            switch (interval)
            {
                case RealTimePowerTypeEnum.RealTime:
                    return TimeSpan.FromMinutes(1); // 实时数据前移1分钟
                case RealTimePowerTypeEnum.Hourly:
                    return TimeSpan.FromMinutes(5); // 小时数据前移5分钟
                case RealTimePowerTypeEnum.Daily:
                    return TimeSpan.FromMinutes(30); // 天数据前移30分钟
                case RealTimePowerTypeEnum.Weekly:
                    return TimeSpan.FromHours(2); // 周数据前移2小时
                case RealTimePowerTypeEnum.Monthly:
                    return TimeSpan.FromHours(6); // 月数据前移6小时
                case RealTimePowerTypeEnum.Yearly:
                    return TimeSpan.FromDays(1); // 年数据前移1天
                default:
                    return TimeSpan.FromMinutes(5); // 默认前移5分钟
            }
        }

        /// <summary>
        /// 获取中文星期几
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>中文星期几</returns>
        public static string GetChineseDayOfWeek(DateTime date)
        {
            string[] weekdays = { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
            int dayOfWeek = (int)date.DayOfWeek;
            return weekdays[dayOfWeek];
        }
    }
} 