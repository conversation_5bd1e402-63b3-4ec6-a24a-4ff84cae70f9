﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbWorkflow
    {
        public TbWorkflow()
        {
            TbBusinesses = new HashSet<TbBusiness>();
            TbWfSteps = new HashSet<TbWfStep>();
        }

        public string Id { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string State { get; set; } = null!;

        public virtual ICollection<TbBusiness> TbBusinesses { get; set; }
        public virtual ICollection<TbWfStep> TbWfSteps { get; set; }
    }
}
