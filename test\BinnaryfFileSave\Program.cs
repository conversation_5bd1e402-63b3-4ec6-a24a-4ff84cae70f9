﻿namespace BinnaryfFileSave
{
    using System;
    using System.IO;

    class Program
    {
        static void Main()
        {
            int count = int.MaxValue/2;
            double[] doubleArray = new double[count];
            Parallel.For(0, count, i =>
            {
                double angle = i * 2 * Math.PI / count;
                doubleArray[i] = Math.Sin(angle);
            });
            // 写入double数组到二进制文件
            WriteDoubleArrayToBinaryFile("doubleArray.bin", doubleArray);

            // 从二进制文件读取double数组
            double[] readDoubleArray = ReadDoubleArrayFromBinaryFile("doubleArray.bin");

            Console.WriteLine("原始 double 数组:");
            foreach (double value in doubleArray)
            {
                Console.Write(value + " ");
            }
            Console.WriteLine();

            Console.WriteLine("从文件读取的 double 数组:");
            foreach (double value in readDoubleArray)
            {
                Console.Write(value + " ");
            }
            Console.WriteLine();
        }

        static void WriteDoubleArrayToBinaryFile(string filePath, double[] array)
        {
            using (BinaryWriter writer = new BinaryWriter(File.Open(filePath, FileMode.Create)))
            {
                foreach (double value in array)
                {
                    writer.Write(value);
                }
            }
        }

        static double[] ReadDoubleArrayFromBinaryFile(string filePath)
        {
            using (BinaryReader reader = new BinaryReader(File.Open(filePath, FileMode.Open)))
            {
                long fileSize = new FileInfo(filePath).Length;
                int elementCount = (int)(fileSize / sizeof(double));

                double[] array = new double[elementCount];

                for (int i = 0; i < elementCount; i++)
                {
                    array[i] = reader.ReadDouble();
                }

                return array;
            }
        }
    }
}

