﻿using System;
using System.Collections.Generic;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.Server
{
   
    public class FunctionCodeDescriptions
    {
        // 功能码与描述的映射字典
        private static readonly Dictionary<byte, string> FunctionCodeDescriptionMap = new Dictionary<byte, string>
        {
            { 0, "确认报文" },
            { 1, "装置用户定值信息报文" },
            { 2, "装置厂家定值信息报文" },
            { 3, "装置版本信息报文" },
            { 4, "装置自检信息报文" },
            { 5, "装置开入开出数据信息报文" },
            { 6, "装置板卡通信状态信息报文" },
            { 7, "装置变位上送B码对时状态" },
            { 8, "心跳" }
        };

        // 根据功能码获取描述
        public static string GetDescription(byte functionCode)
        {
            return FunctionCodeDescriptionMap.TryGetValue(functionCode, out var description)
                ? description
                : "未知功能码";
        }
    }

}
