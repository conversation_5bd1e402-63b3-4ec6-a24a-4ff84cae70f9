﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAlert201310
    {
        public string Id { get; set; } = null!;
        public string? StatCode { get; set; }
        public string? DeviceId { get; set; }
        public string Alerter { get; set; } = null!;
        public string AlertType { get; set; } = null!;
        public string AlertLevel { get; set; } = null!;
        public string AlertTime { get; set; } = null!;
        public string TimeSrc { get; set; } = null!;
        public string Content { get; set; } = null!;
        public string State { get; set; } = null!;
        public DateTime RecTime { get; set; }
    }
}
