﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.Server
{
    public static class ConstValue
    {
        public const byte StartByte = 0x16; // 开始位
        //public const byte EndByte = 0x86;   // 结束位
        public const int LengthFieldSize = 2; // 数据长度字段的大小（4字节）
        public static readonly string HeartbeatMessage = "heartbeat";
        public static readonly TimeSpan HeartbeatInterval = TimeSpan.FromSeconds(10);
    }
}
