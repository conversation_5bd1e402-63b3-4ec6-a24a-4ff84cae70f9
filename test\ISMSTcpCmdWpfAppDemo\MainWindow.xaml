﻿<Window x:Class="ISMSTcpCmdWpfAppDemo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:avalon="http://icsharpcode.net/sharpdevelop/avalonedit"
        Title="TCP Client" Height="600" Width="800">
    <Grid Margin="10">
        <TabControl>
            <!-- Tab 1 -->
            <TabItem Header="与ISMS通信">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="60*"/>
                        <ColumnDefinition Width="317*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- IP Address -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,5,0,5" Grid.ColumnSpan="2">
                        <TextBlock Text="IP地址:" Width="80"/>
                        <TextBox x:Name="txtIP" Text="*************" Width="120" TextChanged="TxtIP_TextChanged"/>
                        <TextBlock Text="端口号:" Width="80"/>
                        <TextBox x:Name="txtPort" Text="43916" Width="120" TextChanged="TxtPort_TextChanged"/>
                        <Button x:Name="saveCfg" Content="保存配置" Width="80" Margin="5,0" Click="SaveCfg_Click"/>

                    </StackPanel>


                    <!-- Connection Status -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5,0,5" Grid.ColumnSpan="2">
                        <Button x:Name="btnConnect" Content="连接" Width="80" Margin="5,0" Click="BtnConnect_Click"/>
                        <Button x:Name="btnDisconnect" Content="断开" Width="80" Margin="5,0" Click="BtnDisconnect_Click"/>
                        <TextBlock Text="连接状态:" Width="80"/>
                        <TextBlock x:Name="txtStatus" Text="未连接" Foreground="Red"/>
                        <Button x:Name="clearISMSMsgBtn" Content="清空界面" Width="80" Margin="5,0" Click="clearISMSMsgBtn_Click"/>

                    </StackPanel>
                    <avalon:TextEditor x:Name="txtReceived" Grid.Row="2" Margin="0,5,0,5" IsReadOnly="True"    VerticalScrollBarVisibility="Auto"
                        HorizontalScrollBarVisibility="Auto" VerticalContentAlignment="Center" SyntaxHighlighting="JSON" Grid.ColumnSpan="2"/>

                    <!-- Command Input -->
                    <DockPanel Grid.Row="3" Margin="0,5,0,5" Grid.ColumnSpan="2">
                        <!--<Button x:Name="btnSend" Content="发送" Width="80" Margin="5,0" Click="BtnSend_Click"/>-->
                        <!--<TextBox x:Name="txtSend" AcceptsReturn="True" VerticalContentAlignment="Center"/>-->
                    </DockPanel>
                    <DockPanel Grid.Row="4" Margin="0,5,0,5" Grid.ColumnSpan="2">
                        <Label IsEnabled="False" Content="异常信息" Width="80" Margin="5,0" />
                        <TextBox x:Name="txtErr" MaxHeight="100" Height="100" VerticalScrollBarVisibility="Auto" AcceptsReturn="True" VerticalContentAlignment="Center"/>
                    </DockPanel>
                </Grid>
            </TabItem>
            <!-- Tab 2 -->
            <TabItem Header="与运维后台">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <!-- 服务端配置 -->
                        <RowDefinition Height="*"/>
                        <!-- 消息显示区域 -->
                        <RowDefinition Height="Auto"/>
                        <!-- 异常信息及发送区域 -->
                    </Grid.RowDefinitions>

                    <!-- 上部：服务端配置 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
                        <TextBlock Text="Port:" VerticalAlignment="Center"/>
                        <TextBox Name="PortTextBox" Width="100" Margin="5" IsEnabled="False" Text="38094"/>
                        <Button Name="StartButton" Content="启动服务" Width="100" Margin="5" Click="StartWebApi_Click"/>
                        <Button Name="StopButton" Content="停止服务" Width="100" Margin="5" Click="StopWebApi_Click" />
                        <Button Name="ClearServerButton" Content="清空界面" Width="100" Margin="5" Click="ClearServerButton_Click"/>

                        <TextBlock VerticalAlignment="Center">状态：</TextBlock>
                        <TextBlock VerticalAlignment="Center" Name="webApiState">：</TextBlock>
                    </StackPanel>

                    <!-- 中部：消息显示区域 -->
                    <ListBox Name="MessageList" Grid.Row="1" Margin="10"/>

                    <!-- 下部：异常信息及测试发送区域 -->
                    <StackPanel Grid.Row="2" Margin="10" Height="50">
                        <TextBlock Text="异常信息:" FontWeight="Bold"/>
                        <TextBox Name="ExceptionTextBox" Height="80" Margin="0,5" IsReadOnly="True" VerticalScrollBarVisibility="Auto"/>
                    </StackPanel>
                </Grid>
            </TabItem>
            <TabItem Header="报警信息">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <!-- 服务端配置 -->
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 上部：服务端配置 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
                        <Button Name="ClearAlarmButton" Content="清空界面" Width="100" Margin="5" Click="ClearAlarm_Click"/>
                    </StackPanel>

                    <!-- 中部：消息显示区域 -->
                    <ListBox Name="AlarmMessageList" Grid.Row="1" Margin="10"/>

                </Grid>
            </TabItem>
            <TabItem Header="所有信息">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <!-- 服务端配置 -->
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="40"/>

                    </Grid.RowDefinitions>

                    <!-- 上部：服务端配置 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
                        <Button Name="ClearAllMsgButton" Content="清空界面" Width="100" Margin="5" Click="ClearAllMsg_Click"/>
                    </StackPanel>
                    <!-- 中部：消息显示区域 -->
                    <ListBox Name="AllMsgMessageList" Grid.Row="1" Margin="10">
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Focusable" Value="False"/>
                                <Setter Property="IsEnabled" Value="True"/>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                    <DockPanel Grid.Row="2" Margin="0,5,0,5" Grid.ColumnSpan="2" >
                        <Button x:Name="btnSend" Content="发送" Width="80" Margin="5,0" Click="BtnSend_Click"/>
                        <TextBox x:Name="txtSend" AcceptsReturn="True" VerticalContentAlignment="Center"/>
                    </DockPanel>
                </Grid>
            </TabItem>
        </TabControl>

    </Grid>
</Window>