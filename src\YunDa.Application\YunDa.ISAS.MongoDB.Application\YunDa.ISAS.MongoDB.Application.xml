<?xml version="1.0"?>
<doc>
    <assembly>
        <name>YunDa.ISAS.MongoDB.Application</name>
    </assembly>
    <members>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.AlarmOverviewAppService">
            <summary>
            报警总览
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.AuxiliaryMonitoringEquipmentAlarmAppService">
            <summary>
            辅控报警
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.OnlineMonitoringEquipmentAlarmAppService">
            <summary>
            在线监测报警
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.PrimaryCircuitAlarmAppService">
            <summary>
            一次回路
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.PrimaryEquipmentAlarmAppService">
            <summary>
            一次设备报警
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.SecondaryCircuitAlarmAppService">
            <summary>
            二次回路
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.SecondaryEquipmentAlarmAppService">
            <summary>
            二次设备报警
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.AlarmResult.SecondaryEquipmentAlarmAppService.GetAlarms(System.Guid)">
            <summary>
            获取二次设备报警信息
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ImageAnalysisResult.AllTimeImageAnalysisResultAppService.FindWeeklyDatas(System.Guid)">
            <summary>
            查询实时识别周统计数据
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ILinkageResultAppService.FileUpload">
            <summary>
            联动文件上传接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ILinkageResultAppService.ConfirmAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据报警Id数组确认报警
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ILinkageResultAppService.ConfirmAllAlarmAsync">
            <summary>
            确认所有未确认的报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ILinkageResultAppService.ClearAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据报警Id数组解除确认报警
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ILinkageResultAppService.ClearAllAlarmAsync">
            <summary>
            解除所有未解除的报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ILinkageResultAppService.FindHistoryDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageResultDto.SearchCondition.LinkageResultSearchConditionInput})">
            <summary>
            查询历史联动报警
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService">
            <summary>
            联动结果服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService._linkageResultRepository">
            <summary>
            联动结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageResultDto.EditLinkageResultInput)">
            <summary>
            联动结果增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.CreateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageResultDto.EditLinkageResultInput)">
            <summary>
            联动结果增加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageResultDto.EditLinkageResultInput)">
            <summary>
            联动结果修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.FileUpload">
            <summary>
            联动文件上传。
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个联动结果
            </summary>
            <param name="id">联动结果id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个联动结果
            </summary>
            <param name="ids">联动结果id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageResultDto.SearchCondition.LinkageResultSearchConditionInput})">
            <summary>
            查询联动结果
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.FindHistoryDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.LinkageResultDto.SearchCondition.LinkageResultSearchConditionInput})">
            <summary>
            查询历史报警信息---包含报警说明
            报警历史默认查询一个月的记录
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.ConfirmAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            确认报警
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.ConfirmAllAlarmAsync">
            <summary>
            确认所有报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.ClearAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            解除报警
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.LinkageResultAppService.ClearAllAlarmAsync">
            <summary>
             解除所有报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.MultidimensionalCheckResult.MultidimensionalCheckResultAppService.FindWeeklyDatas(System.Guid)">
            <summary>
            查询多维核对信息
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.MultidimensionalCheckResult.MultidimensionalCheckResultAppService.CreateAsync(System.Guid,System.String,System.String,System.Boolean,System.String,System.String,System.String,System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            创建多维核对信息结果
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.MultidimensionalCheckResult.MultidimensionalCheckResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.MultidimensionalResultDto.MultidimensionalCheckResultSearchConditionInput})">
            <summary>
            查询数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService">
            <summary>
            遥测报警结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmResultDto.SearchCondition.TeleAlarmResultSearchConditionInput})">
            <summary>
            按条件查询遥测报警数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.ConfirmAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据报警Id数组确认报警
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.ConfirmAllAlarmAsync">
            <summary>
            确认所有未确认的报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.ClearAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据报警Id数组解除确认报警
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.ClearAllAlarmAsync">
            <summary>
            解除所有未解除的报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.FindDatasForHistoryAlarmCharts(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查询报警柱状图信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.ExportDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmResultDto.SearchCondition.TeleAlarmResultSearchConditionInput)">
            <summary>
            导出104报警数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.FindHistoryDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmResultDto.SearchCondition.TeleAlarmResultSearchConditionInput})">
            <summary>
            查询历史报警信息---包含报警说明
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.DeleteByIds(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多条历史数据
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.GetAlarmWeekly(System.Guid)">
            <summary>
            查询报警周报
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITeleAlarmResultAppService.FindAlarmDataByEquipemnt(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.EquipmentDetailSearchConditionInput)">
            <summary>
            通过设备id获取报警数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService">
            <summary>
            遥测报警结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmResultDto.SearchCondition.TeleAlarmResultSearchConditionInput})">
            <summary>
            遥测报警数据查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.FindHistoryDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmResultDto.SearchCondition.TeleAlarmResultSearchConditionInput})">
            <summary>
            查询历史报警信息---包含报警说明
            历史报警默认查询最近30天的记录
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.ConfirmAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            多个遥测报警确认报警
            </summary>
            <param name="ids">遥测报警id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.ConfirmAllAlarmAsync">
            <summary>
            确认所有遥测报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.ClearAlarmByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            解除多个遥测报警
            </summary>
            <param name="ids">遥测报警id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.ClearAllAlarmAsync">
            <summary>
            解除所有遥测报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.FindDatasForHistoryAlarmCharts(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查询遥测报警历史数据
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.SelectHistoryAlarmAsync(System.Guid,System.DateTime,System.DateTime)">
            <summary>
            获取遥测报警历史数据
            </summary>
            <param name="telemeteringConfigurationId">遥测id</param>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.DeleteByIds(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除报警历史数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.GetAlarmWeekly(System.Guid)">
            <summary>
            获取报警周报
            </summary>
            <param name="stationId"></param>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.GetAlarmCategoryOrverview(System.Guid,System.String)">
            <summary>
            获取报警分类数目
            </summary>
            <param name="stationId"></param>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.GetAlarmWeeklyOrverview(System.Guid)">
            <summary>
            获取报警周报总览
            </summary>
            <param name="stationId"></param>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TeleAlarmResultAppService.FindAlarmDataByEquipemnt(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.EquipmentDetailSearchConditionInput)">
            <summary>
            通过设备id获取报警数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelecommandResultAppService">
            <summary>
            遥控结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelecommandResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandResultDto.TelecommandResultSearchConditionInput})">
            <summary>
            按条件查询遥信结果数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelecommandResultAppService.ExportDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandResultDto.TelecommandResultSearchConditionInput)">
            <summary>
            导出遥控结果数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelecommandResultAppService">
            <summary>
            遥控结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelecommandResultAppService._telecommandResultRepository">
            <summary>
            遥控结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelecommandResultAppService._telecommandConfigurationResitory">
            <summary>
            遥控配置仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelecommandResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandResultDto.TelecommandResultSearchConditionInput})">
            <summary>
            遥控结果查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService">
            <summary>
            遥测结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.InsertMany(System.Collections.Generic.List{Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelemeteringResult})">
            <summary>
            插入多条遥测结果数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.InsertOne(Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelemeteringResult)">
            <summary>
            插入一条遥测结果数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.FindDatasForHistoryCurve(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查询遥测结果历史曲线数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.InsertTelemeteringHourStatisticsResult(Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelemeteringHourStatisticsResult)">
            <summary>
            插入遥测小时统计数据
            </summary>
            <param name="telemeteringResult"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.ExportDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            导出遥测数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.ExportTxtDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            导出遥测数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.FindDatasForHistoryKline(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查找遥测历史曲线K线数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelemeteringResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput})">
            <summary>
            遥测数据查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ProtectionDeviceYCResultAppService.Get5VTimeSeriesAsync(System.Guid,System.DateTime,System.DateTime)">
            <summary>
            获取装置5V电压曲线图
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService">
            <summary>
            遥测结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService._telemeteringResultRepository">
            <summary>
            巡检项结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatasForHistoryCurve(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            遥测数据统计分析大数据查询方法，遥测结果参数最多为5个
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput})">
            <summary>
            遥测数据查询
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.SelectHistoryDataAsync(System.Guid,System.DateTime,System.DateTime)">
            <summary>
            获取遥测历史数据
            </summary>
            <param name="telemeteringConfigurationId">遥测id</param>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.SelectHistoryPointAsync(System.Guid,System.DateTime,System.DateTime)">
            <summary>
            获取遥测历史数据点数据
            </summary>
            <param name="telemeteringConfigurationId">遥测id</param>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.InsertMany(System.Collections.Generic.List{Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelemeteringResult})">
            <summary>
            插入多个遥测数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.InsertOne(Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelemeteringResult)">
            <summary>
            插入单个遥测数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.InsertTelemeteringHourStatisticsResult(Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelemeteringHourStatisticsResult)">
            <summary>
            遥测小时统计数据
            </summary>
            <param name="telemeteringResult">遥测数据</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.GetTelemeteringResultCollectionName(System.DateTime)">
            <summary>
            获取遥测数据表名称
            </summary>
            <param name="dateTime">时间</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatasForHistoryKline(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查询遥测小时历史数据
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatasForDayHistoryKline(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查询遥测天历史数据
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatasForMonthHistoryKline(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            查询遥测月历史数据
            </summary>
            <param name="input">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.ExportTxtDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringResultDto.SearchCondition.TelemeteringDataSearchConditionInput)">
            <summary>
            遥测数据txt导出
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatasForWeekHistory(System.Int32,System.Guid)">
            <summary>
            查询设备遥测周历史数据
            </summary>
            <param name="modelId">查询条件</param>
            <param name="equipmentId">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelemeteringResultAppService.FindDatasForDayHistory(System.Int32,System.Guid,System.Int32)">
            <summary>
            按天查询设备遥测历史数据
            </summary>
            <param name="modelId">查询条件</param>
            <param name="equipmentId">查询条件</param>
            <param name="dayCount">查询条件</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelesignalisationResultAppService">
            <summary>
            遥信结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelesignalisationResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationResultDto.TelesignalisationResultSearchConditionInput})">
            <summary>
            按条件查询遥信结果数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelesignalisationResultAppService.InsertMany(System.Collections.Generic.List{Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelesignalisationResult})">
            <summary>
            插入多条遥信数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelesignalisationResultAppService.InsertOne(Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelesignalisationResult)">
            <summary>
            插入一条遥信数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.ITelesignalisationResultAppService.ExportDatas(YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationResultDto.TelesignalisationResultSearchConditionInput)">
            <summary>
            导出遥信结果数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelesignalisationResultAppService">
            <summary>
            遥信结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelesignalisationResultAppService._telesignalisationResultRepository">
            <summary>
            遥信结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelesignalisationResultAppService._telesignalisationConfigurationResitory">
            <summary>
            遥信配置仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelesignalisationResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationResultDto.TelesignalisationResultSearchConditionInput})">
            <summary>
            遥信结果查询
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelesignalisationResultAppService.InsertMany(System.Collections.Generic.List{Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelesignalisationResult})">
            <summary>
            插入多个遥信数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.DataMonitoring.TelesignalisationResultAppService.InsertOne(Yunda.ISAS.MongoDB.Entities.DataMonitoring.TelesignalisationResult)">
            <summary>
            插入单个遥信数据
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionItemResultAppService">
            <summary>
            巡检项结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionItemResultAppService.FileUpload">
            <summary>
            文件上传
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionItemResultAppService.DownloadDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.InspectionItemResultSearchConditionInput})">
            <summary>
            下载数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionItemResultAppService.FileAndInformationUpload(System.Guid,System.Guid,System.Guid,System.Guid,System.String,System.Boolean,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            巡检结果上传
            </summary>
            <param name="inspectionResultId"></param>
            <param name="cardId"></param>
            <param name="transformerSubstationId"></param>
            <param name="itemId"></param>
            <param name="analysisTime"></param>
            <param name="analysisStatus"></param>
            <param name="analysisErrorMessage"></param>
            <param name="analysisResult"></param>
            <param name="alarmResult"></param>
            <param name="isLastItem"></param>
            <param name="postedFiles"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionItemResultAppService.LiveRecognizeInfoUpload(System.String,System.Guid)">
            <summary>
            实时识别信息上传
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionItemResultAppService.FindDatasByEquipmentId(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.EquipmentDetailSearchConditionInput)">
            <summary>
            通过时间和设备Id查询巡检项
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService">
            <summary>
            巡检项结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService._inspectionItemResultRepository">
            <summary>
            巡检项结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService._inspectionResultRepository">
            <summary>
            巡检结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.EditInspectionItemResultInput)">
            <summary>
            巡检项结果修改或添加
            </summary>
            <param name="input">巡检结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.NotifyInspectionItemResultAsync(System.Nullable{System.Guid})">
            <summary>
            巡检项文件结果通知(客户端上传完文件通知服务端)
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.EditInspectionItemResultInput)">
            <summary>
            新增巡检项
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.EditInspectionItemResultInput)">
            <summary>
            修改巡检项
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检项
            </summary>
            <param name="id">巡检结果id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个巡检项
            </summary>
            <param name="ids">巡检结果id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.FindDatasByEquipmentId(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.EquipmentDetailSearchConditionInput)">
            <summary>
            通过时间和设备Id查询巡检项
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.FindDatasByEquipmentIdAndTime(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.EquipmentDetailSearchConditionInput)">
            <summary>
            获取设备巡检报告
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.InspectionItemResultSearchConditionInput})">
            <summary>
            查询巡检项
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.DownloadDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionItemResultDto.SearchCondition.InspectionItemResultSearchConditionInput})">
            <summary>
            下载excel表格文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.FileUpload">
            <summary>
            巡检项文件上传。
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.FileAndInformationUpload(System.Guid,System.Guid,System.Guid,System.Guid,System.String,System.Boolean,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            巡检结果项上传
            </summary>
            <param name="inspectionResultId">巡检结果Id</param>
            <param name="cardId">巡检卡片Id</param>
            <param name="transformerSubstationId">场站Id</param>
            <param name="itemId">巡检项Id</param>
            <param name="analysisTime">图像分析或温度测量时间</param>
            <param name="analysisStatus">图像分析或温度测量状态(false/true)</param>
            <param name="analysisErrorMessage">发生错误返回消息</param>
            <param name="analysisResult">图像分析或温度测量结果</param>
            <param name="alarmResult"></param>
            <param name="postedFiles">巡检文件</param>
            <param name="isLastItem">是否为最后一项</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.LiveRecognizeInfoUpload(System.String,System.Guid)">
            <summary>
            实时识别信息上传
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.TestGetAlarmMessage(System.String,System.String,System.String)">
            <summary>
            测试报警api
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.GetAlarmMessage(System.Nullable{System.Guid},System.Int32,System.String)">
            <summary>
            获取报警信息
            </summary>
            <param name="videoDevId"></param>
            <param name="number"></param>
            <param name="analysisResult"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultAppService.ReUploadItemDataAsync(System.Guid)">
            <summary>
            手动重传巡检结果
            </summary>
            <returns></returns>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultFileAppService._inspectionItemResultRepository">
            <summary>
            巡检项结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultFileAppService.FindInspectionItemResultUris(System.Collections.Generic.List{System.Guid})">
            <summary>
            获取巡检项
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionItemResultFileAppService.DownloadMediaFiles(System.String)">
            <summary>
            下载巡检项文件
            </summary>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionResultAppService">
            <summary>
            巡检结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionResultAppService.InspectionFinish(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionResultDto.InspectionResultInput)">
            <summary>
            巡检任务结束
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionResultAppService.DownloadDatas(System.Guid)">
            <summary>
            巡检结果下载
            </summary>
            <param name="inspectionResultId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionResultAppService.GetInspectionDaily(System.Guid)">
            <summary>
            当日巡检情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.IInspectionResultAppService.GetInspectionItemsDaily(System.Guid)">
            <summary>
            当日巡检项情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService">
            <summary>
            巡检结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService._inspectionResultRepository">
            <summary>
            巡检结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService._inspectionTaskExtensionAppService">
            <summary>
            巡检任务扩展服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionResultDto.EditInspectionResultInput)">
            <summary>
            巡检结果增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.CreateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionResultDto.EditInspectionResultInput)">
            <summary>
            巡检结果增加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionResultDto.EditInspectionResultInput)">
            <summary>
            巡检结果修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检结果
            </summary>
            <param name="id">巡检结果id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多条巡检如果
            </summary>
            <param name="ids">巡检结果id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionResultDto.SearchCondition.InspectionResultSearchConditionInput})">
            <summary>
            查询巡检结果
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.InspectionFinish(YunDa.ISAS.DataTransferObject.VideoSurveillance.InspectionResultDto.InspectionResultInput)">
            <summary>
            巡检任务结束
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.SpwanInspectionResult(YunDa.ISAS.Redis.Entities.InspectionCategory.InspectionResultRedis,System.Collections.Generic.List{YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation})">
            <summary>
            生成巡检结果表单
            </summary>
            <param name="inspectionResultRedis"></param>
            <param name="transformerSubstations"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.DownloadDatas(System.Guid)">
            <summary>
            下载巡检结果excel
            </summary>
            <param name="inspectionResultId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.GetInspectionDaily(System.Guid)">
            <summary>
            巡检结果日报查询
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.GetInspectionItemsDaily(System.Guid)">
            <summary>
            查询当日巡检项情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.GetInspectionWeeklyOverview(System.Guid)">
            <summary>
            查询本周巡检项情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.Inspection.InspectionResultAppService.GetInspectionOverview(System.Guid)">
            <summary>
            查询巡检项情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MaintenanceAndOperationsRecord.DeviceSetting.SettingRecordAppService.SaveSettingBaseline(YunDa.SOMS.DataTransferObject.MaintenanceAndOperations.SecondaryEquipment.DzInfoBaselineDto)">
            <summary>
            保存定值基准
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MaintenanceAndOperationsRecord.DeviceSetting.SettingRecordAppService.GetSettingBaselineAsync(System.Guid)">
            <summary>
            查询定值基准
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.IMeasuresTemperatureResultAppService">
            <summary>
            设备测温结果管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.IMeasuresTemperatureResultAppService.GetNearestResultByEquipmentInfoId(System.Guid)">
            <summary>
            根据设备Id查询该设备的测温点结果List，无值测温点则TemperatureValue=null
            </summary>
            <param name="equipmentInfoId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.IMeasuresTemperatureResultAppService.ExportDatas(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.SearchCondition.MeasureTemperatureResultSearchConditonInput)">
            <summary>
            测温结果导出
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.IMeasuresTemperatureResultAppService.FindNearestData(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.MeasureTemperatureResultInput)">
            <summary>
            查询最近测温数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.IMeasuresTemperatureResultAppService.ClientCreate(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.EditMeasureTemperatureResultByClientInput)">
            <summary>
            客户端创建测温结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService">
            <summary>
            设备测温结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService._resultRepository">
            <summary>
            红外测温结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService._mtpRepository">
            <summary>
            红外测温点仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.ClientCreate(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.EditMeasureTemperatureResultByClientInput)">
            <summary>
            客户端创建测温结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.EditMeasureTemperatureResultInput)">
            <summary>
            测温结果编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            根据id删除测温结果
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            根据ids删除多条测温结果
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.FindNearestData(YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.MeasureTemperatureResultInput)">
            <summary>
            查询最近测温数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.VideoSurveillance.MeasureTemperatureResultDto.SearchCondition.MeasureTemperatureResultSearchConditonInput})">
            <summary>
            查询温度测量结果
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.GetNearestResultByEquipmentInfoId(System.Guid)">
            <summary>
            根据设备Id查询该设备的测温点结果List，无值测温点则TemperatureValue=null
            </summary>
            <param name="equipmentInfoId">设备Id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MeasuresTemperature.MeasuresTemperatureResultAppService.FindMeasureTempertureDailyResults(System.Guid)">
            <summary>
            查询测温情况
            </summary>
            <param name="stationId"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.IRobotTaskAlarmResultAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskAlarmResultDto.EditRobotTaskAlarmResultInput})">
            <summary>
            添加多条机器人巡检任务报警结果
            </summary>
            <param name="inputs">巡检结果</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService">
            <summary>
            机器人任务报警结果
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService._robotTaskAlarmResultRepository">
            <summary>
            机器人巡检任务报警结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskAlarmResultDto.EditRobotTaskAlarmResultInput})">
            <summary>
            添加多条机器人巡检项结果
            </summary>
            <param name="inputs">巡检结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskAlarmResultDto.EditRobotTaskAlarmResultInput)">
            <summary>
            添加机器人巡检任务报警结果
            </summary>
            <param name="input">巡检结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个机器人巡检任务报警结果
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个机器人巡检任务报警结果
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskAlarmResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskAlarmResultDto.RobotTaskAlarmResultSearchConditionInput})">
            <summary>
            查询机器人巡检任务报警结果
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.IRobotTaskItemResultAppService">
            <summary>
            
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.IRobotTaskItemResultAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskItemResultDto.EditRobotTaskItemResultInput})">
            <summary>
            添加多条机器人巡检项结果
            </summary>
            <param name="inputs">巡检结果</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService">
            <summary>
            机器人任务项结果
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService._robotTaskResultRepository">
            <summary>
            巡检结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService._robotTaskItemResultRepository">
            <summary>
            机器人巡检项结果仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.CheckItemResultTimeout">
            <summary>
            查询是否有任务已经5分钟没有上送巡检结果了
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskItemResultDto.EditRobotTaskItemResultInput})">
            <summary>
            添加多条机器人巡检项结果
            </summary>
            <param name="inputs">巡检结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskItemResultDto.EditRobotTaskItemResultInput)">
            <summary>
            机器人巡检项结果修改或添加
            </summary>
            <param name="input">巡检结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个机器人巡检项结果
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个个机器人巡检项结果
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskItemResultDto.RobotTaskItemResultSearchConditionInput})">
            <summary>
            查询机器人巡检项结果
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskItemResultAppService.GetTaskItemImgByPath(System.String)">
            <summary>
            获取任务项的img
            </summary>
            <param name="relativePath"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.UploadItemTempData">
            <summary>
            机器人巡检临时存储数据
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.IRobotTaskReportResultAppService">
            <summary>
            机器人任务巡检报告结果
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.IRobotTaskReportResultAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskReportResultDto.EditRobotTaskReportResultInput})">
            <summary>
            添加多条机器人巡检报告果
            </summary>
            <param name="inputs">巡检结果</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService">
            <summary>
            机器人任务巡检报告结果
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService._robotTaskReportResultRepository">
            <summary>
            机器人巡检项结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService._robotInfoRepository">
            <summary>
            机器人仓储
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.CreateManyAsync(System.Collections.Generic.List{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskReportResultDto.EditRobotTaskReportResultInput})">
            <summary>
            添加多条机器人巡检报告果
            </summary>
            <param name="inputs">巡检结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskReportResultDto.EditRobotTaskReportResultInput)">
            <summary>
            机器人巡检报告结果修改或添加
            </summary>
            <param name="input">巡检报告结果</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个机器人巡检项结果
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多个机器人巡检项结果
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskReportResultDto.RobotTaskReportResultSearchConditionInput})">
            <summary>
            查询机器人巡检报告结果
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.GetReportByName(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskReportResultDto.RobotTaskReportResultSearchConditionInput)">
            <summary>
            下载机器人巡检结果xls表格
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskReportResultAppService.GetReportDirFilesInfo(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskReportResultDto.SearchCondition.RobotReportFilesSearchConditionInput})">
            <summary>
            查看机器人巡检结果xls表格
            </summary>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.IRobotTaskResultAppService.CreateByTaskCodeAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskResultDto.EditRobotTaskResultByTaskCode)">
            <summary>
            根据机器人任务code添加巡检任务结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService">
            <summary>
            机器人巡检结果管理服务
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService._robotTaskItemResultRepository">
            <summary>
            机器人巡检项结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService._robotTaskResultRepository">
            <summary>
            巡检结果仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService._robotTaskRepository">
            <summary>
            机器人任务仓储
            </summary>
        </member>
        <member name="F:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService._inspectionTaskExtensionAppService">
            <summary>
            巡检任务扩展服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.CreateByTaskCodeAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskResultDto.EditRobotTaskResultByTaskCode)">
            <summary>
            根据机器人任务code添加巡检任务结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.CreateOrUpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskResultDto.EditRobotTaskResultInput)">
            <summary>
            机器人巡检结果增加或修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.CreateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskResultDto.EditRobotTaskResultInput)">
            <summary>
            机器人巡检结果增加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.UpdateAsync(YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskResultDto.EditRobotTaskResultInput)">
            <summary>
            机器人巡检结果修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.DeleteByIdAsync(System.Guid)">
            <summary>
            删除单个巡检结果
            </summary>
            <param name="id">巡检结果id</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.DeleteByIdsAsync(System.Collections.Generic.List{System.Guid})">
            <summary>
            删除多条巡检如果
            </summary>
            <param name="ids">巡检结果id集合</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.MobileSurveillance.RobotTaskResultAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.MobileSurveillance.RobotTaskResultDto.RobotTaskResultSearchConditionInput})">
            <summary>
            查询机器人巡检结果
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.System.ClientOperateLog.DutyLogAppService">
            <summary>
            客户端操作日志
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.System.ClientOperateLog.DutyLogAppService.CreateAsync(YunDa.ISAS.DataTransferObject.System.DutyLogDto.EditDutyLogInput)">
            <summary>
            创建操作信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.System.ClientOperateLog.IDutyLogAppService">
            <summary>
            系统审计日志管理服务
            </summary>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.System.ISysAuditLogAppService">
            <summary>
            系统审计日志管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.System.ISysAuditLogAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.System.SysAuditLogDto.SysAuditLogSearchConditionInput})">
            <summary>
            按条件查询遥测报警数据
            </summary>
            <param name="searchCondition"></param>
            <returns></returns>
        </member>
        <member name="T:YunDa.ISAS.MongoDB.Application.System.SysAuditLogAppService">
            <summary>
            系统审计日志管理服务
            </summary>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.System.SysAuditLogAppService.FindDatas(YunDa.ISAS.DataTransferObject.PageSearchCondition{YunDa.ISAS.DataTransferObject.System.SysAuditLogDto.SysAuditLogSearchConditionInput})">
            <summary>
            查询遥系统日志数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
        <member name="M:YunDa.ISAS.MongoDB.Application.System.SysAuditLogAppService.FindDatasBytime(YunDa.ISAS.DataTransferObject.System.SysAuditLogDto.SysAuditLogSearchConditionInput)">
            <summary>
            查询遥系统日志数据
            </summary>
            <param name="searchCondition">查询条件</param>
            <returns></returns>
        </member>
    </members>
</doc>
