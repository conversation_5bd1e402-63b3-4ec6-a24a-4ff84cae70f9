﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImCurveType
    {
        public ImCurveType()
        {
            ImCurves = new HashSet<ImCurve>();
        }

        public string CveTypeCode { get; set; } = null!;
        public string CveTypeName { get; set; } = null!;

        public virtual ICollection<ImCurve> ImCurves { get; set; }
    }
}
