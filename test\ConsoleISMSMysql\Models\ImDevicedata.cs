﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDevicedata
    {
        public ImDevicedata()
        {
            ImCurveitem = new HashSet<ImCurveitem>();
            ImReportcfgdata = new HashSet<ImReportcfgdata>();
            ImShebeiDevicedata = new HashSet<ImShebeiDevicedata>();
        }

        public string Id { get; set; }
        public string Deviceid { get; set; }
        public string Datatype { get; set; }
        public string Dataname { get; set; }
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int Autosave { get; set; }
        public int Visible { get; set; }
        public string Beizhu { get; set; }
        public string Srctempdataid { get; set; }
        public string Domain { get; set; }

        public virtual ImProtectdevice Device { get; set; }
        public virtual ImDeviceva ImDeviceva { get; set; }
        public virtual ImDeviceyc ImDeviceyc { get; set; }
        public virtual ImDeviceyk ImDeviceyk { get; set; }
        public virtual ImDeviceym ImDeviceym { get; set; }
        public virtual ImDeviceyx ImDeviceyx { get; set; }
        public virtual ICollection<ImCurveitem> ImCurveitem { get; set; }
        public virtual ICollection<ImReportcfgdata> ImReportcfgdata { get; set; }
        public virtual ICollection<ImShebeiDevicedata> ImShebeiDevicedata { get; set; }
    }
}
