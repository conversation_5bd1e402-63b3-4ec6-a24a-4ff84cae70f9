﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImFaulttype2010
    {
        public ImFaulttype2010()
        {
            ImFaultacttype2010 = new HashSet<ImFaultacttype2010>();
        }

        public string Id { get; set; }
        public string Devicectgy { get; set; }
        public int Faultcode { get; set; }
        public string Faultname { get; set; }

        public virtual ImDevctgy DevicectgyNavigation { get; set; }
        public virtual ICollection<ImFaultacttype2010> ImFaultacttype2010 { get; set; }
    }
}
