﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbXitongrizhi
    {
        public int Id { get; set; }
        public string <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string <PERSON><PERSON><PERSON> { get; set; }
        public string <PERSON><PERSON> { get; set; }
    }
}
