﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleAppSqlite.Models
{
    public partial class DescField
    {
        public long Id { get; set; }
        public long? TbId { get; set; }
        public string FieldName { get; set; }
        public string FieldDesc { get; set; }
        public string DataDesc { get; set; }
        public string DataType { get; set; }
        public long? IsModify { get; set; }
        public string DefaultV { get; set; }
    }
}
