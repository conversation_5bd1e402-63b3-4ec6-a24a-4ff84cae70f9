﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImPuCtgy
    {
        public ImPuCtgy()
        {
            ImAnalogData20102s = new HashSet<ImAnalogData20102>();
            ImAsdu140FltActTypes = new HashSet<ImAsdu140FltActType>();
            ImBreakerNoEnums = new HashSet<ImBreakerNoEnum>();
            ImDeviceDzenumPus = new HashSet<ImDeviceDzenumPu>();
            ImEventTypes = new HashSet<ImEventType>();
            ImFaultActType20102s = new HashSet<ImFaultActType20102>();
            ImProtectDevices = new HashSet<ImProtectDevice>();
            ImPuWaveChls = new HashSet<ImPuWaveChl>();
            ImPuctgyFltRptItems = new HashSet<ImPuctgyFltRptItem>();
        }

        public int PuctgyCode { get; set; }
        public string PuctgyName { get; set; } = null!;
        public string Manufacturer { get; set; } = null!;
        public string DevCtgy { get; set; } = null!;
        public string Generation { get; set; } = null!;
        public int DzzoneCount { get; set; }
        public string? Model { get; set; }
        public int CanSwDzzone { get; set; }
        public int Support12yc { get; set; }
        public int SupportVersion { get; set; }
        public int SupportDkjl { get; set; }
        public int? StartOfDkjl { get; set; }
        public int? EndOfDkjl { get; set; }
        public string AnalogParseMode { get; set; } = null!;
        public string EventParseMode { get; set; } = null!;
        public int SupportGuZhangBg { get; set; }
        public int SupportShiJianBg { get; set; }
        public int SupportZiJianBg { get; set; }
        public int SupportLuBoWj { get; set; }
        public int SupportDz { get; set; }
        public int DzreadOnly { get; set; }
        public int SupportFhluBo { get; set; }
        public string IsCrcc { get; set; } = null!;

        public virtual ImDevCtgy DevCtgyNavigation { get; set; } = null!;
        public virtual ImManufacturer ManufacturerNavigation { get; set; } = null!;
        public virtual ImProtectDeviceTmp? ImProtectDeviceTmp { get; set; }
        public virtual ICollection<ImAnalogData20102> ImAnalogData20102s { get; set; }
        public virtual ICollection<ImAsdu140FltActType> ImAsdu140FltActTypes { get; set; }
        public virtual ICollection<ImBreakerNoEnum> ImBreakerNoEnums { get; set; }
        public virtual ICollection<ImDeviceDzenumPu> ImDeviceDzenumPus { get; set; }
        public virtual ICollection<ImEventType> ImEventTypes { get; set; }
        public virtual ICollection<ImFaultActType20102> ImFaultActType20102s { get; set; }
        public virtual ICollection<ImProtectDevice> ImProtectDevices { get; set; }
        public virtual ICollection<ImPuWaveChl> ImPuWaveChls { get; set; }
        public virtual ICollection<ImPuctgyFltRptItem> ImPuctgyFltRptItems { get; set; }
    }
}
