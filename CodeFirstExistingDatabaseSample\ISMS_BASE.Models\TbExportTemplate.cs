﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbExportTemplate
    {
        public TbExportTemplate()
        {
            TbExportTemplateCols = new HashSet<TbExportTemplateCol>();
        }

        public string TempId { get; set; } = null!;
        public string TempFileName { get; set; } = null!;
        public string? RelateDir { get; set; }
        public int? StartRowNo { get; set; }
        public int? StartColNo { get; set; }
        public string? DefFileName { get; set; }
        public string? DataTag4Filter { get; set; }
        public string? FilterCond { get; set; }
        public string? FilterValue { get; set; }
        public string? FilterValueType { get; set; }

        public virtual ICollection<TbExportTemplateCol> TbExportTemplateCols { get; set; }
    }
}
