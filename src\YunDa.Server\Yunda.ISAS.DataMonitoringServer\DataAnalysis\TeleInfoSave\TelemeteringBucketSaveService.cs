using Abp.Dependency;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Model;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.MongoDB.Repositories;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace Yunda.SOMS.DataMonitoringServer.DataAnalysis.TeleInfoSave
{
    /// <summary>
    /// 遥测数据分桶存储服务
    /// </summary>
    public class TelemeteringBucketSaveService : ISingletonDependency
    {
        private readonly RunningDataCache _runningDataCache;
        private readonly IMongoDbRepository<TelemeteringBucket, Guid> _telemeteringBucketRepository;
        private readonly IMongoDbRepository<TelemeteringStatisticsBucket, Guid> _telemeteringStatisticsBucketRepository;

        public TelemeteringBucketSaveService(
            RunningDataCache runningDataCache,
            IMongoDbRepository<TelemeteringBucket, Guid> telemeteringBucketRepository,
            IMongoDbRepository<TelemeteringStatisticsBucket, Guid> telemeteringStatisticsBucketRepository)
        {
            _runningDataCache = runningDataCache;
            _telemeteringBucketRepository = telemeteringBucketRepository;
            _telemeteringStatisticsBucketRepository = telemeteringStatisticsBucketRepository;
        }

        /// <summary>
        /// 保存实时数据到分桶存储
        /// </summary>
        /// <param name="telemetry">遥测数据</param>
        /// <param name="connectionConfig">连接配置</param>
        /// <returns></returns>
        public async Task SaveToBucket(TelemeteringModel telemetry, ConnectionConfig connectionConfig)
        {
            try
            {
                if (telemetry == null || connectionConfig == null)
                {
                    Log4Helper.Error(this.GetType(), "[SaveToBucket] 参数为 null，跳过处理。");
                    return;
                }

                bool isSave = connectionConfig.SaveMode == (int)SaveModeEnum.Change ||
                              connectionConfig.SaveMode == (int)SaveModeEnum.Both;
                if (!isSave)
                {
                    Log4Helper.Error(this.GetType(), "[SaveToBucket] SaveMode 不符合条件，跳过保存。");
                    return;
                }

                // 检查数据源类型
                if (telemetry.DataSourceCategory == null)
                {
                    Log4Helper.Warn(this.GetType(), $"遥测点 {telemetry.Id} 的DataSourceCategory为空");
                    return;
                }

                // 检查设备信息
                if (telemetry.EquipmentInfoId == null || 
                    !_runningDataCache.EquipmentInfoSimDic.TryGetValue(telemetry.EquipmentInfoId.Value, out var equipment))
                {
                    Log4Helper.Error(this.GetType(), $"[SaveToBucket] 无效 EquipmentInfoId: {telemetry.EquipmentInfoId}");
                    return;
                }

                // 计算当前小时的开始和结束时间
                DateTime now = telemetry.ResultTime;
                DateTime bucketStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);
                DateTime bucketEnd = bucketStart.AddHours(1).AddSeconds(-1);

                // 设置集合名称 - 按年月分表
                string category = ((DataSourceCategoryEnum)telemetry.DataSourceCategory).ToString();
                _telemeteringBucketRepository.CollectionName = $"{nameof(TelemeteringBucket)}_{category}_{now:yyyyMM}";

                // 查找或创建当前小时的桶
                var bucketFilter = Builders<TelemeteringBucket>.Filter.And(
                    Builders<TelemeteringBucket>.Filter.Eq(x => x.TelemeteringConfigurationId, telemetry.Id),
                    Builders<TelemeteringBucket>.Filter.Eq(x => x.StartTime, bucketStart),
                    Builders<TelemeteringBucket>.Filter.Eq(x => x.EndTime, bucketEnd)
                );

                var bucket = await _telemeteringBucketRepository.FirstOrDefaultAsync(bucketFilter);

                // 创建新数据点
                var newPoint = new TelemeteringPoint
                {
                    Timestamp = telemetry.ResultTime,
                    Value = telemetry.ResultValue,
                    SaveMethod = 2 // 变化保存
                };

                if (bucket == null)
                {
                    // 创建新桶
                    bucket = new TelemeteringBucket
                    {
                        TelemeteringConfigurationId = telemetry.Id,
                        Name = telemetry.Name,
                        EquipmentInfoId = telemetry.EquipmentInfoId,
                        EquipmentInfoName = equipment.Name,
                        Unit = telemetry.Unit,
                        DataSourceCategory = (int?)telemetry.DataSourceCategory,
                        StartTime = bucketStart,
                        EndTime = bucketEnd,
                        Count = 1,
                        MinValue = telemetry.ResultValue,
                        MaxValue = telemetry.ResultValue,
                        AvgValue = telemetry.ResultValue,
                        SumValue = telemetry.ResultValue,
                        FirstValue = telemetry.ResultValue,
                        LastValue = telemetry.ResultValue,
                        StdDeviation = 0,
                        Measurements = new List<TelemeteringPoint> { newPoint }
                    };

                    await _telemeteringBucketRepository.InsertAsync(bucket);
                    Log4Helper.Debug(this.GetType(), $"[SaveToBucket] 创建新桶: {telemetry.Id}, 时间: {bucketStart:yyyy-MM-dd HH:mm:ss}");
                }
                else
                {
                    // 更新现有桶
                    var updateBuilder = Builders<TelemeteringBucket>.Update;
                    var updates = new List<UpdateDefinition<TelemeteringBucket>>();

                    // 添加新的测量点
                    updates.Add(updateBuilder.Push(x => x.Measurements, newPoint));
                    updates.Add(updateBuilder.Inc(x => x.Count, 1));
                    updates.Add(updateBuilder.Set(x => x.LastValue, telemetry.ResultValue));
                    
                    // 更新最小值
                    if (telemetry.ResultValue < bucket.MinValue)
                    {
                        updates.Add(updateBuilder.Set(x => x.MinValue, telemetry.ResultValue));
                    }
                    
                    // 更新最大值
                    if (telemetry.ResultValue > bucket.MaxValue)
                    {
                        updates.Add(updateBuilder.Set(x => x.MaxValue, telemetry.ResultValue));
                    }
                    
                    // 更新总和和平均值
                    float newSum = bucket.SumValue + telemetry.ResultValue;
                    float newAvg = newSum / (bucket.Count + 1);
                    updates.Add(updateBuilder.Set(x => x.SumValue, newSum));
                    updates.Add(updateBuilder.Set(x => x.AvgValue, newAvg));
                    
                    // 执行更新
                    var updateResult = await _telemeteringBucketRepository.UpdateAsync(bucketFilter, updateBuilder.Combine(updates));
                    Log4Helper.Debug(this.GetType(), $"[SaveToBucket] 更新桶: {telemetry.Id}, 时间: {bucketStart:yyyy-MM-dd HH:mm:ss}, 结果: {updateResult.ModifiedCount}");
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[SaveToBucket] 保存分桶数据异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存统计数据到分桶存储
        /// </summary>
        /// <param name="statistics">统计结果列表</param>
        /// <param name="telemetry">遥测数据</param>
        /// <param name="interval">时间间隔</param>
        /// <param name="equipmentName">设备名称</param>
        /// <returns></returns>
        public async Task SaveStatisticsToBucket(
            List<TelemeteringStatisticsResult> statistics, 
            TelemeteringModel telemetry, 
            FixedIntervalEnum interval, 
            string equipmentName)
        {
            try
            {
                if (statistics == null || !statistics.Any() || telemetry == null)
                {
                    Log4Helper.Error(this.GetType(), "[SaveStatisticsToBucket] 参数为 null 或为空，跳过处理。");
                    return;
                }

                // 检查数据源类型
                if (telemetry.DataSourceCategory == null)
                {
                    Log4Helper.Warn(this.GetType(), $"遥测点 {telemetry.Id} 的DataSourceCategory为空");
                    return;
                }

                DateTime now = DateTime.Now;
                
                // 计算桶的开始和结束时间（按月）
                DateTime bucketStart = new DateTime(now.Year, now.Month, 1);
                DateTime bucketEnd = bucketStart.AddMonths(1).AddSeconds(-1);

                // 设置集合名称 - 按年份和间隔类型分表
                _telemeteringStatisticsBucketRepository.CollectionName = $"{nameof(TelemeteringStatisticsBucket)}_{interval}_{now.Year}";

                // 查找或创建当前月的统计桶
                var bucketFilter = Builders<TelemeteringStatisticsBucket>.Filter.And(
                    Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.TelemeteringConfigurationId, telemetry.Id),
                    Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.Year, now.Year),
                    Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.Month, now.Month),
                    Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.IntervalType, interval)
                );

                var bucket = await _telemeteringStatisticsBucketRepository.FirstOrDefaultAsync(bucketFilter);

                // 转换统计结果为桶内的结果格式
                var results = statistics.Select(s => new StatisticsResult
                {
                    StatisticsType = s.StatisticsType,
                    StatisticsDateTime = s.StatisticsDateTime,
                    ResultValue = s.ResultValue,
                    DataCount = s.DataCount,
                    ResultTime = s.ResultTime
                }).ToList();

                if (bucket == null)
                {
                    // 创建新的统计桶
                    bucket = new TelemeteringStatisticsBucket
                    {
                        TelemeteringConfigurationId = telemetry.Id,
                        Name = telemetry.Name,
                        EquipmentInfoId = telemetry.EquipmentInfoId,
                        EquipmentInfoName = equipmentName,
                        Unit = telemetry.Unit,
                        DataSourceCategory = (int?)telemetry.DataSourceCategory,
                        IntervalType = interval,
                        StartTime = bucketStart,
                        EndTime = bucketEnd,
                        Count = results.Count,
                        Year = now.Year,
                        Month = now.Month,
                        Results = results
                    };

                    await _telemeteringStatisticsBucketRepository.InsertAsync(bucket);
                    Log4Helper.Debug(this.GetType(), $"[SaveStatisticsToBucket] 创建新统计桶: {telemetry.Id}, 年月: {now:yyyy-MM}, 间隔: {interval}");
                }
                else
                {
                    // 更新现有统计桶
                    var updateBuilder = Builders<TelemeteringStatisticsBucket>.Update;
                    var updates = new List<UpdateDefinition<TelemeteringStatisticsBucket>>();

                    // 添加新的统计结果
                    foreach (var result in results)
                    {
                        updates.Add(updateBuilder.Push(x => x.Results, result));
                    }
                    
                    updates.Add(updateBuilder.Inc(x => x.Count, results.Count));
                    
                    // 执行更新
                    var updateResult = await _telemeteringStatisticsBucketRepository.UpdateAsync(bucketFilter, updateBuilder.Combine(updates));
                    Log4Helper.Debug(this.GetType(), $"[SaveStatisticsToBucket] 更新统计桶: {telemetry.Id}, 年月: {now:yyyy-MM}, 间隔: {interval}, 结果: {updateResult.ModifiedCount}");
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[SaveStatisticsToBucket] 保存统计分桶数据异常: {ex.Message}", ex);
            }
        }
    }
} 