﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LoginTest
{

    public class ConsoleSelectTest
    {
        static int selectedOption = 0; // 初始选择为第一个选项

        public static void Run()
        {
            string[] options = { "选项 1", "选项 2", "选项 3", "退出" };

            while (true)
            {
                Console.Clear(); // 清除控制台内容

                // 显示选项
                for (int i = 0; i < options.Length; i++)
                {
                    if (i == selectedOption)
                    {
                        Console.ForegroundColor = ConsoleColor.White; // 设置选中选项的前景色
                        Console.BackgroundColor = ConsoleColor.DarkBlue; // 设置选中选项的背景色
                    }
                    else
                    {
                        Console.ForegroundColor = ConsoleColor.DarkBlue; // 设置未选中选项的前景色
                        Console.BackgroundColor = ConsoleColor.Black; // 设置未选中选项的背景色
                    }

                    Console.WriteLine(options[i]);
                }

                Console.ResetColor(); // 恢复默认颜色

                // 获取用户按键
                ConsoleKeyInfo keyInfo = Console.ReadKey();

                // 处理用户按键
                switch (keyInfo.Key)
                {
                    case ConsoleKey.UpArrow:
                        selectedOption = Math.Max(0, selectedOption - 1);
                        break;
                    case ConsoleKey.DownArrow:
                        selectedOption = Math.Min(options.Length - 1, selectedOption + 1);
                        break;
                    case ConsoleKey.Enter:
                        if (selectedOption == options.Length - 1)
                        {
                            // 用户选择退出
                            Environment.Exit(0);
                        }
                        else
                        {
                            // 执行选中选项的操作
                            Console.WriteLine($"您选择了：{options[selectedOption]}");
                            Console.ReadKey(); // 等待用户按任意键继续
                        }
                        break;
                }
            }
        }
    }
}


