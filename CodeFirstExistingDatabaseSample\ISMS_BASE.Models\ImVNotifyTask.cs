﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVNotifyTask
    {
        public string TaskId { get; set; } = null!;
        public string TaskName { get; set; } = null!;
        public string StartTime { get; set; } = null!;
        public string EndTime { get; set; } = null!;
        public string Sql4notify { get; set; } = null!;
        public string BusiTabName { get; set; } = null!;
        public string PkfldName { get; set; } = null!;
        public string Sql4phoneNo { get; set; } = null!;
        public string Sql4msg { get; set; } = null!;
        public string State { get; set; } = null!;
        public DateTime CreateTime { get; set; }
        public string AlertType { get; set; } = null!;
        public string TaskDesc { get; set; } = null!;
    }
}
