﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVSheBeiDeviceDatum
    {
        public string RecId { get; set; } = null!;
        public string SheBeiId { get; set; } = null!;
        public string DataId { get; set; } = null!;
        public string SheBeiStatName { get; set; } = null!;
        public string YunXingBh { get; set; } = null!;
        public string SheBeiFullName { get; set; } = null!;
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public string DataFullName { get; set; } = null!;
    }
}
