﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbSm
    {
        public TbSm()
        {
            TbSmsSents = new HashSet<TbSmsSent>();
        }

        public string MsgId { get; set; } = null!;
        public string Content { get; set; } = null!;
        public DateTime CreateTime { get; set; }

        public virtual ICollection<TbSmsSent> TbSmsSents { get; set; }
    }
}
