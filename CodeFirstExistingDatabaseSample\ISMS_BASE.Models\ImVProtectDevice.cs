﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVProtectDevice
    {
        public string DeviceId { get; set; } = null!;
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string? BayName { get; set; }
        public int? DeviceState { get; set; }
        public int PuctgyCode { get; set; }
        public int CanSwDzzone { get; set; }
        public int Support12yc { get; set; }
        public int SupportVersion { get; set; }
        public int SupportDkjl { get; set; }
        public int? StartOfDkjl { get; set; }
        public int? EndOfDkjl { get; set; }
        public string? DeviceType { get; set; }
        public string StatName { get; set; } = null!;
        public string GateWayName { get; set; } = null!;
        public string? GatewayIp1 { get; set; }
        public int? GatewayPort1 { get; set; }
        public string? GatewayIp2 { get; set; }
        public int? GatewayPort2 { get; set; }
        public int PhyAddr { get; set; }
        public string Manufacturer { get; set; } = null!;
        public string DevCtgy { get; set; } = null!;
        public string Generation { get; set; } = null!;
        public int DzzoneCount { get; set; }
        public string? Model { get; set; }
        public string AnalogParseMode { get; set; } = null!;
        public string EventParseMode { get; set; } = null!;
        public int SupportGuZhangBg { get; set; }
        public int SupportShiJianBg { get; set; }
        public int SupportZiJianBg { get; set; }
        public int SupportLuBoWj { get; set; }
        public int SupportDz { get; set; }
        public int DzreadOnly { get; set; }
        public int SupportFhluBo { get; set; }
    }
}
