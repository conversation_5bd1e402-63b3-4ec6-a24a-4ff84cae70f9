﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDztype
    {
        public ImDztype()
        {
            ImDeviceDzTmps = new HashSet<ImDeviceDzTmp>();
            ImDeviceDzs = new HashSet<ImDeviceDz>();
        }

        public int DztypeId { get; set; }
        public string Dztype { get; set; } = null!;

        public virtual ICollection<ImDeviceDzTmp> ImDeviceDzTmps { get; set; }
        public virtual ICollection<ImDeviceDz> ImDeviceDzs { get; set; }
    }
}
