﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbTable
    {
        public TbTable()
        {
            TbFields = new HashSet<TbField>();
            TbTableMapDstTables = new HashSet<TbTableMap>();
            TbTableMapSrcTables = new HashSet<TbTableMap>();
            TbVirtualTableBaseTables = new HashSet<TbVirtualTable>();
            TbVirtualTableDeriveTables = new HashSet<TbVirtualTable>();
        }

        public string TableId { get; set; } = null!;
        public string TableName { get; set; } = null!;
        public string Prcname { get; set; } = null!;
        public string Ctgy { get; set; } = null!;
        public string? DbconnName { get; set; }
        public string? OrderBy { get; set; }
        public int SeqNo { get; set; }
        public string IsView { get; set; } = null!;
        public string? TblName4View { get; set; }

        public virtual ICollection<TbField> TbFields { get; set; }
        public virtual ICollection<TbTableMap> TbTableMapDstTables { get; set; }
        public virtual ICollection<TbTableMap> TbTableMapSrcTables { get; set; }
        public virtual ICollection<TbVirtualTable> TbVirtualTableBaseTables { get; set; }
        public virtual ICollection<TbVirtualTable> TbVirtualTableDeriveTables { get; set; }
    }
}
