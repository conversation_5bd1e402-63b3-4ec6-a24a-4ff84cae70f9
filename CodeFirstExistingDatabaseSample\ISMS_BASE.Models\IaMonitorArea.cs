﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaMonitorArea
    {
        public IaMonitorArea()
        {
            IaMaChannels = new HashSet<IaMaChannel>();
        }

        public string MaId { get; set; } = null!;
        public string? ParentId { get; set; }
        public string Maname { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public int? SeqNo { get; set; }

        public virtual ImStation StatCodeNavigation { get; set; } = null!;
        public virtual ICollection<IaMaChannel> IaMaChannels { get; set; }
    }
}
