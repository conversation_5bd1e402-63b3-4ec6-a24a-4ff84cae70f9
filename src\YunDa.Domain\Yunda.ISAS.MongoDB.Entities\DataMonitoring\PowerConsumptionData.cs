﻿using Abp.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Text;
using Yunda.ISAS.MongoDB.Entities.Helper;

namespace Yunda.SOMS.MongoDB.Entities.DataMonitoring
{
    public class PowerConsumptionData : Entity<Guid>
    {
        /// <summary>
        /// 关联的遥测配置ID
        /// </summary>
        [BsonElement("TelemeteringConfigurationId")]
        [MongoDBDescendingIndex]
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 能耗数据名称
        /// </summary>
        [BsonElement("Name")]
        public virtual string Name { get; set; }

        /// <summary>
        /// 关联设备ID
        /// </summary>
        [BsonElement("EquipmentInfoId")]
        [MongoDBDescendingIndex]
        public virtual Guid? EquipmentInfoId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        [BsonElement("EquipmentInfoName")]
        public virtual string EquipmentInfoName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [BsonElement("StartTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [BsonElement("EndTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime EndTime { get; set; }

        /// <summary>
        /// 平均功率 (kW)
        /// </summary>
        [BsonElement("AveragePower")]
        public virtual float AveragePower { get; set; }

        /// <summary>
        /// 能耗值 (kWh)
        /// </summary>
        [BsonElement("EnergyConsumption")]
        public virtual float EnergyConsumption { get; set; }

        /// <summary>
        /// 平均无功功率 (kVar)
        /// </summary>
        [BsonElement("ReactiveAveragePower")]
        public virtual float ReactiveAveragePower { get; set; }

        /// <summary>
        /// 无功能耗值 (kVarh)
        /// </summary>
        [BsonElement("ReactiveEnergyConsumption")]
        public virtual float ReactiveEnergyConsumption { get; set; }

        /// <summary>
        /// 单位 (默认为kWh)
        /// </summary>
        [BsonElement("Unit")]
        public virtual string Unit { get; set; }

        /// <summary>
        /// 间隔类型 (对应FixedIntervalEnum枚举)
        /// </summary>
        [BsonElement("IntervalType")]
        public virtual int IntervalType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [BsonElement("CreationTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public virtual DateTime CreationTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PowerConsumptionData()
        {
            Id = Guid.NewGuid();
            Unit = "kWh";
            CreationTime = DateTime.Now;
        }

       

        /// <summary>
        /// 转换为BsonDocument
        /// </summary>
        /// <returns>BsonDocument实例</returns>
        public BsonDocument ToBsonDocument()
        {
            return new BsonDocument
            {
                { "_id", Id },
                { "TelemeteringConfigurationId", TelemeteringConfigurationId },
                { "Name", Name },
                { "EquipmentInfoId", EquipmentInfoId },
                { "EquipmentInfoName", EquipmentInfoName },
                { "StartTime", StartTime },
                { "EndTime", EndTime },
                { "AveragePower", AveragePower },
                { "EnergyConsumption", EnergyConsumption },
                { "ReactiveAveragePower", ReactiveAveragePower },
                { "ReactiveEnergyConsumption", ReactiveEnergyConsumption },
                { "Unit", Unit },
                { "IntervalType", IntervalType },
                { "CreationTime", CreationTime }
            };
        }
    }
}

