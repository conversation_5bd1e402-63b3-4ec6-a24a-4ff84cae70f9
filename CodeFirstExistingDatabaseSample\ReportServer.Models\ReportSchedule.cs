﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ReportSchedule
    {
        public Guid ScheduleId { get; set; }
        public Guid ReportId { get; set; }
        public Guid? SubscriptionId { get; set; }
        public int ReportAction { get; set; }

        public virtual Catalog Report { get; set; } = null!;
        public virtual Schedule Schedule { get; set; } = null!;
        public virtual Subscription? Subscription { get; set; }
    }
}
