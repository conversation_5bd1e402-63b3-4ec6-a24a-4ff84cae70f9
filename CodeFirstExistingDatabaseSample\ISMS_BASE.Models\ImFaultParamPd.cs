﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFaultParamPd
    {
        public int ParamCode { get; set; }
        public int FaultCode { get; set; }
        public string ParamName { get; set; } = null!;
        public string? ParamSym1 { get; set; }
        public double? ParamCof1 { get; set; }
        public string? ParamSym2 { get; set; }
        public double? ParamCof2 { get; set; }

        public virtual ImFaultTypePd FaultCodeNavigation { get; set; } = null!;
    }
}
