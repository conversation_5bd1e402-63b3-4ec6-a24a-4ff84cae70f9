﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbGlyphHot
    {
        public string GlyphId { get; set; } = null!;
        public short HotIndex { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public short HotType { get; set; }
        public string? ConnectedGlyph { get; set; }

        public virtual TbGlyph Glyph { get; set; } = null!;
    }
}
