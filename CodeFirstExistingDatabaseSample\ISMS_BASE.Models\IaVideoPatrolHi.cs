﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaVideoPatrolHi
    {
        public IaVideoPatrolHi()
        {
            IaVideoPatrolItemHis = new HashSet<IaVideoPatrolItemHi>();
        }

        public string PatHisId { get; set; } = null!;
        public string PatId { get; set; } = null!;
        public string PatName { get; set; } = null!;
        public string? PatType { get; set; }
        public string? PatUser { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string Result { get; set; } = null!;
        public string? Comment { get; set; }

        public virtual ICollection<IaVideoPatrolItemHi> IaVideoPatrolItemHis { get; set; }
    }
}
