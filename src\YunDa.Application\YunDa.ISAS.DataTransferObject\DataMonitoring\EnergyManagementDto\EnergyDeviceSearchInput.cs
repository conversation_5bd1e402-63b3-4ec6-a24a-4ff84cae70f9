﻿using Abp.Application.Services.Dto;
using System;
using System.Collections.Generic;
using System.Text;

namespace YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto
{
    public class EnergyDeviceSearchInput
    {
        /// <summary>
        /// 设备名称，用于模糊查询
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public int? DeviceType { get; set; }

        /// <summary>
        /// 变电站ID
        /// </summary>
        public Guid? TransformerSubstationId { get; set; }

        /// <summary>
        /// 是否仅查询活动的记录
        /// </summary>
        public bool OnlyActive { get; set; } = true;
    }
}
