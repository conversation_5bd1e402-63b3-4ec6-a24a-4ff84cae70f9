﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImVDevdatalinkage
    {
        public string Id { get; set; }
        public string Deviceid { get; set; }
        public string Datatype { get; set; }
        public string Dataname { get; set; }
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int Autosave { get; set; }
        public int Visible { get; set; }
        public string Beizhu { get; set; }
        public string Srctempdataid { get; set; }
        public string Domain { get; set; }
        public int Deviceaddr { get; set; }
        public string Devicename { get; set; }
        public string Statcode { get; set; }
        public string Gatewayid { get; set; }
        public string Statname { get; set; }
        public string Lnkid { get; set; }
        public string Srcobjid { get; set; }
        public string Srcobjtype { get; set; }
        public string Lnktype { get; set; }
        public string Resource { get; set; }
        public string Action { get; set; }
        public string Actparam { get; set; }
        public string State { get; set; }
    }
}
