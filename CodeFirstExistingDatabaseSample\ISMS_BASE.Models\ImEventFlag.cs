﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImEventFlag
    {
        public string DeviceCtgy { get; set; } = null!;
        public int EvtFlagCode { get; set; }
        public string Param1Mean { get; set; } = null!;
        public string? Param1Sym { get; set; }
        public string? Param1ValueType { get; set; }
        public string Param2Mean { get; set; } = null!;
        public string? Param2Sym { get; set; }
        public string? Param2ValueType { get; set; }
        public string? Param3Mean { get; set; }
        public string? Param3Sym { get; set; }
        public string? Param3ValueType { get; set; }

        public virtual ImDevCtgy DeviceCtgyNavigation { get; set; } = null!;
    }
}
