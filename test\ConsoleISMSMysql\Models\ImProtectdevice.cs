﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImProtectdevice
    {
        public ImProtectdevice()
        {
            ImDevicedata = new HashSet<ImDevicedata>();
            ImDevicedz = new HashSet<ImDevicedz>();
            ImShebeiProtdevice = new HashSet<ImShebeiProtdevice>();
        }

        public string Deviceid { get; set; }
        public int Deviceaddr { get; set; }
        public string Devicename { get; set; }
        public string Statcode { get; set; }
        public string Gatewayid { get; set; }
        public string Bayname { get; set; }
        public int? Devicestate { get; set; }
        public int Puctgycode { get; set; }
        public int Canswdzzone { get; set; }
        public int Support12yc { get; set; }
        public int Supportversion { get; set; }
        public int Supportdkjl { get; set; }
        public int? Startofdkjl { get; set; }
        public int? Endofdkjl { get; set; }
        public string Devicetype { get; set; }
        public string Wavepath { get; set; }

        public virtual ImGateway Gateway { get; set; }
        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
        public virtual ImStation StatcodeNavigation { get; set; }
        public virtual ICollection<ImDevicedata> ImDevicedata { get; set; }
        public virtual ICollection<ImDevicedz> ImDevicedz { get; set; }
        public virtual ICollection<ImShebeiProtdevice> ImShebeiProtdevice { get; set; }
    }
}
