﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImOnlineAnalysis
    {
        public string Id { get; set; } = null!;
        public string Method { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string? DataIdofC2h2 { get; set; }
        public string? DataIdofC2h4 { get; set; }
        public string? DataIdofC2h6 { get; set; }
        public string? DataIdofCh4 { get; set; }
        public string? DataIdofH2 { get; set; }
        public string UseState { get; set; } = null!;
        public string IsAlert { get; set; } = null!;

        public virtual ImProtectDevice Device { get; set; } = null!;
    }
}
