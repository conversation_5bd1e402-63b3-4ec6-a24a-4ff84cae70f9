﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.OperationReport;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.DeviceTcpData;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.DeviceHistoryChange;
using Serilog;
using Newtonsoft.Json;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.TestData
{
    public class SimulateData
    {
        private static int _sendNo = 1;

        public static byte[] SendProductionInformation()
        {
            string msg = "{\r\n  \"station\":\"Bengbu_South_Station\",\r\n  \"device\":\"KX211\",\r\n\"timestamp\":\"2024-10-09 12:12:12\",\r\n\"reason\":1,\r\n\"param\":{\r\n  \"type\":\" Production_Information\",\r\n\"paras\":\r\n        {\r\n         \"number\":\"202400xxxxxx\",\r\n         \"date\":\"2024-10-09\",\r\n}\r\n}\r\n}\r\n";
             byte[] utf8Bytes = System.Text.Encoding.UTF8.GetBytes(msg);
            return utf8Bytes;
        }

        public static void Test1(Server.DotNettyTcpServer _dotNettyTcpServer)
        {
            Log.Information("执行装置履历测试...");
            UploadMainstationBaseInfo<DeviceProductionInfo> uploadMainstationBaseInfo = new UploadMainstationBaseInfo<DeviceProductionInfo>()
            {
                Station = "德州变电所",
                Device = "JDA-211 馈线保护测控装置",
                EventType = "设备履历",
                Subtype = "装置信息",
                EventTime = "2024-15-16",
                DATANO = _sendNo,
                Info = "周期上送",
                Detail = new DeviceProductionInfo
                {
                    DeviceId = "240531914",
                    ManufacturerModel = "JDA-201",
                    InternalName = "201主变保护装置",
                    ManufacturerName = "交大运达电气",
                    DeviceSpecification = "DC110V/1A",
                    DeviceVersion = "4.0",
                    ManufacturingDate = "20240131",
                    B01PluginId = "1933CA00300001",
                    B02PluginId = "1933JA04100001",
                    B03PluginId = "2233IA220V400001",
                    B04PluginId = "2233IA220V400002",
                    B05PluginId = "2233OA0000400001",
                    B06PluginId = "2233OC220V400001",
                    B07PluginId = "1933DA00100001",
                    LCDBoardCardId = "1933YA01200001",
                    RepairRecords = "无维修" // 维修记录为空
                }
            };
            var json = JsonConvert.SerializeObject(uploadMainstationBaseInfo, Formatting.Indented);
            _dotNettyTcpServer.SendMessage(20, 1, json);
            _sendNo++;
        }
        public static void Test2(Server.DotNettyTcpServer _dotNettyTcpServer)
        {
            Log.Information("执行设备参数报文测试...");
            UploadMainstationBaseInfo<CommunicationParameters> uploadMainstationBaseInfo = new UploadMainstationBaseInfo<CommunicationParameters>()
            {
                Station = "德州变电所",
                Device = "JDA-211 馈线保护测控装置",
                EventType = "运行诊断",
                Subtype = "装置自检",
                EventTime = "2024-15-16",
                DATANO = _sendNo,
                Info = "周期上送",
                Detail = new CommunicationParameters
                {
                    IpPort1 = "*************",
                    MaskPort1 = "*************",
                    IpPort2 = "*************",
                    MaskPort2 = "*************",
                    IpPort3 = "*************",
                    MaskPort3 = "*************",
                    Gateway = "***********",
                    SntpServer1Ip = "************",
                    SntpServer2Ip = "************"
                }
            };

            var json = JsonConvert.SerializeObject(uploadMainstationBaseInfo, Formatting.Indented);
            _dotNettyTcpServer.SendMessage(20, 2, json);
            _sendNo++;
        }
        public static void Test3(Server.DotNettyTcpServer _dotNettyTcpServer)
        {
            Log.Information("执行运行诊断操作...");
            UploadMainstationBaseInfo<AbnormalComponent> uploadMainstationBaseInfo = new UploadMainstationBaseInfo<AbnormalComponent>()
            {
                Station = "德州变电所",
                Device = "JDA-211 馈线保护测控装置",
                EventType = "运行诊断",
                Subtype = "装置自检",
                EventTime = "2024-15-16",
                DATANO = _sendNo,
                Info = "周期上送",
                Detail = new AbnormalComponent
                {
                    ComponentName = "机箱温度",
                    AbnormalReason = "当前值63°，超过门限60°",
                    HandlingMeasures = "XXXXX"
                }
            };

            var json = JsonConvert.SerializeObject(uploadMainstationBaseInfo, Formatting.Indented);
            _dotNettyTcpServer.SendMessage(20, 3, json);
            _sendNo++;
        }
        public static void Test4(Server.DotNettyTcpServer _dotNettyTcpServer)
        {
            Log.Information("执行运行状态操作...");
            UploadMainstationBaseInfo<NetworkStatus> uploadMainstationBaseInfo = new UploadMainstationBaseInfo<NetworkStatus>()
            {
                Station = "德州变电所",
                Device = "JDA-211 馈线保护测控装置",
                EventType = "运行状态",
                Subtype = "通信状态",
                EventTime = "2024-15-16",
                DATANO = _sendNo,
                Info = "周期上送",
                Detail = new NetworkStatus
                {
                    // 网口1信息
                    Port1IpAddress = "***********1",
                    Port1Status = "up",
                    Port1SpeedType = "100M/s",
                    Port1StartTime = "2024-10-15  11:00:00",
                    Port1TotalTime = "12000s",
                    Port1SentFrames = 100,
                    Port1SentErrorFrames = 0,
                    Port1ReceivedFrames = 100,
                    Port1ReceivedErrorFrames = 0,

                    // 网口2信息
                    Port2IpAddress = "************",
                    Port2Status = "up",
                    Port2SpeedType = "100M/s",

                    Port2StartTime = "2024-10-15  11:00:00",
                    Port2TotalTime = "12000s",
                    Port2SentFrames = 100,
                    Port2SentErrorFrames = 0,
                    Port2ReceivedFrames = 100,
                    Port2ReceivedErrorFrames = 0,

                    // 网口3信息
                    Port3IpAddress = "************",
                    Port3Status = "up",
                    Port3SpeedType = "100M/s",
                    Port3StartTime = "2024-10-15  11:00:00",
                    Port3TotalTime = "12000s",
                    Port3SentFrames = 100,
                    Port3SentErrorFrames = 0,
                    Port3ReceivedFrames = 100,
                    Port3ReceivedErrorFrames = 0,

                    // 插件通信状态
                    Plugin1Status = "正常",
                    Plugin3Status = "正常",
                    Plugin4Status = "正常",
                    Plugin5Status = "异常",
                    Plugin7Status = "正常",

                    // 网络通信连接状态
                    NetworkConnection1Status = "连接",
                    NetworkConnection2Status = "断开",
                    NetworkConnection3Status = "连接",
                    NetworkConnection4Status = "连接",
                    NetworkConnection5Status = "断开",
                    NetworkConnection6Status = "连接",
                    NetworkConnection7Status = "断开",
                    NetworkConnection8Status = "连接"
                }
            };
            var json = JsonConvert.SerializeObject(uploadMainstationBaseInfo, Formatting.Indented);
            _dotNettyTcpServer.SendMessage(20, 4, json);
            _sendNo++;
        }
    }
}
