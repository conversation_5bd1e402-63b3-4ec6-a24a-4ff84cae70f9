using Abp.Application.Services;
using System;
using System.Threading.Tasks;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsConfigDto;

namespace YunDa.ISAS.Application.DataMonitoring
{
    /// <summary>
    /// 遥测统计配置服务接口
    /// </summary>
    public interface ITelemeteringStatisticsConfigAppService : IApplicationService
    {
        /// <summary>
        /// 创建遥测统计配置
        /// </summary>
        /// <param name="input">遥测统计配置输入</param>
        /// <returns>创建结果</returns>
        Task<RequestResult<TelemeteringStatisticsConfigOutput>> CreateAsync(EditTelemeteringStatisticsConfigInput input);

        /// <summary>
        /// 更新遥测统计配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <param name="input">遥测统计配置输入</param>
        /// <returns>更新结果</returns>
        Task<RequestResult<TelemeteringStatisticsConfigOutput>> UpdateAsync(Guid id, EditTelemeteringStatisticsConfigInput input);

        /// <summary>
        /// 删除遥测统计配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        Task<RequestEasyResult> DeleteAsync(Guid id);

        /// <summary>
        /// 获取遥测统计配置详情
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>配置详情</returns>
        Task<RequestResult<TelemeteringStatisticsConfigOutput>> GetAsync(Guid id);

        /// <summary>
        /// 分页查询遥测统计配置
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>分页结果</returns>
        Task<RequestPageResult<TelemeteringStatisticsConfigOutput>> GetPagedAsync(TelemeteringStatisticsConfigSearchConditionInput input);

        /// <summary>
        /// 获取统计类型列表
        /// </summary>
        /// <returns>统计类型列表</returns>
        Task<RequestResult<SelectModelOutputList>> GetStatisticsTypeListAsync();

        /// <summary>
        /// 获取时间间隔类型列表
        /// </summary>
        /// <returns>时间间隔类型列表</returns>
        Task<RequestResult<SelectModelOutputList>> GetIntervalTypeListAsync();
    }
} 