﻿using System;
using System.Windows;

namespace WpfForWeb
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        string _baseUrl = @"http://192.168.108.57:10086/";
        string _telemeteringAnalysisUrl = @"DataMonitoring/TelemeteringAnalysis";
        string _loginUrl = @"api/TokenAuth/Authenticate";
        public static string accessToken = "";
        public MainWindow()
        {
            InitializeComponent();

            Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            AuthenticateModel loginModel = new AuthenticateModel
            {
                UserName = "admin",
                Password = "123qwe"
            };
            //JObject rstJObject = HttpHelper.HttpPostRequest<JObject>(_baseUrl + _loginUrl, loginModel);
            //AuthenticateResultModel rstModel = JsonConvert.DeserializeObject<AuthenticateResultModel>(rstJObject["result"].ToString());
            //accessToken = rstModel.AccessToken;
            //cef_Chrome.RequestHandler = new MyRequestHandler();
            //cef_Chrome.Address = _baseUrl + _telemeteringAnalysisUrl + "?accessToken=" + rstModel.AccessToken;
            //cef_Chrome.FrameLoadEnd += cef_Chrome_FrameLoadEnd;
            //cef_Chrome.MenuHandler = new MenuHandler();
        }

        private void cef_Chrome_FrameLoadEnd(object sender, CefSharp.FrameLoadEndEventArgs e)
        {
            this.Dispatcher.Invoke(() =>
            {
                this.w_PageWait.Visibility = Visibility.Collapsed;
                if (e.HttpStatusCode == 200)
                    this.tb_Message.Visibility = Visibility.Collapsed;
                else
                    this.tb_Message.Visibility = Visibility.Visible;
            });
        }
        #region 窗体管理按钮
        //private void MinButton_Click(object sender, RoutedEventArgs e)
        //{
        //    this.WindowState = System.Windows.WindowState.Minimized;
        //}

        //private void MaxButton_Click(object sender, RoutedEventArgs e)
        //{
        //    if (this.WindowState == System.Windows.WindowState.Maximized)
        //    {

        //        this.WindowState = System.Windows.WindowState.Normal;
        //        this.sp_Max.Visibility = Visibility.Visible;
        //        this.g_Normal.Visibility = Visibility.Collapsed;
        //    }

        //    else
        //    {
        //        this.WindowState = System.Windows.WindowState.Maximized;
        //        this.sp_Max.Visibility = Visibility.Collapsed;
        //        this.g_Normal.Visibility = Visibility.Visible;
        //    }
        //}

        //private void CloseButton_Click(object sender, RoutedEventArgs e)
        //{
        //    this.Close();
        //}
        //private void TitleGrid_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        //{
        //    this.DragMove();
        //}
        //private void Border_MouseEnter(object sender, MouseEventArgs e)
        //{
        //    this.g_WindowButton.Visibility = Visibility.Visible;
        //}

        //private void Border_MouseLeave(object sender, MouseEventArgs e)
        //{
        //    this.g_WindowButton.Visibility = Visibility.Collapsed;
        //}
        #endregion
    }
    //public class MyRequestHandler : DefaultRequestHandler
    //{
    //    public static readonly string VersionNumberString = String.Format("Chromium: {0}, CEF: {1}, CefSharp: {2}",
    //        Cef.ChromiumVersion, Cef.CefVersion, Cef.CefSharpVersion);

    //    public override CefReturnValue OnBeforeResourceLoad(IWebBrowser browserControl, IBrowser browser, IFrame frame, IRequest request, IRequestCallback callback)
    //    {
    //        Uri url;
    //        if (Uri.TryCreate(request.Url, UriKind.Absolute, out url) == false)
    //        {
    //            return CefReturnValue.Cancel;
    //        }
    //        var headers = request.Headers;
    //        headers["Authorization"] = "Bearer" + " " + MainWindow.accessToken; //传递进去认证Token
    //        request.Headers = headers;
    //        return CefReturnValue.Continue;
    //    }
    //}
    public class AuthenticateModel
    {
        public string UserName { get; set; }

        public string Password { get; set; }

        public bool RememberClient { get; set; }
    }

    public class AuthenticateResultModel
    {
        public string AccessToken { get; set; }

        public string EncryptedAccessToken { get; set; }

        public int ExpireInSeconds { get; set; }

        public Guid UserId { get; set; }
    }
}
