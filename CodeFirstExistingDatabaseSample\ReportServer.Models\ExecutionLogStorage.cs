﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ExecutionLogStorage
    {
        public long LogEntryId { get; set; }
        public string InstanceName { get; set; } = null!;
        public Guid? ReportId { get; set; }
        public string? UserName { get; set; }
        public string? ExecutionId { get; set; }
        public byte RequestType { get; set; }
        public string? Format { get; set; }
        public string? Parameters { get; set; }
        public byte? ReportAction { get; set; }
        public DateTime TimeStart { get; set; }
        public DateTime TimeEnd { get; set; }
        public int TimeDataRetrieval { get; set; }
        public int TimeProcessing { get; set; }
        public int TimeRendering { get; set; }
        public byte Source { get; set; }
        public string Status { get; set; } = null!;
        public long ByteCount { get; set; }
        public long RowCount { get; set; }
        public string? AdditionalInfo { get; set; }
    }
}
