﻿using Abp.Dependency;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using System.Windows.Documents;
using System.Windows.Markup;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Dlls;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.DataAnalysis.DataCollection;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Redis.Entities.DataMonitorCategory;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection
{
    /// <summary>
    /// 数据采集
    /// </summary>
    public class DataCollectionTask : ISingletonDependency
    {
        private Content _settingModel = null;

        /// <summary>
        /// 时间转换算法
        /// </summary>
        private Func<byte[], DateTime> FormatDateTime = tm => new DateTime(2000 + tm[6], tm[5], tm[4], tm[3], tm[2],
                    ((tm[1] << 8) + tm[0]) / 1000, ((tm[1] << 8) + tm[0]) % 1000);
        CancellationTokenSource cancellationTokenSource = default;
        private readonly DataSendTask _dataSendTask;
        private readonly RunningDataCache _runningDataCache;
        private readonly RedisDataRepository _redisDataRepository;
        private readonly WebApiRequest _webApiRequest;

        public DataCollectionTask(Content settingModel
            , DataSendTask dataSendTask,
            RedisDataRepository redisDataRepository,
            WebApiRequest webApiRequest,
            RunningDataCache runningDataCache)
        {
            _dataSendTask = dataSendTask;
            _settingModel = settingModel;
            _runningDataCache = runningDataCache;
            _redisDataRepository = redisDataRepository;
            _webApiRequest = webApiRequest;

        }
        #region 数据采集计时器

        ActionBlock<IECData> _handleDataAction;
        DateTime _lastTime = DateTime.Now;
        private bool _isStarted=  false;
        public ConcurrentDictionary<string, IEC104Client> _clients = new ConcurrentDictionary<string, IEC104Client>();
        private ConcurrentDictionary<string, ConcurrentBag<YC_TYPE_New>> _ycLists = new ConcurrentDictionary<string, ConcurrentBag<YC_TYPE_New>>();
        private ConcurrentDictionary<string, ConcurrentBag<RECORDYXBURST_New>> _yxLists = new ConcurrentDictionary<string, ConcurrentBag<RECORDYXBURST_New>>();
        private ConcurrentDictionary<string, bool> _connectionStarted = new ConcurrentDictionary<string, bool>();

        private IEnumerable<ConnectionConfig> _connections;
        // 新增成员变量
        private ConcurrentDictionary<string, ActionBlock<IECData>> _actionBlocks = new ConcurrentDictionary<string, ActionBlock<IECData>>();


        public async Task CollectionStart(IEnumerable<ConnectionConfig> connections)
        {
            _connections = connections.ToList();
            foreach (var connection in _connections)
            {
                string connKey = connection.Ip + ":" + connection.Port;
                if (_clients.ContainsKey(connKey)) continue;

                // 初始化缓存
                var ycList = _ycLists.GetOrAdd(connKey, _ => new ConcurrentBag<YC_TYPE_New>());
                var yxList = _yxLists.GetOrAdd(connKey, _ => new ConcurrentBag<RECORDYXBURST_New>());
                _connectionStarted.TryAdd(connKey, false);

                // 关键：捕获所有必需变量，**禁止直接捕获 connection 引用**
                var actionBlock = new ActionBlock<IECData>(data =>
                {
                    var thisConnKey = connKey;
                    var thisConnection = _connections.First(c => c.Ip + ":" + c.Port == thisConnKey);

                    switch (data.DataType)
                    {
                        case IECDataType.Telemetering:
                            var yc = new YC_TYPE_New
                            {
                                chgFlag = 0,
                                addr = byte.Parse(thisConnection.RtuAddress),
                                sector = 0,
                                val = data.FValue,
                                inf = data.InfoAddr,
                                time = data.Time
                            };
                            if (_connectionStarted[thisConnKey])
                            {
                                _dataSendTask.RecordYCLogInfo(thisConnection, yc);
                                _dataSendTask.YCTActionBlock.Post(new RecordYC_TYPENewTaskInfo(thisConnection, yc));
                            }
                            else
                            {
                                ycList.Add(yc);
                            }
                            break;

                        case IECDataType.Telesignal:
                            var yx = new RECORDYXBURST_New
                            {
                                dev_addr = byte.Parse(thisConnection.RtuAddress),
                                dev_inf = data.InfoAddr,
                                dev_sector = 0,
                                yx_val = data.IValue,
                                time = data.Time
                            };
                            if (_connectionStarted[thisConnKey])
                            {
                                _dataSendTask.RecordYXLogInfo(thisConnection, yx);
                                _dataSendTask.RECORDYXBURSTActionBlock.Post(new RecordRECORDYXBURSTNewTaskInfo(thisConnection, yx));
                            }
                            else
                            {
                                yxList.Add(yx);
                            }
                            break;

                        case IECDataType.CompleteAll:
                            if (_connectionStarted.TryUpdate(thisConnKey, true, false))
                            {
                                Task.Run(() => UpdateRedisCache(thisConnection, ycList, yxList));
                            }
                            break;

                        case IECDataType.Msg:
                            if (data.Msg.Contains("U格式"))
                            {
                                thisConnection.Status = "已连接";
                                // 心跳等逻辑
                            }
                            break;
                    }
                }, new ExecutionDataflowBlockOptions
                {
                    MaxDegreeOfParallelism = 2,
                    BoundedCapacity = DataflowBlockOptions.Unbounded
                });

                _actionBlocks[connKey] = actionBlock;

                var client = new IEC104Client(connection.Ip, int.Parse(connection.Port), actionBlock);
                await client.ConnectAsync();
                _clients[connKey] = client;

                await Task.Delay(5000); // 节奏控制
            }
        }

        private void UpdateRedisCache(ConnectionConfig connection, ConcurrentBag<YC_TYPE_New> ycList, ConcurrentBag<RECORDYXBURST_New> yxList)
        {
            Task.WhenAll(
                OnlyChangeYC(connection, ycList),
                OnlyChangeYX(connection, yxList));
        }

        public void CallAllData()
        {
            _isStarted = false;
            foreach (var _client in _clients.Values)
            {
                _client?.CallAll();

            }
        }
       
        /// <summary>
        /// 停止
        /// </summary>
        public void CollectionStop()
        {
            try
            {
                _isStarted = false;
                foreach (var _client in _clients.Values)
                {
                    _client?.Close();
                }
                foreach (var connection in _connections)
                {
                    connection.Status = "未连接";
                }
                _clients.Clear();
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler("数据采集关闭", "数据采集");
            }

        }

        #endregion 数据采集计时器


        private async Task OnlyChangeYC(ConnectionConfig connection, ConcurrentBag<YC_TYPE_New> ycList)
        {
            var redisKey = $"{_redisDataRepository.TelemeteringModelListRediskey}_{connection.DataSourceCategoryName}";
            var ycDatas = await _redisDataRepository.TelemeteringModelListRedis.HashSetGetAllAsync(redisKey);

            var updateList = new List<TelemeteringModel>();
            var updateIdList = new List<string>();

            foreach (var yc in ycList)
            {
                var ycData = ycDatas.FirstOrDefault(t => t.DispatcherAddress == yc.inf && t.DeviceAddress == yc.addr);
                if (ycData != null)
                {
                    ycData.ResultTime = yc.time;
                    ycData.ResultValue = yc.val;
                    updateList.Add(ycData);
                    updateIdList.Add($"{ycData.DeviceAddress}_{ycData.DispatcherAddress}");
                }
            }

            await _redisDataRepository.TelemeteringModelListRedis.HashSetUpdateManyAsync(redisKey, updateIdList, updateList);
        }

        private async Task OnlyChangeYX(ConnectionConfig connection, ConcurrentBag<RECORDYXBURST_New> yxList)
        {
            var redisKey = $"{_redisDataRepository.TelesignalisationModelListRediskey}_{connection.DataSourceCategoryName}";
            var yxDatas = await _redisDataRepository.TelesignalisationModelListRedis.HashSetGetAllAsync(redisKey);

            var updateList = new List<TelesignalisationModel>();
            var updateIdList = new List<string>();

            foreach (var yx in yxList)
            {
                var yxData = yxDatas.FirstOrDefault(t => t.DispatcherAddress == yx.dev_inf && t.DeviceAddress == yx.dev_addr);
                if (yxData != null)
                {
                    yxData.ResultTime = yx.time;
                    yxData.ResultValue = yx.yx_val;
                    updateList.Add(yxData);
                    updateIdList.Add($"{yxData.DeviceAddress}_{yxData.DispatcherAddress}");
                }
            }

            await _redisDataRepository.TelesignalisationModelListRedis.HashSetUpdateManyAsync(redisKey, updateIdList, updateList);
        }

        public IEC104Client? GetClientByConnection(ConnectionConfig connection)
        {
            string connKey = connection.Ip + ":" + connection.Port;
            _clients.TryGetValue(connKey, out var client);
            return client;
        }


    }
}