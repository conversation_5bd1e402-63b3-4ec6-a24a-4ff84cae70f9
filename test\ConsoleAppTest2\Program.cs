﻿namespace ConsoleAppTest2
{
    using System;
    using System.Net.Sockets;
    using System.Text;
    using System.Threading.Tasks;

    class Program
    {
        static async Task Main()
        {
            try
            {
                using TcpClient client = new TcpClient();
                await client.ConnectAsync("192.168.110.33", 43916);
                NetworkStream stream = client.GetStream();

                _ = Task.Run(() => ReceiveMessages(stream));

                while (true)
                {
                    string input = Console.ReadLine()?.Trim();
                    if (string.IsNullOrEmpty(input)) continue;

                    if (input.Equals("exit", StringComparison.OrdinalIgnoreCase))
                        break;

                    byte[] data = Encoding.UTF8.GetBytes(input + "\n"); // 添加换行符
                    await stream.WriteAsync(data, 0, data.Length);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
        }

        static async Task ReceiveMessages(NetworkStream stream)
        {
            byte[] buffer = new byte[1024];
            try
            {
                while (true)
                {
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                    if (bytesRead == 0) break;
                    Console.Write(Encoding.UTF8.GetString(buffer, 0, bytesRead));
                }
            }
            catch { }
            Console.WriteLine("\n连接已关闭");
            Environment.Exit(0);
        }
    }


}
