﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class User
    {
        public User()
        {
            CatalogCreatedBies = new HashSet<Catalog>();
            CatalogModifiedBies = new HashSet<Catalog>();
            PolicyUserRoles = new HashSet<PolicyUserRole>();
            Schedules = new HashSet<Schedule>();
            SubscriptionModifiedBies = new HashSet<Subscription>();
            SubscriptionOwners = new HashSet<Subscription>();
        }

        public Guid UserId { get; set; }
        public byte[]? Sid { get; set; }
        public int UserType { get; set; }
        public int AuthType { get; set; }
        public string? UserName { get; set; }

        public virtual ICollection<Catalog> CatalogCreatedBies { get; set; }
        public virtual ICollection<Catalog> CatalogModifiedBies { get; set; }
        public virtual ICollection<PolicyUserRole> PolicyUserRoles { get; set; }
        public virtual ICollection<Schedule> Schedules { get; set; }
        public virtual ICollection<Subscription> SubscriptionModifiedBies { get; set; }
        public virtual ICollection<Subscription> SubscriptionOwners { get; set; }
    }
}
