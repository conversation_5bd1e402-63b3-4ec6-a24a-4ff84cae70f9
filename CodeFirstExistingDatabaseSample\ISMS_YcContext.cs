﻿using System;
using System.Collections.Generic;
using CodeFirstExistingDatabaseSample.ISMS_Yc.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace CodeFirstExistingDatabaseSample
{
    public partial class ISMS_YcContext : DbContext
    {
        public ISMS_YcContext()
        {
        }

        public ISMS_YcContext(DbContextOptions<ISMS_YcContext> options)
            : base(options)
        {
        }

        public virtual DbSet<ImYcdata20001031> ImYcdata20001031s { get; set; } = null!;
        public virtual DbSet<ImYcdata20240722> ImYcdata20240722s { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_Yc;Trusted_Connection=False;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ImYcdata20001031>(entity =>
            {
                entity.HasKey(e => new { e.DataId, e.OccTime });

                entity.ToTable("im_YCData20001031");

                entity.HasIndex(e => e.OccTime, "Ind_im_YCData20001031");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.OccTime).HasColumnType("datetime");

                entity.Property(e => e.SeqNo).ValueGeneratedOnAdd();

                entity.Property(e => e.TimeSrc)
                    .HasMaxLength(8)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImYcdata20240722>(entity =>
            {
                entity.HasKey(e => new { e.DataId, e.OccTime });

                entity.ToTable("im_YCData20240722");

                entity.HasIndex(e => e.OccTime, "Ind_im_YCData20240722");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.OccTime).HasColumnType("datetime");

                entity.Property(e => e.SeqNo).ValueGeneratedOnAdd();

                entity.Property(e => e.TimeSrc)
                    .HasMaxLength(8)
                    .IsUnicode(false);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
