﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImPuCtgy
    {
        public ImPuCtgy()
        {
            ImAnalogdata20102 = new HashSet<ImAnalogdata20102>();
            ImAsdu140Fltacttype = new HashSet<ImAsdu140Fltacttype>();
            ImBreakernoenum = new HashSet<ImBreakernoenum>();
            ImDevicedzenumpu = new HashSet<ImDevicedzenumpu>();
            ImEventtype = new HashSet<ImEventtype>();
            ImFaultacttype20102 = new HashSet<ImFaultacttype20102>();
            ImProtectdevice = new HashSet<ImProtectdevice>();
            ImPuWavechl = new HashSet<ImPuWavechl>();
            ImPuctgyFltrptitem = new HashSet<ImPuctgyFltrptitem>();
        }

        public int Puctgycode { get; set; }
        public string Puctgyname { get; set; }
        public string Manufacturer { get; set; }
        public string Devctgy { get; set; }
        public string Generation { get; set; }
        public int Dzzonecount { get; set; }
        public string Model { get; set; }
        public int Canswdzzone { get; set; }
        public int Support12yc { get; set; }
        public int Supportversion { get; set; }
        public int Supportdkjl { get; set; }
        public int? Startofdkjl { get; set; }
        public int? Endofdkjl { get; set; }
        public string Analogparsemode { get; set; }
        public string Eventparsemode { get; set; }
        public int Supportguzhangbg { get; set; }
        public int Supportshijianbg { get; set; }
        public int Supportzijianbg { get; set; }
        public int Supportlubowj { get; set; }
        public int Supportdz { get; set; }
        public int Dzreadonly { get; set; }
        public int Supportfhlubo { get; set; }
        public string Iscrcc { get; set; }

        public virtual ImDevctgy DevctgyNavigation { get; set; }
        public virtual ImManufacturer ManufacturerNavigation { get; set; }
        public virtual ImProtectdeviceTmp ImProtectdeviceTmp { get; set; }
        public virtual ICollection<ImAnalogdata20102> ImAnalogdata20102 { get; set; }
        public virtual ICollection<ImAsdu140Fltacttype> ImAsdu140Fltacttype { get; set; }
        public virtual ICollection<ImBreakernoenum> ImBreakernoenum { get; set; }
        public virtual ICollection<ImDevicedzenumpu> ImDevicedzenumpu { get; set; }
        public virtual ICollection<ImEventtype> ImEventtype { get; set; }
        public virtual ICollection<ImFaultacttype20102> ImFaultacttype20102 { get; set; }
        public virtual ICollection<ImProtectdevice> ImProtectdevice { get; set; }
        public virtual ICollection<ImPuWavechl> ImPuWavechl { get; set; }
        public virtual ICollection<ImPuctgyFltrptitem> ImPuctgyFltrptitem { get; set; }
    }
}
