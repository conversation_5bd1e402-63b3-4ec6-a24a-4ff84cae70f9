﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbYonghu
    {
        public TbYonghu()
        {
            TbQuanxian = new HashSet<TbQuanxian>();
            TbYonghuguanxiYonghuNavigation = new HashSet<TbYonghuguanxi>();
            TbYonghuguanxiYonghuzuNavigation = new HashSet<TbYonghuguanxi>();
        }

        public string Yonghuming { get; set; }
        public string Leixing { get; set; }
        public string Quanming { get; set; }
        public string <PERSON><PERSON><PERSON>oshu { get; set; }
        public DateTime Chuangjiansj { get; set; }
        public string Kouling { get; set; }
        public string Zhuangtai { get; set; }
        public string Zhuye { get; set; }
        public string Bumenid { get; set; }
        public int Errcount { get; set; }
        public DateTime? Lasterrtime { get; set; }
        public DateTime? Lastchangpasstime { get; set; }

        public virtual ICollection<TbQuanxian> TbQuanxian { get; set; }
        public virtual ICollection<TbYonghuguanxi> TbYonghuguanxiYonghuNavigation { get; set; }
        public virtual ICollection<TbYonghuguanxi> TbYonghuguanxiYonghuzuNavigation { get; set; }
    }
}
