﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImShebeiProtdevice
    {
        public string Recid { get; set; }
        public string Shebeiid { get; set; }
        public string Deviceid { get; set; }

        public virtual ImProtectdevice Device { get; set; }
        public virtual ImShebei Shebei { get; set; }
    }
}
