﻿using System;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis
{
    public delegate void StateEventDelegate(StateEventArgs stateEventArgs);

    public delegate void LogEventDelegate(LogEventArgs stateEventArgs);

    public delegate void MsgEventDelegate(Recoder stateEventArgs);

    public static class MonitoringEventBus
    {
        public static event StateEventDelegate StateEvent;

        public static event LogEventDelegate LogEvent;

        public static event MsgEventDelegate MsgEvent;


        public static void LogHandler(string content, string type)
        {
            LogEvent?.Invoke(new LogEventArgs(type, content, DateTime.Now));
            Log4Helper.Info(typeof(MonitoringEventBus), content);
        }
        public static void ImpletementStateEvent(StateEventArgs value)
        {
            StateEvent?.Invoke(value);
        }
        public static void ImpletementMsgEvent(Recoder value)
        {
            MsgEvent?.Invoke(value);
        }
    }

    public class StateEventArgs
    {
        public bool IsStartSucceed { get; set; }

        public StateEventArgs(bool value)
        {
            IsStartSucceed = value;
        }
    }

    public class LogEventArgs
    {
        /// <summary>
        /// 日志种类
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// 日志内容
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime OccurTime { get; set; }
        public LogEventArgs(string type, string content, DateTime occurTime)
        {
            Type = type;
            Content = content;
            OccurTime = occurTime;
        }
    }
}