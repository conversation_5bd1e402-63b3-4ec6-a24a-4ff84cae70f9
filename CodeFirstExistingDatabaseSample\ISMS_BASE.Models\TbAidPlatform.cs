﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbAidPlatform
    {
        public TbAidPlatform()
        {
            TbAidVideoDevs = new HashSet<TbAidVideoDev>();
        }

        public string PlatId { get; set; } = null!;
        public string PlatType { get; set; } = null!;
        public string PlatName { get; set; } = null!;
        public string PlatImpl { get; set; } = null!;
        public string UseState { get; set; } = null!;
        public string? Ip { get; set; }
        public int? Port { get; set; }
        public string? UserName { get; set; }
        public string? PassEnc { get; set; }
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public string? Comment { get; set; }

        public virtual ICollection<TbAidVideoDev> TbAidVideoDevs { get; set; }
    }
}
