﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class Catalog
    {
        public Catalog()
        {
            DataSetItems = new HashSet<DataSet>();
            DataSetLinks = new HashSet<DataSet>();
            DataSources = new HashSet<DataSource>();
            InverseLinkSource = new HashSet<Catalog>();
            InverseParent = new HashSet<Catalog>();
            ModelDrillModels = new HashSet<ModelDrill>();
            ModelDrillReports = new HashSet<ModelDrill>();
            Subscriptions = new HashSet<Subscription>();
        }

        public Guid ItemId { get; set; }
        public string Path { get; set; } = null!;
        public string Name { get; set; } = null!;
        public Guid? ParentId { get; set; }
        public int Type { get; set; }
        public byte[]? Content { get; set; }
        public Guid? Intermediate { get; set; }
        public Guid? SnapshotDataId { get; set; }
        public Guid? LinkSourceId { get; set; }
        public string? Property { get; set; }
        public string? Description { get; set; }
        public bool? Hidden { get; set; }
        public Guid CreatedById { get; set; }
        public DateTime CreationDate { get; set; }
        public Guid ModifiedById { get; set; }
        public DateTime ModifiedDate { get; set; }
        public string? MimeType { get; set; }
        public int? SnapshotLimit { get; set; }
        public string? Parameter { get; set; }
        public Guid PolicyId { get; set; }
        public bool PolicyRoot { get; set; }
        public int ExecutionFlag { get; set; }
        public DateTime? ExecutionTime { get; set; }
        public string? SubType { get; set; }
        public Guid? ComponentId { get; set; }

        public virtual User CreatedBy { get; set; } = null!;
        public virtual Catalog? LinkSource { get; set; }
        public virtual User ModifiedBy { get; set; } = null!;
        public virtual Catalog? Parent { get; set; }
        public virtual Policy Policy { get; set; } = null!;
        public virtual CachePolicy? CachePolicy { get; set; }
        public virtual ICollection<DataSet> DataSetItems { get; set; }
        public virtual ICollection<DataSet> DataSetLinks { get; set; }
        public virtual ICollection<DataSource> DataSources { get; set; }
        public virtual ICollection<Catalog> InverseLinkSource { get; set; }
        public virtual ICollection<Catalog> InverseParent { get; set; }
        public virtual ICollection<ModelDrill> ModelDrillModels { get; set; }
        public virtual ICollection<ModelDrill> ModelDrillReports { get; set; }
        public virtual ICollection<Subscription> Subscriptions { get; set; }
    }
}
