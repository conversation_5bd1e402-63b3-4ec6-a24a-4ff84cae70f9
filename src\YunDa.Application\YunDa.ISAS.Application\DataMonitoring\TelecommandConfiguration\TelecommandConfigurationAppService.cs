﻿using Abp.Auditing;
using Abp.AutoMapper;
using Abp.Collections.Extensions;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
//using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Session;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.Account;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto.SearchCondition;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;

namespace YunDa.ISAS.Application.DataMonitoring
{
    /// <summary>
    /// 遥控配置管理服务
    /// </summary>
    [Description("遥控配置管理服务")]
    public class TelecommandConfigurationAppService : ISASAppServiceBase, ITelecommandConfigurationAppService
    {
        private IRepository<TelecommandConfiguration, Guid> _telecommandConfigurationResitory = default;
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoResitory;

        private LoginUserOutput _currentUser = null;

        public TelecommandConfigurationAppService(IRepository<EquipmentInfo, Guid> equipmentInfoResitory,
            IRepository<TelecommandConfiguration, Guid> telecommandConfigurationResitory,
            ISessionAppService sessionAppService) : base(sessionAppService)
        {
            _telecommandConfigurationResitory = telecommandConfigurationResitory;
            _equipmentInfoResitory = equipmentInfoResitory;
            _currentUser = sessionAppService.GetCurrentLoginInformations().User;
        }

        #region 增/改

        /// <summary>
        /// 遥控数据增加或修改
        /// </summary>
        /// <param name="input">遥控数据体</param>
        /// <returns></returns>
        [HttpPost, Audited, Description("遥控数据增加或修改")]
        public async Task<RequestResult<TelecommandConfigurationOutput>> CreateOrUpdateAsync(EditTelecommandConfigurationInput input)
        {
            if (input == null) return null;
            if (_currentUser == null)
                _currentUser = base.GetCurrentUser();
            return input.Id != null ? await this.UpdateAsync(input).ConfigureAwait(false) : await this.CreateAsync(input).ConfigureAwait(false);
        }
        /// <summary>
        /// 增加遥控数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<RequestResult<TelecommandConfigurationOutput>> CreateAsync(EditTelecommandConfigurationInput input)
        {
            var rst = new RequestResult<TelecommandConfigurationOutput>();
            try
            {
                input.CreationTime = DateTime.Now;
                input.CreatorUserId = _currentUser.Id;
                var data = ObjectMapper.Map<TelecommandConfiguration>(input);
                data = await _telecommandConfigurationResitory.InsertAsync(data).ConfigureAwait(false);
                rst.ResultData = ObjectMapper.Map<TelecommandConfigurationOutput>(data);
                rst.Flag = true;
                var equipment = await _equipmentInfoResitory.GetAsync((Guid)data.EquipmentInfoId);
                if (equipment != null)
                    equipment.IsRemoteControl = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.ToString();
                Log4Helper.Error(this.GetType(), "增加遥控数据", ex);
            }
            return rst;
        }
        /// <summary>
        /// 修改遥控数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<RequestResult<TelecommandConfigurationOutput>> UpdateAsync(EditTelecommandConfigurationInput input)
        {
            var rst = new RequestResult<TelecommandConfigurationOutput>();
            try
            {
                var data = await _telecommandConfigurationResitory.FirstOrDefaultAsync(u => u.Id == input.Id).ConfigureAwait(false);
                input.CreationTime = data.CreationTime;
                input.CreatorUserId = data.CreatorUserId;
                input.LastModificationTime = DateTime.Now;
                input.CreatorUserId = _currentUser.Id;
                ObjectMapper.Map(input, data);
                //ObjectMapper.Map(input, data);
                var equipment = await _equipmentInfoResitory.GetAsync((Guid)data.EquipmentInfoId);
                if (equipment != null)
                    equipment.IsRemoteControl = true;
                rst.ResultData = ObjectMapper.Map<TelecommandConfigurationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.ToString();
                Log4Helper.Error(this.GetType(), "修改遥控数据", ex);
            }
            return rst;
        }

        #endregion 增/改

        #region 删除

        /// <summary>
        /// 删除单个遥控数据
        /// </summary>
        /// <param name="id">遥控id</param>
        /// <returns></returns>
        [HttpGet, Audited, Description("删除单个遥控数据")]
        public async Task<RequestEasyResult> DeleteByIdAsync(Guid id)
        {
            RequestEasyResult rst = new RequestEasyResult();
            try
            {
                Guid? equipmentInfoId = _telecommandConfigurationResitory.GetAll().Where(t => id == t.Id).Select(t => t.EquipmentInfoId).FirstOrDefault();
                await _telecommandConfigurationResitory.DeleteAsync(u => u.Id == id).ConfigureAwait(false);
                if (equipmentInfoId != null)
                {
                    EquipmentInfo eInfo = _equipmentInfoResitory.GetAll().Where(e => equipmentInfoId == e.Id).FirstOrDefault();
                    eInfo.IsRemoteControl = _telecommandConfigurationResitory.GetAll().Where(t => t.EquipmentInfoId == eInfo.Id).Any();
                }
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.ToString();
                Log4Helper.Error(this.GetType(), "删除单个遥控数据", ex);
            }
            return rst;
        }

        /// <summary>
        /// 删除多个遥控数据
        /// </summary>
        /// <param name="ids">遥控id集合</param>
        /// <returns></returns>
        [HttpPost, Audited, Description("删除多个遥控数据")]
        public async Task<RequestEasyResult> DeleteByIdsAsync(List<Guid> ids)
        {
            RequestEasyResult rst = new RequestEasyResult();
            try
            {
                // 找到所有遥控数据并且按设备id分组 在删除一个遥控项时
                var telecommandAll = _telecommandConfigurationResitory.GetAll().ToList();
                var telecommand = telecommandAll.Where(t => ids.Contains(t.Id)).ToList();

                var telecomandAllkeyvalues = telecommandAll.GroupBy(t => t.EquipmentInfoId);
                var telecomandkeyvalues = telecommand.GroupBy(t => t.EquipmentInfoId);

                foreach (var itemAll in telecomandAllkeyvalues)
                {
                    foreach (var item in telecomandkeyvalues)
                    {
                        if (itemAll.Key == item.Key)
                        {
                            if (itemAll.Count() == item.Count())
                            {
                                var eq = _equipmentInfoResitory.Get((Guid)item.Key);
                                if (eq != null)
                                {
                                    eq.IsRemoteControl = false;
                                    _equipmentInfoResitory.Update(eq);
                                }
                            }
                        }
                    }
                }
                await _telecommandConfigurationResitory.DeleteAsync(u => ids.Contains(u.Id));
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.ToString();
                Log4Helper.Error(this.GetType(), "删除多个遥控数据", ex);
            }
            return rst;
        }

        #endregion 删除

        #region 查询

        /// <summary>
        /// 查询遥控数据
        /// </summary>
        /// <param name="searchCondition">查询条件</param>
        /// <returns></returns>
        [HttpPost, Description("查询遥控数据")]
        public RequestPageResult<TelecommandConfigurationOutput> FindDatas(PageSearchCondition<TelecommandConfigurationSearchConditionInput> searchCondition)
        {
            var rst = new RequestPageResult<TelecommandConfigurationOutput>();
            try
            {
                var datas = _telecommandConfigurationResitory
                    .GetAllIncluding(t => t.EquipmentInfo, t => t.EquipmentType, t => t.RelatedTelesignalisation)
                    .Where(t => t.EquipmentType.IsActive)
                    ;
                datas = datas
                    
                    .WhereIf(searchCondition.SearchCondition.TransformerSubstationId.HasValue,
                        t => t.TransformerSubstationId == searchCondition.SearchCondition.TransformerSubstationId)
                    .WhereIf(searchCondition.SearchCondition.EquipmentTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.SearchCondition.EquipmentTypeId)
                    .WhereIf(searchCondition.SearchCondition.DataSourceCategory.HasValue,
                    t=>t.DataSourceCategory == searchCondition.SearchCondition.DataSourceCategory)
                    .WhereIf(searchCondition.SearchCondition.Id.HasValue,
                        t => t.Id == searchCondition.SearchCondition.Id).WhereIf(
                        searchCondition.SearchCondition.EquipmentInfoId.HasValue,
                        t => t.EquipmentInfoId == searchCondition.SearchCondition.EquipmentInfoId);
                var rstDatas = datas.ToList().AsQueryable();
                if (!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name))
                {
                    rstDatas = rstDatas.Where(t => t.Name.Contains(searchCondition.SearchCondition.Name));
                    //.Where(item => item.Name.ToArray().Intersect(searchCondition.SearchCondition.Name.ToArray()).Count() == searchCondition.SearchCondition.Name.ToArray().Distinct().Count()).AsQueryable();
                }

                rstDatas = rstDatas.OrderBy(t => t.EquipmentType.SeqNo).ThenBy(t => t.EquipmentInfo.SeqNo).ThenBy(t => t.DispatcherAddress);
                rst.TotalCount = rstDatas.Count();
                int skipCount = searchCondition.PageIndex <= 0 ? -1 : ((searchCondition.PageIndex - 1) * searchCondition.PageSize);
                if (skipCount != -1)
                    rstDatas = rstDatas.PageBy(skipCount, searchCondition.PageSize);
                rst.ResultData = ObjectMapper.Map<List<TelecommandConfigurationOutput>>(rstDatas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "查询遥控数据", ex);
            }
            return rst;
        }

        /// <summary>
        /// 根据查询条件查询所有遥控数据
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns>遥控数据list</returns>
        [HttpPost, AllowAnonymous]
        public RequestResult<List<TelecommandConfigurationOutput>> FindDatasNoPageList(TelecommandConfigurationSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<TelecommandConfigurationOutput>>();
            try
            {
                var datas = _telecommandConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo, t => t.EquipmentType, t => t.RelatedTelesignalisation);
                datas = datas
                    .Where(t => t.IsActive)
                    .WhereIf(searchCondition.DispatcherAddress.HasValue,
                    t => t.DispatcherAddress == searchCondition.DispatcherAddress)
                    .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.Name),
                        t => t.Name.Contains(searchCondition.Name))
                    .WhereIf(searchCondition.TransformerSubstationId.HasValue,
                        t => t.TransformerSubstationId == searchCondition.TransformerSubstationId)
                    .WhereIf(searchCondition.EquipmentTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.EquipmentTypeId)
                    .WhereIf(searchCondition.Id.HasValue,
                        t => t.Id == searchCondition.Id).WhereIf(
                        searchCondition.EquipmentInfoId.HasValue,
                        t => t.EquipmentInfoId == searchCondition.EquipmentInfoId);
                datas = datas.OrderBy(t => t.SeqNo);
                var dic = datas.AsEnumerable().GroupBy(t => t.EquipmentInfoId);
                var list = new List<TelecommandConfiguration>();

                foreach (var item in dic)
                {
                    var itemSort = item.OrderBy(t => t.InfoAddress);
                    list.AddRange(itemSort);
                }
                datas = list.AsQueryable();
                rst.TotalCount = datas.Count();

                rst.ResultData = ObjectMapper.Map<List<TelecommandConfigurationOutput>>(datas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "根据查询条件查询所有遥控数据", ex);
            }
            return rst;
        }

        /// <summary>
        /// 查询遥控数据选择列表
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [HttpPost]
        [ShowApi]
        [AllowAnonymous]
        public RequestResult<List<SelectModelOutput>> FindDatasForSelect(TelecommandConfigurationSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<SelectModelOutput>>();
            try
            {
                var datas = _telecommandConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo, t => t.EquipmentType);
                datas = datas
                    .Where(t => t.IsActive)
                    .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.Name),
                        t => t.Name.Contains(searchCondition.Name))
                    .WhereIf(searchCondition.TransformerSubstationId.HasValue,
                        t => t.TransformerSubstationId == searchCondition.TransformerSubstationId)
                    .WhereIf(searchCondition.EquipmentTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.EquipmentTypeId)
                    .WhereIf(searchCondition.Id.HasValue,
                        t => t.Id == searchCondition.Id)
                    .WhereIf(searchCondition.EquipmentInfoId.HasValue,
                        t => t.EquipmentInfoId == searchCondition.EquipmentInfoId);

                datas = datas.OrderBy(t => t.SeqNo);
                var dic = datas.AsEnumerable().GroupBy(t => t.EquipmentInfoId);
                var list = new List<TelecommandConfiguration>();

                foreach (var item in dic)
                {
                    var itemSort = item.OrderBy(t => t.InfoAddress);
                    list.AddRange(itemSort);
                }
                datas = list.AsQueryable();
                rst.ResultData = datas.Select(m => new SelectModelOutput
                {
                    Key = m.Id,
                    Text = m.Name,
                    Value = m.Id.ToString().ToLower()
                }).ToList();

                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "查询遥控数据选择列表", ex);
            }
            return rst;
        }

        /// <summary>
        /// 获取遥控类型
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public RequestResult<List<SelectModelOutput>> GetTelecommandTypes()
        {
            var rst = new RequestResult<List<SelectModelOutput>>();
            try
            {
                //YunDa.ISAS.Application.Core
                var data = base.GetEnumTypes<RemoteTypeEnum>();
                rst.ResultData = data;// ObjectMapper.Map<List<SelectModelOutput>>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "获取遥控类型", ex);
            }
            return rst;
        }

        /// <summary>
        /// 根据设备id查询遥控命令
        /// </summary>
        /// <param name="id">设备Id</param>
        /// <returns></returns>
        [HttpGet, Description("根据设备id查询遥控命令")]
        [ShowApi]
        public RequestResult<List<TelecommandConfigurationOutput>> FindTelecommandByEquipmentInfoId(Guid id)
        {
            RequestResult<List<TelecommandConfigurationOutput>> rst = new RequestResult<List<TelecommandConfigurationOutput>>();
            rst.Flag = false;
            if (id == default) return rst;
            try
            {
                var datas = _telecommandConfigurationResitory.GetAllIncluding(command => command.RelatedTelesignalisation).Where(tc => tc.IsActive && tc.EquipmentInfoId == id);
                rst.ResultData = ObjectMapper.Map<List<TelecommandConfigurationOutput>>(datas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "根据设备id查询遥控命令", ex);
            }
            return rst;
        }
        /// <summary>
        /// 获取区域遥控信息数据
        /// </summary>
        /// <param name="stationId"></param>
        [HttpGet]
        [ShowApi]
        [AllowAnonymous]
        public RequestResult<List<AreaTeleCommandPorperty>> FindAreaTeleCommandDatas(Guid stationId)
        {
            RequestResult<List<AreaTeleCommandPorperty>> rst = new RequestResult<List<AreaTeleCommandPorperty>>();
            try
            {
                if (stationId == default)
                {
                    rst.Message = "id为空";
                    return rst;
                }
                var telecomandRepo = _telecommandConfigurationResitory
                    .GetAllIncluding(t => t.EquipmentInfo, t => t.EquipmentInfo.EquipmentType)
                    .Where(t => t.IsActive)
                    .Where(t => t.TransformerSubstationId == stationId)
                    .Where(t => t.EquipmentInfo != null)
                    .Where(t => t.EquipmentType != null)
                    .ToList();
                var datas = telecomandRepo
                    .Where(t => t.EquipmentType.Name.Contains("灯")|| t.EquipmentType.Name.Contains("照明"))
                    .Where(t =>!string.IsNullOrWhiteSpace(t.EquipmentInfo.InstallationArea))
                    .GroupBy(t => t.EquipmentInfo.InstallationArea)
                    .Select(t => new AreaTeleCommandPorperty
                    {
                        InstallationArea = t.Key,
                        TelecommandConfigurations = t.Select(x =>
                        new TelecommandConfigurationSimpleOutput
                        {
                            CPUSector = x.CPUSector,
                            NoContent = x.NoContent,
                            UnsurenessContent = x.UnsurenessContent,
                            YesContent = x.YesContent,
                            DispatcherAddress = x.DispatcherAddress,
                            Id = x.Id,
                            InfoDeviceAddress = x.InfoDeviceAddress,
                            Name = x.Name,
                            RemoteType = x.RemoteType,
                        }
                        ).ToList(),
                    });
                rst.ResultData = datas.ToList();
                rst.TotalCount = rst.ResultData.Count;
                rst.Flag = true;
            }
            catch (Exception ex)
            {

                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "获取区域遥控信息数据", ex);
            }
            return rst;
        }
        #endregion 查询
    }
}