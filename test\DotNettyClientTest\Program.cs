﻿using System;
using System.Threading.Tasks;
using System.Net.WebSockets;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using System.Net;

namespace DotNettyClientTest
{
    //class TcpSocketClient : BaseTcpSocketClient<ITcpSocketClient, byte[]>, ITcpSocketClient
    //{
    //    public TcpSocketClient(string ip, int port, TcpSocketCientEvent<ITcpSocketClient, byte[]> clientEvent)
    //        : base(ip, port, clientEvent)
    //    {
    //    }

    //    public override void OnChannelReceive(IChannelHandlerContext ctx, object msg)
    //    {
    //        PackException(() =>
    //        {
    //            var bytes = (msg as IByteBuffer).ToArray();
    //            _clientEvent.OnRecieve?.Invoke(this, bytes);
    //        });
    //    }

    //    public async Task Send(byte[] bytes)
    //    {
    //        try
    //        {
    //            await _channel.WriteAndFlushAsync(Unpooled.WrappedBuffer(bytes));
    //            await Task.Run(() =>
    //            {
    //                _clientEvent.OnSend?.Invoke(this, bytes);
    //            });
    //        }
    //        catch (Exception ex)
    //        {
    //            _clientEvent.OnException?.Invoke(ex);
    //        }
    //    }

    //    public async Task Send(string msgStr)
    //    {
    //        await Send(Encoding.UTF8.GetBytes(msgStr));
    //    }
    //}
    internal class Program
    {
        public const ushort Mark = 0xEB90;
        public static byte[] ToBytes(Int64 Serial,string Content)
        {

            byte[] startBytes = BitConverter.GetBytes(Mark).Reverse().ToArray();
            byte[] serialBytes = BitConverter.GetBytes(Serial).ToArray();
            byte[] contentBytes = Encoding.UTF8.GetBytes(Content);
            var ContentLength = contentBytes.Length;
            byte[] contentLengthBytes = BitConverter.GetBytes(ContentLength).ToArray();

            byte[] endBytes = BitConverter.GetBytes(Mark).Reverse().ToArray();
            int bLength = startBytes.Length + serialBytes.Length + contentLengthBytes.Length + contentBytes.Length + endBytes.Length;
            byte[] bytes = new byte[bLength];
            startBytes.CopyTo(bytes, 0);
            serialBytes.CopyTo(bytes, startBytes.Length);
            contentLengthBytes.CopyTo(bytes, startBytes.Length + serialBytes.Length);
            contentBytes.CopyTo(bytes, startBytes.Length + serialBytes.Length + contentLengthBytes.Length);
            endBytes.CopyTo(bytes, startBytes.Length + serialBytes.Length + contentLengthBytes.Length + contentBytes.Length);
            return bytes;
        }
        static async Task Main(string[] args)
        {
            const int size = 406;
            var  client = new TcpClient();
            client.Connect(IPAddress.Parse("127.0.0.1"), 10012);
            NetworkStream strem = client.GetStream();
            //string str = this.textBox1.Text.Trim();
            string str = Console.ReadLine();
            byte[] b = ToBytes(10,str);
            strem.Write(b, 0, b.Length);
            strem.Close();
            client.Close();
        }
    }
}