﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAlertlevel
    {
        public ImAlertlevel()
        {
            ImAlerttype = new HashSet<ImAlerttype>();
            ImDeviceyx = new HashSet<ImDeviceyx>();
            ImDeviceyxTmp = new HashSet<ImDeviceyxTmp>();
            ImEventtype = new HashSet<ImEventtype>();
            ImEventtype2010 = new HashSet<ImEventtype2010>();
        }

        public string Alertlevelcode { get; set; }
        public string Alertlevelname { get; set; }
        public int Color { get; set; }

        public virtual ICollection<ImAlerttype> ImAlerttype { get; set; }
        public virtual ICollection<ImDeviceyx> ImDeviceyx { get; set; }
        public virtual ICollection<ImDeviceyxTmp> ImDeviceyxTmp { get; set; }
        public virtual ICollection<ImEventtype> ImEventtype { get; set; }
        public virtual ICollection<ImEventtype2010> ImEventtype2010 { get; set; }
    }
}
