using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;
using YunDa.SOMS.Entities.DataMonitoring;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    /// <summary>
    /// 能耗配置服务接口
    /// </summary>
    public interface IEnergyConsumptionConfigService
    {
        /// <summary>
        /// 获取能耗配置列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>配置列表结果</returns>
        Task<RequestPageResult<EnergyConsumptionConfig>> GetConfigs(PageSearchCondition<EnergyConfigSearchInput> input);

        /// <summary>
        /// 获取所有能耗配置信息
        /// </summary>
        /// <returns>能耗配置列表</returns>
        Task<List<EnergyConsumptionConfig>> GetAllAsync();

        /// <summary>
        /// 根据ID获取能耗配置信息
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>能耗配置</returns>
        Task<RequestResult<EnergyConsumptionConfig>> GetByIdAsync(Guid id);

        /// <summary>
        /// 创建能耗配置
        /// </summary>
        /// <param name="input">能耗配置信息</param>
        /// <returns>创建结果</returns>
        Task<RequestResult<EnergyConsumptionConfig>> CreateAsync(EnergyConsumptionConfig input);

        /// <summary>
        /// 更新能耗配置
        /// </summary>
        /// <param name="input">能耗配置信息</param>
        /// <returns>更新结果</returns>
        Task<RequestResult<EnergyConsumptionConfig>> UpdateAsync(EnergyConsumptionConfig input);

        /// <summary>
        /// 删除能耗配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        Task<RequestResult<bool>> DeleteAsync(Guid id);

        /// <summary>
        /// 批量删除能耗配置
        /// </summary>
        /// <param name="ids">配置ID列表</param>
        /// <returns>删除结果</returns>
        Task<RequestResult<bool>> DeleteByIdsAsync(List<Guid> ids);

        /// <summary>
        /// 添加或更新能耗配置
        /// </summary>
        /// <param name="input">能耗配置信息</param>
        /// <returns>操作结果</returns>
        Task<RequestResult<EnergyConsumptionConfig>> CreateOrUpdateAsync(EnergyConsumptionConfig input);

        /// <summary>
        /// 获取能耗配置下拉选择列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>配置选择列表</returns>
        Task<RequestResult<List<SelectModelOutput>>> GetConfigsForSelect(EnergyConfigSearchInput input);

        /// <summary>
        /// 根据设备ID获取相关配置
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>配置列表</returns>
        Task<RequestResult<List<EnergyConsumptionConfig>>> GetByDeviceIdAsync(Guid deviceId);
    }
}
