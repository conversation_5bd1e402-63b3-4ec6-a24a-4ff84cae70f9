﻿对现有数据库使用 Code First
https://learn.microsoft.com/zh-cn/ef/ef6/modeling/code-first/workflows/existing-database
SQL Server
添加ADO.Net实体框架提供“项目的目标框架不包含实体框架运行时程序集”
https://cloud.tencent.com/developer/ask/sof/106576813

命令：
Scaffold-DbContext "Server=192.168.110.161;User ID=**;Password=**;Database=ReportServerTempDB;Trusted_Connection=False;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir ReportServerTempDB.Models
Scaffold-DbContext "Server=192.168.110.161;User ID=**;Password=**;Database=ReportServer;Trusted_Connection=False;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir ReportServer.Models
Scaffold-DbContext "Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_Yc;Trusted_Connection=False;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir ISMS_Yc.Models
Scaffold-DbContext "Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_Data;Trusted_Connection=False;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir ISMS_Data.Models
Scaffold-DbContext "Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_BASE;Trusted_Connection=False;" Microsoft.EntityFrameworkCore.SqlServer -OutputDir ISMS_BASE.Models


