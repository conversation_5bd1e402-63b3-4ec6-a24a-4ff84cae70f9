﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImFaultreportitem
    {
        public ImFaultreportitem()
        {
            ImPuctgyFltrptitem = new HashSet<ImPuctgyFltrptitem>();
        }

        public string Itemname { get; set; }
        public int Seqno { get; set; }

        public virtual ICollection<ImPuctgyFltrptitem> ImPuctgyFltrptitem { get; set; }
    }
}
