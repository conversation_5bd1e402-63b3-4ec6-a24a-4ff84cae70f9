﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_Data.Models
{
    public partial class ImAlert200010
    {
        public string Id { get; set; } = null!;
        public string? StatCode { get; set; }
        public string? DeviceId { get; set; }
        public string Alerter { get; set; } = null!;
        public string AlertType { get; set; } = null!;
        public string AlertLevel { get; set; } = null!;
        public string AlertTime { get; set; } = null!;
        public string TimeSrc { get; set; } = null!;
        public string Content { get; set; } = null!;
        public string State { get; set; } = null!;
        public DateTime RecTime { get; set; }
        public string AlertDomain { get; set; } = null!;
        public string? Reserved { get; set; }
        public string? LnkType { get; set; }
    }
}
