﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVDeviceYk
    {
        public string Id { get; set; } = null!;
        public string Ykname { get; set; } = null!;
        public string? RelatedYxId { get; set; }
        public string YkType { get; set; } = null!;
        public string? SwOnStr { get; set; }
        public string? SwOffStr { get; set; }
        public string? SwUncertStr { get; set; }
        public string? PreYxId { get; set; }
        public int? PreState4Yk { get; set; }
        public string LockMode { get; set; } = null!;
        public string IsResetCmd { get; set; } = null!;
        public string? PreYxIdOff { get; set; }
        public int? PreState4YkOff { get; set; }
        public string LockModeOff { get; set; } = null!;
        public string? RelatedYxName { get; set; }
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
    }
}
