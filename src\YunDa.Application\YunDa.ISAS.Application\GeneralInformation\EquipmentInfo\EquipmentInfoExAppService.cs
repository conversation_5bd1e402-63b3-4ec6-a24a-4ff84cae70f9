﻿using Abp.Application.Services;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto.SearchCondition;
using YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Redis.Entities.SortDefines;
using YunDa.ISAS.Redis.Repositories;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentTypeDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TeleDataCommon;
using YunDa.ISAS.Entities.DataMonitoring;
//using System.Linq.Dynamic.Core;
using Abp.Collections.Extensions;
using Abp.Timing;
using Abp.Linq.Extensions;
using YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentDataCategoryDto;
using NPOI.SS.Formula.Functions;
using YunDa.ISAS.Entities.System;
using AutoMapper;
using YunDa.SOMS.DataTransferObject.Iec104;
using Abp.Auditing;
using Abp.Domain.Uow;
using Microsoft.EntityFrameworkCore;
using YunDa.SOMS.Entities.GeneralInformation;
using YunDa.SOMS.BASE.Entities.Models;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace YunDa.ISAS.Application.GeneralInformation
{
    /// <summary>
    ///  设备信息-扩展类
    /// </summary>
    [Description("设备信息管理服务-扩展类")]
    [DisableAuditing]
    public class EquipmentInfoExAppService : ApplicationService, IEquipmentInfoExAppService
    {
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoRepository;
        private readonly IRepository<ProtectionDeviceInfo, Guid> _protectionDeviceInfoRepository;
        private readonly IRepository<BoardCardInfo, Guid> _boardCardInfoRepository;

        private readonly IRepository<EquipmentType, Guid> _equipmentTypeRepository;
        private readonly IRedisRepository<SearchRecord, string> _equipemntSearchRecord;
        private readonly EquipmentTypeExAppService _equipmentTypeExAppService;
        private readonly EquipmentDataCategoryAppService _equipmentDataCategoryAppService;
        private readonly IRepository<TelemeteringConfiguration, Guid> _telemeteringConfigurationResitory;
        private readonly IRepository<TelesignalisationConfiguration, Guid> _telesignalisationConfigurationResitory;
        private readonly IRepository<EquipmentLinkTeledata, Guid> _equipmentLinkTeledataReponsitory;
        private readonly IRepository<SysFunction, Guid> _sysFunctionRepository;
        private readonly IRepository<ImProtectDevice, string> _imProtectDeviceRepository;
        /// <summary>
        /// 遥测数据实时库
        /// </summary>
        public IRedisRepository<TelemeteringModel, string> _telemeteringModelListRedis;
        /// <summary>
        /// 遥信数据实时库
        /// </summary>
        public IRedisRepository<TelesignalisationModel, string> _telesignalisationModelListRedis;

        /// <summary>
        /// 遥测数据变位库
        /// </summary>
        public IRedisRepository<TelemeteringModel, string> _telemeteringModelInflectionListRedis;
        /// <summary>
        /// 遥信数据变位库
        /// </summary>
        public IRedisRepository<TelesignalisationModel, string> _telesignalisationModelInflectionListRedis;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        public EquipmentInfoExAppService(
             IRepository<EquipmentInfo, Guid> repositoryEquipmentInfo
            , IRepository<EquipmentType, Guid> repositoryEquipmentType
            , IRedisRepository<SearchRecord, string> equipemntSearchRecord
            , EquipmentTypeExAppService equipmentTypeExAppService
             , IRepository<TelemeteringConfiguration, Guid> telemeteringConfigurationResitory
            , IRepository<TelesignalisationConfiguration, Guid> telesignalisationConfigurationResitory
            ,IRepository<EquipmentLinkTeledata, Guid> equipmentLinkTeledataReponsitory
            , IRepository<SysFunction, Guid> sysFunctionRepository
            ,IRedisRepository<TelemeteringModel, string> telemeteringModelListRedis
            ,IRedisRepository<TelesignalisationModel, string> telesignalisationModelListRedis
            , IRedisRepository<TelemeteringModel, string> telemeteringModelInflectionListRedis
            ,IRedisRepository<TelesignalisationModel, string> telesignalisationModelInflectionListRedis
            , IUnitOfWorkManager unitOfWorkManager
            , IRepository<ProtectionDeviceInfo, Guid> protectionDeviceInfoRepository
            , IRepository<BoardCardInfo, Guid> boardCardInfoRepository
            , IRepository<ImProtectDevice, string> imProtectDeviceRepository
            //, EquipmentDataCategoryAppService equipmentDataCategoryAppService
            )
        {
            _equipmentInfoRepository = repositoryEquipmentInfo;
            _equipmentTypeRepository = repositoryEquipmentType;
            _equipemntSearchRecord = equipemntSearchRecord;
            _equipmentTypeExAppService = equipmentTypeExAppService;
            _telemeteringConfigurationResitory = telemeteringConfigurationResitory;
            _telesignalisationConfigurationResitory = telesignalisationConfigurationResitory;
            //_equipmentDataCategoryAppService = equipmentDataCategoryAppService;
            _equipmentLinkTeledataReponsitory = equipmentLinkTeledataReponsitory;
            _sysFunctionRepository = sysFunctionRepository;
            _telemeteringModelListRedis = telemeteringModelListRedis;
            _telesignalisationModelListRedis = telesignalisationModelListRedis;
            _telemeteringModelInflectionListRedis = telemeteringModelInflectionListRedis;
            _telesignalisationModelInflectionListRedis = telesignalisationModelInflectionListRedis;
            _unitOfWorkManager = unitOfWorkManager;
            _protectionDeviceInfoRepository = protectionDeviceInfoRepository;
            _boardCardInfoRepository = boardCardInfoRepository;
            _imProtectDeviceRepository = imProtectDeviceRepository;
        }
        /// <summary>
        /// 查询设备搜索列表
        /// </summary>
        /// <returns></returns>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public async Task<RequestResult<List<EquipmentInfoSearchRecord>>> FindEquipmentSearchRecordAsync(Guid stationId)
        {
            var rst = new RequestResult<List<EquipmentInfoSearchRecord>>();

            try
            {
                // 并行化 Redis 和数据库查询
                var equipmentTypeRepo = _equipmentTypeRepository.GetAll().ToList();
                var equipmentRepo = _equipmentInfoRepository.GetAllIncluding().ToList();
                var sysFunctionRepo = _sysFunctionRepository.GetAll();
                var matchingEquipmentTypes = equipmentTypeRepo.Where(t => t.Name.Contains("一次") && !t.EquipmentTypeId.HasValue).AsEnumerable();
                var equipmentRecod1=await GetEquipmentCategoryData(matchingEquipmentTypes, equipmentTypeRepo, equipmentRepo, "柜",true, EquipmentInfoSearchRecordEnum.Equipment);
                var equipmentRecod2 = await GetEquipmentCategoryData(matchingEquipmentTypes, equipmentTypeRepo, equipmentRepo, "柜", false, EquipmentInfoSearchRecordEnum.Equipment);
                matchingEquipmentTypes = equipmentTypeRepo.Where(t => t.Name.Contains("二次") && !t.EquipmentTypeId.HasValue).AsEnumerable();
                var equipmentRecod3 = await GetEquipmentCategoryData(matchingEquipmentTypes, equipmentTypeRepo, equipmentRepo, "柜", true, EquipmentInfoSearchRecordEnum.UnitedEquipment);
                var equipmentRecod4 = await GetEquipmentCategoryData(matchingEquipmentTypes, equipmentTypeRepo, equipmentRepo, "柜", false, EquipmentInfoSearchRecordEnum.SecondEquipment);
                List<EquipmentInfoSearchRecord> equipmentRecords = new List<EquipmentInfoSearchRecord>();
                equipmentRecords.AddRange(equipmentRecod1);
                equipmentRecords.AddRange(equipmentRecod2);
                equipmentRecords.AddRange(equipmentRecod3);
                equipmentRecords.AddRange(equipmentRecod4);
                
                //// 加载
                var maintenanceSystemClientFunctions = sysFunctionRepo
                    .Where(t => t.Type == FunctionType.MaintenanceSystemClient)
                    .OrderBy(t => t.SeqNo)
                    .Select(t => new EquipmentInfoSearchRecord
                    {
                        Description = "",
                        Id = t.Id,
                        Name = t.Name,
                        Remark = t.Remark,
                        Type = EquipmentInfoSearchRecordEnum.FunctionPage
                    })
                    .ToList();

                //// 合并结果并计算总数
                equipmentRecords.AddRange(maintenanceSystemClientFunctions);
                rst.Flag = true;
                rst.ResultData = equipmentRecords.OrderBy(t=>t.Type).ToList();
                rst.TotalCount = equipmentRecords.Count;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "查询设备搜索列表", ex);
            }

            return rst;
        }
        private async Task<IEnumerable<EquipmentInfoSearchRecord>> GetEquipmentCategoryData(IEnumerable<EquipmentType> matchingEquipmentTypes, List<EquipmentType> equipmentTypeRepo, List<EquipmentInfo> equipmentRepo,  string v1,bool IsContainFlag, EquipmentInfoSearchRecordEnum recordEnum)
        {
            // 2. 初始化结果列表
            var bottomLevelEquipmentTypes = new List<EquipmentType>();

            // 3. 遍历查找到的设备类型，递归查找最底层类型
            foreach (var equipmentType in matchingEquipmentTypes)
            {
                var bottomTypes = await GetBottomLevelEquipmentType(equipmentType, equipmentTypeRepo);
                bottomLevelEquipmentTypes.AddRange(bottomTypes);
            }

            // 简化 LINQ 查询，只选择必要字段
            var equipments = (from equipment in equipmentRepo
                              join equipmentType in bottomLevelEquipmentTypes on equipment.EquipmentTypeId equals equipmentType.Id
                              where IsContainFlag?equipment.Name.Contains(v1): !equipment.Name.Contains(v1)
                              select new EquipmentInfoSearchRecord
                              {
                                  Type = recordEnum,
                                  Id = equipment.Id,
                                  Name = equipment.Name,
                              }).AsEnumerable();
            return equipments;

        }
        // 递归查找最底层设备类型
        private async Task<List<EquipmentType>> GetBottomLevelEquipmentType(EquipmentType equipmentType,  List<EquipmentType> equipmentTypeRepo)
        {
            var equipmentTypeSelectedids = equipmentTypeRepo.Where(t => t.EquipmentTypeId == equipmentType.Id ).Select(t=>t.Id);
            var bottomLevelEquipmentTypes = equipmentTypeRepo.Where(t=> t.EquipmentTypeId.HasValue&& equipmentTypeSelectedids.Contains(t.EquipmentTypeId.Value));
            return bottomLevelEquipmentTypes.ToList();
        }
        /// <summary>
        /// 查询确定
        /// </summary>
        /// <returns></returns>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public async Task<RequestEasyResult> SeachConfirm(Guid equipmentId)
        {
            RequestEasyResult rst = new RequestEasyResult();
            try
            {
                var equipemntSearchRecord = await _equipemntSearchRecord.HashSetGetAllAsync(nameof(EquipmentInfoSearchRecord));
                if (equipemntSearchRecord.Count == 0)
                {
                    var equipmentRepo = _equipmentInfoRepository.GetAllIncluding();
                    var tempEquipemntSearchRecord = from equipment in equipmentRepo
                                                    select new SearchRecord
                                                    {
                                                        Id = equipment.Id,
                                                        Name = equipment.Name,
                                                        SearchCount = 0,
                                                        //SearchTime = DateTime.MinValue,
                                                    };
                    var equipemnt = tempEquipemntSearchRecord.FirstOrDefault(t => t.Id == equipmentId);
                    if (equipemnt != null)
                    {
                        equipemnt.SearchCount++;
                        equipemnt.SearchTime = DateTime.Now;
                    }
                    await _equipemntSearchRecord.HashSetUpdateManyAsync(nameof(EquipmentInfoSearchRecord), tempEquipemntSearchRecord.Select(t => t.Id).ToList(), tempEquipemntSearchRecord.ToList());
                }
                else
                {
                    var equipemnt = equipemntSearchRecord.FirstOrDefault(t => t.Id == equipmentId);
                    if (equipemnt != null)
                    {
                        equipemnt.SearchCount++;
                        equipemnt.SearchTime = DateTime.Now;
                        await _equipemntSearchRecord.HashSetUpdateOneAsync(nameof(EquipmentInfoSearchRecord), equipemnt.Id.ToString(), equipemnt);
                    }
                }
            }
            catch (Exception ex)
            {

                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "查询设备搜索列表", ex);
            }
            return rst;
        }
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public async Task<RequestResult<List<EquipmentInfoOutput>>> FindEquipmentInfoData(EquipmentInfoSearchExInput input)
        {
            RequestResult<List<EquipmentInfoOutput>> rst = new RequestResult<List<EquipmentInfoOutput>>();
            try
            {
                // 查找根设备类型
                var rootEquipmentType = _equipmentTypeRepository.GetAll()
                    .FirstOrDefault(et => et.Name == input.EquipmentTypeName);

                if (rootEquipmentType == null)
                {
                    return rst;
                }

                // 获取所有子节点的设备类型ID
                var equipmentTypeIds = await _equipmentTypeExAppService.GetEquipmentTypeAndDescendantsAsync(rootEquipmentType.Id);

                // 查询设备信息
                var equipmentInfos = _equipmentInfoRepository.GetAll()
                    .Where(ei => equipmentTypeIds.Contains(ei.EquipmentTypeId.Value))
                    .ToList();
                var data = ObjectMapper.Map<List<EquipmentInfoOutput>>(equipmentInfos);
                rst.ResultData = data;
                rst.TotalCount = data.Count;
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
            }
            return rst;

        }
        /// <summary>
        /// 查询设备树型结构
        /// </summary>
        /// <returns></returns>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public async Task<RequestResult<List<EquipmentTypeOutput>>> FindEquipmentTreeData(string? typeName, EquipmentTypeLevelEnum level)
        {
            RequestResult<List<EquipmentTypeOutput>> rst = new RequestResult<List<EquipmentTypeOutput>>();
            try
            {
                rst.ResultData = await _equipmentTypeExAppService.GetEquipmentTypeTreeAndEquipementAsync(typeName, level);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "查询设备树型结构", ex);
                rst.Message = ex.Message;
            }
            return rst;
        }
        /// <summary>
        /// 查询子设备
        /// </summary>
        /// <returns></returns>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public RequestResult<List<EquipmentInfoSimpleOutput>> GetSubEquipements(Guid parentEquipmentId)
        {
            RequestResult<List<EquipmentInfoSimpleOutput>> rst = new RequestResult<List<EquipmentInfoSimpleOutput>>();
            try
            {
                if (parentEquipmentId != default)
                {
                    var equipmentTypes = _equipmentTypeRepository.GetAll();
                    var data = _equipmentInfoRepository.GetAll().Where(t => t.BelongEquipmentInfoId == parentEquipmentId);
                    rst.TotalCount = data.Count();
                    var datas = ObjectMapper.Map<List<EquipmentInfoSimpleOutput>>(data);
                    foreach (var item in datas)
                    {
                        var equipementType = equipmentTypes.FirstOrDefault(t => t.Id == item.EquipmentTypeId);
                        while (equipementType != null && equipementType.EquipmentTypeLevel != EquipmentTypeLevelEnum.System)
                        {
                            equipementType = equipmentTypes.FirstOrDefault(t => t.Id == equipementType.EquipmentTypeId);
                        }
                        item.TypeName = equipementType.Name;
                    }
                    rst.ResultData = datas;
                    rst.Flag = true;
                }
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "设备信息管理服务", ex);
            }
            return rst;
        }
        public RequestResult<List<EquipmentInfoOutput>> GetEquipementsByType(Guid typeId)
        {
            RequestResult<List<EquipmentInfoOutput>> rst = new RequestResult<List<EquipmentInfoOutput>>();
            try
            {
                var data = _equipmentInfoRepository.GetAllIncluding(t => t.EquipmentType);
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "设备信息管理服务", ex);
            }
            return rst;
        }
        /// <summary>
        /// 查询设备资产信息
        /// </summary>
        /// <returns></returns>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        [DisableAuditing]
        [UnitOfWork(isTransactional: false)]

        public RequestResult<EquipementInfoAssetOutput> GetEquipmentInfoAsset(Guid equipementId)
        {
            //GetEquipmentInfoAsset
            RequestResult<EquipementInfoAssetOutput> rst = new RequestResult<EquipementInfoAssetOutput>();
            try
            {
                EquipmentInfo entity = default;
                ProtectionDeviceInfo protection = default;
                using (var unitOfWork = _unitOfWorkManager.Begin())
                {
                    entity = _equipmentInfoRepository.GetAllIncluding(t => t.ManufacturerInfo,t=>t.EquipmentType).FirstOrDefault(t => t.Id == equipementId);
                    if (entity != null)
                    {
                        rst.ResultData = ObjectMapper.Map<EquipementInfoAssetOutput>(entity);
                         protection = _protectionDeviceInfoRepository.GetAllIncluding(t=>t.ProtectionDeviceType).FirstOrDefault(t => t.EquipmentInfoId == entity.Id); ;
                        if (protection != null)
                        {
                            var boards = _boardCardInfoRepository.GetAll().Where(t => t.ProtectionDeviceInfoId == protection.Id)?.ToList();
                            if (boards != null && boards.Count > 0)
                            {
                                var cpuboard = boards.FirstOrDefault(t => t.BoardType == "CPU插件");
                                if (cpuboard != null)
                                {
                                    rst.ResultData.SoftwareVersion = $"保护：{cpuboard.ProtectionVersion}\n接口：{cpuboard.InterfaceVersion}";
                                }
                            }
                            rst.ResultData.ProductionDate = entity.ProductionDate;
                            rst.ResultData.HardwareVersion = protection.HardwareVersion;
                            rst.ResultData.Model = protection.ProtectionDeviceType?.Model;
                        }
                        rst.ResultData.EquipmentTypeName = entity.EquipmentType.Name;
                    }
                    unitOfWork.Complete();
                }
                if (protection != null)
                {
                    using (var unitOfWork = _unitOfWorkManager.Begin())
                    {
                        var device = _imProtectDeviceRepository.GetAllIncluding(t => t.GateWay).FirstOrDefault(t=>t.Id == protection.ISMS_DeviceId);
                        if (device!=null)
                        {
                            rst.ResultData.ProtectDeviceCoreInfo = new ProtectDeviceCoreInfo
                            {
                                StatCode = device.StatCode,
                                DeviceAddr = device.DeviceAddr,
                                DeviceID = device.Id,
                                DeviceName = device.DeviceName,
                                GateWayAddr = device.GateWay.PhyAddr,
                            };

                        }
                        unitOfWork.Complete();

                    }
                }
               

                rst.Flag = true;

            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "设备信息管理服务", ex);
            }
            return rst;
        }
        [HttpPost]
        public RequestPageResult<TelemeteringAndTelesignalProperty> GetTeleDataByEquipmentId(PageSearchCondition<EquipmentInfoIdAndNameSearchConditionInput> searchCondition)
        {
            RequestPageResult<TelemeteringAndTelesignalProperty> rst = new RequestPageResult<TelemeteringAndTelesignalProperty>();

            try
            {
                var telemeterings = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo, t => t.EquipmentType)
                     .WhereIf(searchCondition.SearchCondition.EquipmentInfoId.HasValue, t => t.EquipmentInfoId == searchCondition.SearchCondition.EquipmentInfoId).AsEnumerable()
                     .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name), t => t.Name.Contains(searchCondition.SearchCondition.Name))
                     .Select(t => new TelemeteringAndTelesignalProperty
                     {
                         Id = t.Id,
                         EquipementName = t.EquipmentInfo.Name,
                         Name = t.Name,
                         EquipementTypeName = t.EquipmentInfo.EquipmentType.Name,
                         EquipementInfoId = t.EquipmentInfo.Id,
                         SeqNo = t.SeqNo,
                         IsActive = t.IsActive,
                         Property = TeleDataPropertyEnum.Telemetering,
                         Remark = ""
                     }).ToList();

                var telesignals = _telesignalisationConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo, t => t.EquipmentType)
                     .WhereIf(searchCondition.SearchCondition.EquipmentInfoId.HasValue, t => t.EquipmentInfoId == searchCondition.SearchCondition.EquipmentInfoId).AsEnumerable()
                    .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name), t => t.Name.Contains(searchCondition.SearchCondition.Name))
                    .Select(t => new TelemeteringAndTelesignalProperty
                    {
                        Id = t.Id,
                        EquipementName = t.EquipmentInfo.Name,
                        Name = t.Name,
                        EquipementTypeName = t.EquipmentInfo.EquipmentType.Name,
                        EquipementInfoId = t.EquipmentInfo.Id,
                        SeqNo = t.SeqNo,
                        IsActive = t.IsActive,
                        Property = TeleDataPropertyEnum.Telemetering,
                        Remark = ""
                    }).ToList();
                var datas = telemeterings.Concat(telesignals);
                rst.TotalCount = datas.Count();
                int skipCount = (searchCondition.PageIndex - 1)* searchCondition.PageSize;
                if (skipCount>0)
                {
                    datas = datas.Skip(skipCount);
                }
                datas = datas.Take(searchCondition.PageSize);
                rst.ResultData = datas.ToList();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "", ex);
            }

            return rst; 
        }
        public RequestEasyResult CreateEquipmentLinkTeledata(List<EquipmentLinkTeleDataInput> inputs)
        {
            var rst = new RequestEasyResult();
            try
            {
                var entities = ObjectMapper.Map<List<EquipmentLinkTeledata>>(inputs);

                foreach (var entity in entities)
                {
                    entity.CreationTime = DateTime.Now;
                    _equipmentLinkTeledataReponsitory.Insert(entity);
                }
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "", ex);
            }

            return rst;
        }


        public RequestPageResult<EquipmentLinkTeleDataOutput> FindEquipmentLinkTeleData(PageSearchCondition<EquipmentLinkTeleDataSearchCondition> searchCondition)
        {
            RequestPageResult<EquipmentLinkTeleDataOutput> rst = new RequestPageResult<EquipmentLinkTeleDataOutput>();

            try
            {
                var repo =  _equipmentLinkTeledataReponsitory.GetAllIncluding(t=>t.DataEquipmentInfo,t=>t.DataEquipmentInfo.EquipmentType, t=>t.TelesignalisationConfiguration, t => t.TelemeteringConfiguration)
                    .WhereIf(searchCondition.SearchCondition.EquipmentDataCategoryExactlyId.HasValue,t=>t.EquipmentDataCategoryExactlyId == searchCondition.SearchCondition.EquipmentDataCategoryExactlyId)
                    .WhereIf(searchCondition.SearchCondition.EquipmentInfoId.HasValue,t=>t.EquipmentInfoId == searchCondition.SearchCondition.EquipmentInfoId)
                    .WhereIf(searchCondition.SearchCondition.Id.HasValue, t => t.Id == searchCondition.SearchCondition.Id)
                    .AsEnumerable();
                repo = repo.Where(t => t.TelemeteringConfiguration!=null&&t.TelemeteringConfiguration.Name.Contains(searchCondition.SearchCondition.Name)|| t.TelesignalisationConfiguration != null && t.TelesignalisationConfiguration.Name.Contains(searchCondition.SearchCondition.Name)).AsEnumerable();
                rst.TotalCount = repo.Count();
               
                int skipCount = (searchCondition.PageIndex - 1) * searchCondition.PageSize;
                var data = repo.Skip(skipCount).Take(searchCondition.PageSize);
                rst.ResultData = ObjectMapper.Map<List<EquipmentLinkTeleDataOutput>>(data);
                rst.Flag = true;
                rst.PageIndex = searchCondition.PageIndex;
                rst.PageSize = searchCondition.PageSize;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(),"",ex);
            }
            return rst;
        }
        /// <summary>
        /// 初始化遥测队列
        /// </summary>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        [DisableAuditing]
        public async Task InitYCListAsync(DataSourceCategoryEnum dataSourceCategory)
        {
            try
            {
                string rediskey = "telemeteringModelList_" + dataSourceCategory.ToString();
                var telemeterings = _telemeteringConfigurationResitory.GetAll()
                     .Where(t => t.DataSourceCategory == dataSourceCategory)
                    //.Where(t => t.IsActive)
                    .ToList();
                var duplicateIds = telemeterings
                .GroupBy(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}")
                .Where(g => g.Count() > 1) // 找出重复的标识符
                .Select(g => new
                {
                    Identifier = g.Key,
                    Count = g.Count(),
                    Items = g.ToList() // 保存重复数据项
                })
                .ToList();

                // 打印重复的数据
                foreach (var duplicate in duplicateIds)
                {
                    Log4Helper.Info(this.GetType(),$"重复标识符: {duplicate.Identifier}, 出现次数: {duplicate.Count}");
                }
                // 删除重复的数据，只保留每个标识符的第一个
                var uniqueTelemeterings = telemeterings
                    .GroupBy(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}")
                    .Select(g => g.First()) // 只保留每组的第一个
                    .ToList();
                // 输出去重后的数据
                telemeterings = uniqueTelemeterings;
                List<TelemeteringModel> telemeteringsRedis = await _telemeteringModelListRedis.HashSetGetAllAsync(rediskey);
                if (telemeteringsRedis != null && telemeteringsRedis.Count > 0)
                {
                    //// 假设 TelesignalisationModel 有一个 Id 属性作为比较依据
                    var difference = telemeterings
                                     .Where(localItem => !telemeteringsRedis.Any(redisItem => redisItem.Id == localItem.Id))
                                     .ToList();
                    var ids = difference.Select(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}").ToList();
                    var entities = ObjectMapper.Map<List<TelemeteringModel>>(difference);
                    _telemeteringModelListRedis.HashSetUpdateManyAsync(rediskey, ids, entities);
                }
                else
                {
                    var ids = telemeterings.Select(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}").ToList();
                    var entities = ObjectMapper.Map<List<TelemeteringModel>>(telemeterings);
                    _telemeteringModelListRedis.HashSetUpdateManyAsync(rediskey, ids, entities);
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "初始化遥测", ex);
            }
        }
        /// <summary>
        /// 初始化遥信队列
        /// </summary>
        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public async Task InitYXListAsync(DataSourceCategoryEnum dataSourceCategory)
        {
            try
            {
                List<TelesignalisationConfiguration> telesignalisations = _telesignalisationConfigurationResitory.GetAll()
                    .Where(t=>t.DataSourceCategory == dataSourceCategory)
                    //.Where(t=>t.IsActive)
                    .ToList();
                var duplicateIds = telesignalisations
               .GroupBy(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}")
               .Where(g => g.Count() > 1) // 找出重复的标识符
               .Select(g => new
               {
                   Identifier = g.Key,
                   Count = g.Count(),
                   Items = g.ToList() // 保存重复数据项
               })
               .ToList();
                
                // 打印重复的数据
                foreach (var duplicate in duplicateIds)
                {
                    Log4Helper.Info(this.GetType(), $"重复标识符: {duplicate.Identifier}, 出现次数: {duplicate.Count}");
                }
                // 删除重复的数据，只保留每个标识符的第一个
                var uniqueTelemeterings = telesignalisations
                    .GroupBy(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}")
                    .Select(g => g.First()) // 只保留每组的第一个
                    .ToList();

                // 输出去重后的数据
                telesignalisations = uniqueTelemeterings;
                string rediskey = "telesignalisationModelList_"+ dataSourceCategory.ToString();
                List<TelesignalisationModel> telesignalisationRedis =await _telesignalisationModelListRedis.HashSetGetAllAsync(rediskey);
                if (telesignalisationRedis != null && telesignalisationRedis.Count > 0)
                {
                    // 假设 TelesignalisationModel 有一个 Id 属性作为比较依据
                    var difference = telesignalisations
                                     .Where(localItem => !telesignalisationRedis.Any(redisItem => redisItem.Id == localItem.Id))
                                     .ToList();
                    var ids = difference.Select(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}").ToList();
                    var entities = ObjectMapper.Map<List<TelesignalisationModel>>(difference);
                    _telesignalisationModelListRedis.HashSetUpdateManyAsync(rediskey, ids, entities);
                }
                else
                {
                    var ids = telesignalisations.Select(t => $"{t.DeviceAddress}_{t.CPUSector}_{t.DispatcherAddress}_{(int)t.DataSourceCategory}").ToList();
                    var entities = ObjectMapper.Map<List<TelesignalisationModel>>(telesignalisations);
                    _telesignalisationModelListRedis.HashSetUpdateManyAsync(rediskey, ids, entities);
                }
               

            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "初始化遥信", ex);
            }

        }

        [HttpGet, AbpAllowAnonymous]
        [ShowApi]
        public IEnumerable<string> GetAllEquipments()
        {
            try
            {
                return  _equipmentInfoRepository.GetAll().Select(t => t.Name).ToList();
            }
            catch (Exception ex)
            {

                Log4Helper.Error(this.GetType(), "查询所有设备",ex);
            }
            return default;
        }
     
    }
}






