﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDeviceykTmp
    {
        public string Id { get; set; }
        public string Ykname { get; set; }
        public string RelatedyxId { get; set; }
        public string YkType { get; set; }
        public string Swonstr { get; set; }
        public string Swoffstr { get; set; }
        public string Swuncertstr { get; set; }
        public string PreyxId { get; set; }
        public int? Prestate4yk { get; set; }
        public string Lockmode { get; set; }
        public string Isresetcmd { get; set; }
        public string PreyxIdOff { get; set; }
        public int? Prestate4ykOff { get; set; }
        public string LockmodeOff { get; set; }

        public virtual ImDevicedataTmp IdNavigation { get; set; }
    }
}
