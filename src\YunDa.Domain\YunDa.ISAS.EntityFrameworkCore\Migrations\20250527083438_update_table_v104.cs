﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace YunDa.ISAS.Migrations
{
    public partial class update_table_v104 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "EnergyConsumptionDeviceId",
                type: "char(36) CHARACTER SET utf8mb4", 
                maxLength: 36,
                table: "dm_energy_consumption_config",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "dm_energy_consumption_config",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_consumption_config_EnergyConsumptionDeviceId",
                table: "dm_energy_consumption_config",
                column: "EnergyConsumptionDeviceId");

            migrationBuilder.AddForeignKey(
                name: "FK_dm_energy_consumption_config_dm_energy_consumption_device_En~",
                table: "dm_energy_consumption_config",
                column: "EnergyConsumptionDeviceId",
                principalTable: "dm_energy_consumption_device",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_dm_energy_consumption_config_dm_energy_consumption_device_En~",
                table: "dm_energy_consumption_config");

            migrationBuilder.DropIndex(
                name: "IX_dm_energy_consumption_config_EnergyConsumptionDeviceId",
                table: "dm_energy_consumption_config");

            migrationBuilder.DropColumn(
                name: "EnergyConsumptionDeviceId",
                table: "dm_energy_consumption_config");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "dm_energy_consumption_config");
        }
    }
}
