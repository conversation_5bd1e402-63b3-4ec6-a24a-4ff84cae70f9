﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class Notification
    {
        public Guid NotificationId { get; set; }
        public Guid SubscriptionId { get; set; }
        public Guid? ActivationId { get; set; }
        public Guid ReportId { get; set; }
        public DateTime? SnapShotDate { get; set; }
        public string ExtensionSettings { get; set; } = null!;
        public string Locale { get; set; } = null!;
        public string? Parameters { get; set; }
        public DateTime? ProcessStart { get; set; }
        public DateTime NotificationEntered { get; set; }
        public DateTime? ProcessAfter { get; set; }
        public int? Attempt { get; set; }
        public DateTime SubscriptionLastRunTime { get; set; }
        public string DeliveryExtension { get; set; } = null!;
        public Guid SubscriptionOwnerId { get; set; }
        public bool IsDataDriven { get; set; }
        public Guid? BatchId { get; set; }
        public DateTime? ProcessHeartbeat { get; set; }
        public int Version { get; set; }
        public int ReportZone { get; set; }

        public virtual Subscription Subscription { get; set; } = null!;
    }
}
