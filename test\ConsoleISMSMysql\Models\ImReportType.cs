﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImReportType
    {
        public ImReportType()
        {
            ImReportcfg = new HashSet<ImReportcfg>();
        }

        public string Rpttypecode { get; set; }
        public string Rpttypename { get; set; }

        public virtual ICollection<ImReportcfg> ImReportcfg { get; set; }
    }
}
