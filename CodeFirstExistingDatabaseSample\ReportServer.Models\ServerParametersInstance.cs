﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ServerParametersInstance
    {
        public string ServerParametersId { get; set; } = null!;
        public string? ParentId { get; set; }
        public string Path { get; set; } = null!;
        public DateTime CreateDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public int Timeout { get; set; }
        public DateTime Expiration { get; set; }
        public byte[] ParametersValues { get; set; } = null!;
    }
}
