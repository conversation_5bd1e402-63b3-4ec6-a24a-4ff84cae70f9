﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbDaTask
    {
        public string TaskId { get; set; } = null!;
        public string TaskName { get; set; } = null!;
        public string StartTime { get; set; } = null!;
        public string EndTime { get; set; } = null!;
        public string Sql4acquisition { get; set; } = null!;
        public string? Sql4record { get; set; }
        public string? SrcServer { get; set; }
        public string? SrcDatabase { get; set; }
        public string? SrcTable { get; set; }
        public string State { get; set; } = null!;
        public DateTime CreateTime { get; set; }
        public string? Memo { get; set; }
        public int? SeqNo { get; set; }
    }
}
