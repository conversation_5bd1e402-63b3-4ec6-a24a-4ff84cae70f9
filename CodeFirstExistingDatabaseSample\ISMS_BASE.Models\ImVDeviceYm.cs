﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVDeviceYm
    {
        public string Id { get; set; } = null!;
        public string Ymname { get; set; } = null!;
        public string? Unit { get; set; }
        public double? Cof { get; set; }
        public double? MaxValue { get; set; }
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
    }
}
