﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LoginTest
{
    public class MemoryToString
    {
        public static void Run()
        {
            // 创建一个MemoryStream并写入一些数据
            string data = "Hello, 创建一个MemoryStream并写入一些数据";
            byte[] byteArray = Encoding.UTF8.GetBytes(data);
            using (MemoryStream memoryStream = new MemoryStream(byteArray))
            {
                // 使用StreamReader读取MemoryStream中的数据并转换为字符串
                using (StreamReader reader = new StreamReader(memoryStream, Encoding.UTF8))
                {
                    string result = reader.ReadToEnd();
                    Console.WriteLine("从MemoryStream中读取的字符串:");
                    Console.WriteLine(result);
                }
            }
        }
    }
}
