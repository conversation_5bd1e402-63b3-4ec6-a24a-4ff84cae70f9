﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImProtectDeviceTmp
    {
        public ImProtectDeviceTmp()
        {
            ImDeviceDataTmps = new HashSet<ImDeviceDataTmp>();
            ImDeviceDzTmps = new HashSet<ImDeviceDzTmp>();
        }

        public string Id { get; set; } = null!;
        public int PuctgyCode { get; set; }
        public string DeviceName { get; set; } = null!;

        public virtual ImPuCtgy PuctgyCodeNavigation { get; set; } = null!;
        public virtual ICollection<ImDeviceDataTmp> ImDeviceDataTmps { get; set; }
        public virtual ICollection<ImDeviceDzTmp> ImDeviceDzTmps { get; set; }
    }
}
