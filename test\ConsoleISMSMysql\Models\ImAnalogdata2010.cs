﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAnalogdata2010
    {
        public string Devicectgy { get; set; }
        public int Datacode { get; set; }
        public string Dataname { get; set; }
        public string Sym1 { get; set; }
        public double Cof1 { get; set; }
        public int Precise1 { get; set; }
        public string Sym2 { get; set; }
        public double Cof2 { get; set; }
        public int Precise2 { get; set; }
        public string Enumstr { get; set; }

        public virtual ImDevctgy DevicectgyNavigation { get; set; }
    }
}
