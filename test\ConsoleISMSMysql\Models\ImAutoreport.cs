﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAutoreport
    {
        public ImAutoreport()
        {
            ImAutoreportitem = new HashSet<ImAutoreportitem>();
        }

        public string Rptid { get; set; }
        public string Rptname { get; set; }
        public string Rpttype { get; set; }
        public string Tempfileobjid { get; set; }
        public string Savepath { get; set; }
        public string State { get; set; }
        public DateTime Createtime { get; set; }

        public virtual ICollection<ImAutoreportitem> ImAutoreportitem { get; set; }
    }
}
