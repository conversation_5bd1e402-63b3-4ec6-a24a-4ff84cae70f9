﻿using ConsoleAppSqlite.Models;
using System;
using System.Linq;

namespace ConsoleAppSqlite
{
    internal class Program
    {
        static void Main(string[] args)
        {
            using (var context = new iodb1Context("D:\\Project\\SOMS\\server\\src\\YunDa.Server\\Yunda.ISAS.DataMonitoringServer\\bin\\Debug\\netcoreapp3.1\\SaveProtection\\files\\6\\iodb4.sql3"))
            {
                // 查询数据
                var ios = context.Caniopara.OrderBy(io => io.Id).ToList();
                foreach (var item in ios)
                {
                    Console.WriteLine(item.ParaName);
                }
            }
                Console.WriteLine("Hello World!");
        }
    }
}
