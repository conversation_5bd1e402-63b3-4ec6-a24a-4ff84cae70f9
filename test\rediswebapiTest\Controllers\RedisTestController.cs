﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using RedisDemo;
using StackExchange.Redis;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace rediswebapiTest.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class RedisTestController : ControllerBase
    {
        private readonly IDatabase _redis;
        private readonly IServer _server;
        private readonly RedisHelper _client;
        private readonly IMemoryCache _cache;
        private readonly QuratznetTest _quratznetTest;


        public RedisTestController(RedisHelper client, IMemoryCache cache, QuratznetTest quratznetTest)
        {
            _client = client;
            _redis = client.GetDatabase();
            _cache = cache;
            _server = client.GetServer();
            _quratznetTest = quratznetTest;
            _client.GetSubscriber().Subscribe("__keyevent@0__:expired", (channel, notificationType) =>
            {
                Debug.WriteLine(channel + "|" + notificationType);
                Task.Run(() =>
                {

                    while (true)
                    {
                        Task.Delay(1000).Wait();
                        Debug.WriteLine(notificationType);
                    }
                });
            });
            //_redis.StreamCreateConsumerGroupAsync("Consumer", "test").Wait();

        }
        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] string key)
        {
            var res = await _server.MemoryStatsAsync();
            var dis = res.ToDictionary();
            foreach (var item in dis.Values)
            {
                var s = item.ToString();
                Console.WriteLine(s);
            }
            string name = _redis.StringGet(key);
            return Ok("");
        }
        [HttpPut]
        public IActionResult Put([FromQuery] string key, string value)
        {
            var res = _redis.StringSet(key, value, TimeSpan.FromSeconds(10));
            return Ok(res);
        }
        [HttpPost]
        public async Task<IActionResult> Post([FromQuery] string key, string value)
        {
            var res = await _redis.StreamAddAsync(key, "test", value);
            return Ok(res);
        }
        [HttpDelete]
        public async Task<IActionResult> Delete([FromQuery] string key, string value)
        {
            var res = await _redis.StreamReadAsync(key, "");
            return Ok(res);
        }
    }
}
