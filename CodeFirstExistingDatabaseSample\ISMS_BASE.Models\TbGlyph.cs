﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbGlyph
    {
        public TbGlyph()
        {
            TbGlyphHots = new HashSet<TbGlyphHot>();
            TbGlyphLinkObjs = new HashSet<TbGlyphLinkObj>();
        }

        public string Id { get; set; } = null!;
        public string? ParentId { get; set; }
        public string DiagId { get; set; } = null!;
        public int GlyphType { get; set; }
        public double? Left { get; set; }
        public double? Top { get; set; }
        public double? Right { get; set; }
        public double? Bottom { get; set; }
        public short? LineWidth { get; set; }
        public int? LineColor { get; set; }
        public int? BkColor { get; set; }
        public short? LineStyle { get; set; }
        public short? Transparent { get; set; }
        public short? Direction { get; set; }
        public string? Name { get; set; }
        public short? ZOrder { get; set; }
        public short? State { get; set; }
        public string? BusiObjId { get; set; }
        public string? Reserved1 { get; set; }
        public string? Reserved2 { get; set; }
        public string? BusiObjName { get; set; }
        public short? Locked { get; set; }
        public short? Hide { get; set; }

        public virtual TbDataViewer? TbDataViewer { get; set; }
        public virtual TbDiagram? TbDiagram { get; set; }
        public virtual TbGrid? TbGrid { get; set; }
        public virtual TbLabel? TbLabel { get; set; }
        public virtual ICollection<TbGlyphHot> TbGlyphHots { get; set; }
        public virtual ICollection<TbGlyphLinkObj> TbGlyphLinkObjs { get; set; }
    }
}
