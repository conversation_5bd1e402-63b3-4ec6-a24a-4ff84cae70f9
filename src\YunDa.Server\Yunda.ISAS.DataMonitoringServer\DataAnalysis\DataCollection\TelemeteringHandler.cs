using Abp.Dependency;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Dlls;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Interfaces;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.DataAnalysis.DataCollection;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Redis.Entities.DataMonitorCategory;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection
{
    /// <summary>
    /// Handles telemetry data
    /// </summary>
    public class TelemeteringHandler : ITelemeteringHandler, ISingletonDependency
    {
        private readonly RedisDataRepository _redisDataRepository;
        private readonly ISecondaryCircuitAlarmChecker _secondaryCircuitAlarmChecker;
        private readonly IDeviceMonitoringHandler _deviceMonitoringHandler;
        private readonly Content _settingModel;
        private readonly TelemeteringResultSaveTask _telemeteringResultSaveTask;
        public TelemeteringHandler(
            RedisDataRepository redisDataRepository,
            ISecondaryCircuitAlarmChecker secondaryCircuitAlarmChecker,
            IDeviceMonitoringHandler deviceMonitoringHandler,
            TelemeteringResultSaveTask telemeteringResultSaveTask,
            Content settingModel)
        {
            _redisDataRepository = redisDataRepository;
            _secondaryCircuitAlarmChecker = secondaryCircuitAlarmChecker;
            _deviceMonitoringHandler = deviceMonitoringHandler;
            _settingModel = settingModel;
            _telemeteringResultSaveTask = telemeteringResultSaveTask;
        }

        /// <summary>
        /// Handles telemetry data processing
        /// </summary>
        public async Task HandleTelemeteringDataAsync(RecordYC_TYPENewTaskInfo ri)
        {
            try
            {
                var yc = ri.Record;
                var categoryValue = ri.Connection.DataSourceCategoryName;
                string redisKey = $"{_redisDataRepository.TelemeteringModelListRediskey}_{(DataSourceCategoryEnum)categoryValue}";
                string redisChannel = $"{_redisDataRepository.TelemeteringInflectionInflectionZZChannelRediskey}_{(DataSourceCategoryEnum)categoryValue}";

                string haskey = $"{yc.addr}_{0}_{yc.inf}_{categoryValue}";
                var ycData = await _redisDataRepository.TelemeteringModelListRedis.HashSetGetOneAsync(redisKey, haskey);

                if (ycData == null)
                {
                    Log4Helper.Error(GetType(), $"更新数据失败: 地址：{yc.inf} 类型：{categoryValue} 键：{haskey}");
                    return;
                }

                await UpdateTelemeteringDataAsync(ycData, yc, redisKey, redisChannel, haskey);
                _telemeteringResultSaveTask.SaveWithCache(ycData, ri.Connection);
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler($"Error in HandleTelemeteringDataAsync: {ex.ToString()}", "Error");
            }
        }

        /// <summary>
        /// Updates telemetry data
        /// </summary>
        public async Task UpdateTelemeteringDataAsync(TelemeteringModel ycData, YC_TYPE_New yc, string redisKey, string redisChannel, string haskey)
        {
            ycData.ResultTime = yc.time;
            ycData.ResultValue = yc.val * ycData.Coefficient;

            var tasks = new List<Task>
            {
                _redisDataRepository.TelemeteringModelListRedis.HashSetUpdateOneAsync(redisKey, haskey, ycData),
                _secondaryCircuitAlarmChecker.CheckSecondaryCircuitAlarmAsync(haskey, ycData, null),
                _deviceMonitoringHandler.HandleDeviceMonitoringDataAsync(ycData),
                Task.Run(() => _redisDataRepository.TelemeteringModelInflectionListRedis.PublishAsync(redisChannel, ycData)),
                SetEnvironmentTempAsync(ycData)
            };

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// Sets environment temperature
        /// </summary>
        public async Task SetEnvironmentTempAsync(TelemeteringModel ycData)
        {
            // Cache environment temperature (if applicable)
            if (ycData.IsEnvironmentTemp)
            {
                var environmentTempValue = new EnvironmentTempValue(ycData.ResultValue);
                await _redisDataRepository.EnvironmentTempValueRedis.HashSetUpdateOneAsync(
                    nameof(EnvironmentTempValue), 
                    ycData.Id.ToString(), 
                    environmentTempValue);
            }
        }

        /// <summary>
        /// Gets CPU visual YC info
        /// </summary>
        public async Task<TelemeteringModel> GetCPUVisualYcInfoAsync(TelemeteringModel devYc)
        {
            string redisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + 
                _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);
            
            var ycDatas = await _redisDataRepository.TelemeteringModelListRedis.HashSetGetAllAsync(redisKey);
            if (ycDatas != null)
            {
                var ycData = ycDatas
                    .Where(t => t.IsVirtualDevice && t.EquipmentInfoId == devYc.EquipmentInfoId)
                    .FirstOrDefault(t => t.Name == "CPU温度");
                
                return ycData;
            }
            
            return null;
        }
    }
}
