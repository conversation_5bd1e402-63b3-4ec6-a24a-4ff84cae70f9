﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImSheBei
    {
        public ImSheBei()
        {
            ImSheBeiDeviceData = new HashSet<ImSheBeiDeviceDatum>();
            ImSheBeiProtDevices = new HashSet<ImSheBeiProtDevice>();
        }

        public string SheBeiId { get; set; } = null!;
        public int Sblxbm { get; set; }
        public string StatCode { get; set; } = null!;
        public string YunXingBh { get; set; } = null!;
        public string? SheBeiBh { get; set; }
        public string? SheBeiXh { get; set; }
        public string? ZhiZaoChang { get; set; }
        public DateTime? ChuChangRq { get; set; }
        public DateTime? AnZhuangRq { get; set; }
        public DateTime? TouYunRq { get; set; }
        public double? GuZiJiaZhi { get; set; }
        public string? GuZiBh { get; set; }
        public string? DaXiuZhouQi { get; set; }
        public string? ZhongXiuZhouQi { get; set; }
        public string? XiaoXiuZhouQi { get; set; }
        public string? BeiZhu { get; set; }

        public virtual ImSheBeiLx SblxbmNavigation { get; set; } = null!;
        public virtual ImStation StatCodeNavigation { get; set; } = null!;
        public virtual ICollection<ImSheBeiDeviceDatum> ImSheBeiDeviceData { get; set; }
        public virtual ICollection<ImSheBeiProtDevice> ImSheBeiProtDevices { get; set; }
    }
}
