﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDiagram
    {
        public string Id { get; set; }
        public string Prjid { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public int? Gridsize { get; set; }
        public short? Showgrid { get; set; }
        public double? Scale { get; set; }
        public int? Pwroncolor { get; set; }
        public int? Pwroffcolor { get; set; }
        public int? Switchoncolor { get; set; }
        public int? Switchoffcolor { get; set; }
        public int? Switchunknowncolor { get; set; }
        public DateTime? Createtime { get; set; }
        public string Creator { get; set; }

        public virtual ImGlyph IdNavigation { get; set; }
        public virtual ImProject Prj { get; set; }
    }
}
