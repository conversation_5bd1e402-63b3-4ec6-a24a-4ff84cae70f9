﻿using System;
using System.Collections.Generic;
using System.Text;

namespace YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto
{
    /// <summary>
    /// 馈线功率组成数据
    /// </summary>
    public class FeederPowerComposition
    {
        /// <summary>
        /// 馈线名称列表
        /// </summary>
        public List<string> FeederNames { get; set; } = new List<string>();

        /// <summary>
        /// 有功功率数据列表
        /// </summary>
        public List<float> ActivePowerValues { get; set; } = new List<float>();

        /// <summary>
        /// 无功功率数据列表
        /// </summary>
        public List<float> ReactivePowerValues { get; set; } = new List<float>();

        /// <summary>
        /// 视在功率数据列表
        /// </summary>
        public List<float> ApparentPowerValues { get; set; } = new List<float>();

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum IntervalType { get; set; }
    }

    /// <summary>
    /// 馈线功率因数数据
    /// </summary>
    public class FeederPowerFactors
    {
        /// <summary>
        /// 馈线名称列表
        /// </summary>
        public List<string> FeederNames { get; set; } = new List<string>();

        /// <summary>
        /// 功率因数值列表
        /// </summary>
        public List<float> PowerFactorValues { get; set; } = new List<float>();

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum IntervalType { get; set; }
    }

        

        

        /// <summary>
        /// 馈线时间序列能耗数据
        /// </summary>
        public class FeederTimeSeriesData
        {
            /// <summary>
            /// 时间标签列表
            /// </summary>
            public List<string> TimeLabels { get; set; } = new List<string>();

            /// <summary>
            /// 馈线数据列表，每个馈线包含一组时间点对应的能耗值
            /// </summary>
            public List<FeederSeries> FeederSeries { get; set; } = new List<FeederSeries>();

            /// <summary>
            /// 时间间隔类型
            /// </summary>
            public RealTimePowerTypeEnum IntervalType { get; set; }
        }

        /// <summary>
        /// 单个馈线的时间序列数据
        /// </summary>
        public class FeederSeries
        {
            /// <summary>
            /// 馈线名称
            /// </summary>
            public string FeederName { get; set; }

            /// <summary>
            /// 能耗值列表，与TimeLabels一一对应
            /// </summary>
            public List<float> Values { get; set; } = new List<float>();
        }

}
