﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LoginTest
{
    public   class SearchSqlFile
    {
        public static void Run()
        {
            //FindSqlFiles("C:/");
            FindSqlFiles("D:/ISAS");
            Console.WriteLine("查找完成");
        }
        static void FindSqlFiles(string directory)
        {
            try
            {
                // 获取目录中的所有文件
                string[] files = Directory.GetFiles(directory);

                foreach (string file in files)
                {
                    // 检查文件扩展名是否为.sql
                    if (Path.GetExtension(file).Equals(".sql", StringComparison.OrdinalIgnoreCase)&& file.Contains("isas"))
                    {
                        Console.WriteLine("SQL文件: " + file);
                    }
                }

                // 递归遍历子目录
                string[] subdirectories = Directory.GetDirectories(directory);
                foreach (string subdirectory in subdirectories)
                {
                    FindSqlFiles(subdirectory);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("发生错误: " + ex.Message);
            }
        }
    }
}
