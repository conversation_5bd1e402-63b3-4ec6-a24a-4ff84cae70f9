﻿//#define Test

using Abp.Auditing;
using Abp.Authorization;
using Abp.Collections.Extensions;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Session;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.Account;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto.SearchCondition;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Redis.Entities.DataMonitorCategory;
using YunDa.ISAS.Redis.Repositories;
using Abp.EntityFrameworkCore.Repositories;

namespace YunDa.ISAS.Application.DataMonitoring
{
    /// <summary>
    /// 遥测配置管理服务
    /// </summary>
    [Description("遥测配置管理服务")]
    public class TelemeteringConfigurationAppService : ISASAppServiceBase, ITelemeteringConfigurationAppService
    {
        private readonly IRepository<EquipmentInfo, Guid> _repositoryEquipmentInfo;
        private readonly IRepository<TelemeteringConfiguration, Guid> _telemeteringConfigurationResitory;
        //private readonly IRepository<TelesignalisationConfiguration, Guid> _telesignalisationConfigurationResitory;

        private LoginUserOutput _currentUser;
        private readonly IRepository<EquipmentType, Guid> _repositoryEquipmentType;
        private readonly IRedisRepository<EnvironmentTempValue, string> _redisRepositoryEnvironmentTempValue;


        public TelemeteringConfigurationAppService(
            IRepository<TelemeteringConfiguration, Guid> telemeteringConfigurationResitory,
            //IRepository<TelesignalisationConfiguration, Guid> telesignalisationConfigurationResitory,
            IRepository<EquipmentInfo, Guid> repositoryEquipmentInfo,
            IRepository<EquipmentType, Guid> repositoryEquipmentType,
            IRedisRepository<EnvironmentTempValue, string> redisRepositoryEnvironmentTempValue,
            ISessionAppService sessionAppService) : base(sessionAppService)
        {
            _telemeteringConfigurationResitory = telemeteringConfigurationResitory;
            //_telesignalisationConfigurationResitory = telesignalisationConfigurationResitory;
            _repositoryEquipmentInfo = repositoryEquipmentInfo;
            _repositoryEquipmentType = repositoryEquipmentType;
            _currentUser = sessionAppService.GetCurrentLoginInformations().User;
            _redisRepositoryEnvironmentTempValue = redisRepositoryEnvironmentTempValue;
        }



        #region 增/改

        /// <summary>
        /// 遥测数据增加或修改
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Audited]
        [Description("遥测数据增加或修改")]
        public async Task<RequestResult<TelemeteringConfigurationOutput>> CreateOrUpdateAsync(
            EditTelemeteringConfigurationInput input)
        {
            if (input == null) return null;
            if (_currentUser == null)
                _currentUser = base.GetCurrentUser();
            return input.Id != null
                ? await UpdateAsync(input).ConfigureAwait(false)
                : await CreateAsync(input).ConfigureAwait(false);
        }
        /// <summary>
        /// 新增遥测
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<RequestResult<TelemeteringConfigurationOutput>> CreateAsync(
            EditTelemeteringConfigurationInput input)
        {
            var rst = new RequestResult<TelemeteringConfigurationOutput>();
            try
            {
                input.CreationTime = DateTime.Now;
                input.CreatorUserId = _currentUser.Id;
                var data = ObjectMapper.Map<TelemeteringConfiguration>(input);
                data = await _telemeteringConfigurationResitory.InsertAsync(data).ConfigureAwait(false);
                rst.ResultData = ObjectMapper.Map<TelemeteringConfigurationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "新增遥测", ex);
            }

            return rst;
        }
        /// <summary>
        /// 修改遥测
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<RequestResult<TelemeteringConfigurationOutput>> UpdateAsync(
            EditTelemeteringConfigurationInput input)
        {
            var rst = new RequestResult<TelemeteringConfigurationOutput>();
            try
            {
                var data = await _telemeteringConfigurationResitory.FirstOrDefaultAsync(u => u.Id == input.Id)
                    .ConfigureAwait(false);
                input.CreationTime = data.CreationTime;
                input.CreatorUserId = data.CreatorUserId;
                input.LastModificationTime = DateTime.Now;
                input.CreatorUserId = _currentUser.Id;
                input.DataSourceCategory = data.DataSourceCategory;
                ObjectMapper.Map(input, data);
                rst.ResultData = ObjectMapper.Map<TelemeteringConfigurationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "修改遥测", ex);
            }

            return rst;
        }

        #endregion 增/改

        #region 删除

        /// <summary>
        /// 删除单个遥测数据
        /// </summary>
        /// <param name="id">遥测id</param>
        /// <returns></returns>
        [HttpPost]
        [Audited]
        [Description("删除单个遥测数据")]
        public async Task<RequestEasyResult> DeleteByIdAsync(Guid id)
        {
            var rst = new RequestEasyResult();
            try
            {
                await _telemeteringConfigurationResitory.DeleteAsync(u => u.Id == id).ConfigureAwait(false);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "删除单个遥测数据", ex);
            }

            return rst;
        }

        /// <summary>
        /// 删除多个遥测数据
        /// </summary>
        /// <param name="ids">遥测id集合</param>
        /// <returns></returns>
        [HttpPost]
        [Audited]
        [Description("删除多个遥测数据")]
        public async Task<RequestEasyResult> DeleteByIdsAsync(List<Guid> ids)
        {
            var rst = new RequestEasyResult();
            try
            {
                //ids.ForEach(t => _telemeteringConfigurationResitory.Delete(u => u.Id == t));
                var entities = _telemeteringConfigurationResitory.GetAll().Where(t => ids.Contains(t.Id));
                _telemeteringConfigurationResitory.GetDbContext().RemoveRange(entities);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "删除多个遥测数据", ex);
            }


            return rst;
        }

        #endregion 删除

        #region 查询

        /// <summary>
        /// 查询遥测数据
        /// </summary>
        /// <param name="searchCondition">查询条件</param>
        /// <returns></returns>
        [HttpPost]
        //[Audited]
        [Description("查询遥测数据")]
        public RequestPageResult<TelemeteringConfigurationOutput> FindDatas(
            PageSearchCondition<TelemeteringConfigurationSearchConditionInput> searchCondition)
        {
            var rst = new RequestPageResult<TelemeteringConfigurationOutput>();
            try
            {
                var equipTypeG = _repositoryEquipmentType.GetAll()
                    .WhereIf(searchCondition.SearchCondition.EquipmentTypeTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.SearchCondition.EquipmentTypeTypeId)
                    
                    ;
                var datas = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentType,
                    s => s.EquipmentInfo)
                     .Where(t => t.EquipmentType.IsActive)
                    ;
                datas = datas
                    .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name),
                        t => t.Name.Contains(searchCondition.SearchCondition.Name))
                    .WhereIf(searchCondition.SearchCondition.TransformerSubstationId.HasValue,
                        t => t.TransformerSubstationId == searchCondition.SearchCondition.TransformerSubstationId)
                    .WhereIf(searchCondition.SearchCondition.EquipmentTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.SearchCondition.EquipmentTypeId)
                     .WhereIf(searchCondition.SearchCondition.DataSourceCategory.HasValue,
                        t => t.DataSourceCategory == searchCondition.SearchCondition.DataSourceCategory)
                    //.WhereIf(equipTypeG.Key.HasValue, t => equipDictionary.ContainsKey(t.Id))
                    .WhereIf(searchCondition.SearchCondition.Id.HasValue,
                        t => t.Id == searchCondition.SearchCondition.Id).WhereIf(
                        searchCondition.SearchCondition.EquipmentInfoId.HasValue,
                        t => t.EquipmentInfoId == searchCondition.SearchCondition.EquipmentInfoId)
                    .Join(equipTypeG, f => f.EquipmentTypeId, s => s.Id, (f, s) => f);
                datas = datas.OrderBy(t => t.EquipmentType.SeqNo).ThenBy(t => t.EquipmentInfo.SeqNo).ThenBy(t => t.DispatcherAddress);
                var rstDatas = datas.ToList().AsQueryable();
                if (!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name))
                {
                    rstDatas = rstDatas.Where(t => t.Name.Contains(searchCondition.SearchCondition.Name));
                    //.Where(item => item.Name.ToArray().Intersect(searchCondition.SearchCondition.Name.ToArray()).Count() == searchCondition.SearchCondition.Name.ToArray().Distinct().Count()).AsQueryable();
                }

                rst.TotalCount = rstDatas.Count();

                var skipCount = searchCondition.PageIndex <= 0
                    ? -1
                    : (searchCondition.PageIndex - 1) * searchCondition.PageSize;
                if (skipCount != -1)
                    rstDatas = rstDatas.PageBy(skipCount, searchCondition.PageSize);
                rst.ResultData = ObjectMapper.Map<List<TelemeteringConfigurationOutput>>(rstDatas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "查询遥测数据", ex);
            }
            return rst;
        }

        /// <summary>
        /// 查询所有遥测数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AbpAllowAnonymous]
        public RequestPageResult<TelemeteringConfigurationOutput> FindAllDatas()
        {
            var rst = new RequestPageResult<TelemeteringConfigurationOutput>();
            try
            {
                var datas = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo,
                    t => t.EquipmentType)
                    .Where(t => t.EquipmentType.IsActive)
                    ;

                datas = datas.OrderBy(t => t.SeqNo);//.ThenBy(t => t.InfoAddress);
                var dic = datas.AsEnumerable().GroupBy(t => t.EquipmentInfoId);
                var list = new List<TelemeteringConfiguration>();

                foreach (var item in dic)
                {
                    var itemSort = item.OrderBy(t => t.InfoAddress);
                    list.AddRange(itemSort);
                }
                datas = list.AsQueryable();
                rst.ResultData = ObjectMapper.Map<List<TelemeteringConfigurationOutput>>(datas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "查询所有遥测数据", ex);
            }

            return rst;
        }
        /// <summary>
        /// 查询要测通讯数据结构
        /// </summary>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        public RequestResult<dynamic> FindTelesignalisationConfigurationDataStruct(TelemeteringConfigurationSearchConditionInput telemeteringConfigurationSearchConditionInput)
        {
            var rst = new RequestResult<dynamic>();
            try
            {
                var datas = _telemeteringConfigurationResitory.GetAll().
                    Where(t => t.IsActive && t.TransformerSubstationId == telemeteringConfigurationSearchConditionInput.TransformerSubstationId).ToList();
                var TelemeteringCount = datas.Count;
                var TelemeteringStartAddress = datas.Min(t => t.DispatcherAddress);
                var Address = datas.First().DeviceAddress;
                rst.ResultData = new
                {
                    TelemeteringCount,
                    TelemeteringStartAddress,
                    Address,
                };
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "查询要测通讯数据结构", ex);
            }
            return rst;
        }
        /// <summary>
        /// 遥测数据树形结构获取
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [HttpPost]
        public RequestResult<List<TreeModelOutput>> GetTelemetringConfigurationTreeNode(TelemeteringConfigurationSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<TreeModelOutput>> { Flag = false };
            if (searchCondition == null)
                return rst;

            try
            {
                var datas = _telemeteringConfigurationResitory.GetAll().Where(s => s.IsActive && s.EquipmentInfoId == searchCondition.EquipmentInfoId);

                datas = datas.OrderBy(t => t.SeqNo);
                //datas = datas.OrderBy(t => t.Name);
                var dic = datas.AsEnumerable().GroupBy(t => t.EquipmentInfoId);
                var list = new List<TelemeteringConfiguration>();

                foreach (var item in dic)
                {
                    var itemSort = item.OrderBy(t => t.InfoAddress);
                    list.AddRange(itemSort);
                }
                datas = list.AsQueryable();
                rst.ResultData = datas.Select(e => new TreeModelOutput
                {
                    Id = e.Id,
                    Text = e.Name,
                    Type = TreeModelType.TeleInfo
                }).ToList();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
            }
            return rst;
        }

        /// <summary>
        /// 根据条件查询遥测下拉框内容
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [HttpPost]
        [ShowApi]
        [AllowAnonymous]
        public RequestResult<List<SelectModelOutput>> FindTelemeteringConfigurationForSelect(SelectTelemeteringSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<SelectModelOutput>> { Flag = false };
            try
            {
                var datas = _telemeteringConfigurationResitory.GetAll().Where(m => m.IsActive);
                datas = datas.WhereIf(searchCondition.EquipmentInfoId.HasValue, m => m.EquipmentInfoId == searchCondition.EquipmentInfoId)
                     .WhereIf(searchCondition.IsVirtualDevice.HasValue, m => m.IsVirtualDevice == searchCondition.IsVirtualDevice)
                     .WhereIf(searchCondition.TransformerSubstationId.HasValue, m => m.TransformerSubstationId == searchCondition.TransformerSubstationId);

                datas = datas.OrderBy(t => t.SeqNo);
                var dic = datas.AsEnumerable().GroupBy(t => t.EquipmentInfoId);
                var list = new List<TelemeteringConfiguration>();

                foreach (var item in dic)
                {
                    var itemSort = item.OrderBy(t => t.InfoAddress);
                    list.AddRange(item);
                }
                datas = list.AsQueryable();

                rst.ResultData = datas.Select(m => new SelectModelOutput
                {
                    Key = m.Id,
                    Text = m.Name,
                    Value = m.Id.ToString().ToLower(),
                    
                    NodeObj = searchCondition.IsNeedNodeObj.Value? new TelemeteringConfiguration
                    {
                        InfoAddress = m.InfoAddress,
                        CPUSector = m.CPUSector,
                        DeviceAddress = m.DeviceAddress
                    }:null
                }).ToList();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
            }
            return rst;
        }
        /// <summary>
        /// 查询环境温度
        /// </summary>
        /// <param name="transubstationId"></param>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [AbpAllowAnonymous]
        public async Task<RequestResult<float>> GetEnvironmentTempValue(Guid? transubstationId)
        {
            var rst = new RequestResult<float>() { ResultData = 0.1F };
            try
            {
                
                var xValue =await _redisRepositoryEnvironmentTempValue.HashSetGetAllAsync(nameof(EnvironmentTempValue));
                if (xValue.Count > 0)
                {
                    rst.ResultData = xValue[0].TempValue;
                }
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "查询环境温度", ex);
            }
            return rst;
        }
        /// <summary>
        ///  查询所有遥测数据
        /// </summary>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        public RequestResult<List<TelemeteringConfigurationOutput>> FindListDatas(TelemeteringConfigurationSearchConditionInput input)
        {
            var rst = new RequestResult<List<TelemeteringConfigurationOutput>>();
            try
            {
                var datas = _telemeteringConfigurationResitory.GetAllIncluding(t => t.SelfCheckingConfiguration
                    )
                     .WhereIf(!string.IsNullOrWhiteSpace(input.Name), t => t.Name.Contains(input.Name))
                    .WhereIf(input.TransformerSubstationId.HasValue, t => t.TransformerSubstationId == input.TransformerSubstationId)
                    .WhereIf(input.EquipmentTypeId.HasValue, t => t.EquipmentTypeId == input.EquipmentTypeId)
                    .WhereIf(input.Id.HasValue, t => t.Id == input.Id)
                    .WhereIf(input.EquipmentInfoId.HasValue, t => t.EquipmentInfoId == input.EquipmentInfoId)
                    .WhereIf(input.IsVirtualDevice.HasValue, t => t.IsVirtualDevice == input.IsVirtualDevice)
                    ;
                datas = datas.OrderBy(t => t.EquipmentInfoId);
                datas = datas.OrderBy(t => t.SeqNo).ThenBy(t => t.InfoAddress);
                rst.ResultData = ObjectMapper.Map<List<TelemeteringConfigurationOutput>>(datas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "查询所有遥测数据", ex);

            }

            return rst;
        }

        #endregion 查询

        /// <summary>
        /// 填充序列
        /// </summary>
        /// <param name="start"></param>
        /// <param name="count"></param>
        /// <param name="dataSourceCategory"></param>
        /// 
        /// <returns></returns>
        [HttpGet]
        public RequestEasyResult PaddingSequence(int start,int count,DataSourceCategoryEnum dataSourceCategory = DataSourceCategoryEnum.None)
        {
            RequestEasyResult rst = new RequestEasyResult();
            try
            {
                if (count==0|| dataSourceCategory == DataSourceCategoryEnum.None)
                {
                    return rst;
                }
                var datas =  _telemeteringConfigurationResitory.GetAllIncluding(t=>t.EquipmentInfo)
                    .OrderBy(t=>t.EquipmentInfo.SeqNo)
                    .Where(t => t.DataSourceCategory == dataSourceCategory);
                
                foreach (var item in datas)
                {
                    if (start< count)
                    {
                        item.DispatcherAddress = start;
                        start++;

                    }
                }

            }
            catch (Exception ex)
            {

                Log4Helper.Error(this.GetType(), "删除单个遥测数据", ex);

            }

            return rst;
        }
    }
}