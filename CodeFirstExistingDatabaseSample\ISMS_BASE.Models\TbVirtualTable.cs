﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbVirtualTable
    {
        public string TableName { get; set; } = null!;
        public string? OrderBy { get; set; }
        public int SeqNo { get; set; }
        public string BaseTableId { get; set; } = null!;
        public string DeriveTableId { get; set; } = null!;
        public string? CtgyInBaseTable { get; set; }
        public string? CtgyFldInBaseTable { get; set; }

        public virtual TbTable BaseTable { get; set; } = null!;
        public virtual TbTable DeriveTable { get; set; } = null!;
    }
}
