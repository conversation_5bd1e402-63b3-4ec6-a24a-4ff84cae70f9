﻿using StackExchange.Redis;
using System.Diagnostics;

namespace rediswebapiTest
{
    public static class staticRedisTest
    {

        public static void start()
        {
            var ret = ConnectionMultiplexer.Connect("127.0.0.1:6379,allowadmin=true");
            IDatabase database = ret.GetDatabase(0);
            ISubscriber subscriber = ret.GetSubscriber();
            subscriber.Subscribe("__keyevent@0__:expired", (channel, notificationType) =>
            {
                Debug.WriteLine(channel + "|" + notificationType);
            });
        }
    }
}
