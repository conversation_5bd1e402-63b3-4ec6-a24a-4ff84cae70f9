﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFltDistDevGrp
    {
        public ImFltDistDevGrp()
        {
            ImFltDistDevices = new HashSet<ImFltDistDevice>();
            ImFltDistParams = new HashSet<ImFltDistParam>();
        }

        public string Id { get; set; } = null!;
        public string GrpName { get; set; } = null!;
        public string PrjId { get; set; } = null!;

        public virtual ImProject Prj { get; set; } = null!;
        public virtual ICollection<ImFltDistDevice> ImFltDistDevices { get; set; }
        public virtual ICollection<ImFltDistParam> ImFltDistParams { get; set; }
    }
}
