﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceYcTmp
    {
        public string Id { get; set; } = null!;
        public string Ycname { get; set; } = null!;
        public string? Unit { get; set; }
        public double? Cof { get; set; }
        public int? Precise { get; set; }
        public double? UpLimit { get; set; }
        public double? DownLimit { get; set; }
        public double? Intl4Save { get; set; }
        public string? Unit2 { get; set; }
        public double? Cof2 { get; set; }
        public int? Precise2 { get; set; }
        public string SaveMode { get; set; } = null!;
        public double? UpLimit4Stat { get; set; }

        public virtual ImDeviceDataTmp IdNavigation { get; set; } = null!;
    }
}
