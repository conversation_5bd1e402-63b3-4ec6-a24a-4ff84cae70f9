﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using WpfAppUniqueSelectorGrid;

namespace UniqueSelectorGrid
{
    public class MainViewModel : INotifyPropertyChanged
    {
        public ObservableCollection<string> AllCategories { get; } = new ObservableCollection<string>
        {
            "无", "综自", "配电", "辅控", "在线监测", "机器人"
        };

        public ObservableCollection<RowViewModel> Rows { get; } = new ObservableCollection<RowViewModel>();

        public MainViewModel()
        {
            Rows.Add(new RowViewModel(this));
            Rows.Add(new RowViewModel(this));
            Rows.Add(new RowViewModel(this));
            Rows.Add(new RowViewModel(this));
            Rows.Add(new RowViewModel(this));
            Rows.Add(new RowViewModel(this));

        }

        public event PropertyChangedEventHandler PropertyChanged;
    }
}
