﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImGlyphdevdata
    {
        public string Glyphid { get; set; }
        public string Measuredataid { get; set; }
        public string Measuredataname { get; set; }
        public string Controldataid { get; set; }
        public string Controldataname { get; set; }

        public virtual ImGlyph Glyph { get; set; }
    }
}
