﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ExecutionLog
    {
        public string InstanceName { get; set; } = null!;
        public Guid? ReportId { get; set; }
        public string? UserName { get; set; }
        public bool? RequestType { get; set; }
        public string? Format { get; set; }
        public string? Parameters { get; set; }
        public DateTime TimeStart { get; set; }
        public DateTime TimeEnd { get; set; }
        public int TimeDataRetrieval { get; set; }
        public int TimeProcessing { get; set; }
        public int TimeRendering { get; set; }
        public int Source { get; set; }
        public string Status { get; set; } = null!;
        public long ByteCount { get; set; }
        public long RowCount { get; set; }
    }
}
