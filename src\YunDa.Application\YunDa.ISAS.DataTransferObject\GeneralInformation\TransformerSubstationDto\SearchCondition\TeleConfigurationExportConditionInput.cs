﻿using System;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;

namespace YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto
{
    public class TeleConfigurationExportConditionInput
    {
        /// <summary>
        /// 所属变电站
        /// </summary>
        public virtual Guid TransformerSubstationId { get; set; }

        /// <summary>
        /// 变电站名称
        /// </summary>
        public virtual string SubstationName { get; set; }
        /// <summary>
        /// 数据来源
        /// </summary>
        public virtual DataSourceCategoryEnum? DataSourceCategory { get; set; }
    }
}