﻿using System;
using System.Collections.Generic;
using CodeFirstExistingDatabaseSample.ISMS_Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace CodeFirstExistingDatabaseSample
{
    public partial class ISMS_DataContext : DbContext
    {
        public ISMS_DataContext()
        {
        }

        public ISMS_DataContext(DbContextOptions<ISMS_DataContext> options)
            : base(options)
        {
        }

        public virtual DbSet<ImAbnormalYxcount> ImAbnormalYxcounts { get; set; } = null!;
        public virtual DbSet<ImAlert200010> ImAlert200010s { get; set; } = null!;
        public virtual DbSet<ImAlert202407> ImAlert202407s { get; set; } = null!;
        public virtual DbSet<ImDeviceDataValue> ImDeviceDataValues { get; set; } = null!;
        public virtual DbSet<ImDzdataDetail> ImDzdataDetails { get; set; } = null!;
        public virtual DbSet<ImDzdataMain> ImDzdataMains { get; set; } = null!;
        public virtual DbSet<ImYcstat202407> ImYcstat202407s { get; set; } = null!;
        public virtual DbSet<ImYxdatum> ImYxdata { get; set; } = null!;
        public virtual DbSet<TbDataModiHi> TbDataModiHis { get; set; } = null!;
        public virtual DbSet<TbSession> TbSessions { get; set; } = null!;
        public virtual DbSet<TbXiTongRiZhi> TbXiTongRiZhis { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_Data;Trusted_Connection=False;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ImAbnormalYxcount>(entity =>
            {
                entity.HasKey(e => e.DeviceId)
                    .HasName("PK_AbnormalYXCount");

                entity.ToTable("im_AbnormalYXCount");

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.LastConfirmTime)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImAlert200010>(entity =>
            {
                entity.ToTable("im_Alert200010");

                entity.HasIndex(e => e.AlertTime, "Ind_im_Alert200010");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.AlertDomain)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('系统')");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.AlertTime)
                    .HasMaxLength(25)
                    .IsUnicode(false);

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Alerter)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Content)
                    .HasMaxLength(2500)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.LnkType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.RecTime).HasColumnType("datetime");

                entity.Property(e => e.Reserved)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TimeSrc)
                    .HasMaxLength(8)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImAlert202407>(entity =>
            {
                entity.ToTable("im_Alert202407");

                entity.HasIndex(e => e.AlertTime, "Ind_im_Alert202407");

                entity.Property(e => e.Id)
                    .HasMaxLength(38)
                    .IsUnicode(false)
                    .HasColumnName("ID");

                entity.Property(e => e.AlertDomain)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('系统')");

                entity.Property(e => e.AlertLevel)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.AlertTime)
                    .HasMaxLength(25)
                    .IsUnicode(false);

                entity.Property(e => e.AlertType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Alerter)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.Content)
                    .HasMaxLength(2500)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.LnkType)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.RecTime).HasColumnType("datetime");

                entity.Property(e => e.Reserved)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.StatCode)
                    .HasMaxLength(30)
                    .IsUnicode(false);

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.TimeSrc)
                    .HasMaxLength(8)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImDeviceDataValue>(entity =>
            {
                entity.HasKey(e => e.DataId)
                    .HasName("PK_DeviceDataValue");

                entity.ToTable("im_DeviceDataValue");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.OccTime).HasColumnType("datetime");

                entity.Property(e => e.TimeSrc)
                    .HasMaxLength(8)
                    .IsUnicode(false);

                entity.Property(e => e.Unit)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ValueStr)
                    .HasMaxLength(30)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImDzdataDetail>(entity =>
            {
                entity.HasKey(e => e.DzdataDetailId)
                    .HasName("PK_DZData_Detail");

                entity.ToTable("im_DZData_Detail");

                entity.HasIndex(e => new { e.DzdataMainId, e.DzId }, "UK_DZData_Detail")
                    .IsUnique();

                entity.Property(e => e.DzdataDetailId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DZDataDetailID");

                entity.Property(e => e.DzComment)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Comment");

                entity.Property(e => e.DzId)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("DZ_ID");

                entity.Property(e => e.DzIndex).HasColumnName("DZ_Index");

                entity.Property(e => e.DzUnit)
                    .HasMaxLength(30)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Unit");

                entity.Property(e => e.DzValue)
                    .HasMaxLength(100)
                    .IsUnicode(false)
                    .HasColumnName("DZ_Value");

                entity.Property(e => e.DzdataMainId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DZDataMainID");

                entity.HasOne(d => d.DzdataMain)
                    .WithMany(p => p.ImDzdataDetails)
                    .HasForeignKey(d => d.DzdataMainId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DZData_Detail_Main");
            });

            modelBuilder.Entity<ImDzdataMain>(entity =>
            {
                entity.HasKey(e => e.DzdataMainId)
                    .HasName("PK_DZData_Main");

                entity.ToTable("im_DZData_Main");

                entity.Property(e => e.DzdataMainId)
                    .HasMaxLength(40)
                    .IsUnicode(false)
                    .HasColumnName("DZDataMainID");

                entity.Property(e => e.CpuIndex).HasColumnName("CPU_Index");

                entity.Property(e => e.CreateTime).HasColumnType("datetime");

                entity.Property(e => e.DataType)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.DeviceId)
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("DeviceID");

                entity.Property(e => e.UserName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<ImYcstat202407>(entity =>
            {
                entity.HasKey(e => new { e.DataId, e.HourTime });

                entity.ToTable("im_YCStat202407");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.HourTime).HasColumnType("datetime");

                entity.Property(e => e.BeiZhu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.MaxOccTime).HasColumnType("datetime");

                entity.Property(e => e.MinOccTime).HasColumnType("datetime");

                entity.Property(e => e.SeqNo).ValueGeneratedOnAdd();

                entity.Property(e => e.State)
                    .HasMaxLength(10)
                    .IsUnicode(false)
                    .HasColumnName("State_")
                    .HasDefaultValueSql("('有效')");
            });

            modelBuilder.Entity<ImYxdatum>(entity =>
            {
                entity.ToTable("im_YXData");

                entity.HasIndex(e => e.DataId, "Ind_im_YXData_DataID");

                entity.HasIndex(e => e.OccTime, "Ind_im_YXData_OccTime");

                entity.HasIndex(e => new { e.DataId, e.OccTime }, "UK_YXData")
                    .IsUnique();

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Comment)
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.CreateTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.DataId)
                    .HasMaxLength(12)
                    .IsUnicode(false)
                    .HasColumnName("DataID");

                entity.Property(e => e.NormalOrNot)
                    .HasMaxLength(20)
                    .IsUnicode(false)
                    .HasDefaultValueSql("('未知')");

                entity.Property(e => e.OccTime)
                    .HasMaxLength(25)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbDataModiHi>(entity =>
            {
                entity.ToTable("tb_DataModiHis");

                entity.HasIndex(e => e.DstTable, "Ind_DataModiHis_DstTable");

                entity.HasIndex(e => e.EditTime, "Ind_DataModiHis_EditTime");

                entity.HasIndex(e => e.Editor, "Ind_DataModiHis_Editor");

                entity.HasIndex(e => e.Type, "Ind_DataModiHis_Type");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Content).IsUnicode(false);

                entity.Property(e => e.DstObjId)
                    .HasMaxLength(255)
                    .IsUnicode(false)
                    .HasColumnName("DstObjID");

                entity.Property(e => e.DstObjName)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.DstTable)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.EditTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.Editor)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Type)
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.Upd20200410)
                    .HasMaxLength(1)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbSession>(entity =>
            {
                entity.HasKey(e => e.SessionId)
                    .HasName("PK_Session");

                entity.ToTable("tb_Session");

                entity.Property(e => e.SessionId)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.ClientIp)
                    .HasMaxLength(50)
                    .IsUnicode(false)
                    .HasColumnName("ClientIP");

                entity.Property(e => e.LastTime)
                    .HasColumnType("datetime")
                    .HasDefaultValueSql("(getdate())");

                entity.Property(e => e.SoftInfo)
                    .HasMaxLength(2000)
                    .IsUnicode(false);

                entity.Property(e => e.UserName)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<TbXiTongRiZhi>(entity =>
            {
                entity.ToTable("tb_XiTongRiZhi");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.FaShengRiQi)
                    .HasMaxLength(10)
                    .IsUnicode(false);

                entity.Property(e => e.FaShengShiJian)
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.MiaoShu)
                    .HasMaxLength(255)
                    .IsUnicode(false);

                entity.Property(e => e.YongHu)
                    .HasMaxLength(20)
                    .IsUnicode(false);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
