﻿<!-- MainWindow.xaml 主要修改界面部分 -->
<Window x:Class="TcpForwarder.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:avalon="http://icsharpcode.net/sharpdevelop/avalonedit"
        Title="TCP 转发器" Height="800" Width="1000">
    <Grid>
        <TabControl>
            <!-- TCP 客户端页 -->
            <TabItem Header="ISMS 客户端">
                <!-- 原有客户端界面内容 -->
            </TabItem>

            <!-- TCP 服务端页 -->
            <TabItem Header="转发服务端">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 服务端控制 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <TextBlock Text="监听端口:" Width="80"/>
                        <TextBox x:Name="txtServerPort" Width="120" Text="8080"/>
                        <Button x:Name="btnStartServer" Content="启动服务" Click="BtnStartServer_Click"/>
                        <Button x:Name="btnStopServer" Content="停止服务" Click="BtnStopServer_Click"/>
                    </StackPanel>

                    <!-- 客户端连接列表 -->
                    <DataGrid x:Name="dgClients" Grid.Row="1" AutoGenerateColumns="False">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="客户端ID" Binding="{Binding Id}"/>
                            <DataGridTextColumn Header="远程地址" Binding="{Binding RemoteEndPoint}"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 服务端日志 -->
                    <avalon:TextEditor x:Name="txtServerLog" Grid.Row="2" IsReadOnly="True"/>
                </Grid>
            </TabItem>

            <!-- Redis 配置页 -->
            <TabItem Header="Redis 配置">
                <StackPanel Margin="10">
                    <TextBox x:Name="txtRedisHost" Text="127.0.0.1" Header="Host"/>
                    <TextBox x:Name="txtRedisPort" Text="6379" Header="Port"/>
                    <TextBox x:Name="txtRedisAuth" Header="Password"/>
                    <Button Content="测试连接" Click="TestRedisConnection_Click"/>
                </StackPanel>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
