﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAutoReport
    {
        public ImAutoReport()
        {
            ImAutoReportItems = new HashSet<ImAutoReportItem>();
        }

        public string RptId { get; set; } = null!;
        public string RptName { get; set; } = null!;
        public string RptType { get; set; } = null!;
        public string? TempFileObjId { get; set; }
        public string? SavePath { get; set; }
        public string State { get; set; } = null!;
        public DateTime CreateTime { get; set; }

        public virtual ICollection<ImAutoReportItem> ImAutoReportItems { get; set; }
    }
}
