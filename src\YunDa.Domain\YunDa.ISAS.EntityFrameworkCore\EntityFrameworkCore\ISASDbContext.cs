﻿using Abp.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using YunDa.ISAS.Entities.ClientConfiguration.ThreeDimension;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.Foundation;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Entities.MobileSurveillance;
using YunDa.ISAS.Entities.MySQL.DataMonitoring;
using YunDa.ISAS.Entities.System;
using YunDa.ISAS.Entities.VideoSurveillance;
using YunDa.SOMS.Entities.DataMonitoring;
using YunDa.SOMS.Entities.GeneralInformation;
using YunDa.SOMS.Entities.System;

namespace YunDa.ISAS.EntityFrameworkCore.EntityFrameworkCore
{
    public class ISASDbContext : AbpDbContext
    {
        public ISASDbContext(DbContextOptions<ISASDbContext> options)
            : base(options)
        {
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
#if DEBUG
            //optionsBuilder.EnableSensitiveDataLogging(true);
#endif

            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            foreach (var foreignKey in modelBuilder.Model.GetEntityTypes().SelectMany(d => d.GetForeignKeys()))
            {
                //Cascade:级联删除
                //ClientSetNull:外键属性设置为 null
                //SetNull:外键属性设置为 null
                //Restrict:无
                foreignKey.DeleteBehavior = DeleteBehavior.Cascade;
            }
            base.OnModelCreating(modelBuilder);
        }
        #region 系统基本信息
        /// <summary>
        /// 系统用户
        /// </summary>
        public virtual DbSet<SysUser> SysUserDbSet { get; set; }

        /// <summary>
        /// 系统角色
        /// </summary>
        public virtual DbSet<SysRole> SysRoleDbSet { get; set; }

        /// <summary>
        /// 系统功能
        /// </summary>
        public virtual DbSet<SysFunction> SysFunctionDbSet { get; set; }

        /// <summary>
        /// 系统角色功能对照表
        /// </summary>
        public virtual DbSet<SysRoleFunction> SysRoleFunctionDbSet { get; set; }

        /// <summary>
        /// 系统用户角色对照表
        /// </summary>
        public virtual DbSet<SysRoleUser> SysUserRoleDbSet { get; set; }
        /// <summary>
        /// 系统配置数据存储表
        /// </summary>
        public virtual DbSet<SysConfiguration> SysConfigurationDbSet { get; set; }
        public virtual DbSet<EquipmentTypeViewPoint> EquipmentTypeViewPointDbSet { get; set; }
        public virtual DbSet<EquipmentViewPoint> EquipmentViewPointDbSet { get; set; }



        #endregion 系统基本信息

        #region 业务基本信息

        /// <summary>
        /// 线路
        /// </summary>
        public virtual DbSet<PowerSupplyLine> PowerSupplyLineDbSet { get; set; }

        /// <summary>
        /// 变电所
        /// </summary>
        public virtual DbSet<TransformerSubstation> TransformerSubstationDbSet { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public virtual DbSet<EquipmentType> EquipmentTypeDbSet { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        public virtual DbSet<EquipmentLocation> EquipmentLocationDbSet { get; set; }

        /// <summary>
        /// 设备
        /// </summary>
        public virtual DbSet<EquipmentInfo> EquipmentInfoDbSet { get; set; }
        public virtual DbSet<EquipmentDataCategory> EquipmentDataCategoryDbSet { get; set; }
        /// <summary>
        /// 信息类型关联表
        /// </summary>
        public virtual DbSet<EquipmentDataCategoryExactly> EquipmentDataCategoryExactlyDbSet { get; set; }
        /// <summary>
        /// 设备下属分类关联的遥测遥信表
        /// </summary>
        public virtual DbSet<EquipmentLinkTeledata> EquipmentLinkTeledataDbSet { get; set; }

        
        /// <summary>
        /// 生产商
        /// </summary>
        public virtual DbSet<ManufacturerInfo> ManufacturerInfoDbSet { get; set; }
        public virtual DbSet<MasterStation> MasterStationDbSet { get; set; }
        public virtual DbSet<SubMasterStationRelation> SubMasterStationRelationDbSet { get; set; }

        #endregion 业务基本信息

        #region 视频相关

        /// <summary>
        /// 视频设备（如NVR、DVR或摄像头）
        /// </summary>
        public virtual DbSet<VideoDev> VideoDevDbSet { get; set; }
        /// <summary>
        /// 视频监控终端（摄像头）预置点
        /// </summary>
        public virtual DbSet<PresetPoint> PresetPointDbSet { get; set; }
        /// <summary>
        /// 视频监控终端（摄像头）预置点
        /// </summary>
        public virtual DbSet<MeasureTemperaturePoint> MeasureTemperaturePointDbSet { get; set; }

        /// <summary>
        /// 视频巡检任务单
        /// </summary>
        public virtual DbSet<InspectionCard> InspectionCardDbSet { get; set; }

        /// <summary>
        /// 视频巡检任务项
        /// </summary>
        public virtual DbSet<InspectionItem> InspectionItemDbSet { get; set; }

        /// <summary>
        /// 视频巡检计划任务d
        /// </summary>
        public virtual DbSet<InspectionPlanTask> InspectionPlanTaskDbSet { get; set; }
        /// <summary>
        /// 摄像机权限
        /// </summary>

        public virtual DbSet<CameraAuthentication> CameraAuthenticationDbSet { get; set; }
        /// <summary>
        /// 巡检灯控
        /// </summary>

        public virtual DbSet<LightingControl> LightingControlDbSet { get; set; }
        /// <summary>
        /// 遥控计划配置表
        /// </summary>
        public virtual DbSet<TeleCommandPlanSetting> TeleCommandPlanSettingDbSet { get; set; }
        /// <summary>
        /// 遥控计划时间配置表
        /// </summary>
        public virtual DbSet<TeleCommandPlanTime> TeleCommandPlanTimeDbSet { get; set; }
        /// <summary>
        /// 遥控计划项
        /// </summary>
        public virtual DbSet<TeleCommandSettingItem> TeleCommandSettingItemDbSet { get; set; }


        #endregion 视频相关

        #region 监测数据相关
        /// <summary>
        /// 报警种类
        /// </summary>
        public virtual DbSet<DMAlarmCategory> DMAlarmCategoryDbSet { get; set; }
        /// <summary>
        /// 遥测模板表
        /// </summary>
        public virtual DbSet<TelemeteringTemplate> TelemeteringTemplateDbSet { get; set; }
        /// <summary>
        /// 遥测模板表
        /// </summary>
        public virtual DbSet<TelemeteringAlarmTemplate> TelemeteringAlarmTemplateDbSet { get; set; }
        /// <summary>
        /// 遥信模板表
        /// </summary>
        public virtual DbSet<TelesignalisationTemplate> TelesignalisationTemplateDbSet { get; set; }
        /// <summary>
        /// 遥控模板表
        /// </summary>
        public virtual DbSet<TelecommandTemplate> TelecommandTemplateDbSet { get; set; }
        /// <summary>
        /// 遥控配置表
        /// </summary>
        public virtual DbSet<TelecommandConfiguration> TelecommandConfigurationDbSet { get; set; }

        /// <summary>
        /// 遥测配置表
        /// </summary>
        public virtual DbSet<TelemeteringConfiguration> TelemeteringConfigurationDbSet { get; set; }

        /// <summary>
        /// 遥测报警策略表
        /// </summary>
        public virtual DbSet<TelemeteringAlarmStrategy> TelemeteringAlarmStrategyDbSet { get; set; }

        /// <summary>
        /// 遥信配置表
        /// </summary>
        public virtual DbSet<TelesignalisationConfiguration> TelesignalisationConfigurationDbSet { get; set; }

        /// <summary>
        /// 联动策略
        /// </summary>
        public virtual DbSet<LinkageStrategy> LinkageStrategyDbSet { get; set; }

        /// <summary>
        /// 联动策略条件
        /// </summary>
        public virtual DbSet<LinkageCondition> LinkageConditionDbSet { get; set; }

        /// <summary>
        /// 联动策略执行的活动
        /// </summary>
        public virtual DbSet<LinkageExecuteActivity> LinkageExecuteActivityDbSet { get; set; }

        #endregion 监测数据相关

        #region 移动检测 机器人
        /// <summary>
        /// 机器人信息
        /// </summary>
        public virtual DbSet<RobotInfo> RobotInfoDbSet { get; set; }

        /// <summary>
        /// 机器人设备点位信息
        /// </summary>
        public virtual DbSet<RobotDeviceInfo> RobotDeviceInfoDbSet { get; set; }

        /// <summary>
        /// 机器人巡检任务
        /// </summary>
        public virtual DbSet<RobotTask> RobotTaskDbSet { get; set; }
        /// <summary>
        /// 机器人巡检任务
        /// </summary>
        public virtual DbSet<RobotTaskItemLink> RobotTaskItemDbSet { get; set; }
        #endregion

        #region 客户端配置
        /// <summary>
        /// 三维配置
        /// </summary>
        public virtual DbSet<CCThreeDimension> CCThreeDimensionDbSet { get; set; }
        /// <summary>
        /// 图像识别配置
        /// </summary>
        public virtual DbSet<PatternRecognitionConfigutration> PatternRecognitionConfigutrationDbSet { get; set; }
        /// <summary>
        /// 自检信息策略
        /// </summary>
        public virtual DbSet<SelfCheckingConfiguration> SelfCheckingConfigurationDbSet { get; set; }

        /// <summary>
        /// 多维信息核对
        /// </summary>
        public virtual DbSet<MultidimensionalCheck> MultidimensionalCheckDbSet { get; set; }
        /// <summary>
        /// 多维信息核对时刻表
        /// </summary>
        public virtual DbSet<MultidimensionalCheckSchedule> MultidimensionalCheckScheduleDbSet { get; set; }

        #endregion

        #region 通用数据类
        /// <summary>
        /// 名称与文本
        /// </summary>
        public virtual DbSet<NameDateText> NameDateTextDbSet { get; set; }
        #endregion

        #region 保护装置扩展
        public virtual DbSet<ProtectionDeviceGateway> ProtectionDeviceGatewayDbSet { get; set; }
        public virtual DbSet<ProtectionDeviceInfo> ProtectionDeviceInfoDbSet { get; set; }
        public virtual DbSet<ProtectionDeviceType> ProtectionDeviceTypeDbSet { get; set; }
        /// <summary>
        /// 装置中的板卡信息
        /// </summary>
        public virtual DbSet<BoardCardInfo> BoardCardInfoDbSet { get; set; }
        /// <summary>
        /// 保护装置更换记录
        /// </summary>
        public virtual DbSet<ProtectionDeviceHistory> ProtectionDeviceHistoryDbSet { get; set; }
        /// <summary>
        /// 板卡历史记录
        /// </summary>
        public virtual DbSet<BoardCardHistory> BoardCardHistoryDbSet { get; set; }
        /// <summary>
        /// 二次回路
        /// </summary>
        public virtual DbSet<SecondaryCircuit> SecondaryCircuitDbSet { get; set; }
        /// <summary>
        /// 二次回路运算表达式
        /// </summary>
        public virtual DbSet<SecondaryCircuitLogicExpression> SecondaryCircuitLogicExpressionDbSet { get; set; }
        /// <summary>
        /// 二次回路关联保护装置
        /// </summary>
        public virtual DbSet<SecondaryCircuitProtectionDevice> SecondaryCircuitProtectionDeviceDbSet { get; set; }


        public virtual DbSet<ProtectionSetting> ProtectionSettingtDbSet { get; set; }
        public virtual DbSet<ProtectionSettingType> ProtectionSettingTypeDbSet { get; set; }
        public virtual DbSet<IntervalEquipmentInfo> IntervalEquipmentInfoDbSet { get; set; }
        public virtual DbSet<SubstationInterval> SubstationIntervalDbSet { get; set; }

        #endregion
        public virtual DbSet<EquipmentDataCategoryBase> EquipmentDataCategoryBaseDbSet { get; set; }

        public virtual DbSet<EquipmentIndicatorComment> EquipmentIndicatorCommentDbSet { get; set; }
        public virtual DbSet<EquipmentIndicatorConfig> EquipmentIndicatorConfigDbSet { get; set; }
        /// <summary>
        /// URL配置表
        /// </summary>
        public virtual DbSet<AppUrlConfiguration> UrlConfigurationDbSet { get; set; }

        public virtual DbSet<EnergyConsumptionConfig> EnergyConsumptionConfigDbSet { get; set; }
        public virtual DbSet<EnergyConsumptionDevice> EnergyConsumptionDeviceDbSet { get; set; }
        public virtual DbSet<EnergyOperationCriteria> EnergyOperationCriteriaDbSet { get; set; }
            
    }
}