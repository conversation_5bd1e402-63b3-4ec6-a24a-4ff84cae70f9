﻿using CodeFirstExistingDatabaseSample.ISMS_BASE;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using YunDa.ISAS.Core;
using YunDa.ISAS.Core.Configuration;
using YunDa.ISAS.Core.Web;

namespace CodeFirstExistingDatabaseSample
{
    /* This class is needed to run EF Core PMC commands. Not used anywhere else */

    public class ISMSDbContextFactory : IDesignTimeDbContextFactory<ISMS_BASEContext>
    {
        public ISMS_BASEContext CreateDbContext(string[] args)
        {
            var builder = new DbContextOptionsBuilder<ISMS_BASEContext>();
            var configuration = ISASConfiguration.Get(WebContentDirectoryFinder.CalculateContentRootFolder());
           
            DbContextOptionsConfigurer.Configure(
                builder,
                configuration.GetConnectionString(ISASConsts.ISMS_BASESqlServerSettingStringKey)
            );
            return new ISMS_BASEContext(builder.Options);
        }
    }

    /* This class is needed to run EF Core PMC commands. Not used anywhere else */
}