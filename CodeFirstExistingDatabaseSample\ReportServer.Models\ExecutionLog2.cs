﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ExecutionLog2
    {
        public string InstanceName { get; set; } = null!;
        public string? ReportPath { get; set; }
        public string? UserName { get; set; }
        public string? ExecutionId { get; set; }
        public string RequestType { get; set; } = null!;
        public string? Format { get; set; }
        public string? Parameters { get; set; }
        public string ReportAction { get; set; } = null!;
        public DateTime TimeStart { get; set; }
        public DateTime TimeEnd { get; set; }
        public int TimeDataRetrieval { get; set; }
        public int TimeProcessing { get; set; }
        public int TimeRendering { get; set; }
        public string Source { get; set; } = null!;
        public string Status { get; set; } = null!;
        public long ByteCount { get; set; }
        public long RowCount { get; set; }
        public string? AdditionalInfo { get; set; }
    }
}
