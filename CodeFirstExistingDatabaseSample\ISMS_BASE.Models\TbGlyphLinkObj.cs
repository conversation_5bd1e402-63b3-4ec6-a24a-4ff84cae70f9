﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbGlyphLinkObj
    {
        public string Id { get; set; } = null!;
        public string GlyphId { get; set; } = null!;
        public string LinkObjName { get; set; } = null!;
        public string LinkObjType { get; set; } = null!;
        public string LinkObjPos { get; set; } = null!;
        public string? LinkObjPortal { get; set; }
        public string? LinkObjParam { get; set; }

        public virtual TbGlyph Glyph { get; set; } = null!;
    }
}
