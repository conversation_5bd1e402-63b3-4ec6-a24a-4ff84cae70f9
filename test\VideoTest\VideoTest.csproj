﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net6.0-windows</TargetFramework>
		<UseWPF>true</UseWPF>
		<UseWindowsForms>true</UseWindowsForms>
		<Platforms>AnyCPU;x64;x86</Platforms>
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
	  <DefineConstants>DEBUG;TRACE</DefineConstants>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
	  <DefineConstants>DEBUG;TRACE</DefineConstants>
	  <PlatformTarget>x86</PlatformTarget>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
	  <PlatformTarget>AnyCPU</PlatformTarget>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
	  <PlatformTarget>AnyCPU</PlatformTarget>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
	  <PlatformTarget>x86</PlatformTarget>
	</PropertyGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\src\YunDa.Util\VideoSurveillanceAdapter\VideoSurveillanceAdapter.csproj" />
	</ItemGroup>

</Project>