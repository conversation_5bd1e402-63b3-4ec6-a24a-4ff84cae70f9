﻿<Window x:Class="Send104DataForm.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Send104DataForm"
        mc:Ignorable="d"
        Title="MainWindow" Height="450" Width="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
            <TextBlock Text="设备地址:" VerticalAlignment="Center"/>
            <TextBox x:Name="DevAddrTextBox" Text="{Binding DevAddr}" Width="50" Margin="5"/>

            <TextBlock Text="扇区号:" VerticalAlignment="Center" Margin="10"/>
            <TextBox x:Name="CpuSectorTextBox" Text="{Binding CpuSector}" Width="50" Margin="5"/>

            <TextBlock Text="起始信息地址:" VerticalAlignment="Center" Margin="10"/>
            <TextBox x:Name="StartInfoAddrTextBox" Text="{Binding StartInfoAddr}" Width="50" Margin="5"/>

            <TextBlock Text="信息数量:" VerticalAlignment="Center" Margin="10"/>
            <TextBox x:Name="InfoCountTextBox" Text="{Binding InfoCount}" Width="50" Margin="5"/>
            <TextBlock Text="发送时间间隔ms:" VerticalAlignment="Center" Margin="10"/>
            <TextBox x:Name="IntervalTextBox" Text="{Binding Interval}" Width="50" Margin="5"/>
            <TextBlock Text="模拟装置地址:" VerticalAlignment="Center" Margin="10"/>
            <TextBox x:Name="UrlTextBox" Text="{Binding Url}" Width="80" Margin="5"/>
        </StackPanel>

        <Button Content="开始测试" Click="StartTest_Click" Grid.Row="1" Margin="5"/>
        <Button Content="停止测试" Click="StopTest_Click" Grid.Row="2" Margin="5"/>
        <ScrollViewer    VerticalScrollBarVisibility="Auto" Grid.Row="3" MaxHeight="500">
            <RichTextBox x:Name="InfoTextBlock"    Margin="5,5,5,-130"/>
        </ScrollViewer>

    </Grid>
</Window>
