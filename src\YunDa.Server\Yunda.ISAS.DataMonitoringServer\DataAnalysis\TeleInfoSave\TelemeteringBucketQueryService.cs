using Abp.Dependency;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Model;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.MongoDB.Repositories;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace Yunda.SOMS.DataMonitoringServer.DataAnalysis.TeleInfoSave
{
    /// <summary>
    /// 遥测数据分桶查询服务
    /// </summary>
    public class TelemeteringBucketQueryService : ISingletonDependency
    {
        private readonly IMongoDbRepository<TelemeteringBucket, Guid> _telemeteringBucketRepository;
        private readonly IMongoDbRepository<TelemeteringStatisticsBucket, Guid> _telemeteringStatisticsBucketRepository;

        public TelemeteringBucketQueryService(
            IMongoDbRepository<TelemeteringBucket, Guid> telemeteringBucketRepository,
            IMongoDbRepository<TelemeteringStatisticsBucket, Guid> telemeteringStatisticsBucketRepository)
        {
            _telemeteringBucketRepository = telemeteringBucketRepository;
            _telemeteringStatisticsBucketRepository = telemeteringStatisticsBucketRepository;
        }

        /// <summary>
        /// 查询指定时间范围内的实时数据
        /// </summary>
        /// <param name="telemeteringId">遥测ID</param>
        /// <param name="dataSourceCategory">数据源类型</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>遥测数据列表</returns>
        public async Task<List<TelemeteringModel>> QueryRealTimeData(
            Guid telemeteringId,
            int dataSourceCategory,
            DateTime startTime,
            DateTime endTime)
        {
            try
            {
                List<TelemeteringModel> result = new List<TelemeteringModel>();
                
                // 计算需要查询的月份范围
                var months = GetMonthsBetween(startTime, endTime);
                
                foreach (var yearMonth in months)
                {
                    // 设置集合名称 - 按年月分表
                    string category = ((DataSourceCategoryEnum)dataSourceCategory).ToString();
                    _telemeteringBucketRepository.CollectionName = $"{nameof(TelemeteringBucket)}_{category}_{yearMonth}";

                    // 查询条件
                    var filter = Builders<TelemeteringBucket>.Filter.And(
                        Builders<TelemeteringBucket>.Filter.Eq(x => x.TelemeteringConfigurationId, telemeteringId),
                        Builders<TelemeteringBucket>.Filter.Lte(x => x.StartTime, endTime),
                        Builders<TelemeteringBucket>.Filter.Gte(x => x.EndTime, startTime)
                    );

                    // 执行查询
                    var buckets =  _telemeteringBucketRepository.GetAllIncludeToFindFluent(filter).ToList();
                    
                    if (buckets != null && buckets.Any())
                    {
                        foreach (var bucket in buckets)
                        {
                            // 筛选时间范围内的数据点
                            var points = bucket.Measurements
                                .Where(p => p.Timestamp >= startTime && p.Timestamp <= endTime)
                                .OrderBy(p => p.Timestamp)
                                .ToList();

                            // 转换为TelemeteringModel
                            foreach (var point in points)
                            {
                                result.Add(new TelemeteringModel
                                {
                                    Id = bucket.TelemeteringConfigurationId,
                                    Name = bucket.Name,
                                    EquipmentInfoId = bucket.EquipmentInfoId,
                                    ResultTime = point.Timestamp,
                                    ResultValue = point.Value,
                                    Unit = bucket.Unit,
                                    DataSourceCategory = (DataSourceCategoryEnum?)bucket.DataSourceCategory
                                });
                            }
                        }
                    }
                }

                return result.OrderBy(x => x.ResultTime).ToList();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[QueryRealTimeData] 查询分桶数据异常: {ex.Message}", ex);
                return new List<TelemeteringModel>();
            }
        }

        /// <summary>
        /// 查询指定时间范围内的统计数据
        /// </summary>
        /// <param name="telemeteringId">遥测ID</param>
        /// <param name="interval">时间间隔</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="statisticsType">统计类型</param>
        /// <returns>统计结果列表</returns>
        public async Task<List<TelemeteringStatisticsResult>> QueryStatisticsData(
            Guid telemeteringId,
            FixedIntervalEnum interval,
            DateTime startTime,
            DateTime endTime,
            StatisticsTypeEnum statisticsType = StatisticsTypeEnum.RealTime)
        {
            try
            {
                List<TelemeteringStatisticsResult> result = new List<TelemeteringStatisticsResult>();
                
                // 计算需要查询的年份范围
                var years = GetYearsBetween(startTime, endTime);
                
                foreach (var year in years)
                {
                    // 设置集合名称 - 按年份和间隔类型分表
                    _telemeteringStatisticsBucketRepository.CollectionName = $"{nameof(TelemeteringStatisticsBucket)}_{interval}_{year}";

                    // 查询条件
                    var filter = Builders<TelemeteringStatisticsBucket>.Filter.And(
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.TelemeteringConfigurationId, telemeteringId),
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.IntervalType, interval),
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.Year, year)
                    );

                    // 执行查询
                    var buckets =  _telemeteringStatisticsBucketRepository.GetAllIncludeToFindFluent(filter).ToList();
                    
                    if (buckets != null && buckets.Any())
                    {
                        foreach (var bucket in buckets)
                        {
                            // 筛选时间范围内的统计结果
                            var stats = bucket.Results
                                .Where(r => r.StatisticsDateTime >= startTime && 
                                           r.StatisticsDateTime <= endTime && 
                                           r.StatisticsType == statisticsType)
                                .OrderBy(r => r.StatisticsDateTime)
                                .ToList();

                            // 转换为TelemeteringStatisticsResult
                            foreach (var stat in stats)
                            {
                                result.Add(new TelemeteringStatisticsResult
                                {
                                    TelemeteringConfigurationId = bucket.TelemeteringConfigurationId,
                                    Name = bucket.Name,
                                    EquipmentInfoName = bucket.EquipmentInfoName,
                                    ResultValue = stat.ResultValue,
                                    Unit = bucket.Unit,
                                    ResultTime = stat.ResultTime,
                                    StatisticsType = stat.StatisticsType,
                                    IntervalType = bucket.IntervalType,
                                    StatisticsDateTime = stat.StatisticsDateTime,
                                    DataCount = stat.DataCount
                                });
                            }
                        }
                    }
                }

                return result.OrderBy(x => x.StatisticsDateTime).ToList();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[QueryStatisticsData] 查询统计分桶数据异常: {ex.Message}", ex);
                return new List<TelemeteringStatisticsResult>();
            }
        }

        /// <summary>
        /// 获取两个日期之间的月份列表（格式：yyyyMM）
        /// </summary>
        private List<string> GetMonthsBetween(DateTime startDate, DateTime endDate)
        {
            List<string> months = new List<string>();
            
            DateTime current = new DateTime(startDate.Year, startDate.Month, 1);
            DateTime end = new DateTime(endDate.Year, endDate.Month, 1);
            
            while (current <= end)
            {
                months.Add(current.ToString("yyyyMM"));
                current = current.AddMonths(1);
            }
            
            return months;
        }

        /// <summary>
        /// 获取两个日期之间的年份列表
        /// </summary>
        private List<int> GetYearsBetween(DateTime startDate, DateTime endDate)
        {
            List<int> years = new List<int>();
            
            for (int year = startDate.Year; year <= endDate.Year; year++)
            {
                years.Add(year);
            }
            
            return years;
        }

        /// <summary>
        /// 获取遥测点的聚合统计数据
        /// </summary>
        /// <param name="telemeteringId">遥测ID</param>
        /// <param name="dataSourceCategory">数据源类型</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>聚合统计结果</returns>
        public async Task<TelemeteringAggregateResult> GetAggregateStatistics(
            Guid telemeteringId,
            int dataSourceCategory,
            DateTime startTime,
            DateTime endTime)
        {
            try
            {
                // 查询原始数据
                var data = await QueryRealTimeData(telemeteringId, dataSourceCategory, startTime, endTime);
                
                if (data == null || !data.Any())
                {
                    return null;
                }
                
                // 计算聚合统计值
                var result = new TelemeteringAggregateResult
                {
                    TelemeteringId = telemeteringId,
                    StartTime = startTime,
                    EndTime = endTime,
                    Count = data.Count,
                    MinValue = data.Min(x => x.ResultValue),
                    MaxValue = data.Max(x => x.ResultValue),
                    AvgValue = data.Average(x => x.ResultValue),
                    SumValue = data.Sum(x => x.ResultValue)
                };
                
                // 计算标准差
                if (data.Count > 1)
                {
                    float mean = result.AvgValue;
                    float sumOfSquaresOfDifferences = data.Sum(x => (x.ResultValue - mean) * (x.ResultValue - mean));
                    result.StdDeviation = (float)Math.Sqrt(sumOfSquaresOfDifferences / data.Count);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"[GetAggregateStatistics] 获取聚合统计数据异常: {ex.Message}", ex);
                return null;
            }
        }
    }

    /// <summary>
    /// 遥测聚合统计结果
    /// </summary>
    public class TelemeteringAggregateResult
    {
        /// <summary>
        /// 遥测ID
        /// </summary>
        public Guid TelemeteringId { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 数据点数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public float MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public float MaxValue { get; set; }

        /// <summary>
        /// 平均值
        /// </summary>
        public float AvgValue { get; set; }

        /// <summary>
        /// 总和
        /// </summary>
        public float SumValue { get; set; }

        /// <summary>
        /// 标准差
        /// </summary>
        public float StdDeviation { get; set; }
    }
} 