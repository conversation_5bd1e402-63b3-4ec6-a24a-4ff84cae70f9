﻿using Abp.Auditing;
using Abp.Authorization;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Configuration;
using YunDa.ISAS.Application.Core.OfficeHelper;
using YunDa.ISAS.Application.Core.Session;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringAlarmStrategyDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringConfigurationDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto;
using YunDa.ISAS.DataTransferObject.GeneralInformation.EquipmentInfoDto;
using YunDa.ISAS.DataTransferObject.GeneralInformation.TransformerSubstationDto;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Entities.MobileSurveillance;
using YunDa.ISAS.Entities.MySQL.DataMonitoring;
using YunDa.ISAS.Entities.VideoSurveillance;
using YunDa.ISAS.EntityFrameworkCore.EntityFrameworkCore;
using Abp.Domain.Uow;
namespace YunDa.ISAS.Application.GeneralInformation
{
    /// <summary>
    /// 变电所信息
    /// </summary>
    [Description("变电所管理服务")]
    public class TransformerSubstationAppService : ISASAppServiceBase, ITransformerSubstationAppService
    {
        private readonly IRepository<EquipmentType, Guid> _equipmentTypeRepository;
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoRepository;
        private readonly IRepository<TelemeteringConfiguration, Guid> _telemeteringConfigurationResitory;
        private readonly IRepository<TelesignalisationConfiguration, Guid> _telesignalisationConfigurationResitory;
        private readonly IRepository<TelecommandConfiguration, Guid> _telecommandConfigurationResitory;
        private readonly IRepository<TelemeteringAlarmStrategy, Guid> _telemeteringAlarmStrategyResitory;
        private readonly IRepository<TransformerSubstation, Guid> _transformerSubstationRepository;
        private readonly IRepository<VideoDev, Guid> _videoDevRepository;
        private readonly IRepository<PresetPoint, Guid> _presetPointRepository;
        private readonly IRepository<LinkageStrategy, Guid> _linkageStrategyRepository;
        private readonly IRepository<InspectionItem, Guid> _inspectionItemRepository;
        private readonly IRepository<InspectionCard, Guid> _inspectionCardRepository;
        private readonly IRepository<RobotDeviceInfo, Guid> _robotPresetRepository;
        private readonly IRepository<RobotInfo, Guid> _robotInfoRepository;
        private readonly IRepository<DMAlarmCategory, Guid> _dmalarmCategoryResitory;
        private readonly IRepository<SelfCheckingConfiguration, Guid> _selfCheckingConfigurationResitory;
        //private readonly ISASDbContext _context;

        public TransformerSubstationAppService(IRepository<TransformerSubstation, Guid> transformerSubstationRepository,
            IRepository<EquipmentType, Guid> equipmentTypeRepository,
            IRepository<EquipmentInfo, Guid> equipmentInfoRepository,
            IRepository<TelemeteringConfiguration, Guid> telemeteringConfigurationResitory,
            IRepository<TelesignalisationConfiguration, Guid> telesignalisationConfigurationResitory,
            IRepository<TelecommandConfiguration, Guid> telecommandConfigurationResitory,
            IRepository<VideoDev, Guid> videoDevRepository,
            IRepository<PresetPoint, Guid> presetPointRepository,
            IRepository<LinkageStrategy, Guid> linkageStrategyRepository,
            IRepository<InspectionItem, Guid> inspectionItemRepository,
            IRepository<InspectionCard, Guid> inspectionCardRepository,
            IRepository<RobotDeviceInfo, Guid> robotPresetRepository,
            IAppServiceConfiguration appServiceConfiguration,
            IRepository<RobotInfo, Guid> robotInfoRepository,
            IRepository<SelfCheckingConfiguration, Guid> selfCheckingConfigurationResitory,
            IRepository<TelemeteringAlarmStrategy, Guid> telemeteringAlarmStrategyResitory,
            IRepository<DMAlarmCategory, Guid> dmalarmCategoryResitory,
            //ISASDbContext context,
        ISessionAppService sessionAppService) :
            base(sessionAppService)
        {
            _transformerSubstationRepository = transformerSubstationRepository;
            _equipmentTypeRepository = equipmentTypeRepository;
            _equipmentInfoRepository = equipmentInfoRepository;
            _telemeteringConfigurationResitory = telemeteringConfigurationResitory;
            _telesignalisationConfigurationResitory = telesignalisationConfigurationResitory;
            _telecommandConfigurationResitory = telecommandConfigurationResitory;
            _videoDevRepository = videoDevRepository;
            _presetPointRepository = presetPointRepository;
            _linkageStrategyRepository = linkageStrategyRepository;
            _inspectionItemRepository = inspectionItemRepository;
            _inspectionCardRepository = inspectionCardRepository;
            _robotPresetRepository = robotPresetRepository;
            _robotInfoRepository = robotInfoRepository;
            _telemeteringAlarmStrategyResitory = telemeteringAlarmStrategyResitory;
            _dmalarmCategoryResitory = dmalarmCategoryResitory;
            _selfCheckingConfigurationResitory = selfCheckingConfigurationResitory;
            //_context = context;
        }

        #region 增/改

        /// <summary>
        /// 变电所增加或修改
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, Audited, Description("变电所增加或修改")]
        public async Task<RequestResult<TransformerSubstationOutput>> CreateOrUpdateAsync(
            EditTransformerSubstationInput input)
        {
            if (input == null) return new RequestResult<TransformerSubstationOutput>();
            RequestResult<TransformerSubstationOutput> rst;
            var currentUser = base.GetCurrentUser();
            if (input.Id != null)
            {
                input.LastModificationTime = DateTime.Now;
                input.LastModifierUserId = currentUser.Id;
                rst = await UpdateAsync(input).ConfigureAwait(false);
            }
            else
            {
                input.CreationTime = DateTime.Now;
                input.CreatorUserId = currentUser.Id;
                rst = await CreateAsync(input).ConfigureAwait(false);
            }

            return rst;
        }

        private async Task<RequestResult<TransformerSubstationOutput>> UpdateAsync(EditTransformerSubstationInput input)
        {
            var rst = new RequestResult<TransformerSubstationOutput>();
            try
            {
                var data = await _transformerSubstationRepository.FirstOrDefaultAsync(u => u.Id == input.Id)
                    .ConfigureAwait(false);

                ObjectMapper.Map(input, data);
                rst.Flag = true;
                rst.ResultData = ObjectMapper.Map<TransformerSubstationOutput>(data);
                ;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }

            return rst;
        }

        private async Task<RequestResult<TransformerSubstationOutput>> CreateAsync(EditTransformerSubstationInput input)
        {
            var rst = new RequestResult<TransformerSubstationOutput>();
            try
            {
                var data = ObjectMapper.Map<TransformerSubstation>(input);
                data = await _transformerSubstationRepository.InsertAsync(data).ConfigureAwait(false);
                rst.ResultData = ObjectMapper.Map<TransformerSubstationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }

            return rst;
        }

        #endregion 增/改

        #region 删

        /// <summary>
        ///  删除单个变电所
        /// </summary>
        /// <param name="id">变电所id</param>
        /// <returns></returns>
        [HttpGet, Audited, Description("删除单个变电所")]
        public async Task<RequestEasyResult> DeleteByIdAsync(Guid id)
        {
            var rst = new RequestEasyResult();
            try
            {
                await _transformerSubstationRepository.DeleteAsync(item => item.Id == id).ConfigureAwait(false);
                rst.Flag = true;
            }
            catch
            {
                rst.Flag = false;
            }

            return rst;
        }

        /// <summary>
        /// 删除多个变电所
        /// </summary>
        /// <param name="ids">变电所id集合</param>
        /// <returns></returns>
        [HttpPost, Audited, Description("删除多个变电所")]
        public async Task<RequestEasyResult> DeleteByIdsAsync(List<Guid> ids)
        {
            var rst = new RequestEasyResult();
            try
            {
                await _transformerSubstationRepository.DeleteAsync(item => ids.Contains(item.Id)).ConfigureAwait(false);
                ;
                rst.Flag = true;
            }
            catch
            {
                rst.Flag = false;
            }

            return rst;
        }

        #endregion 删

        #region 查

        /// <summary>
        /// 查询变电所信息
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [HttpPost,  Description("查询变电所信息")]
        public RequestPageResult<TransformerSubstationOutput> FindDatas(
            PageSearchCondition<TransformerSubstationSearchConditionInput> searchCondition)
        {
            var rst = new RequestPageResult<TransformerSubstationOutput>();
            if (searchCondition == null) return rst;
            try
            {
                var datas = searchCondition.SearchCondition.IsNeedChildren
                    ? _transformerSubstationRepository.GetAllIncluding(Station => Station.VideoDevs)
                    : _transformerSubstationRepository.GetAll();
                //datas = datas.WhereIf( !string.IsNullOrWhiteSpace( searchCondition.SearchCondition.SubstationName),
                //    sub => sub.SubstationName.Contains(searchCondition.SearchCondition.SubstationName,
                //        StringComparison.OrdinalIgnoreCase));
                datas = datas.WhereIf(searchCondition.SearchCondition.Id.HasValue,
                    sub => sub.Id == searchCondition.SearchCondition.Id);

                datas = datas.WhereIf(searchCondition.SearchCondition.PowerSupplyLineId != null,
                    sub => (sub.PowerSupplyLineId ?? new Guid()).Equals(searchCondition.SearchCondition
                        .PowerSupplyLineId));

                var rstDatas = datas.ToList().AsQueryable();
                if (!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.SubstationName))
                {
                    rstDatas = rstDatas
                     .Where(item => item.SubstationName.ToArray().
                     Intersect(searchCondition.SearchCondition.SubstationName.ToArray()).Count() ==
                     searchCondition.SearchCondition.SubstationName.ToArray().Distinct().Count()).AsQueryable();
                }
                //获取条件下所有数量
                rst.TotalCount = rstDatas.Count();
                //排序
                rstDatas = rstDatas.OrderBy(t => t.SeqNo).ThenBy(t => t.SubstationName);

                //分页
                var skipCount = searchCondition.PageIndex <= 0
                    ? -1
                    : (searchCondition.PageIndex - 1) * searchCondition.PageSize;
                if (skipCount != -1)
                    rstDatas = rstDatas.PageBy(skipCount, searchCondition.PageSize);
                var dataList = rstDatas.ToList();
                dataList.ForEach(d =>
                {
                    SetMasterStationInfoForSubstation(ref d);
                });
                rst.ResultData = ObjectMapper.Map<List<TransformerSubstationOutput>>(dataList);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);
                rst.Flag = false;
            }

            return rst;
        }

        /// <summary>
        /// 获取变电所普通设备层级结构
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [HttpPost]
        [ShowApi]
        public RequestResult<List<TreeModelOutput>> GetTransformerSubstationTreeNode(TransformerSubstationSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<TreeModelOutput>>();
            if (searchCondition == null) return rst;
            try
            {
                var trans = _transformerSubstationRepository.GetAll();
                var subList = trans.WhereIf(searchCondition.Id.HasValue,
                         t => t.Id == searchCondition.Id)
                     .WhereIf(searchCondition.PowerSupplyLineId.HasValue,
                         t => t.PowerSupplyLineId == (Guid)searchCondition.PowerSupplyLineId)
                     .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.SubstationName),
                         t => t.SubstationName == searchCondition.SubstationName)
                     .Where(t => t.IsActive).ToList();
                subList.ForEach(d =>
                {
                    SetMasterStationInfoForSubstation(ref d);
                });
                rst.ResultData = subList.Select(sub => new TreeModelOutput
                {
                    Id = sub.Id,
                    Type = TreeModelType.TransformerSubstation,
                    Text = sub.SubstationName,
                    NodeObj = ObjectMapper.Map<TransformerSubstationProperty>(sub)
                }).ToList();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);
                rst.Flag = false;
            }

            return rst;
        }
      
        /// <summary>
        /// 获取变电所的监测数据配置项
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [ShowApi]
        [HttpPost]
        public RequestResult<List<MonitoringInfoOutput>> GetMonitoringInfo(MonitoringInfoSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<MonitoringInfoOutput>>();
            if (searchCondition == null || !searchCondition.TransformerSubstationId.HasValue) return rst;
            string eTypeName = searchCondition.MonitoringGroupType == MonitoringGroupTypeEnum.DLHJ ? EquipmentType.DLHJ : (searchCondition.MonitoringGroupType == MonitoringGroupTypeEnum.ZXJC ? EquipmentType.ZXJC : "");
            if (string.IsNullOrWhiteSpace(eTypeName)) return rst;
            rst.Flag = false;
            try
            {
                var sEquipmentType = _equipmentTypeRepository.GetAll().Where(eT => eT.IsActive && eTypeName.Equals(eT.Name)).FirstOrDefault();
                if (sEquipmentType == null) return rst;
                var equipmentTypes = _equipmentTypeRepository.GetAll().Where(eT => eT.IsActive);
                //获取类型级设备类型
                var MonitoringInfos = equipmentTypes.Where(eT => eT.EquipmentTypeId == sEquipmentType.Id && eT.IsActive).Select(eT => new MonitoringInfoOutput
                {
                    TypeId = eT.Id,
                    SeqNo = eT.SeqNo,
                    TypeName = eT.Name
                }).OrderBy(eT => eT.SeqNo).ToList();
                //获取遥测配置项
                var meters = _telemeteringConfigurationResitory.GetAll().Where(meter => meter.TransformerSubstationId == searchCondition.TransformerSubstationId && meter.IsActive);
                //获取遥信配置项
                var signals = _telesignalisationConfigurationResitory.GetAll().Where(signal => signal.TransformerSubstationId == searchCondition.TransformerSubstationId && signal.IsActive);
                //获取设备
                var equipmentInfos = _equipmentInfoRepository.GetAll().Where(e => e.TransformerSubstationId == searchCondition.TransformerSubstationId && e.IsActive).ToList();
                //获取监控设备
                var monitoringEquipments = equipmentInfos.GroupJoin(meters, e => e.Id, m => m.EquipmentInfoId, (e, ms) => new
                {
                    Id = e.Id,
                    Name = e.Name,
                    SeqNo = e.SeqNo,
                    EquipmentTypeId = e.EquipmentTypeId,
                    IsRemoteControl = e.IsRemoteControl,
                    TelemeteringConfigurations = ObjectMapper.Map<List<TelemeteringConfigurationProperty>>(ms.OrderBy(m => m.SeqNo))
                }).GroupJoin(signals, e => e.Id, s => s.EquipmentInfoId, (e, ss) => new
                {
                    Id = e.Id,
                    Name = e.Name,
                    SeqNo = e.SeqNo,
                    EquipmentTypeId = e.EquipmentTypeId,
                    IsRemoteControl = e.IsRemoteControl,
                    TelemeteringConfigurations = e.TelemeteringConfigurations,
                    TelesignalisationConfigurations = ObjectMapper.Map<List<TelesignalisationConfigurationProperty>>(ss.OrderBy(s => s.SeqNo))
                }).OrderBy(e => e.SeqNo).ToList();
                //获取最终结果
                rst.ResultData = MonitoringInfos.GroupJoin(equipmentTypes, mInfo => mInfo.TypeId, eTs => eTs.EquipmentTypeId, (mInfo, eTs) => new MonitoringInfoOutput
                {
                    TypeId = mInfo.TypeId,
                    SeqNo = mInfo.SeqNo,
                    TypeName = mInfo.TypeName,
                    MonitoringEquipmentTypes = eTs.Select(eT => new
                    {
                        TypeId = eT.Id,
                        SeqNo = eT.SeqNo,
                        TypeName = eT.Name
                    }).GroupJoin(monitoringEquipments, eT => eT.TypeId, e => e.EquipmentTypeId, (eT, es) => new MonitoringEquipmentType
                    {
                        TypeId = eT.TypeId,
                        SeqNo = eT.SeqNo,
                        TypeName = eT.TypeName,
                        MonitoringEquipments = es.Select(e => new MonitoringEquipmentInfoOutput
                        {
                            EquipmentInfoId = e.Id,
                            EquipmentTypeId = eT.TypeId,
                            SeqNo = e.SeqNo,
                            EquipmentInfoName = e.Name,
                            IsRemoteControl = e.IsRemoteControl,
                            GroupType = searchCondition.MonitoringGroupType,
                            Telemeterings = e.TelemeteringConfigurations,
                            Telesignalisations = e.TelesignalisationConfigurations
                        })
                    })
                }).ToList();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

                rst.Flag = false;
            }
            return rst;
        }

        /// <summary>
        /// 根据条件查询变电所下拉框内容
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [AbpAllowAnonymous]
        [HttpPost]
        [ShowApi]
        public RequestResult<List<SelectModelOutput>> FindTransformerSubstationForSelect(SelectTransformerSubstationSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<SelectModelOutput>> { Flag = false };
            try
            {
                var datas = _transformerSubstationRepository.GetAll().Where(sub => sub.IsActive);
                var dataList = datas.WhereIf(searchCondition.PowerSupplyLineId != null, sub => (sub.PowerSupplyLineId ?? new Guid()).Equals(searchCondition.PowerSupplyLineId)).ToList();
                dataList.ForEach(d =>
                {
                    SetMasterStationInfoForSubstation(ref d);
                });
                rst.ResultData = dataList.Select(m => new SelectModelOutput
                {
                    Key = m.Id,
                    Text = m.SubstationName,
                    Value = m.Id.ToString().ToLower(),
                    NodeObj = ObjectMapper.Map<TransformerSubstationProperty>(m)
                }).ToList();

                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);
                rst.Flag = false;

            }
            return rst;
        }
        /// <summary>
        /// 获取主站类型
        /// </summary>
        /// <returns></returns>
        [AbpAllowAnonymous]
        [HttpGet]
        [ShowApi]
        public RequestResult<List<SelectModelOutput>> FindMastStationTypeForSelect()
        {
            var rst = new RequestResult<List<SelectModelOutput>> { Flag = false };
            try
            {
                rst.ResultData = base.GetEnumTypes<MasterStationTypeEnum>();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);
                rst.Flag = false;
            }
            return rst;
        }
        /// <summary>
        /// 根据id查询变电所
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [AbpAllowAnonymous]
        public RequestResult<TransformerSubstationOutput> FindTransformerSubstationById(Guid id)
        {
            var rst = new RequestResult<TransformerSubstationOutput>();
            try
            {
                var data = _transformerSubstationRepository.FirstOrDefault(item => item.Id == id);
                //SetMasterStationInfoForSubstation(ref data);
                rst.ResultData = ObjectMapper.Map<TransformerSubstationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);
                rst.Flag = false;
            }

            return rst;
        }
        /// <summary>
        /// 根据名称查询变电所
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [AbpAllowAnonymous]
        public RequestResult<TransformerSubstationOutput> FindTransformerSubstationByName(string name)
        {
            var rst = new RequestResult<TransformerSubstationOutput>();
            try
            {
                var data = _transformerSubstationRepository.GetAll().AsEnumerable().FirstOrDefault(item => item.SubstationName.Contains(name));
                SetMasterStationInfoForSubstation(ref data);
                rst.ResultData = ObjectMapper.Map<TransformerSubstationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);
                rst.Flag = false;
            }
            return rst;
        }
        
        private void SetMasterStationInfoForSubstation(ref TransformerSubstation sub)
        {
            //if (sub == null) return;
            //RequestResult<MasterStationTypeOutput> mstRst = GetMasterStationType();
            //if (mstRst.Flag)
            //{
            //    sub.MasterStationType = (MasterStationTypeEnum)mstRst.ResultData.Code;
            //    sub.MasterStationAddress = mstRst.ResultData.BaseUrl;
            //}
        }
       
        #endregion 查

        #region 数据配置导入导出

        /// <summary>
        /// 导出遥测、遥信、遥控配置文档
        /// </summary>
        /// <returns></returns>
        [HttpPost, Audited, Description("导出遥测、遥信、遥控配置文档")]
        public FileStreamResult ExportTeleConfigurationExcel(TeleConfigurationExportConditionInput input)
        {
            List<ExcelData> excelDatas = new List<ExcelData>();//配置
            ExcelHandler excelWrite = new DataConfigurationExcelHandle();//执行类
            try
            {
                var telemeteringRepo = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo, t => t.TelemeteringAlarmStrategys)
                     .Where(t => t.IsActive && t.TransformerSubstationId == input.TransformerSubstationId)
                     .WhereIf(input.DataSourceCategory.HasValue,t=>t.DataSourceCategory == input.DataSourceCategory)
                    .OrderBy(t => t.EquipmentType.SeqNo).ThenBy(t => t.EquipmentInfo.SeqNo).ThenBy(t => t.SeqNo).ThenBy(t => t.DispatcherAddress)
                    .ToList();

                var telesignaRepo = _telesignalisationConfigurationResitory.
                    GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo, t => t.DMAlarmCategory, t => t.SelfCheckingConfiguration)
                     .Where(t => t.TransformerSubstationId == input.TransformerSubstationId)
                      .WhereIf(input.DataSourceCategory.HasValue, t => t.DataSourceCategory == input.DataSourceCategory)
                    .OrderBy(t => t.EquipmentType.SeqNo).ThenBy(t => t.EquipmentInfo.SeqNo).ThenBy(t => t.SeqNo).ThenBy(t => t.DispatcherAddress);

                var telecommandRepo = _telecommandConfigurationResitory
                    .GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                    .Where(t => t.TransformerSubstationId == input.TransformerSubstationId)
                     .WhereIf(input.DataSourceCategory.HasValue, t => t.DataSourceCategory == input.DataSourceCategory)
                    .OrderBy(t => t.EquipmentType.SeqNo).ThenBy(t => t.EquipmentInfo.SeqNo).ThenBy(t => t.SeqNo).ThenBy(t => t.DispatcherAddress);

                #region 遥测

                List<TelemeteringConfigurationExcel> telemeteringConfigurationExcels = ObjectMapper.Map<List<TelemeteringConfigurationExcel>>(telemeteringRepo);

                if (telemeteringConfigurationExcels != null)
                {
                    ExcelData excelData = new ExcelData();
                    excelData.SheetName = "遥测";
                    var clumnTexts = new List<string>() {
                        "行号","ID",
                     // "顺序号",  "关联监控设备", "关联监控设备类型", "遥测名称",,
                     //  是否为虚拟装置点位, "信息体地址" "装置地址", "装置扇区号",调度装置地址, 调度扇区号,"调度地址",
                     //  "单位", "系数", "小数位数","上限"，"下限"
                     //"是否保存", "是否可见", "是否传给调度", "是否在用",
                    };
                    var propertyInfos = typeof(TelemeteringConfigurationExcel).GetProperties();
                    foreach (var item in propertyInfos)
                    {
                        var c = item.GetCustomAttributes(typeof(JsonPropertyAttribute), true);
                        if (c.Length > 0)
                        {
                            JsonPropertyAttribute jsonProperty = c[0] as JsonPropertyAttribute;
                            clumnTexts.Add(jsonProperty.PropertyName);
                        }
                    }
                    var clumnWidths = new List<int>();
                    for (int i = 0; i < clumnTexts.Count; i++)
                    {
                        clumnWidths.Add(clumnTexts[i].Length * 4);
                    }
                    excelData.ClumnWidthList = clumnWidths;
                    excelData.AutoClumnWidths = true;
                    excelData.ClumnHeadTextList = clumnTexts;
                    var content = new List<List<string>>();
                    int rowIndex = 1;
                    foreach (var telemetering in telemeteringConfigurationExcels)
                    {
                        if (telemetering.EquipmentInfo == null || telemetering.EquipmentType == null)
                        {
                            continue;
                        }
                        content.Add(new List<string>() {
                            rowIndex.ToString(),telemetering.Id.ToString()
                            ,telemetering.SeqNo.ToString(), 
                            telemetering.EquipmentInfo?.Name,
                            telemetering.EquipmentType?.Name,
                            telemetering.Name,
                            telemetering.IsVirtualDevice?"是":"否",
                            telemetering.IsSelfCheckingValue?"是":"否",
                            telemetering.InfoAddress.ToString(),
                            telemetering.InfoDeviceAddress.ToString(),
                            telemetering.InfoCPUSector.ToString(),
                            telemetering.DeviceAddress.ToString(),
                            telemetering.CPUSector.ToString(),
                            telemetering.DispatcherAddress.ToString(),
                            telemetering.Unit,
                            telemetering.Coefficient.ToString(),
                            telemetering.DecimalDigits.ToString(),
                            telemetering.UpperLimit.ToString(),
                            telemetering.LowerLimit.ToString(),
                            telemetering.DataSourceCategory.ToString(),
                            telemetering.IsSave?"是":"否",
                            telemetering.IsVisible?"是":"否",
                            telemetering.IsSendDispatcher?"是":"否",
                            telemetering.IsActive?"是":"否"
                        });
                        //telemetering.GetType().GetProperties(BindingFlags.GetProperty);
                        rowIndex++;
                    }
                    excelData.ContentInfoList = content;
                    excelDatas.Add(excelData);
                }

                #endregion 遥测

                #region 遥测报警
                if (telemeteringRepo != null && telemeteringRepo.Count() > 0)
                {
                    var dMAlarmCategoryRepo = _dmalarmCategoryResitory.GetAll().ToList();

                    ExcelData excelData = new ExcelData();
                    excelData.SheetName = "遥测报警";
                    var clumnTexts = new List<string>();
                    var propertyInfos = typeof(TelemeteringAlarmStrategyExcel).GetProperties();
                    foreach (var item in propertyInfos)
                    {
                        var c = item.GetCustomAttributes(typeof(JsonPropertyAttribute), true);
                        if (c.Length > 0)
                        {
                            JsonPropertyAttribute jsonProperty = c[0] as JsonPropertyAttribute;
                            clumnTexts.Add(jsonProperty.PropertyName);
                        }
                    }
                    var clumnWidths = new List<int>();
                    for (int i = 0; i < clumnTexts.Count; i++)
                    {
                        clumnWidths.Add(clumnTexts[i].Length * 4);
                    }
                    excelData.ClumnWidthList = clumnWidths;
                    excelData.AutoClumnWidths = true;
                    excelData.ClumnHeadTextList = clumnTexts;
                    var content = new List<List<string>>();
                    foreach (var telemetering in telemeteringRepo)
                    {
                        if (telemetering.EquipmentInfo == null || telemetering.EquipmentType == null)
                        {
                            continue;
                        }
                        if (telemetering.TelemeteringAlarmStrategys != null && telemetering.TelemeteringAlarmStrategys.Count() > 0)
                        {
                            foreach (var item in telemetering.TelemeteringAlarmStrategys)
                            {
                                var dma = dMAlarmCategoryRepo.FirstOrDefault(t => t.Id == item.DMAlarmCategoryId);
                                var sublist = new List<string>();

                                if (dma == null)
                                {
                                    sublist.Add(telemetering.Id.ToString());
                                    sublist.Add(telemetering.Name);
                                    sublist.Add(telemetering.EquipmentType.Name);
                                    sublist.Add(telemetering.EquipmentInfo.Name);
                                    sublist.Add("");
                                    sublist.Add("");
                                    sublist.Add("");
                                    sublist.Add("");
                                    sublist.Add("");
                                    sublist.Add("");
                                    sublist.Add("");
                                    content.Add(sublist);
                                }
                                else
                                {
                                    sublist.Add(telemetering.Id.ToString());
                                    sublist.Add(telemetering.Name);
                                    sublist.Add(telemetering.EquipmentType.Name);
                                    sublist.Add(telemetering.EquipmentInfo.Name);
                                    sublist.Add(item.Id.ToString());
                                    sublist.Add(item.SeqNo.ToString());
                                    sublist.Add(item.MinValue.ToString());
                                    sublist.Add(item.MaxValue.ToString());
                                    sublist.Add(dma.Level.ToString());
                                    sublist.Add(dma.Name.ToString());
                                    sublist.Add(item.IsActive ? "是" : "否");
                                }
                                content.Add(sublist);
                            }
                        }
                        else
                        {
                            var sublist = new List<string>();
                            sublist.Add(telemetering.Id.ToString());
                            sublist.Add(telemetering.Name);
                            sublist.Add(telemetering.EquipmentType.Name);
                            sublist.Add(telemetering.EquipmentInfo.Name);
                            sublist.Add("");
                            sublist.Add("");
                            sublist.Add("");
                            sublist.Add("");
                            sublist.Add("");
                            sublist.Add("");
                            sublist.Add("");
                            content.Add(sublist);
                        }
                    }
                    excelData.ContentInfoList = content;
                    excelDatas.Add(excelData);
                }
                #endregion

                #region 遥信

                List<TelesignalisationConfigurationExcel> telesignalisationConfigurationExcels = ObjectMapper.Map<List<TelesignalisationConfigurationExcel>>(telesignaRepo);
                if (telesignalisationConfigurationExcels != null)
                {
                    ExcelData excelData = new ExcelData();
                    var dmRepo = _dmalarmCategoryResitory.GetAll().ToList();
                    var selfCheckingConfigurationRepo = _selfCheckingConfigurationResitory.GetAll().ToList();

                    excelData.SheetName = "遥信";
                    var clumnTexts = new List<string>() {
                        "行号","ID",
                          // "顺序号", "关联监控设备", "关联监控设备类型", "遥信名称", "信息地址",
                          //  "装置地址", "CPU扇区号", "传给调度的地址", "对象状态", "合闸字符串", "分闸字符串",
                          //"不定字符串","自检策略名称" "是否是关联设备通信状态","通信状态code", "是否保存", "是否可见", "是否传给调度", "是否在用",
                    };
                    var propertyInfos = typeof(TelesignalisationConfigurationExcel).GetProperties();
                    foreach (var item in propertyInfos)
                    {
                        var c = item.GetCustomAttributes(typeof(JsonPropertyAttribute), true);
                        if (c.Length > 0)
                        {
                            JsonPropertyAttribute jsonProperty = c[0] as JsonPropertyAttribute;
                            clumnTexts.Add(jsonProperty.PropertyName);
                        }
                    }
                    var clumnWidths = new List<int>();
                    for (int i = 0; i < clumnTexts.Count; i++)
                    {
                        clumnWidths.Add(clumnTexts[i].Length * 4);
                    }
                    excelData.ClumnWidthList = clumnWidths;
                    excelData.AutoClumnWidths = true;
                    excelData.ClumnHeadTextList = clumnTexts;
                    var content = new List<List<string>>();
                    int rowIndex = 1;
                    foreach (var telesigna in telesignalisationConfigurationExcels)
                    {
                        if (telesigna.EquipmentInfo == null || telesigna.EquipmentType == null)
                        {
                            continue;
                        }
                        var dm = dmRepo.FirstOrDefault(t => t.Id == telesigna.DMAlarmCategoryId);
                        content.Add(new List<string>() {
                            rowIndex.ToString(),
                            telesigna.Id.ToString(),
                            telesigna.SeqNo.ToString(), 
                            telesigna.EquipmentInfo.Name,
                            telesigna.EquipmentType?.Name,
                            telesigna.Name,
                            telesigna.IsVirtualDevice?"是":"否",
                            telesigna.IsSelfCheckingValue?"是":"否",
                            telesigna.InfoAddress.ToString(),
                            telesigna.InfoDeviceAddress.ToString(),
                            telesigna.InfoCPUSector.ToString(),
                            telesigna.DeviceAddress.ToString(),
                            telesigna.CPUSector.ToString(),
                            telesigna.DispatcherAddress.ToString(),
                            base.GetEnumDescription<RemoteTypeEnum>(telesigna.RemoteType.ToString())  ,
                            telesigna.YesContent,
                            telesigna.NoContent,
                            telesigna.UnsurenessContent,
                            telesigna.DataSourceCategory.ToString(),
                            telesigna.SelfCheckingConfiguration?.Name,
                            telesigna.IsCommStatus?"是":"否",
                            telesigna.CommValue.ToString(),
                            dm!=null? dm.Id.ToString():"",
                            dm!=null? dm.Level.ToString():"",
                            dm!=null? dm.Name:"",
                            telesigna.IsSave?"是":"否",
                            telesigna.IsVisible?"是":"否",
                            telesigna.IsSendDispatcher?"是":"否",
                            telesigna.IsActive?"是":"否"
                        });
                        rowIndex++;
                    }
                    excelData.ContentInfoList = content;
                    excelDatas.Add(excelData);

                }

                #endregion 遥信

                #region 遥控

                List<TelecommandConfigurationExcel> telecommandConfigurationExcels = ObjectMapper.Map<List<TelecommandConfigurationExcel>>(telecommandRepo);

                if (telecommandConfigurationExcels != null)
                {
                    ExcelData excelData = new ExcelData();

                    excelData.SheetName = "遥控";
                    var clumnTexts = new List<string>() {
                        "行号","ID",
                      //  "顺序号", "关联监控设备", "关联监控设备类型", "遥信名称", "信息地址",
                      //  "装置地址", "CPU扇区号", "传给调度的地址","遥控类型", "相关遥信", "合闸字符串", "分闸字符串",
                      //"不定字符串",  "是否保存", "是否可见", "是否传给调度", "是否在用",
                    };
                    var propertyInfos = typeof(TelecommandConfigurationExcel).GetProperties();
                    foreach (var item in propertyInfos)
                    {
                        var c = item.GetCustomAttributes(typeof(JsonPropertyAttribute), true);
                        if (c.Length > 0)
                        {
                            JsonPropertyAttribute jsonProperty = c[0] as JsonPropertyAttribute;
                            clumnTexts.Add(jsonProperty.PropertyName);
                        }
                    }
                    var clumnWidths = new List<int>();
                    for (int i = 0; i < clumnTexts.Count; i++)
                    {
                        clumnWidths.Add(clumnTexts[i].Length * 4);
                    }
                    excelData.ClumnWidthList = clumnWidths;
                    excelData.AutoClumnWidths = true;
                    excelData.ClumnHeadTextList = clumnTexts;
                    var content = new List<List<string>>();
                    int rowIndex = 1;
                    foreach (var telecommand in telecommandConfigurationExcels)
                    {
                        if (telecommand.EquipmentInfo == null || telecommand.EquipmentType == null)
                        {
                            continue;
                        }
                        content.Add(new List<string>() {
                            rowIndex.ToString(),
                            telecommand.Id.ToString(),
                            telecommand.SeqNo.ToString(), 
                            telecommand.EquipmentInfo.Name,
                            telecommand.EquipmentType.Name,
                            telecommand.Name,
                            telecommand.IsVirtualDevice?"是":"否",
                            telecommand.InfoAddress.ToString(),
                            telecommand.InfoDeviceAddress.ToString(),
                            telecommand.InfoCPUSector.ToString(),
                            telecommand.DeviceAddress.ToString(),
                            telecommand.CPUSector.ToString(),
                            telecommand.DispatcherAddress.ToString(),
                            base.GetEnumDescription<RemoteTypeEnum>(telecommand.RemoteType.ToString()),
                            telecommand.RelatedTelesignalisationId.ToString(),
                            telecommand.YesContent,
                            telecommand.NoContent,
                            telecommand.UnsurenessContent,
                            telecommand.IsSave?"是":"否",
                            telecommand.IsVisible?"是":"否",
                            telecommand.IsSendDispatcher?"是":"否",
                            telecommand.IsActive?"是":"否"
                        });
                        rowIndex++;
                    }
                    excelData.ContentInfoList = content;
                    excelDatas.Add(excelData);

                }

                #endregion 遥控

                var memoryStream = new MemoryStream();
                excelWrite.WriteExcel(memoryStream, excelDatas);
                memoryStream.Seek(0, SeekOrigin.Begin);

                var fileName = $"数据配置-{input.DataSourceCategory}.xlsx"; // 你想要的文件名
                var contentType = "application/octet-stream";

                var fileStreamResult = new FileStreamResult(memoryStream, contentType);
                fileStreamResult.FileDownloadName = fileName; // 关键代码，ASP.NET Core自带
                return fileStreamResult;

            }
            catch (Exception ex)
            {
                //rst.Message = ex.Message;
                //rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }
            return null;
        }

        /// <summary>
        /// 导入遥测、遥信、遥控配置文档，并修改数据库对应配置项
        /// </summary>
        /// <returns></returns>
        [HttpPost, Audited, Description("导入遥测、遥信、遥控配置文档，并修改数据库对应配置项")]
        [UnitOfWork(isTransactional: false)]
        public  async Task<RequestEasyResult> ImportTeleConfigurationExcelOldAsync([FromForm] IFormCollection formCollection, [FromQuery] Guid? id)
        {
            RequestEasyResult requestEasyResult = new RequestEasyResult();
            try
            {
                FormFileCollection fileCollection = (FormFileCollection)formCollection.Files;

                var equipmentRepo = _equipmentInfoRepository.GetAll().ToList();
                var equipmentTypeRepo = _equipmentTypeRepository.GetAll().ToList();
                var selfCheckingConfigurationRepo = _selfCheckingConfigurationResitory.GetAll().ToList();
                var telemeteringRepo = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo).ToList();
                var dmalarmCategoryRepo = _dmalarmCategoryResitory.GetAll().ToList();

                if (fileCollection != null && fileCollection.Count == 1)
                {
                    IFormFile file = fileCollection[0];
                    ExcelHandler excelRead = new DataConfigurationExcelHandle();
                    string ext = file.FileName.Split('.')?.LastOrDefault();

                    #region 遥测
                    try
                    {
                        var telemeteringStr = excelRead.ReadExcel(file.OpenReadStream(), "遥测", ext)?.ToString();
                        if (telemeteringStr != null)
                        {
                            var repo = _telemeteringConfigurationResitory.GetAll().ToDictionary(t => t.Id);

                            JObject rss = JObject.Parse(telemeteringStr);
                            var jsonToken = rss["data"];
                            var list = jsonToken?.ToObject<List<TelemeteringConfigurationExcel>>();
                            var results = rss["data"].Children().ToList();

                            var entitiesToUpdate = new List<TelemeteringConfiguration>();
                            var entitiesToInsert = new List<TelemeteringConfiguration>();
                            var entitiesToDelete = new List<Guid>();

                            foreach (JToken result in results)
                            {
                                var item = result.ToObject<TelemeteringConfigurationExcel>();
                                if (item.Id != default && repo.ContainsKey(item.Id.Value))
                                {
                                    // 更新已有的记录
                                    var entity = repo[item.Id.Value];
                                    entity.Name = item.Name;
                                    entity.SeqNo = item.SeqNo;
                                    entity.IsActive = item.IsActive;
                                    entity.IsSave = item.IsSave;
                                    entity.IsSendDispatcher = item.IsSendDispatcher;
                                    entity.IsVisible = item.IsVisible;
                                    entity.DispatcherAddress = item.DispatcherAddress;
                                    entity.CPUSector = item.CPUSector;
                                    entity.DeviceAddress = item.DeviceAddress;
                                    entity.InfoAddress = item.InfoAddress;
                                    entity.InfoCPUSector = item.InfoCPUSector;
                                    entity.InfoDeviceAddress = item.InfoDeviceAddress;
                                    entity.IsVirtualDevice = item.IsVirtualDevice;
                                    entity.IsSelfCheckingValue = item.IsSelfCheckingValue;
                                    entity.DecimalDigits = item.DecimalDigits;
                                    entity.Coefficient = item.Coefficient;
                                    entity.UpperLimit = item.UpperLimit;
                                    entity.LowerLimit = item.LowerLimit;
                                    entity.Unit = item.Unit;
                                    entity.DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory;
                                    var equipent = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
                                    var equipemntType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
                                    if (equipent != null && equipemntType != null && equipent.EquipmentTypeId == equipemntType.Id)
                                    {
                                        entity.EquipmentInfoId = equipent.Id;
                                        entity.EquipmentTypeId = equipemntType.Id;
                                    }

                                    entitiesToUpdate.Add(entity); // 将需要更新的实体添加到批量更新列表
                                }
                                else if (item.Id != default && !repo.ContainsKey(item.Id.Value))
                                {
                                    entitiesToDelete.Add(item.Id.Value);
                                }
                                else
                                {
                                    // 新增的记录
                                    var entity = new TelemeteringConfiguration();
                                    if (string.IsNullOrEmpty(item.Name))
                                    {
                                        continue;
                                    }
                                    entity.DispatcherAddress = item.DispatcherAddress;
                                    entity.CPUSector = item.CPUSector;
                                    entity.DeviceAddress = item.DeviceAddress;
                                    entity.InfoAddress = item.InfoAddress;
                                    entity.InfoCPUSector = item.InfoCPUSector;
                                    entity.InfoDeviceAddress = item.InfoDeviceAddress;
                                    entity.IsVirtualDevice = item.IsVirtualDevice;
                                    entity.IsSelfCheckingValue = item.IsSelfCheckingValue;
                                    entity.Name = item.Name;
                                    entity.SeqNo = item.SeqNo;
                                    entity.IsActive = item.IsActive;
                                    entity.IsSave = item.IsSave;
                                    entity.IsSendDispatcher = item.IsSendDispatcher;
                                    entity.IsVisible = item.IsVisible;
                                    entity.UpperLimit = item.UpperLimit;
                                    entity.LowerLimit = item.LowerLimit;
                                    entity.DecimalDigits = item.DecimalDigits;
                                    entity.Coefficient = item.Coefficient;
                                    entity.Unit = item.Unit;
                                    entity.TransformerSubstationId = id;
                                    entity.DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory;

                                    var equipent = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
                                    var equipemntType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
                                    if (equipent != null && equipemntType != null && equipent.EquipmentTypeId == equipemntType.Id)
                                    {
                                        entity.EquipmentInfoId = equipent.Id;
                                        entity.EquipmentTypeId = equipemntType.Id;
                                    }

                                    entitiesToInsert.Add(entity); // 将需要插入的实体添加到批量插入列表
                                }
                            }

                            // 批量更新和插入
                            // 批量更新和插入
                            if (entitiesToUpdate.Any())
                            {
                                _telemeteringConfigurationResitory.GetDbContext().UpdateRange(entitiesToUpdate);

                            }

                            if (entitiesToInsert.Any())
                            {
                                _telemeteringConfigurationResitory.GetDbContext().AddRange(entitiesToInsert);
                            }
                            if (entitiesToDelete.Any())
                            {
                                _telemeteringConfigurationResitory.GetDbContext().RemoveRange(entitiesToDelete);
                            }
                            _telemeteringConfigurationResitory.GetDbContext().SaveChanges();
                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), "变电所管理服务-遥测", ex);
                    }

                    #endregion
                    #region 遥测报警
                    try
                    {
                        var telemeteringAlarmStr = excelRead.ReadExcel(file.OpenReadStream(), "遥测报警", ext)?.ToString();
                        if (telemeteringAlarmStr != null)
                        {
                            var repo = _telemeteringAlarmStrategyResitory.GetAll().ToList();



                            JObject rss = JObject.Parse(telemeteringAlarmStr);
                            var results = rss["data"].Children().ToList();
                            foreach (JToken result in results)
                            {
                                var item = result.ToObject<TelemeteringAlarmStrategyExcel>();
                                if (!item.TelemeteringConfigurationId.HasValue)
                                {
                                    if (!string.IsNullOrWhiteSpace(item.TelemeteringConfigurationName))
                                    {
                                        var telemeterItem = telemeteringRepo.FirstOrDefault(t => t.Name == item.TelemeteringConfigurationName && t.EquipmentInfo.Name == item.EquipmentInfoName);
                                        if (telemeterItem != null)
                                        {
                                            item.TelemeteringConfigurationId = telemeterItem.Id;
                                        }
                                    }
                                }
                                if (item.Id != null) //报警策略id
                                {
                                    var entity = repo.FirstOrDefault(t => t.Id == item.Id);
                                    if (entity != null)
                                    {

                                        entity.SeqNo = (int)item.SeqNo;
                                        entity.IsActive = (bool)item.IsActive;
                                        entity.LastModificationTime = DateTime.Now;
                                        entity.LastModifierUserId = base.GetCurrentUser().Id;
                                        entity.MaxValue = (float)item.MaxValue;
                                        entity.MinValue = (float)item.MinValue;
                                        _telemeteringAlarmStrategyResitory.Update(entity);
                                    }
                                }
                                else
                                {
                                    var entity = new TelemeteringAlarmStrategy();
                                    try
                                    {
                                        if (item.AlarmLevel == null || string.IsNullOrEmpty(item.AlarmName))
                                        {
                                            continue;
                                        }
                                        var telematering = telemeteringRepo.FirstOrDefault(t => t.Id == item.TelemeteringConfigurationId);

                                        if (telematering == null)
                                        {
                                            continue;
                                        }
                                        entity.SeqNo = (int)item.SeqNo;

                                        entity.IsActive = (bool)item.IsActive;
                                        entity.CreationTime = DateTime.Now;
                                        entity.CreatorUserId = base.GetCurrentUser().Id;
                                        entity.MaxValue = (float)item.MaxValue;
                                        entity.MinValue = (float)item.MinValue;
                                        entity.TelemeteringConfigurationId = telematering.Id;
                                        entity.Id = Guid.NewGuid();
                                        var dma = dmalarmCategoryRepo.FirstOrDefault(t => t.Name == item.AlarmName && t.Level == item.AlarmLevel);
                                        if (dma != null)
                                        {
                                            entity.DMAlarmCategoryId = dma.Id;
                                            _telemeteringAlarmStrategyResitory.Insert(entity);
                                        }

                                    }
                                    catch (Exception ex)
                                    {
                                        Log4Helper.Error(this.GetType(), "变电所管理服务-遥测报警", ex);

                                    }

                                }

                            }

                        }
                    }
                    catch (Exception ex)
                    {

                        Log4Helper.Error(this.GetType(), "变电所管理服务-遥测报警", ex);

                    }
                    #endregion
                    #region 遥信
                    try
                    {
                        var telesignalStr = excelRead.ReadExcel(file.OpenReadStream(), "遥信", ext)?.ToString();
                        var dmRepo = _dmalarmCategoryResitory.GetAll().ToList();

                        if (telesignalStr != null)
                        {
                            var repo = _telesignalisationConfigurationResitory.GetAll().ToDictionary(t => t.Id);

                            JObject rss = JObject.Parse(telesignalStr);
                            var jsonToken = rss["data"];
                            var list = jsonToken?.ToObject<List<TelesignalisationConfigurationExcel>>();
                            var results = rss["data"].Children().ToList();

                            var entitiesToUpdate = new List<TelesignalisationConfiguration>();
                            var entitiesToInsert = new List<TelesignalisationConfiguration>();
                            var entitiesToDelete = new List<Guid>();
                            foreach (JToken result in results)
                            {
                                var item = result.ToObject<TelesignalisationConfigurationExcel>();
                                if (item.Id != default && repo.ContainsKey(item.Id.Value))
                                {
                                    // 更新已有的记录
                                    var entity = repo[item.Id.Value];
                                    entity.Name = item.Name;
                                    entity.DispatcherAddress = item.DispatcherAddress;
                                    entity.CPUSector = item.CPUSector;
                                    entity.DeviceAddress = item.DeviceAddress;
                                    entity.InfoAddress = item.InfoAddress;
                                    entity.InfoCPUSector = item.InfoCPUSector;
                                    entity.InfoDeviceAddress = item.InfoDeviceAddress;
                                    entity.IsVirtualDevice = item.IsVirtualDevice;
                                    entity.IsSelfCheckingValue = item.IsSelfCheckingValue;
                                    entity.SeqNo = item.SeqNo;
                                    entity.IsActive = item.IsActive;
                                    entity.IsSave = item.IsSave;
                                    entity.IsSendDispatcher = item.IsSendDispatcher;
                                    entity.IsVisible = item.IsVisible;
                                    entity.RemoteType = item.RemoteType;
                                    entity.YesContent = item.YesContent;
                                    entity.NoContent = item.NoContent;
                                    entity.UnsurenessContent = string.IsNullOrWhiteSpace( item.UnsurenessContent)?"不定" : item.UnsurenessContent;
                                    entity.IsCommStatus = item.IsCommStatus;
                                    entity.DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory;

                                    if (item.DMAlarmCategoryId.HasValue)
                                    {
                                        var dm = dmRepo.FirstOrDefault(t => t.Id == item.DMAlarmCategoryId);
                                        entity.DMAlarmCategoryId = dm?.Id;
                                    }
                                    else
                                    {
                                        var dm = dmRepo.FirstOrDefault(t => t.Name == item.AlarmName && t.Level == item.AlarmLevel);
                                        entity.DMAlarmCategoryId = dm?.Id;
                                    }

                                    var selfChecking = selfCheckingConfigurationRepo.FirstOrDefault(t => t.Name == result["自检策略名称"].ToString());
                                    if (selfChecking != null)
                                    {
                                        entity.SelfCheckingConfigurationId = selfChecking.Id;
                                    }

                                    var equipent = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
                                    var equipemntType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
                                    if (equipent != null && equipemntType != null && equipent.EquipmentTypeId == equipemntType.Id)
                                    {
                                        entity.EquipmentInfoId = equipent.Id;
                                        entity.EquipmentTypeId = equipemntType.Id;
                                    }

                                    entitiesToUpdate.Add(entity); // 将需要更新的实体添加到批量更新列表
                                } 
                                else if (item.Id != default&&!repo.ContainsKey(item.Id.Value))
                                {
                                    entitiesToDelete.Add(item.Id.Value);
                                }
                                else
                                {
                                    // 新增的记录
                                    var entity = new TelesignalisationConfiguration
                                    {
                                        Name = item.Name,
                                        DispatcherAddress = item.DispatcherAddress,
                                        CPUSector = item.CPUSector,
                                        DeviceAddress = item.DeviceAddress,
                                        InfoAddress = item.InfoAddress,
                                        InfoCPUSector = item.InfoCPUSector,
                                        InfoDeviceAddress = item.InfoDeviceAddress,
                                        IsVirtualDevice = item.IsVirtualDevice,
                                        IsSelfCheckingValue = item.IsSelfCheckingValue,
                                        SeqNo = item.SeqNo,
                                        IsActive = item.IsActive,
                                        IsSave = item.IsSave,
                                        IsSendDispatcher = item.IsSendDispatcher,
                                        IsVisible = item.IsVisible,
                                        RemoteType = item.RemoteType,
                                        YesContent = item.YesContent,
                                        NoContent = item.NoContent,
                                        UnsurenessContent = string.IsNullOrWhiteSpace(item.UnsurenessContent) ? "不定" : item.UnsurenessContent,
                                        IsCommStatus = item.IsCommStatus,
                                        TransformerSubstationId = id
                                    };
                                    entity.DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory;

                                    if (item.DMAlarmCategoryId.HasValue)
                                    {
                                        var dm = dmRepo.FirstOrDefault(t => t.Id == item.DMAlarmCategoryId);
                                        entity.DMAlarmCategoryId = dm?.Id;
                                    }
                                    else
                                    {
                                        var dm = dmRepo.FirstOrDefault(t => t.Name == item.AlarmName && t.Level == item.AlarmLevel);
                                        entity.DMAlarmCategoryId = dm?.Id;
                                    }

                                    var equipent = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
                                    var equipemntType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
                                    if (equipent != null && equipemntType != null && equipent.EquipmentTypeId == equipemntType.Id)
                                    {
                                        entity.EquipmentInfoId = equipent.Id;
                                        entity.EquipmentTypeId = equipemntType.Id;
                                    }

                                    var selfChecking = selfCheckingConfigurationRepo.FirstOrDefault(t => t.Name == result["自检策略名称"].ToString());
                                    if (selfChecking != null)
                                    {
                                        entity.SelfCheckingConfigurationId = selfChecking.Id;
                                    }

                                    entitiesToInsert.Add(entity); // 将需要插入的实体添加到批量插入列表
                                }
                            }

                            // 批量更新和插入
                            if (entitiesToUpdate.Any())
                            {
                                _telesignalisationConfigurationResitory.GetDbContext().UpdateRange(entitiesToUpdate);
                                
                            }

                            if (entitiesToInsert.Any())
                            {
                                _telesignalisationConfigurationResitory.GetDbContext().AddRange(entitiesToInsert);
                            }
                            if (entitiesToDelete.Any())
                            {
                                _telesignalisationConfigurationResitory.GetDbContext().RemoveRange(entitiesToDelete);
                            }
                            _telesignalisationConfigurationResitory.GetDbContext().SaveChanges();

                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), "变电所管理服务-遥信导入", ex);
                    }


                    #endregion 遥信
                    #region 遥控
                    try
                    {
                        var telecommandStr = excelRead.ReadExcel(file.OpenReadStream(), "遥控", ext)?.ToString();
                        if (telecommandStr != null)
                        {
                            var repo = _telecommandConfigurationResitory.GetAll();
                            JObject rss = JObject.Parse(telecommandStr);
                            var jsonToken = rss["data"];
                            var list = jsonToken?.ToObject<List<TelecommandConfigurationExcel>>();
                            //var listId = list.Select(t => t.Id);
                            //if (listId != null)
                            //{
                            //    _telecommandConfigurationResitory.Delete(t => !listId.Contains(t.Id));
                            //}
                            var results = rss["data"].Children().ToList();
                            foreach (JToken result in results)
                            {
                                var item = result.ToObject<TelecommandConfigurationExcel>();
                                if (item.Id != default)
                                {
                                    var entity = repo.FirstOrDefault(t => t.Id == item.Id);
                                    if (entity != null)
                                    {
                                        entity.Name = item.Name;
                                        //entity.Id = telemetering.Id
                                        entity.DispatcherAddress = item.DispatcherAddress;
                                        entity.CPUSector = item.CPUSector;
                                        entity.DeviceAddress = item.DeviceAddress;
                                        entity.InfoAddress = item.InfoAddress;
                                        entity.InfoCPUSector = item.InfoCPUSector;
                                        entity.InfoDeviceAddress = item.InfoDeviceAddress;
                                        entity.IsVirtualDevice = item.IsVirtualDevice;
                                        entity.SeqNo = item.SeqNo;
                                        entity.IsActive = item.IsActive;
                                        entity.IsSave = item.IsSave;
                                        entity.IsSendDispatcher = item.IsSendDispatcher;
                                        entity.IsVisible = item.IsVisible;
                                        entity.RemoteType = item.RemoteType;
                                        entity.YesContent = item.YesContent;
                                        entity.NoContent = item.NoContent;
                                        entity.UnsurenessContent = string.IsNullOrWhiteSpace( item.UnsurenessContent)?"不定":item.UnsurenessContent;
                                        entity.RelatedTelesignalisationId = item.RelatedTelesignalisationId;
                                        var equipent = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
                                        var equipemntType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
                                        if (equipent != null && equipemntType != null)
                                        {
                                            if (equipent.EquipmentTypeId == equipemntType.Id)
                                            {
                                                if (entity.EquipmentInfoId != equipent.Id)
                                                {
                                                    entity.EquipmentInfoId = equipent.Id;
                                                }
                                                if (entity.EquipmentTypeId != equipemntType.Id)
                                                {
                                                    entity.EquipmentTypeId = equipemntType.Id;
                                                }
                                            }

                                        }
                                        _telecommandConfigurationResitory.UpdateAsync(entity);
                                    }
                                }
                                else
                                {
                                    var entity = new TelecommandConfiguration();
                                    if (string.IsNullOrEmpty(item.Name))
                                    {
                                        continue;
                                    }
                                    entity.Name = item.Name;
                                    entity.DispatcherAddress = item.DispatcherAddress;
                                    entity.CPUSector = item.CPUSector;
                                    entity.DeviceAddress = item.DeviceAddress;
                                    entity.InfoAddress = item.InfoAddress;
                                    entity.InfoCPUSector = item.InfoCPUSector;
                                    entity.InfoDeviceAddress = item.InfoDeviceAddress;
                                    entity.IsVirtualDevice = item.IsVirtualDevice;
                                    entity.SeqNo = item.SeqNo;
                                    entity.IsActive = item.IsActive;
                                    entity.IsSave = item.IsSave;
                                    entity.IsSendDispatcher = item.IsSendDispatcher;
                                    entity.IsVisible = item.IsVisible;
                                    entity.CPUSector = item.CPUSector;
                                    entity.RemoteType = item.RemoteType;
                                    entity.YesContent = item.YesContent;
                                    entity.NoContent = item.NoContent;
                                    entity.UnsurenessContent = item.UnsurenessContent;
                                    entity.RelatedTelesignalisationId = item.RelatedTelesignalisationId;
                                    entity.TransformerSubstationId = id;

                                    var equipent = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
                                    var equipemntType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
                                    if (equipent != null && equipemntType != null)
                                    {
                                        if (equipent.EquipmentTypeId == equipemntType.Id)
                                        {
                                            if (entity.EquipmentInfoId != equipent.Id)
                                            {
                                                entity.EquipmentInfoId = equipent.Id;
                                            }
                                            if (entity.EquipmentTypeId != equipemntType.Id)
                                            {
                                                entity.EquipmentTypeId = equipemntType.Id;
                                            }
                                            _telecommandConfigurationResitory.Insert(entity);

                                        }

                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {

                        Log4Helper.Error(this.GetType(), "变电所管理服务-遥控导入", ex);

                    }
                    #endregion 遥控
                }

                requestEasyResult.Flag = true;
            }

            catch (Exception ex)
            {

                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }


            return requestEasyResult;
        }


        // ... existing code ...

        [HttpPost, Audited, Description("导入遥测、遥信、遥控配置文档，并修改数据库对应配置项")]
        [UnitOfWork(isTransactional: false)]
        public async Task<RequestEasyResult> ImportTeleConfigurationExcelAsync([FromForm] IFormCollection formCollection, [FromQuery] Guid? id)
        {
            RequestEasyResult requestEasyResult = new RequestEasyResult();
            try
            {
                FormFileCollection fileCollection = (FormFileCollection)formCollection.Files;
                if (fileCollection == null || fileCollection.Count != 1)
                {
                    return requestEasyResult;
                }

                IFormFile file = fileCollection[0];
                ExcelHandler excelRead = new DataConfigurationExcelHandle();
                string ext = file.FileName.Split('.')?.LastOrDefault();

                // 获取基础数据
                var equipmentRepo = await _equipmentInfoRepository.GetAll().ToListAsync();
                var equipmentTypeRepo = await _equipmentTypeRepository.GetAll().ToListAsync();
                var selfCheckingConfigurationRepo = await _selfCheckingConfigurationResitory.GetAll().ToListAsync();
                var telemeteringRepo = await _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo).ToListAsync();
                var dmalarmCategoryRepo = await _dmalarmCategoryResitory.GetAll().ToListAsync();

                // 处理遥测配置
                await ProcessTelemeteringConfigurations(file, excelRead, ext, id, equipmentRepo, equipmentTypeRepo, telemeteringRepo);

                // 处理遥测报警配置
                await ProcessTelemeteringAlarmStrategies(file, excelRead, ext, telemeteringRepo, dmalarmCategoryRepo);

                // 处理遥信配置
                await ProcessTelesignalisationConfigurations(file, excelRead, ext, id, equipmentRepo, equipmentTypeRepo, selfCheckingConfigurationRepo, dmalarmCategoryRepo);

                // 处理遥控配置
                await ProcessTelecommandConfigurations(file, excelRead, ext, id, equipmentRepo, equipmentTypeRepo);

                requestEasyResult.Flag = true;
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务-导入配置", ex);
                requestEasyResult.Flag = false;
                requestEasyResult.Message = ex.Message;
            }

            return requestEasyResult;
        }

        private async Task ProcessTelemeteringConfigurations(IFormFile file, ExcelHandler excelRead, string ext, Guid? id,
            List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo, List<TelemeteringConfiguration> telemeteringRepo)
        {
            try
            {
                var telemeteringStr = excelRead.ReadExcel(file.OpenReadStream(), "遥测", ext)?.ToString();
                if (telemeteringStr == null) return;

                JObject rss = JObject.Parse(telemeteringStr);
                var results = rss["data"].Children().ToList();

                var entitiesToUpdate = new List<TelemeteringConfiguration>();
                var entitiesToInsert = new List<TelemeteringConfiguration>();

                foreach (JToken result in results)
                {
                    var item = result.ToObject<TelemeteringConfigurationExcel>();
                    if (string.IsNullOrEmpty(item.Name)) continue;

                    TelemeteringConfiguration existingConfig = null;
                    if (item.Id != null)
                    {
                        existingConfig = telemeteringRepo.FirstOrDefault(t => t.Id == item.Id);
                    }
                    if (existingConfig == null)
                    {
                        existingConfig = telemeteringRepo.FirstOrDefault(t =>
                            t.TransformerSubstationId == id &&
                            t.EquipmentInfo?.Name == result["关联监控设备"].ToString() &&
                            t.Name == item.Name);
                    }

                    if (existingConfig != null)
                    {
                        // 更新现有记录
                        UpdateTelemeteringConfiguration(existingConfig, item, result, equipmentRepo, equipmentTypeRepo);
                        entitiesToUpdate.Add(existingConfig);
                    }
                    else
                    {
                        // 创建新记录
                        var newConfig = CreateTelemeteringConfiguration(item, result, id, equipmentRepo, equipmentTypeRepo);
                        if (newConfig != null)
                        {
                            entitiesToInsert.Add(newConfig);
                        }
                    }
                }

                // 批量更新和插入
                if (entitiesToUpdate.Any())
                {
                    _telemeteringConfigurationResitory.GetDbContext().UpdateRange(entitiesToUpdate);
                }

                if (entitiesToInsert.Any())
                {
                    _telemeteringConfigurationResitory.GetDbContext().AddRange(entitiesToInsert);
                }

                await _telemeteringConfigurationResitory.GetDbContext().SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务-遥测导入", ex);
                throw;
            }
        }

        private void UpdateTelemeteringConfiguration(TelemeteringConfiguration config, TelemeteringConfigurationExcel item,
            JToken result, List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo)
        {
            config.Name = item.Name;
            config.SeqNo = item.SeqNo;
            config.IsActive = item.IsActive;
            config.IsSave = item.IsSave;
            config.IsSendDispatcher = item.IsSendDispatcher;
            config.IsVisible = item.IsVisible;
            config.DispatcherAddress = item.DispatcherAddress;
            config.CPUSector = item.CPUSector;
            config.DeviceAddress = item.DeviceAddress;
            config.InfoAddress = item.InfoAddress;
            config.InfoCPUSector = item.InfoCPUSector;
            config.InfoDeviceAddress = item.InfoDeviceAddress;
            config.IsVirtualDevice = item.IsVirtualDevice;
            config.IsSelfCheckingValue = item.IsSelfCheckingValue;
            config.DecimalDigits = item.DecimalDigits;
            config.Coefficient = item.Coefficient;
            config.UpperLimit = item.UpperLimit;
            config.LowerLimit = item.LowerLimit;
            config.Unit = item.Unit;
            config.DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory;

            var equipment = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
            var equipmentType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
            
            if (equipment != null && equipmentType != null && equipment.EquipmentTypeId == equipmentType.Id)
            {
                config.EquipmentInfoId = equipment.Id;
                config.EquipmentTypeId = equipmentType.Id;
            }
        }

        private TelemeteringConfiguration CreateTelemeteringConfiguration(TelemeteringConfigurationExcel item,
            JToken result, Guid? id, List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo)
        {
            var config = new TelemeteringConfiguration
            {
                Name = item.Name,
                DispatcherAddress = item.DispatcherAddress,
                CPUSector = item.CPUSector,
                DeviceAddress = item.DeviceAddress,
                InfoAddress = item.InfoAddress,
                InfoCPUSector = item.InfoCPUSector,
                InfoDeviceAddress = item.InfoDeviceAddress,
                IsVirtualDevice = item.IsVirtualDevice,
                IsSelfCheckingValue = item.IsSelfCheckingValue,
                SeqNo = item.SeqNo,
                IsActive = item.IsActive,
                IsSave = item.IsSave,
                IsSendDispatcher = item.IsSendDispatcher,
                IsVisible = item.IsVisible,
                UpperLimit = item.UpperLimit,
                LowerLimit = item.LowerLimit,
                DecimalDigits = item.DecimalDigits,
                Coefficient = item.Coefficient,
                Unit = item.Unit,
                TransformerSubstationId = id,
                DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory
            };

            var equipment = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
            var equipmentType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
            if (equipment != null && equipmentType != null && equipment.EquipmentTypeId == equipmentType.Id)
            {
                config.EquipmentInfoId = equipment.Id;
                config.EquipmentTypeId = equipmentType.Id;
                return config;
            }

            return null;
        }

        private async Task ProcessTelemeteringAlarmStrategies(IFormFile file, ExcelHandler excelRead, string ext,
            List<TelemeteringConfiguration> telemeteringRepo, List<DMAlarmCategory> dmalarmCategoryRepo)
        {
            try
            {
                var telemeteringAlarmStr = excelRead.ReadExcel(file.OpenReadStream(), "遥测报警", ext)?.ToString();
                if (telemeteringAlarmStr == null) return;

                JObject rss = JObject.Parse(telemeteringAlarmStr);
                var results = rss["data"].Children().ToList();
                var existingStrategies = await _telemeteringAlarmStrategyResitory.GetAll().ToListAsync();

                foreach (JToken result in results)
                {
                    var item = result.ToObject<TelemeteringAlarmStrategyExcel>();
                    if (!item.TelemeteringConfigurationId.HasValue && !string.IsNullOrWhiteSpace(item.TelemeteringConfigurationName))
                    {
                        var telemeterItem = telemeteringRepo.FirstOrDefault(t =>
                            t.Name == item.TelemeteringConfigurationName &&
                            t.EquipmentInfo?.Name == item.EquipmentInfoName);
                        if (telemeterItem != null)
                        {
                            item.TelemeteringConfigurationId = telemeterItem.Id;
                        }
                    }

                    if (item.Id != null)
                    {
                        var entity = existingStrategies.FirstOrDefault(t => t.Id == item.Id);
                        if (entity != null)
                        {
                            UpdateTelemeteringAlarmStrategy(entity, item);
                            await _telemeteringAlarmStrategyResitory.UpdateAsync(entity);
                        }
                    }
                    else
                    {
                        await CreateTelemeteringAlarmStrategy(item, telemeteringRepo, dmalarmCategoryRepo);
                    }
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务-遥测报警导入", ex);
                throw;
            }
        }

        private void UpdateTelemeteringAlarmStrategy(TelemeteringAlarmStrategy entity, TelemeteringAlarmStrategyExcel item)
        {
            entity.SeqNo = (int)item.SeqNo;
            entity.IsActive = (bool)item.IsActive;
            entity.LastModificationTime = DateTime.Now;
            entity.LastModifierUserId = base.GetCurrentUser().Id;
            entity.MaxValue = (float)item.MaxValue;
            entity.MinValue = (float)item.MinValue;
        }

        private async Task CreateTelemeteringAlarmStrategy(TelemeteringAlarmStrategyExcel item,
            List<TelemeteringConfiguration> telemeteringRepo, List<DMAlarmCategory> dmalarmCategoryRepo)
        {
            if (item.AlarmLevel == null || string.IsNullOrEmpty(item.AlarmName)) return;

            var telemetering = telemeteringRepo.FirstOrDefault(t => t.Id == item.TelemeteringConfigurationId);
            if (telemetering == null) return;

            var entity = new TelemeteringAlarmStrategy
            {
                SeqNo = (int)item.SeqNo,
                IsActive = (bool)item.IsActive,
                CreationTime = DateTime.Now,
                CreatorUserId = base.GetCurrentUser().Id,
                MaxValue = (float)item.MaxValue,
                MinValue = (float)item.MinValue,
                TelemeteringConfigurationId = telemetering.Id,
                Id = Guid.NewGuid()
            };

            var dma = dmalarmCategoryRepo.FirstOrDefault(t => t.Name == item.AlarmName && t.Level == item.AlarmLevel);
            if (dma != null)
            {
                entity.DMAlarmCategoryId = dma.Id;
                await _telemeteringAlarmStrategyResitory.InsertAsync(entity);
            }
        }

        private async Task ProcessTelesignalisationConfigurations(IFormFile file, ExcelHandler excelRead, string ext, Guid? id,
            List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo,
            List<SelfCheckingConfiguration> selfCheckingConfigurationRepo, List<DMAlarmCategory> dmalarmCategoryRepo)
        {
            try
            {
                var telesignalStr = excelRead.ReadExcel(file.OpenReadStream(), "遥信", ext)?.ToString();
                if (telesignalStr == null) return;

                JObject rss = JObject.Parse(telesignalStr);
                var results = rss["data"].Children().ToList();
                var existingConfigs = await _telesignalisationConfigurationResitory.GetAll().ToListAsync();

                var entitiesToUpdate = new List<TelesignalisationConfiguration>();
                var entitiesToInsert = new List<TelesignalisationConfiguration>();

                foreach (JToken result in results)
                {
                    var item = result.ToObject<TelesignalisationConfigurationExcel>();
                    if (string.IsNullOrEmpty(item.Name)) continue;

                    // 查找现有配置
                    var existingConfig = existingConfigs.FirstOrDefault(t =>
                        t.TransformerSubstationId == id &&
                        t.EquipmentInfo?.Name == result["关联监控设备"].ToString() &&
                        t.Name == item.Name);

                    if (existingConfig != null)
                    {
                        UpdateTelesignalisationConfiguration(existingConfig, item, result, equipmentRepo,
                            equipmentTypeRepo, selfCheckingConfigurationRepo, dmalarmCategoryRepo);
                        entitiesToUpdate.Add(existingConfig);
                    }
                    else
                    {
                        var newConfig = CreateTelesignalisationConfiguration(item, result, id, equipmentRepo,
                            equipmentTypeRepo, selfCheckingConfigurationRepo, dmalarmCategoryRepo);
                        if (newConfig != null)
                        {
                            entitiesToInsert.Add(newConfig);
                        }
                    }
                }

                if (entitiesToUpdate.Any())
                {
                    _telesignalisationConfigurationResitory.GetDbContext().UpdateRange(entitiesToUpdate);
                }

                if (entitiesToInsert.Any())
                {
                    _telesignalisationConfigurationResitory.GetDbContext().AddRange(entitiesToInsert);
                }

                await _telesignalisationConfigurationResitory.GetDbContext().SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务-遥信导入", ex);
                throw;
            }
        }

        private void UpdateTelesignalisationConfiguration(TelesignalisationConfiguration config,
            TelesignalisationConfigurationExcel item, JToken result, List<EquipmentInfo> equipmentRepo,
            List<EquipmentType> equipmentTypeRepo, List<SelfCheckingConfiguration> selfCheckingConfigurationRepo,
            List<DMAlarmCategory> dmalarmCategoryRepo)
        {
            config.Name = item.Name;
            config.DispatcherAddress = item.DispatcherAddress;
            config.CPUSector = item.CPUSector;
            config.DeviceAddress = item.DeviceAddress;
            config.InfoAddress = item.InfoAddress;
            config.InfoCPUSector = item.InfoCPUSector;
            config.InfoDeviceAddress = item.InfoDeviceAddress;
            config.IsVirtualDevice = item.IsVirtualDevice;
            config.IsSelfCheckingValue = item.IsSelfCheckingValue;
            config.SeqNo = item.SeqNo;
            config.IsActive = item.IsActive;
            config.IsSave = item.IsSave;
            config.IsSendDispatcher = item.IsSendDispatcher;
            config.IsVisible = item.IsVisible;
            config.RemoteType = item.RemoteType;
            config.YesContent = item.YesContent;
            config.NoContent = item.NoContent;
            config.UnsurenessContent = string.IsNullOrWhiteSpace(item.UnsurenessContent) ? "不定" : item.UnsurenessContent;
            config.IsCommStatus = item.IsCommStatus;
            config.DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory;

            if (item.DMAlarmCategoryId.HasValue)
            {
                var dm = dmalarmCategoryRepo.FirstOrDefault(t => t.Id == item.DMAlarmCategoryId);
                config.DMAlarmCategoryId = dm?.Id;
            }
            else
            {
                var dm = dmalarmCategoryRepo.FirstOrDefault(t => t.Name == item.AlarmName && t.Level == item.AlarmLevel);
                config.DMAlarmCategoryId = dm?.Id;
            }

            var selfChecking = selfCheckingConfigurationRepo.FirstOrDefault(t => t.Name == result["自检策略名称"].ToString());
            if (selfChecking != null)
            {
                config.SelfCheckingConfigurationId = selfChecking.Id;
            }

            var equipment = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
            var equipmentType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
            if (equipment != null && equipmentType != null && equipment.EquipmentTypeId == equipmentType.Id)
            {
                config.EquipmentInfoId = equipment.Id;
                config.EquipmentTypeId = equipmentType.Id;
            }
        }

        private TelesignalisationConfiguration CreateTelesignalisationConfiguration(TelesignalisationConfigurationExcel item,
            JToken result, Guid? id, List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo,
            List<SelfCheckingConfiguration> selfCheckingConfigurationRepo, List<DMAlarmCategory> dmalarmCategoryRepo)
        {
            var config = new TelesignalisationConfiguration
            {
                Name = item.Name,
                DispatcherAddress = item.DispatcherAddress,
                CPUSector = item.CPUSector,
                DeviceAddress = item.DeviceAddress,
                InfoAddress = item.InfoAddress,
                InfoCPUSector = item.InfoCPUSector,
                InfoDeviceAddress = item.InfoDeviceAddress,
                IsVirtualDevice = item.IsVirtualDevice,
                IsSelfCheckingValue = item.IsSelfCheckingValue,
                SeqNo = item.SeqNo,
                IsActive = item.IsActive,
                IsSave = item.IsSave,
                IsSendDispatcher = item.IsSendDispatcher,
                IsVisible = item.IsVisible,
                RemoteType = item.RemoteType,
                YesContent = item.YesContent,
                NoContent = item.NoContent,
                UnsurenessContent = string.IsNullOrWhiteSpace(item.UnsurenessContent) ? "不定" : item.UnsurenessContent,
                IsCommStatus = item.IsCommStatus,
                TransformerSubstationId = id,
                DataSourceCategory = (DataSourceCategoryEnum)item.DataSourceCategory
            };

            if (item.DMAlarmCategoryId.HasValue)
            {
                var dm = dmalarmCategoryRepo.FirstOrDefault(t => t.Id == item.DMAlarmCategoryId);
                config.DMAlarmCategoryId = dm?.Id;
            }
            else
            {
                var dm = dmalarmCategoryRepo.FirstOrDefault(t => t.Name == item.AlarmName && t.Level == item.AlarmLevel);
                config.DMAlarmCategoryId = dm?.Id;
            }

            var equipment = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
            var equipmentType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
            if (equipment != null && equipmentType != null && equipment.EquipmentTypeId == equipmentType.Id)
            {
                config.EquipmentInfoId = equipment.Id;
                config.EquipmentTypeId = equipmentType.Id;
            }

            var selfChecking = selfCheckingConfigurationRepo.FirstOrDefault(t => t.Name == result["自检策略名称"].ToString());
            if (selfChecking != null)
            {
                config.SelfCheckingConfigurationId = selfChecking.Id;
            }

            return config;
        }

        private async Task ProcessTelecommandConfigurations(IFormFile file, ExcelHandler excelRead, string ext, Guid? id,
            List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo)
        {
            try
            {
                var telecommandStr = excelRead.ReadExcel(file.OpenReadStream(), "遥控", ext)?.ToString();
                if (telecommandStr == null) return;

                JObject rss = JObject.Parse(telecommandStr);
                var results = rss["data"].Children().ToList();
                var existingConfigs = await _telecommandConfigurationResitory.GetAll().ToListAsync();

                foreach (JToken result in results)
                {
                    var item = result.ToObject<TelecommandConfigurationExcel>();
                    if (string.IsNullOrEmpty(item.Name)) continue;

                    // 查找现有配置
                    var existingConfig = existingConfigs.FirstOrDefault(t =>
                        t.TransformerSubstationId == id &&
                        t.EquipmentInfo?.Name == result["关联监控设备"].ToString() &&
                        t.Name == item.Name);

                    if (existingConfig != null)
                    {
                        UpdateTelecommandConfiguration(existingConfig, item, result, equipmentRepo, equipmentTypeRepo);
                        await _telecommandConfigurationResitory.UpdateAsync(existingConfig);
                    }
                    else
                    {
                        var newConfig = CreateTelecommandConfiguration(item, result, id, equipmentRepo, equipmentTypeRepo);
                        if (newConfig != null)
                        {
                            await _telecommandConfigurationResitory.InsertAsync(newConfig);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "变电所管理服务-遥控导入", ex);
                throw;
            }
        }

        private void UpdateTelecommandConfiguration(TelecommandConfiguration config, TelecommandConfigurationExcel item,
            JToken result, List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo)
        {
            config.Name = item.Name;
            config.DispatcherAddress = item.DispatcherAddress;
            config.CPUSector = item.CPUSector;
            config.DeviceAddress = item.DeviceAddress;
            config.InfoAddress = item.InfoAddress;
            config.InfoCPUSector = item.InfoCPUSector;
            config.InfoDeviceAddress = item.InfoDeviceAddress;
            config.IsVirtualDevice = item.IsVirtualDevice;
            config.SeqNo = item.SeqNo;
            config.IsActive = item.IsActive;
            config.IsSave = item.IsSave;
            config.IsSendDispatcher = item.IsSendDispatcher;
            config.IsVisible = item.IsVisible;
            config.RemoteType = item.RemoteType;
            config.YesContent = item.YesContent;
            config.NoContent = item.NoContent;
            config.UnsurenessContent = string.IsNullOrWhiteSpace(item.UnsurenessContent) ? "不定" : item.UnsurenessContent;
            config.RelatedTelesignalisationId = item.RelatedTelesignalisationId;

            var equipment = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
            var equipmentType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
            if (equipment != null && equipmentType != null)
            {
                if (equipment.EquipmentTypeId == equipmentType.Id)
                {
                    if (config.EquipmentInfoId != equipment.Id)
                    {
                        config.EquipmentInfoId = equipment.Id;
                    }
                    if (config.EquipmentTypeId != equipmentType.Id)
                    {
                        config.EquipmentTypeId = equipmentType.Id;
                    }
                }
            }
        }

        private TelecommandConfiguration CreateTelecommandConfiguration(TelecommandConfigurationExcel item,
            JToken result, Guid? id, List<EquipmentInfo> equipmentRepo, List<EquipmentType> equipmentTypeRepo)
        {
            var config = new TelecommandConfiguration
            {
                Name = item.Name,
                DispatcherAddress = item.DispatcherAddress,
                CPUSector = item.CPUSector,
                DeviceAddress = item.DeviceAddress,
                InfoAddress = item.InfoAddress,
                InfoCPUSector = item.InfoCPUSector,
                InfoDeviceAddress = item.InfoDeviceAddress,
                IsVirtualDevice = item.IsVirtualDevice,
                SeqNo = item.SeqNo,
                IsActive = item.IsActive,
                IsSave = item.IsSave,
                IsSendDispatcher = item.IsSendDispatcher,
                IsVisible = item.IsVisible,
                RemoteType = item.RemoteType,
                YesContent = item.YesContent,
                NoContent = item.NoContent,
                UnsurenessContent = item.UnsurenessContent,
                RelatedTelesignalisationId = item.RelatedTelesignalisationId,
                TransformerSubstationId = id
            };

            var equipment = equipmentRepo.FirstOrDefault(t => t.Name == result["关联监控设备"].ToString());
            var equipmentType = equipmentTypeRepo.FirstOrDefault(t => t.Name == result["关联监控设备类型"].ToString());
            if (equipment != null && equipmentType != null)
            {
                if (equipment.EquipmentTypeId == equipmentType.Id)
                {
                    if (config.EquipmentInfoId != equipment.Id)
                    {
                        config.EquipmentInfoId = equipment.Id;
                    }
                    if (config.EquipmentTypeId != equipmentType.Id)
                    {
                        config.EquipmentTypeId = equipmentType.Id;
                    }
                    return config;
                }
            }

            return null;
        }

        // ... existing code ...
        #endregion 数据配置导入导出

        #region 基础数据文件

        /// <summary>
        /// 下载基础数据文件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public FileStreamResult ExportBaseData(ExportBaseDataConditionInput input)
        {
            List<ExcelData> excelDatas = new List<ExcelData>();//配置
            try
            {
                var task1 = WriteStationExcelData(input); //被控站基本情况
                var task2 = WriteVideoServiceEquipmentSettingExcelData(input); //2视频服务器配置信息
                var task3 = WriteVideoDevEquipmentSettingExcelData(input); //3.1视频设备配置表
                var task4 = WriteEquipmentExcelData(input); //3.2非视频设备配置表
                var task5 = WriteTeleCommandExcelData(input); //4遥控点表
                var task6 = WriteTeleSignalExcelData(input); //5遥信点表
                var task7 = WriteTelemeteringExcelData(input);// 6遥测点表
                var task8 = WriteTeleDebugExcelData(input);// 7遥调点表
                var task9 = WritePresetExcelData(input);// 8摄像机预置位点表
                var task10 = WriteVideoLinkageExcelData(input);// 9.1视频联动点表
                var task11 = WriteTeleCommandLinkageExcelData(input);// 9.2动环联动点表
                var task12 = WriteInspectionItemExcelData(input);// 10巡检卡片
                List<Task<ExcelData>> excelDataTasks = new List<Task<ExcelData>>
               {
                   task1, task2, task3, task4, task5, task6,
                   task7, task8, task9, task10, task11, task12
               };
                foreach (var item in excelDataTasks)
                {
                    item.Start();
                    //item.Wait();
                }
                Task.WaitAll(excelDataTasks.ToArray());
                excelDataTasks.ForEach(t =>
                {
                    excelDatas.Add(t.Result);
                });

                MemoryStream memoryStream = new MemoryStream();
                ExcelHandler excelWrite = new BaseDataExcelHandle();//执行类
                excelWrite.WriteExcel(memoryStream, excelDatas);
                memoryStream.Seek(0, SeekOrigin.Begin);
                FileStreamResult fileStreamResult = new FileStreamResult(memoryStream, "application/octet-stream");
                return fileStreamResult;
            }
            catch (Exception ex)
            {
                //rst.Message = ex.Message;
                //rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }
            return null;
        }

        private object locker = new object();
        /// <summary>
        /// 10巡检卡片
        /// </summary>
        private Task<ExcelData> WriteInspectionItemExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "10巡检卡片";//sheet名称
                excelData.TitleTextList = new List<string> { "巡检卡片" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "卡片名称", "本卡片执行顺序", "摄像机名称","预置位编号", "预置位名称" };//列头文字
                List<InspectionItem> repo = null;
                //List<EquipmentInfo> equipemntRepo = null;
                lock (locker)
                {
                    // repo = _inspectionCardRepository.GetAllIncluding(t=>t.InspectionItems,t=>t.TransformerSubstation).ToList();
                    repo = _inspectionItemRepository.GetAllIncluding(t => t.VideoDev, t => t.PresetPoint, t => t.InspectionCard)
                                 .Where(t => t.InspectionCard.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).OrderBy(t => t.InspectionCard.CardName).ThenBy(t => t.SeqNo).ToList();

                    //equipemntRepo = _equipmentInfoRepository.GetAllIncluding(t => t.TransformerSubstation, t => t.EquipmentType)
                    //.Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).ToList();
                }

                if (repo != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    int cardIndex = 0;

                    string cardName = "";
                    foreach (var item in repo)
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        arr.Add(input.SubstationName);
                        arr.Add(item.InspectionCard.CardName);
                        //if (item.InspectionCard.CardName != cardName)
                        //{
                        //    cardName = item.InspectionCard.CardName;
                        //    cardIndex++;
                        //}
                        arr.Add(item.SeqNo.ToString());
                        arr.Add(item.VideoDev.DevName);
                        arr.Add(item.PresetPoint.Number.ToString());
                        arr.Add(item.PresetPoint.Name);
                        list.Add(arr);
                        index++;
                    }
                    excelData.ContentInfoList = list;
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }
        /// <summary>
        /// 9.2动环联动点表
        /// </summary>
        private Task<ExcelData> WriteTeleCommandLinkageExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "9.2动环联动点表";//sheet名称
                excelData.TitleTextList = new List<string> { "动环联动点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "触发联动设备名称", "触发联动点位名称", "触发联动类型", "联动设备名称","联动点位名称" };//列头文字
                List<LinkageStrategy> repo;
                List<EquipmentInfo> equipemntRepo;
                List<TelemeteringConfiguration> telemeteringRepo;
                List<TelesignalisationConfiguration> telesignaRepo;
                List<TelecommandConfiguration> telecommandRepo;

                lock (locker)
                {
                    repo = _linkageStrategyRepository.GetAllIncluding(t => t.TransformerSubstation, t => t.LinkageExecuteActivities, t => t.LinkageConditions)
                    .Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).ToList();

                    equipemntRepo = _equipmentInfoRepository.GetAllIncluding(t => t.TransformerSubstation, t => t.EquipmentType)
                    .Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).ToList();

                    telemeteringRepo = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo).ToList();
                    telemeteringRepo = telemeteringRepo.Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).OrderBy(t => t.SeqNo).ToList();
                    telesignaRepo = _telesignalisationConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo).ToList();
                    telesignaRepo = telesignaRepo.Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).OrderBy(t => t.SeqNo).ToList();
                    telecommandRepo = _telecommandConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                      .Where(t => t.TransformerSubstationId == input.TransformerSubstationId).OrderBy(t => t.SeqNo).ToList();
                }

                if (repo != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    repo.ForEach(t =>
                    {
                        t.LinkageExecuteActivities.ForEach(a =>
                        {
                            t.LinkageConditions.ForEach(c =>
                            {
                                var arr = new List<string>();
                                arr.Add(index.ToString());
                                index++;
                                if (a.ActivityType == ActivityTypeEnum.Telecommand)
                                {
                                    arr.Add(t.TransformerSubstation.SubstationName);
                                    var str = equipemntRepo.FirstOrDefault(x => x.Id == c.EquipmentInfoId)?.Name; //"触发联动设备名称"
                                    arr.Add(str);
                                    if (c.TelemeteringConfigurationId.HasValue)
                                    {
                                        str = telemeteringRepo.FirstOrDefault(x => x.Id == c.TelemeteringConfigurationId)?.Name;
                                        arr.Add(str);
                                        arr.Add("遥测");
                                    }
                                    else if (c.TelesignalisationConfigurationId.HasValue)
                                    {
                                        str = telesignaRepo.FirstOrDefault(x => x.Id == c.TelemeteringConfigurationId)?.Name;
                                        arr.Add(str);
                                        arr.Add("遥信");
                                    }

                                    var telecommand = telecommandRepo.FirstOrDefault(x => x.Id == a.TelecommandConfigurationId);
                                    if (telecommand != null)
                                    {
                                        arr.Add(telecommand.EquipmentInfo.Name);
                                        arr.Add(telecommand.Name);

                                    }


                                    list.Add(arr);
                                }
                            });
                        });
                    });
                    excelData.ContentInfoList = list;
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }
        /// <summary>
        /// 9.1视频联动点表
        /// </summary>
        private Task<ExcelData> WriteVideoLinkageExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "9.1视频联动点表";//sheet名称
                excelData.TitleTextList = new List<string> { "视频联动点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "触发联动设备名称", "触发联动点位名称", "触发联动类型", "联动摄像机名称","预置位编号","预置位名称" };//列头文字
                List<TelemeteringConfiguration> telemeteringRepo;
                List<TelesignalisationConfiguration> telesignaRepo;
                List<TelecommandConfiguration> telecommandRepo;
                List<LinkageStrategy> repo;
                //List<EquipmentInfo> equipemntRepo;
                List<PresetPoint> presetPointRepo;
                lock (locker)
                {
                    repo = _linkageStrategyRepository.GetAllIncluding(t => t.TransformerSubstation, t => t.LinkageExecuteActivities, t => t.LinkageConditions)
                                  .Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).ToList();
                    //equipemntRepo = _equipmentInfoRepository.GetAllIncluding(t => t.TransformerSubstation, t => t.EquipmentType)
                    //.Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).ToList();

                    telemeteringRepo = _telemeteringConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                                                                         .Where(t => t.IsActive && t.TransformerSubstationId == input.TransformerSubstationId)
                                                                         .OrderBy(t => t.SeqNo)
                                                                         .ToList();
                    telesignaRepo = _telesignalisationConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                                                                           .Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive)
                                                                           .OrderBy(t => t.SeqNo)
                                                                           .ToList();
                    telecommandRepo = _telecommandConfigurationResitory.GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                                                                       .Where(t => t.EquipmentInfoId.HasValue && t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive)
                                                                       .OrderBy(t => t.SeqNo)
                                                                       .ToList();
                    presetPointRepo = _presetPointRepository.GetAllIncluding(p => p.VideoDev, p => p.EquipmentInfo)
                                                           .Where(t => t.VideoDevId.HasValue && t.VideoDev.TransformerSubstationId == input.TransformerSubstationId && t.VideoDev.IsActive && t.IsActive)
                                                           .OrderBy(t => t.VideoDev.SeqNo)
                                                           .ToList(); ;
                    //videoDevRepo = _videoDevRepository.GetAllIncluding(t => t.TransformerSubstation, t => t.PresetPoints)
                    //.Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive).OrderBy(t => t.SeqNo)
                    //.Where(t => t.VideoDevId.HasValue).ToList();
                }
                var list1 = new List<List<string>>();
                int index = 1;
                //联动
                if (repo != null)
                {
                    repo.ForEach(t =>
                    {
                        t.LinkageExecuteActivities.ForEach(a =>
                        {
                            t.LinkageConditions.ForEach(c =>
                            {
                                var arr = new List<string>();
                                arr.Add(index.ToString());
                                if (a.ActivityType == ActivityTypeEnum.Video)
                                {
                                    var preset = presetPointRepo.FirstOrDefault(x => x.Id == a.PresetPointId);
                                    if (preset != null)
                                    {
                                        arr.Add(t.TransformerSubstation.SubstationName);
                                        //var str = equipemntRepo.FirstOrDefault(x => x.Id == c.EquipmentInfoId)?.Name; //"触发联动设备名称"
                                        arr.Add(preset.EquipmentInfo?.Name);
                                        string lName = "";
                                        if (c.TelemeteringConfigurationId.HasValue)
                                        {
                                            lName = telemeteringRepo.FirstOrDefault(x => x.Id == c.TelemeteringConfigurationId)?.Name;
                                            arr.Add(lName);
                                            arr.Add("遥测");
                                        }
                                        else if (c.TelesignalisationConfigurationId.HasValue)
                                        {
                                            lName = telesignaRepo.FirstOrDefault(x => x.Id == c.TelesignalisationConfigurationId)?.Name;
                                            arr.Add(lName);
                                            arr.Add("遥信");
                                        }
                                        arr.Add(preset.VideoDev.DevName);
                                        arr.Add(preset.Number.ToString());
                                        arr.Add(preset.Name);
                                        list1.Add(arr);
                                        index++;
                                    }
                                }
                            });
                        });
                    });
                }
                //遥控联动
                var list2 = new List<List<string>>();
                if (telecommandRepo != null && telecommandRepo.Count > 0)
                {
                    telecommandRepo.ForEach(commond =>
                    {
                        var presets = presetPointRepo.Where(x => x.EquipmentInfo?.Id == commond.EquipmentInfoId).ToList();
                        if (presets != null && presets.Count > 0)
                        {
                            presets.ForEach(p =>
                            {
                                var arr = new List<string>();
                                arr.Add(index.ToString());
                                arr.Add(commond.TransformerSubstation.SubstationName);
                                arr.Add(p.EquipmentInfo?.Name);
                                arr.Add(commond.Name);
                                arr.Add("遥控");
                                arr.Add(p.VideoDev.DevName);
                                arr.Add(p.Number.ToString());
                                arr.Add(p.Name);
                                list2.Add(arr);
                                index++;
                            });
                        }
                    });
                }
                excelData.ContentInfoList = list1.Concat(list2).ToList();
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 8摄像机预置位点表
        /// </summary>
        private Task<ExcelData> WritePresetExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "8摄像机预置位点表";//sheet名称
                excelData.TitleTextList = new List<string> { "摄像机预置位点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "目标设备名称", "摄像机名称", "预置位编号", "预置位名称" };//列头文字
                List<PresetPoint> pointDatas;
                List<RobotDeviceInfo> robotDeviceInfoDatas;
                List<RobotInfo> robots;
                lock (locker)
                {
                    pointDatas = _presetPointRepository.GetAllIncluding(p => p.EquipmentInfo, p => p.VideoDev, p => p.EquipmentType)
                    .Where(t => t.IsActive)
                    .Where(t => t.EquipmentInfo == null ? true : t.EquipmentInfo.IsActive)
                    .Where(t => t.EquipmentInfo == null ? true : t.EquipmentInfo.EquipmentType.IsActive)
                    .Where(p => p.VideoDev.TransformerSubstationId == input.TransformerSubstationId)
                    .OrderBy(t => t.VideoDev.SeqNo)
                    .ThenBy(t => t.VideoDev.ChannelNo)
                    .ThenBy(t => t.Number)
                    .ToList();
                    //var pre = _robotPresetRepository.GetAll().ToList();
                    robotDeviceInfoDatas = _robotPresetRepository.GetAllIncluding(t => t.RobotInfo)
                    .Where(p => p.IsActive && p.RobotInfo.TransformerSubstationId == input.TransformerSubstationId).ToList();
                    robots = _robotInfoRepository.GetAllIncluding(t => t.GeneralCamera, t => t.InfraredCamera,t=>t.ManufacturerInfo).ToList();

                }

                if (pointDatas != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    pointDatas.ForEach(t =>
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        arr.Add(input.SubstationName);
                        arr.Add(t.EquipmentInfo == null ? "" : t.EquipmentInfo.Name);
                        arr.Add(t.VideoDev.DevName);
                        arr.Add(t.Number.ToString()); //设备大类 暂时设置为只有视频
                        arr.Add(t.Name);
                        list.Add(arr);
                        index++;
                    });
                    excelData.ContentInfoList = list;
                }
                if (robotDeviceInfoDatas != null && robots != null)
                {
                    var generalCameralist = new List<List<string>>();
                    var infraredCameralist = new List<List<string>>();

                    int index = excelData.ContentInfoList.Count;

                    foreach (var item in robotDeviceInfoDatas)
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        arr.Add(input.SubstationName);
                        //arr.Add(item.DeviceName);
                        arr.Add("");

                        var robot = robots.FirstOrDefault(r => r.Id == item.RobotInfoId);
                        if (robot == null)
                        {
                            continue;
                        }

                        if (item.RecognitionTypeList != "红外测温" && !item.DeviceName.Contains("红外")) //用主设备名称来判断机器人的摄像头
                        {
                            arr.Add(robot.GeneralCamera?.DevName);
                            arr.Add(item.DeviceId);
                            
                            arr.Add(string.IsNullOrWhiteSpace(item.DeviceName) ? "" : item.DeviceName);
                            generalCameralist.Add(arr);
                            index++;
                        }

                    }
                    foreach (var item in robotDeviceInfoDatas)
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        arr.Add(input.SubstationName);
                        arr.Add("");

                        var robot = robots.FirstOrDefault(r => r.Id == item.RobotInfoId);
                        if (robot == null)
                        {
                            continue;
                        }

                        if (item.RecognitionTypeList == "红外测温"|| item.DeviceName.Contains("红外")) //用主设备名称来判断机器人的摄像头
                        {
                            arr.Add(robot.InfraredCamera?.DevName);
                            arr.Add(item.DeviceId);
                            arr.Add(string.IsNullOrWhiteSpace(item.DeviceName) ? "" : item.DeviceName);
                            infraredCameralist.Add(arr);
                            index++;
                        }
                    }
                    excelData.ContentInfoList.AddRange(generalCameralist);
                    excelData.ContentInfoList.AddRange(infraredCameralist);
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 7遥调点表
        /// </summary>
        private Task<ExcelData> WriteTeleDebugExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "7遥调点表";//sheet名称
                excelData.TitleTextList = new List<string> { "遥调点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "设备名称", "点位名称", "操作类型",
                    "“单点对象状态=0”或“双点对象状态=1”的遥调状态描述","“单点对象状态=1”或“双点对象状态=2”的遥调状态描述",
                    "遥调编码" };//列头文字
                return excelData;
            };
            return new Task<ExcelData>(func);
        }
        /// <summary>
        /// 6遥测点表
        /// </summary>
        private Task<ExcelData> WriteTelemeteringExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "6遥测点表";//sheet名称
                excelData.TitleTextList = new List<string> { "遥测点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "设备名称", "点位名称", "单位","遥测编码","越限阈值（上限）","越限阈值（下限）" };//列头文字
                List<TelemeteringConfiguration> repo;
                List<EquipmentType> equipmentTypeRepo;
                lock (locker)
                {
                    repo = _telemeteringConfigurationResitory
                    .GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo, t => t.TelemeteringAlarmStrategys)
                    .Where(t => t.IsActive)
                    .Where(t => t.IsSendDispatcher)
                    .Where(t => t.EquipmentInfo.IsActive)
                    .Where(t => t.EquipmentType.IsActive)
                    .Where(t => t.TransformerSubstationId == input.TransformerSubstationId)
                    .OrderBy(t => t.EquipmentType.SeqNo)
                    .ThenBy(t => t.EquipmentInfo.SeqNo)
                    .ThenBy(t => t.SeqNo)
                    .ToList();
                    equipmentTypeRepo = _equipmentTypeRepository.GetAllIncluding().ToList();
                }


                if (repo != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    repo.ForEach(t =>
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        index++;
                        var alarm = t.TelemeteringAlarmStrategys.OrderBy(t => t.SeqNo).FirstOrDefault();
                        arr.Add(t.TransformerSubstation.SubstationName);
                        arr.Add(t.EquipmentInfo.Name);
                        arr.Add(t.Name);
                        arr.Add(t.Unit);
                        arr.Add(t.DispatcherAddress.ToString());
                        arr.Add(t.UpperLimit.ToString());
                        arr.Add(t.LowerLimit.ToString());

                        //if (alarm != null)
                        //{
                        //    arr.Add(alarm.MaxValue.ToString()); //越限阈值（上限）
                        //    arr.Add(alarm.MinValue.ToString()); //越限阈值（下线）
                        //}
                        //else
                        //{
                        //    arr.Add(""); //越限阈值（上限）
                        //    arr.Add(""); //越限阈值（下线）
                        //}

                        list.Add(arr);
                    });
                    excelData.ContentInfoList = list;
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 5遥信点表
        /// </summary>
        private Task<ExcelData> WriteTeleSignalExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "5遥信点表";//sheet名称
                excelData.TitleTextList = new List<string> { "遥信点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "设备名称", "点位名称", "“单点对象状态=0”或“双点对象状态=1”的遥信状态描述", "“单点对象状态=1”或“双点对象状态=2”的遥信状态描述", "遥信编码","所属类别" };//列头文字

                lock (locker)
                {
                    var sTypes = _equipmentTypeRepository.GetAll()
                    .Where(eT => eT.EquipmentTypeLevel == EquipmentTypeLevelEnum.System && eT.IsActive)
                    .Select(et => new
                    {
                        Id = et.Id,
                        Name = et.Name,
                    }).ToList();
                    if (sTypes == null || sTypes.Count == 0)
                        return excelData;
                    var equipmentTypes = _equipmentTypeRepository.GetAll()
                    .Where(eT => eT.IsActive)
                    .ToList();
                    //获取类型级设备类型
                    var type_2 = sTypes.Join(equipmentTypes, sT => sT.Id, t => t.EquipmentTypeId, (sT, t) => new
                    {
                        Id = t.Id,
                        Name = t.Name,
                        ParentName = sT.Name,
                    }).ToList();

                    var signalDatas = _telesignalisationConfigurationResitory
                    .GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                        .Where(t => t.IsActive)
                        .Where(t => t.IsSendDispatcher)
                        .Where(t => t.EquipmentInfo.IsActive)
                        .Where(t => t.EquipmentType.IsActive)
                        .Where(t => t.TransformerSubstationId == input.TransformerSubstationId)
                        .OrderBy(t => t.EquipmentType.SeqNo)
                        .ThenBy(t => t.EquipmentInfo.SeqNo)
                        .ThenBy(t => t.SeqNo).ToList();
                    var datas = type_2.Join(signalDatas, type => type.Id, singal => singal.EquipmentType?.EquipmentTypeId, (type, singal) => new
                    {
                        Name = singal.Name,
                        SubstationName = singal.TransformerSubstation.SubstationName,
                        EquipmentInfoName = singal.EquipmentInfo.Name,
                        NoContent = singal.NoContent,
                        YesContent = singal.YesContent,
                        DispatcherAddress = singal.DispatcherAddress,
                        SysEquipmentTypeName = type.ParentName
                    }).ToList();

                    if (datas != null)
                    {
                        var list = new List<List<string>>();
                        int index = 1;
                        datas.ForEach(t =>
                        {
                            var arr = new List<string>();
                            arr.Add(index.ToString());
                            index++;
                            arr.Add(t.SubstationName);
                            arr.Add(t.EquipmentInfoName);
                            arr.Add(t.Name);
                            arr.Add(t.NoContent);
                            arr.Add(t.YesContent);
                            arr.Add(t.DispatcherAddress.ToString());
                            arr.Add(t.SysEquipmentTypeName);
                            list.Add(arr);
                        });
                        excelData.ContentInfoList = list;
                    }
                    return excelData;
                }

            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 4遥控点表
        /// </summary>
        private Task<ExcelData> WriteTeleCommandExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "4遥控点表";//sheet名称
                excelData.TitleTextList = new List<string> { "遥控点表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "设备名称", "点位名称", "“单点对象状态=0”或“双点对象状态=1”的遥控状态描述", "“单点对象状态=1”或“双点对象状态=2”的遥控状态描述", "遥控编码" };//列头文字
                List<TelecommandConfiguration> repo;
                lock (locker)
                {
                    repo = _telecommandConfigurationResitory
                    .GetAllIncluding(t => t.EquipmentType, t => t.TransformerSubstation, t => t.EquipmentInfo)
                    .Where(t => t.IsActive)
                    .Where(t => t.IsSendDispatcher)
                    .Where(t => t.EquipmentInfo.IsActive)
                    .Where(t => t.EquipmentType.IsActive)
                    .Where(t => t.TransformerSubstationId == input.TransformerSubstationId)
                    .OrderBy(t => t.EquipmentType.SeqNo)
                    .ThenBy(t => t.EquipmentInfo.SeqNo)
                    .ThenBy(t => t.SeqNo).ToList();
                }

                if (repo != null)
                {
                    var list = new List<List<string>>();
                    int index = 1;
                    repo.ForEach(t =>
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        index++;
                        arr.Add(t.TransformerSubstation.SubstationName);
                        arr.Add(t.EquipmentInfo.Name);
                        arr.Add(t.Name);
                        arr.Add(t.NoContent);
                        arr.Add(t.YesContent);
                        arr.Add(t.DispatcherAddress.ToString());
                        list.Add(arr);
                    });
                    excelData.ContentInfoList = list;
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 3.2非视频设备配置表
        /// </summary>

        private Task<ExcelData> WriteEquipmentExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();

                excelData.SheetName = "3.2非视频设备配置表";//sheet名称
                excelData.TitleTextList = new List<string> { "非视频设备配置表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                { "序号", "所亭名称", "安装区域", "位置描述", "设备大类", "设备子类",  "设备名称" };//列头文字
                lock (locker)
                {
                    var eTypeDatas = _equipmentTypeRepository.GetAll().Where(eType => eType.IsActive && eType.EquipmentTypeLevel == EquipmentTypeLevelEnum.Type);
                    var equipmentInfos = _equipmentInfoRepository
                    .GetAllIncluding(t => t.TransformerSubstation, t => t.EquipmentType)
                    .Where(t => t.IsActive)
                    .Where(t => t.EquipmentType.IsActive)
                     .Where(t => t.TransformerSubstationId == input.TransformerSubstationId && t.IsActive)
                     .OrderBy(t => t.EquipmentType.SeqNo)
                     .ThenBy(t => t.SeqNo)
                     .ToList();
                    var datas = equipmentInfos.Join(eTypeDatas, e => e.EquipmentType?.EquipmentTypeId, eType => eType.Id, (e, eType) => new
                    {
                        EquipmentInfoName = e.Name,
                        InstallationArea = e.InstallationArea,
                        PostionDescription = e.PostionDescription,
                        EquipmentTypeName = e.EquipmentType?.Name,
                        EquipmentTypeParentName = eType?.Name,
                    }).ToList();
                    var equipmentTypeRepo = _equipmentTypeRepository.GetAllIncluding();
                    if (datas != null)
                    {

                        var list = new List<List<string>>();
                        int index = 1;
                        datas.ForEach(t =>
                        {
                            var arr = new List<string>();
                            arr.Add(index.ToString());
                            index++;
                            arr.Add(input.SubstationName);
                            arr.Add(t.InstallationArea);
                            arr.Add(t.PostionDescription);
                            arr.Add(t.EquipmentTypeParentName); //设备大类
                            arr.Add(t.EquipmentTypeName); //
                            arr.Add(t.EquipmentInfoName);
                            list.Add(arr);
                        });
                        excelData.ContentInfoList = list;
                    }

                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 3.1视频设备配置表
        /// </summary>

        private Task<ExcelData> WriteVideoDevEquipmentSettingExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "3.1视频设备配置表";//sheet名称
                excelData.TitleTextList = new List<string> { "视频设备配置表" }; //标题
                excelData.ClumnHeadTextList = new List<string>()
                    { "序号", "所亭名称", "安装区域", "位置描述", "设备大类", "设备子类", "双光谱摄像机通道", "设备名称", "音频支持" };//列头文字

                List<VideoDev> repo;
                lock (locker)
                {
                    repo = _videoDevRepository.GetAllIncluding(t => t.TransformerSubstation)
                    .Where(t => t.IsActive)
                   .Where(t => t.TransformerSubstationId == input.TransformerSubstationId)
                   .Where(t => t.VideoDevId.HasValue)
                   .OrderBy(t => t.SeqNo)
                   .ToList(); //除了NVR
                }

                if (repo != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    repo.ForEach(t =>
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        index++;
                        arr.Add(t.TransformerSubstation.SubstationName);
                        arr.Add(t.InstallationArea);
                        arr.Add(t.PostionDescription);
                        arr.Add("视频"); //设备大类 暂时设置为只有视频
                        var devType = base.GetEnumDescription<VideoDevTypeEnum>(t.DevType.ToString());
                        if (t.DevType == VideoDevTypeEnum.热成像枪机_可见光
                        || t.DevType == VideoDevTypeEnum.热成像球机_可见光
                        || t.DevType == VideoDevTypeEnum.轨道式机器人_可见光
                        || t.DevType == VideoDevTypeEnum.轮式机器人_可见光

                        )
                        {
                            arr.Add(devType.Substring(0, devType.Length - 1));
                            arr.Add("A");
                        }
                        else if (t.DevType == VideoDevTypeEnum.轨道式机器人_热成像
                       || t.DevType == VideoDevTypeEnum.热成像球机_热成像
                       || t.DevType == VideoDevTypeEnum.热成像枪机_热成像
                       || t.DevType == VideoDevTypeEnum.轮式机器人_热成像

                       )
                        {
                            arr.Add(devType.Substring(0, devType.Length - 1));
                            arr.Add("B");
                        }
                        else
                        {
                            arr.Add(devType);
                            arr.Add("");
                        }
                        arr.Add(t.DevName);
                        string voiceTypeStr = "";
                        if (t.VoiceType != VoiceTypeEnum.Null)
                            voiceTypeStr = ((DescriptionAttribute)Attribute.GetCustomAttribute(typeof(VoiceTypeEnum).GetField(t.VoiceType.ToString()), typeof(DescriptionAttribute))).Description;
                        arr.Add(voiceTypeStr);
                        list.Add(arr);
                    });
                    excelData.ContentInfoList = list;
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 2视频服务器配置信息
        /// </summary>

        private Task<ExcelData> WriteVideoServiceEquipmentSettingExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = () =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "2视频服务器配置信息";//sheet名称
                excelData.TitleTextList = new List<string> { "视频服务器配置信息" }; //标题
                excelData.ClumnHeadTextList = new List<string>() { "序号", "所亭名称", "IP地址及端口号", "用户名", "密码", "备注" };//列头文字
                List<VideoDev> repo;
                lock (locker)
                {
                    repo = _videoDevRepository.GetAllIncluding(t => t.TransformerSubstation)
                            .Where(t => t.IsActive)
                           .Where(t => t.TransformerSubstationId == input.TransformerSubstationId).OrderBy(t => t.SeqNo).ThenBy(t => t.DevName)
                           .Where(t => !t.VideoDevId.HasValue).ToList(); //只查NVR
                }
                if (repo != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    repo.ForEach(t =>
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        index++;
                        arr.Add(t.TransformerSubstation.SubstationName);
                        arr.Add($"{(string.IsNullOrWhiteSpace(t.ExternalCommIP) ? t.IP : t.ExternalCommIP)}:{t.Port}");
                        arr.Add(t.DevUserName);
                        arr.Add(t.DevPassword);
                        arr.Add("");//备注 暂时不设置
                        list.Add(arr);
                    });
                    excelData.ContentInfoList = list;
                }
                return excelData;
            };
            return new Task<ExcelData>(func);
        }

        /// <summary>
        /// 被控站基本情况
        /// </summary>

        private Task<ExcelData> WriteStationExcelData(ExportBaseDataConditionInput input)
        {
            Func<ExcelData> func = new Func<ExcelData>(() =>
            {
                ExcelData excelData = new ExcelData();
                excelData.SheetName = "1被控站基本情况";//sheet名称
                excelData.TitleTextList = new List<string> { "被控站基本情况" }; //标题
                excelData.ClumnHeadTextList = new List<string>() { "序号", "线路名称", "所亭名称", "辅助监控设备布置图", "所亭坐标", "HTTP服务的地址" };//列头文字
                List<TransformerSubstation> repo;
                lock (locker)
                {
                    repo = _transformerSubstationRepository.GetAllIncluding(t => t.PowerSupplyLine)
                        .Where(t => t.Id == input.TransformerSubstationId && t.IsActive).OrderBy(t => t.SeqNo).ToList();
                }
                if (repo != null)
                {

                    var list = new List<List<string>>();
                    int index = 1;
                    repo.ForEach(t =>
                    {
                        var arr = new List<string>();
                        arr.Add(index.ToString());
                        index++;
                        arr.Add(t.PowerSupplyLine.LineName);
                        arr.Add(t.SubstationName);
                        arr.Add("");//辅助监控设备布置图 暂时不设置
                        arr.Add($"{t.Latitude.Value.ToString("F5")},{t.Longitude.Value.ToString("F5")}");
                        arr.Add(t.ExternalCommAddress);//HTTP服务的地址 暂时不设置
                        list.Add(arr);
                    });
                    excelData.ContentInfoList = list;
                }
                return excelData;
            });
            return new Task<ExcelData>(func);
        }
        #endregion

        /// <summary>
        ///  获取当前主站；调用方向：辅助监控系统巡检服务  ->  辅助监控系统
        /// </summary>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        [ShowApi]
        public RequestResult<MasterStationTypeOutput> GetMasterStationType()
        {
            RequestResult<MasterStationTypeOutput> rst = new RequestResult<MasterStationTypeOutput>();
            rst.Flag = true;
            try
            {
                rst.ResultData = new MasterStationTypeOutput
                {
                    Code = _appServiceConfiguration.MasterStationType,
                    BaseUrl = _appServiceConfiguration.MasterStationBaseUrl
                };
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }
            return rst;
        }
        public async Task BatchUpdateAsync(IEnumerable<Guid> ids, Action<TelesignalisationConfiguration> updateAction)
        {
            // 获取要更新的实体
            var entitiesToUpdate = await _telesignalisationConfigurationResitory.GetAllListAsync(t => ids.Contains(t.Id));

            // 遍历每个实体，执行更新操作
            foreach (var entity in entitiesToUpdate)
            {
                updateAction(entity); // 执行传入的更新操作
                await _telesignalisationConfigurationResitory.UpdateAsync(entity); // 更新实体
            }
        }

        /// <summary>
        /// 修改104实时数据地址
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        public RequestResult<TransformerSubstationOutput> UpdateDataMonitoringAddressById(EditDataMonitoringAddressInput input)
        {
            var rst = new RequestResult<TransformerSubstationOutput>();
            try
            {
                var data = _transformerSubstationRepository.FirstOrDefault(u => u.Id == input.Id);
                if (data == null) return rst;
                var currentUser = base.GetCurrentUser();
                if (string.IsNullOrEmpty(data.DataMonitoringAddress))
                {
                    data.DataMonitoringAddress = input.DataMonitoringAddress;
                }
                data.LastModificationTime = DateTime.Now;
                rst.Flag = true;
                rst.ResultData = ObjectMapper.Map<TransformerSubstationOutput>(data);
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }

            return rst;
        }
        /// <summary>
        /// 修改机器人服务地址
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        public RequestResult<TransformerSubstationOutput> UpdateRobotServerAddressById(EditRobotServerAddressInput input)
        {
            var rst = new RequestResult<TransformerSubstationOutput>();
            try
            {
                var data = _transformerSubstationRepository.FirstOrDefault(u => u.Id == input.Id);
                if (data == null) return rst;
                data.RobotServerAddress = input.RobotServerAddress;
                if (string.IsNullOrWhiteSpace(data.RobotServiceBaseUrl))
                {
                    data.RobotServiceBaseUrl = "http://127.0.0.1:8092/isas/api/robotService/";
                }
                data.LastModificationTime = DateTime.Now;
                rst.Flag = true;
                rst.ResultData = ObjectMapper.Map<TransformerSubstationOutput>(data);
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                Log4Helper.Error(this.GetType(), "变电所管理服务", ex);

            }

            return rst;
        }
    }
}