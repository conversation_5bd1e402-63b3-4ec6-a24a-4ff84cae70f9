﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAsdu140Fltacttype
    {
        public string Companyname { get; set; }
        public int Puctgycode { get; set; }
        public int Actcode { get; set; }
        public string Actname { get; set; }

        public virtual ImManufacturer CompanynameNavigation { get; set; }
        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
    }
}
