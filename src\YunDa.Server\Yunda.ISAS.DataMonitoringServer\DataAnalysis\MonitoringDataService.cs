﻿using Abp.Dependency;
using Amazon.Runtime.Internal.Transform;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Forms;
using ToolLibrary;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Helper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.ProtectionDeviceHandle;
using Yunda.SOMS.DataMonitoringServer.TcpSocket.Server;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.SOMS.Commdb.Models;
using YunDa.SOMS.DataTransferObject.GeneralInformation.SecondaryCircuitDto;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis
{

    public class MonitoringDataService : ISingletonDependency
    {
        /// <summary>
        /// 是否在运行
        /// </summary>
        //public bool IsRunning { get; set; }
        private readonly DataCollectionTask _dataCollectionTask;
        private readonly TelemeteringResultSaveTask _telemeteringResultSaveTask;
        private readonly ConfigurationHepler _configurationHepler;
        private readonly WebApiRequest _webApiRequest;
        private readonly RunningDataCache _runningDataCache;
        private readonly TelecomDataCenter _telecomDataCenter;
        private readonly DotNettyTcpServer _dotNettyTcpServer;
        private readonly ProtectionDeviceDataCenter _protectionDeviceDataCenter;
        private readonly ProtectionDeviceDZDataHandle _protectionDeviceDZDataHandle;
        private readonly RedisDataRepository _redisDataRepository;
        WPF.ViewModel.Content _settingModel;
        public MonitoringDataService(WebApiRequest webApiRequest,
            RunningDataCache runningDataCache,
            DataCollectionTask dataCollectionTask,
            TelemeteringResultSaveTask telemeteringResultSaveTask,
             ConfigurationHepler configurationHepler,
             TelecomDataCenter telecomDataCenter,
             DotNettyTcpServer dotNettyTcpServer,
             ProtectionDeviceDZDataHandle protectionDeviceDZDataHandle,
             ProtectionDeviceDataCenter protectionDeviceDataCenter,
             RedisDataRepository redisDataRepository
            //WebApiServer webApiServer
            )
        {
            _telecomDataCenter = telecomDataCenter;
            _webApiRequest = webApiRequest;
            _runningDataCache = runningDataCache;
            _dataCollectionTask = dataCollectionTask;
            _telemeteringResultSaveTask = telemeteringResultSaveTask;
            _configurationHepler = configurationHepler;
            _dotNettyTcpServer = dotNettyTcpServer;
            _protectionDeviceDZDataHandle = protectionDeviceDZDataHandle;
            _protectionDeviceDataCenter = protectionDeviceDataCenter;
            _redisDataRepository = redisDataRepository;
        }
        private bool IsOpenWebSocket = false;
        private Action<string> _recvMsgAc =msg =>
        {
            MessageBox.Show(msg);
        };
        /// <summary>
        /// 开始服务
        /// </summary>
        /// <returns></returns>
        public async void DataServiceStart(WPF.ViewModel.Content settingModel,  IEnumerable< ConnectionConfig> connections)
        {
            SetMonitoringData(_configurationHepler.SubstationId);
            _settingModel = settingModel;
            _telemeteringResultSaveTask.SaveStart(connections);//开启遥测数据保存
            _dataCollectionTask.CollectionStart(connections);//开启数据采集
            foreach (var connection in connections)
            {
                if (connection.DataSourceCategoryName == (int)DataSourceCategoryEnum.Zongzi)
                {
                    MonitoringEventBus.LogHandler("清理scmgateway程序", "装置信息");
                    PortProcessManager.KillProcessByName("scmgateway");
                    MonitoringEventBus.LogHandler("启动scmgateway程序", "装置信息");
                    await _protectionDeviceDataCenter.InitProtectionDeviceComms(connection);
                    await InitSecondaryCircuitLogicExpressionDic(connection);
                }
            }
        
        }
        private void StartWebsocket()
        {
            //var path = _configurationHepler.GetAppsettingsValue("ConnectionStrings", "Websocket", "Path");
            //var port = _configurationHepler.GetAppsettingsValue("ConnectionStrings", "Websocket", "Port");
            //_webSocketServer.WebSocketStartAsync(path, int.Parse(port));
        }
        public void CallAllData()
        {
            _dataCollectionTask.CallAllData();
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public void DataServiceStop()
        {
            _dataCollectionTask.CollectionStop(); //数据采集停止
            _dotNettyTcpServer.StopServerAsync();
            if (_settingModel.DataSourceCategoryName == "综自")
            {
                _protectionDeviceDataCenter.StopAllProcesses();
                PortProcessManager.KillProcessByName("scmgateway");

            }

        }
        /// <summary>
        /// 从服务端获取遥信遥测数据 填入缓存中
        /// </summary>
        private void SetMonitoringData(Guid transformerSubstationId)
        {
            try
            {
                _webApiRequest.GetTransubstationInfo(transformerSubstationId);
                _webApiRequest.GetEquipmentInfoSimDatas(transformerSubstationId);
                _webApiRequest.GetEquipmentTypeSimDatas();
                _webApiRequest.GetDMAlarmCategorySimpleSimDatas(transformerSubstationId);
                _webApiRequest.GetTelemeteringAlarmStrategySimDatas(transformerSubstationId);
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler(ex.StackTrace, "异常信息");
            }
        }
        public async Task DeleteYXYCRedis(IEnumerable<DataSourceCategoryEnum> dataSourceCategorites)
        {
            foreach (var dataSourceCategory in dataSourceCategorites)
            {
                string rediskey1 = "telemeteringModelList_" + dataSourceCategory.ToString();
                await _redisDataRepository.TelemeteringModelListRedis.DeleteKeyAsync(rediskey1);
                await Task.Delay(1000);
                string rediskey2 = "telesignalisationModelList_" + dataSourceCategory.ToString();
                await _redisDataRepository.TelesignalisationModelListRedis.DeleteKeyAsync(rediskey2);
                await Task.Delay(1000);
            }
        }

        private async Task InitSecondaryCircuitLogicExpressionDic(ConnectionConfig connection)
        {
            try
            {
                _runningDataCache.SecondaryCircuitLogicExpressionDic.Clear();
                var yxlist =await _redisDataRepository.GetTelesignalisationModels();
                var yclist =await _redisDataRepository.GetTelemeteringModels();
                var ycCheckList =( await _redisDataRepository.GetCheckTelemeteringModels())?.ToDictionary(t => t.Id);
                var yxSendList =( await _redisDataRepository.GetSendTelesignalisationModels())?.ToDictionary(t => t.Id);

                var yxDic = yxlist.ToDictionary(t => t.Id);
                var ycDic = yclist.ToDictionary(t => t.Id);
                var list = _webApiRequest.GetSecondaryCircuitLogicExpressionList();
                foreach (var item in list) 
                {
                    
                    // 正则表达式匹配花括号中的内容，包括 GUID
                    string pattern = @"\{([^}]*:([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}))\}";
                    Regex regex = new Regex(pattern);

                    item.LogicalExpression = regex.Replace(item.LogicalExpression, match =>
                    {
                        // 提取 GUID
                        var guidStr = match.Groups[2].Value;
                        var guid = Guid.Parse(guidStr);

                        // 查找对应的 key
                        if (yxDic.TryGetValue(guid, out var yx))
                        {
                            return $"{{{yx.DeviceAddress}_{yx.CPUSector}_{yx.DispatcherAddress}_{(int)yx.DataSourceCategory}}}";

                        }
                        else if (ycDic.TryGetValue(guid, out var yc))
                        {
                            // 构造 key 替换
                            return $"{{{yc.DeviceAddress}_{yc.CPUSector}_{yc.DispatcherAddress}_{(int)yc.DataSourceCategory}}}";
                        }
                        // 如果未找到，保留原内容
                        return match.Value;
                    });
                    // 输出替换后的 LogicalExpression
                    Debug.WriteLine($"替换后的 LogicalExpression: {item.LogicalExpression}");
                    // 正则表达式匹配花括号中的内容
                    string pattern1 = @"\{([^}]*)\}";
                    Regex regex1 = new Regex(pattern1);
                    // 提取匹配结果
                    MatchCollection matches = regex1.Matches(item.LogicalExpression);
                    List<string> extractedValues = new List<string>();
                    

                    if (!item.IsCompleteExpression)
                    {
                        if (ycCheckList !=null&& ycCheckList.ContainsKey(item.TelemeteringConfigurationId.Value))
                        {
                            var checktelemeteringConfiguration = ycCheckList[item.TelemeteringConfigurationId.Value];

                            float? upperLimit = checktelemeteringConfiguration.UpperLimit;
                            float? lowerLimit = checktelemeteringConfiguration.LowerLimit;
                            item.LogicalExpression = $"({item.LogicalExpression})>={lowerLimit}&&({item.LogicalExpression})<={upperLimit}";
                        }

                    }
                    if (yxSendList != null)
                    {
                        if (yxSendList.ContainsKey(item.TelesignalisationConfigurationId.Value))
                        {
                            var sendtelesignalConfiguration = yxSendList[item.TelesignalisationConfigurationId.Value];


                            foreach (Match match in matches)
                            {
                                string key = match.Groups[1].Value;
                                var logicExpressionTelesignalisation = new LogicExpressionTelesignalisation
                                {
                                    SecondaryCircuitId = item.SecondaryCircuitId,
                                    LogicExpression = item.LogicalExpression,
                                    TelesignalisationAddr = $"{sendtelesignalConfiguration.DeviceAddress}_{sendtelesignalConfiguration.CPUSector}_{sendtelesignalConfiguration.DispatcherAddress}_{(int)sendtelesignalConfiguration.DataSourceCategory}"
                                };
                                if (_runningDataCache.SecondaryCircuitLogicExpressionDic.ContainsKey(key))
                                {
                                    _runningDataCache.SecondaryCircuitLogicExpressionDic[key].Add(logicExpressionTelesignalisation);
                                }
                                else
                                {
                                    _runningDataCache.SecondaryCircuitLogicExpressionDic.Add(key, new List<LogicExpressionTelesignalisation> { logicExpressionTelesignalisation });
                                }
                            }
                        }
                    }

                    Debug.WriteLine($"最终 LogicalExpression: {item.LogicalExpression}");

                }
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler(ex.StackTrace, "异常信息");

            }
        }

        
       
    }
}