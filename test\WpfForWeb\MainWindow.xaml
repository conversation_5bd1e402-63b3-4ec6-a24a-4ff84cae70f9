﻿<Window x:Class="WpfForWeb.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:cefSharp="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        Title="MainWindow" Height="600" Width="800"
        xmlns:local="clr-namespace:WpfForWeb"
        SnapsToDevicePixels="True"
        WindowStartupLocation="CenterScreen" 
        WindowState="Maximized"
        Icon="Photoes/WpfForWeb.ico"
        >
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/WpfForWeb;Component/Styles/MetroWindow.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Border Background="#D6DBE9">
            <cefSharp:ChromiumWebBrowser Name="cef_Chrome" Grid.Row="0" Address=""/>
        </Border>
        <Border>
            <Grid>
                <local:Wait x:Name="w_PageWait" Visibility="Collapsed"/>
                <TextBlock x:Name="tb_Message" Text="网络连接错误或者请求服务端报错！" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="30" Visibility="Collapsed"/>
            </Grid>
        </Border>
        <!--<Border VerticalAlignment="Top" HorizontalAlignment="Right" Background="Transparent" Height="10" BorderThickness="0" MouseEnter="Border_MouseEnter" Width="70">
        </Border>-->
        <!--<Border  x:Name="g_WindowButton" VerticalAlignment="Top" HorizontalAlignment="Right" Height="34" MouseLeave="Border_MouseLeave" Padding="10,4,10,0" Background="White" Visibility="Collapsed">
            <Grid MouseLeftButtonDown="TitleGrid_MouseLeftButtonDown" Margin="0" >
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                -->
        <!--<Image Source="Photoes/fileServerIco.ico" Height="25" Width="25" Margin="2,0" VerticalAlignment="Center"/>
                <TextBlock x:Name="WindowTitle" Grid.Column="1" Text="检修管理系统" VerticalAlignment="Center" 
                                       FontSize="18" FontWeight="Bold" Margin="0,0" Foreground="White"/>-->
        <!--
                <Button x:Name="MaxWinButton" Grid.Column="3" Style="{StaticResource WinMaxBtnStyle}" 
                                        VerticalContentAlignment="Center" 
                                        HorizontalContentAlignment="Center"
                        Click="MaxButton_Click">
                    <Button.Content>
                        <Grid>
                            <StackPanel x:Name="sp_Max" Visibility="Collapsed">
                                <Path Stroke="#81BD5D" StrokeThickness="2" Data="M1,1 L14,1 14,14 1,14 1,1"/>
                            </StackPanel>
                            <Grid x:Name="g_Normal"  Visibility="Visible">
                                <Path Stroke="#81BD5D" StrokeThickness="2" Data="M0,5 11,5 11,14 1,14 1,5"/>
                                <Path Stroke="#81BD5D" StrokeThickness="2" Data="M5,1 14,1 14,9 5,9 5,0"/>
                            </Grid>
                        </Grid>
                    </Button.Content>
                </Button>
                <Button x:Name="MinWinButton" Grid.Column="2" Style="{StaticResource WinMinBtnStyle}" 
                                        VerticalContentAlignment="Center" 
                                        HorizontalContentAlignment="Center"
                        Click="MinButton_Click">
                    <Button.Content>
                        <StackPanel>
                            <Path Stroke="#81BD5D" StrokeThickness="2" Data="M1,4 15,4"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
                <Button x:Name="CloseWinButton" Grid.Column="4" Style="{StaticResource WinCloseBtnStyle}"
                                        HorizontalContentAlignment="Center" 
                                        VerticalContentAlignment="Center"
                        Click="CloseButton_Click">
                    <Button.Content>
                        <StackPanel>
                            <Path Stroke="#81BD5D" StrokeThickness="2" Data="M1,1 14,14 M1,14 14,1"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </Grid>
        </Border>-->
    </Grid>
</Window>
