﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImEventtype
    {
        public int Evtcode { get; set; }
        public int Puctgycode { get; set; }
        public string Evtname { get; set; }
        public string Alertlevel { get; set; }
        public string Isyicisb { get; set; }

        public virtual ImAlertlevel AlertlevelNavigation { get; set; }
        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
    }
}
