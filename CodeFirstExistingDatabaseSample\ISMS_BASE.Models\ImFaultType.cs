﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFaultType
    {
        public ImFaultType()
        {
            ImFaultActTypes = new HashSet<ImFaultActType>();
        }

        public int FaultCode { get; set; }
        public string FaultName { get; set; } = null!;

        public virtual ICollection<ImFaultActType> ImFaultActTypes { get; set; }
    }
}
