﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImNotifyTask
    {
        public ImNotifyTask()
        {
            ImNotifyTaskAlertObjs = new HashSet<ImNotifyTaskAlertObj>();
            ImNotifyTaskPhones = new HashSet<ImNotifyTaskPhone>();
        }

        public string TaskId { get; set; } = null!;
        public string AlertType { get; set; } = null!;
        public string TaskDesc { get; set; } = null!;

        public virtual TbNotifyTask Task { get; set; } = null!;
        public virtual ICollection<ImNotifyTaskAlertObj> ImNotifyTaskAlertObjs { get; set; }
        public virtual ICollection<ImNotifyTaskPhone> ImNotifyTaskPhones { get; set; }
    }
}
