﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServerTempDB.Models
{
    public partial class SessionDatum
    {
        public string SessionId { get; set; } = null!;
        public Guid? CompiledDefinition { get; set; }
        public Guid? SnapshotDataId { get; set; }
        public bool? IsPermanentSnapshot { get; set; }
        public string? ReportPath { get; set; }
        public int Timeout { get; set; }
        public int? AutoRefreshSeconds { get; set; }
        public DateTime Expiration { get; set; }
        public byte[]? ShowHideInfo { get; set; }
        public byte[]? DataSourceInfo { get; set; }
        public Guid OwnerId { get; set; }
        public string? EffectiveParams { get; set; }
        public DateTime CreationTime { get; set; }
        public bool? HasInteractivity { get; set; }
        public DateTime? SnapshotExpirationDate { get; set; }
        public DateTime? HistoryDate { get; set; }
        public double? PageHeight { get; set; }
        public double? PageWidth { get; set; }
        public double? TopMargin { get; set; }
        public double? BottomMargin { get; set; }
        public double? LeftMargin { get; set; }
        public double? RightMargin { get; set; }
        public bool? AwaitingFirstExecution { get; set; }
        public string? EditSessionId { get; set; }
        public byte[]? DataSetInfo { get; set; }
        public string? SitePath { get; set; }
        public int SiteZone { get; set; }
        public string? ReportDefinitionPath { get; set; }
    }
}
