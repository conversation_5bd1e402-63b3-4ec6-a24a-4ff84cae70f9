﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LoginTest
{
    
    internal class GetCudaEnvTest
    {
        internal static void Run()
        {
            string cudaVersion = GetCudaVersion();
            Console.WriteLine("cudaVersion:" + cudaVersion);
            var envPath =  GetCudaEnvPathVersion();
            Console.WriteLine("envPath:"+envPath);
            var env = GetCudaEnvVersion();
            Console.WriteLine("env:"+env);
           
        }
        static string GetCudaEnvPathVersion()
        {
            string pathVariable = Environment.GetEnvironmentVariable("PATH", EnvironmentVariableTarget.Machine);

            if (!string.IsNullOrEmpty(pathVariable))
            {
                string[] paths = pathVariable.Split(';');

                Console.WriteLine("Paths in PATH environment variable:");

                foreach (string path in paths)
                {
                    Console.WriteLine(path);
                    if (path == "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.0\\bin")
                    {
                        return path;

                    }
                }
            }
            else
            {
                Console.WriteLine("PATH environment variable is not set or does not exist.");
            }
            return null;
        }
        static string GetCudaEnvVersion()
        {
            string variableName = "CUDA_PATH";

            // 查询系统环境变量的值
            string variableValue = Environment.GetEnvironmentVariable(variableName, EnvironmentVariableTarget.Machine);

            if (!string.IsNullOrEmpty(variableValue))
            {
                Console.WriteLine($"The value of {variableName} is: {variableValue}");
                if (variableValue == "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.0")
                {
                    return variableValue;
                }
            }
            else
            {
                Console.WriteLine($"{variableName} is not set or does not exist.");
            }
            return null;
        }
        static string GetCudaVersion()
        {
            const string uninstallKey = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall";
            const string nvidiaKey = "NVIDIA CUDA";
            string cudaVersion = null;

            using (RegistryKey key = Registry.LocalMachine.OpenSubKey(uninstallKey))
            {
                if (key != null)
                {
                    foreach (string subkeyName in key.GetSubKeyNames())
                    {
                        using (RegistryKey subkey = key.OpenSubKey(subkeyName))
                        {
                            object displayName = subkey.GetValue("DisplayName");
                            if (displayName != null && displayName.ToString().Contains(nvidiaKey))
                            {
                                object displayVersion = subkey.GetValue("DisplayVersion");
                                if (displayVersion != null)
                                {
                                    cudaVersion = displayVersion.ToString();
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            return cudaVersion;
        }
    }
}


