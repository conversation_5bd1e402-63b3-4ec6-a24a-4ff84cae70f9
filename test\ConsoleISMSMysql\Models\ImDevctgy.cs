﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDevctgy
    {
        public ImDevctgy()
        {
            ImAnalogdata2010 = new HashSet<ImAnalogdata2010>();
            ImEventflag = new HashSet<ImEventflag>();
            ImEventparam = new HashSet<ImEventparam>();
            ImEventtype2010 = new HashSet<ImEventtype2010>();
            ImFaulttype2010 = new HashSet<ImFaulttype2010>();
            ImPuCtgy = new HashSet<ImPuCtgy>();
        }

        public string Devctgycode { get; set; }
        public string Devctgyname { get; set; }

        public virtual ICollection<ImAnalogdata2010> ImAnalogdata2010 { get; set; }
        public virtual ICollection<ImEventflag> ImEventflag { get; set; }
        public virtual ICollection<ImEventparam> ImEventparam { get; set; }
        public virtual ICollection<ImEventtype2010> ImEventtype2010 { get; set; }
        public virtual ICollection<ImFaulttype2010> ImFaulttype2010 { get; set; }
        public virtual ICollection<ImPuCtgy> ImPuCtgy { get; set; }
    }
}
