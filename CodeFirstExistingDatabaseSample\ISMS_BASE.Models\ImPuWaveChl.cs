﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImPuWaveChl
    {
        public string Id { get; set; } = null!;
        public int PuctgyCode { get; set; }
        public string ChlType { get; set; } = null!;
        public int ChlNum { get; set; }
        public string ChlName { get; set; } = null!;
        public string? ChlUnit1 { get; set; }
        public string? ChlUnit2 { get; set; }

        public virtual ImPuCtgy PuctgyCodeNavigation { get; set; } = null!;
    }
}
