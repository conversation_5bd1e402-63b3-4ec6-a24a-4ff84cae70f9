﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class VwTableDataDup
    {
        public string DataDupId { get; set; } = null!;
        public string TableMapId { get; set; } = null!;
        public string? SrcWhereClause { get; set; }
        public string DataDupCtgy { get; set; } = null!;
        public string SrcTableName { get; set; } = null!;
        public string SrcFullName { get; set; } = null!;
        public string DstFullName { get; set; } = null!;
        public string DstTableName { get; set; } = null!;
        public int MapSeqNo { get; set; }
        public string? MapBeiZhu { get; set; }
    }
}
