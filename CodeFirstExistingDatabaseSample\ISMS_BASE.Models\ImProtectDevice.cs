﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImProtectDevice
    {
        public ImProtectDevice()
        {
            ImDeviceData = new HashSet<ImDeviceDatum>();
            ImDeviceDzs = new HashSet<ImDeviceDz>();
            ImOnlineAnalyses = new HashSet<ImOnlineAnalysis>();
        }

        public string DeviceId { get; set; } = null!;
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string? BayName { get; set; }
        public int? DeviceState { get; set; }
        public int PuctgyCode { get; set; }
        public int CanSwDzzone { get; set; }
        public int Support12yc { get; set; }
        public int SupportVersion { get; set; }
        public int SupportDkjl { get; set; }
        public int? StartOfDkjl { get; set; }
        public int? EndOfDkjl { get; set; }
        public string? DeviceType { get; set; }

        public virtual ImGateWay GateWay { get; set; } = null!;
        public virtual ImPuCtgy PuctgyCodeNavigation { get; set; } = null!;
        public virtual ImStation StatCodeNavigation { get; set; } = null!;
        public virtual ImFltDistDevice? ImFltDistDevice { get; set; }
        public virtual ImSheBeiProtDevice? ImSheBeiProtDevice { get; set; }
        public virtual ICollection<ImDeviceDatum> ImDeviceData { get; set; }
        public virtual ICollection<ImDeviceDz> ImDeviceDzs { get; set; }
        public virtual ICollection<ImOnlineAnalysis> ImOnlineAnalyses { get; set; }
    }
}
