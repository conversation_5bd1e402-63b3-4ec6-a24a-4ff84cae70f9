﻿using Abp.Domain.Entities;
using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceDatum : Entity<string>
    {
        public ImDeviceDatum()
        {
            ImCurveItems = new HashSet<ImCurveItem>();
            ImDeviceActRules = new HashSet<ImDeviceActRule>();
            ImReportCfgData = new HashSet<ImReportCfgDatum>();
        }

        //public string Id { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
        public string? BeiZhu { get; set; }
        public string? SrcTempDataId { get; set; }
        public string Domain { get; set; } = null!;

        public virtual ImProtectDevice Device { get; set; } = null!;
        public virtual ImDeviceVa? ImDeviceVa { get; set; }
        public virtual ImDeviceYc? ImDeviceYc { get; set; }
        public virtual ImDeviceYk? ImDeviceYk { get; set; }
        public virtual ImDeviceYm? ImDeviceYm { get; set; }
        public virtual ImDeviceYx? ImDeviceYx { get; set; }
        public virtual ImSheBeiDeviceDatum? ImSheBeiDeviceDatum { get; set; }
        public virtual ICollection<ImCurveItem> ImCurveItems { get; set; }
        public virtual ICollection<ImDeviceActRule> ImDeviceActRules { get; set; }
        public virtual ICollection<ImReportCfgDatum> ImReportCfgData { get; set; }
    }
}
