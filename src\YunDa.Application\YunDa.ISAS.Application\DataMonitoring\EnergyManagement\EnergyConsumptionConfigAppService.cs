﻿using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Session;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;
using YunDa.SOMS.Entities.DataMonitoring;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    /// <summary>
    /// 能耗配置服务类，提供对能耗配置的相关操作
    /// </summary>
    [Description("能耗配置管理")]
    public class EnergyConsumptionConfigAppService : ISASAppServiceBase, IEnergyConsumptionConfigService
    {
        private readonly IRepository<EnergyConsumptionConfig, Guid> _configRepository;
        private readonly IRepository<EnergyConsumptionDevice, Guid> _deviceRepository;
        private readonly IRepository<TelemeteringConfiguration, Guid> _telemeteringRepository;
        private readonly IRepository<TelesignalisationConfiguration, Guid> _telesignalisationRepository;

        public EnergyConsumptionConfigAppService(
            IRepository<EnergyConsumptionConfig, Guid> configRepository,
            IRepository<EnergyConsumptionDevice, Guid> deviceRepository,
            IRepository<TelemeteringConfiguration, Guid> telemeteringRepository,
            IRepository<TelesignalisationConfiguration, Guid> telesignalisationRepository,
            ISessionAppService sessionAppService) : base(sessionAppService)
        {
            _configRepository = configRepository;
            _deviceRepository = deviceRepository;
            _telemeteringRepository = telemeteringRepository;
            _telesignalisationRepository = telesignalisationRepository;
        }

        /// <summary>
        /// 获取能耗配置列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>配置列表结果</returns>
        [HttpPost, Description("获取能耗配置列表")]
        public async Task<RequestPageResult<EnergyConsumptionConfig>> GetConfigs(PageSearchCondition<EnergyConfigSearchInput> input)
        {
            var result = new RequestPageResult<EnergyConsumptionConfig> { Flag = false };

            try
            {
                // 构建查询
                var query = _configRepository.GetAllIncluding(c => c.EnergyConsumptionDevice,t=>t.TelemeteringConfiguration)
                    .WhereIf(input.SearchCondition.ConfigType.HasValue, c => c.ConfigType == input.SearchCondition.ConfigType.Value)
                    .WhereIf(input.SearchCondition.ValueType.HasValue, c => c.ValueType == input.SearchCondition.ValueType.Value)
                    .WhereIf(input.SearchCondition.EnergyConsumptionDeviceId.HasValue, c => c.EnergyConsumptionDeviceId == input.SearchCondition.EnergyConsumptionDeviceId.Value)
                    .WhereIf(!input.SearchCondition.Name.IsNullOrEmpty(), c => c.Name.Contains(input.SearchCondition.Name));

                // 计算总数
                int totalCount = await query.CountAsync();
                result.TotalCount = totalCount;

                // 分页和排序
                var items = await query
                    .OrderBy(input.Sorting.IsNullOrEmpty() ? "SeqNo ASC" : input.Sorting)
                    .Skip((input.PageIndex-1)*input.PageSize)
                    .Take(input.PageSize)
                    .ToListAsync();

                // 设置结果
                result.ResultData = items;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "获取能耗配置列表失败", ex);
            }

            return result;
        }

        /// <summary>
        /// 获取所有能耗配置信息
        /// </summary>
        /// <returns>能耗配置列表</returns>
        [HttpGet, Description("获取所有能耗配置")]
        public async Task<List<EnergyConsumptionConfig>> GetAllAsync()
        {
            try
            {
                return await _configRepository.GetAllIncluding(c => c.EnergyConsumptionDevice).ToListAsync();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取所有能耗配置失败", ex);
                return new List<EnergyConsumptionConfig>();
            }
        }

        /// <summary>
        /// 根据ID获取能耗配置信息
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>能耗配置</returns>
        [HttpGet, Description("根据ID获取能耗配置")]
        public async Task<RequestResult<EnergyConsumptionConfig>> GetByIdAsync(Guid id)
        {
            var result = new RequestResult<EnergyConsumptionConfig> { Flag = false };

            try
            {
                var config = await _configRepository.GetAll()
                    .Include(c => c.EnergyConsumptionDevice)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (config == null)
                {
                    result.Message = "未找到指定的能耗配置";
                    return result;
                }

                result.ResultData = config;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "获取能耗配置详情失败", ex);
            }

            return result;
        }

        /// <summary>
        /// 创建能耗配置
        /// </summary>
        /// <param name="input">能耗配置信息</param>
        /// <returns>创建结果</returns>
        [HttpPost, Description("创建能耗配置")]
        public async Task<RequestResult<EnergyConsumptionConfig>> CreateAsync(EnergyConsumptionConfig input)
        {
            var result = new RequestResult<EnergyConsumptionConfig> { Flag = false };
            
            try
            {
                // 设置审计信息
                var currentUser = GetCurrentUser();
                input.CreatorUserId = currentUser.Id;
                input.CreationTime = DateTime.Now;
                input.Id = Guid.NewGuid();
                
                // 保存配置
                var config = await _configRepository.InsertAsync(input);
                
                result.ResultData = config;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "创建能耗配置失败", ex);
            }
            
            return result;
        }

        /// <summary>
        /// 更新能耗配置
        /// </summary>
        /// <param name="input">能耗配置信息</param>
        /// <returns>更新结果</returns>
        [HttpPost, Description("更新能耗配置")]
        public async Task<RequestResult<EnergyConsumptionConfig>> UpdateAsync(EnergyConsumptionConfig input)
        {
            var result = new RequestResult<EnergyConsumptionConfig> { Flag = false };
            
            try
            {
                // 获取现有配置
                var config = await _configRepository.GetAsync(input.Id);
                if (config == null)
                {
                    result.Message = "未找到要更新的能耗配置";
                    return result;
                }
                
                // 更新参数
                config.SeqNo = input.SeqNo;
                config.Name = input.Name;
                config.TelesignalisationId = input.TelesignalisationId;
                config.TelemeteringId = input.TelemeteringId;
                config.EnergyConsumptionDeviceId = input.EnergyConsumptionDeviceId;
                config.ConfigValue = input.ConfigValue;
                config.Description = input.Description;
                config.ConfigType = input.ConfigType;
                config.ValueType = input.ValueType;
                config.IsActive = input.IsActive;
                config.Remark = input.Remark;
                
                // 设置审计信息
                var currentUser = GetCurrentUser();
                config.LastModifierUserId = currentUser.Id;
                config.LastModificationTime = DateTime.Now;
                
                // 保存更新
                await _configRepository.UpdateAsync(config);
                
                result.ResultData = config;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "更新能耗配置失败", ex);
            }
            
            return result;
        }

        /// <summary>
        /// 删除能耗配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        [HttpPost, Description("删除能耗配置")]
        public async Task<RequestResult<bool>> DeleteAsync(Guid id)
        {
            var result = new RequestResult<bool> { Flag = false };
            
            try
            {
                await _configRepository.DeleteAsync(id);
                result.ResultData = true;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "删除能耗配置失败", ex);
            }
            
            return result;
        }

        /// <summary>
        /// 批量删除能耗配置
        /// </summary>
        /// <param name="ids">配置ID列表</param>
        /// <returns>删除结果</returns>
        [HttpPost, Description("批量删除能耗配置")]
        public async Task<RequestResult<bool>> DeleteByIdsAsync(List<Guid> ids)
        {
            var result = new RequestResult<bool> { Flag = false };
            
            try
            {
                foreach (var id in ids)
                {
                    await _configRepository.DeleteAsync(id);
                }
                
                result.ResultData = true;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "批量删除能耗配置失败", ex);
            }
            
            return result;
        }

        /// <summary>
        /// 添加或更新能耗配置
        /// </summary>
        /// <param name="input">能耗配置信息</param>
        /// <returns>操作结果</returns>
        [HttpPost, Description("添加或更新能耗配置")]
        public async Task<RequestResult<EnergyConsumptionConfig>> CreateOrUpdateAsync(EnergyConsumptionConfig input)
        {
            if (input == null) 
                return new RequestResult<EnergyConsumptionConfig> { Flag = false, Message = "输入参数不能为空" };
            
            if (input.Id != Guid.Empty && !input.Id.Equals(Guid.Empty))
            {
                return await UpdateAsync(input);
            }
            else
            {
                return await CreateAsync(input);
            }
        }

        /// <summary>
        /// 获取能耗配置下拉选择列表
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>配置选择列表</returns>
        [HttpPost, Description("获取能耗配置下拉选择列表")]
        public async Task<RequestResult<List<SelectModelOutput>>> GetConfigsForSelect(EnergyConfigSearchInput input)
        {
            var result = new RequestResult<List<SelectModelOutput>> { Flag = false };

            try
            {
                // 构建查询
                var query = _configRepository.GetAll()
                    .Include(c => c.EnergyConsumptionDevice)
                    .Where(c => c.IsActive)
                    .WhereIf(input.ConfigType.HasValue, c => c.ConfigType == input.ConfigType.Value)
                    .WhereIf(input.EnergyConsumptionDeviceId.HasValue, c => c.EnergyConsumptionDeviceId == input.EnergyConsumptionDeviceId.Value);

                var configs = await query.ToListAsync();

                var selectItems = configs.Select(c => new SelectModelOutput
                {
                    Key = c.Id,
                    Value = c.Id.ToString(),
                    Text = !string.IsNullOrEmpty(c.Name) ? c.Name : $"配置-{c.SeqNo}",
                    NodeObj = c
                }).ToList();

                result.ResultData = selectItems;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "获取能耗配置下拉列表失败", ex);
            }

            return result;
        }

        /// <summary>
        /// 根据设备ID获取相关配置
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>配置列表</returns>
        [HttpGet, Description("根据设备ID获取相关配置")]
        public async Task<RequestResult<List<EnergyConsumptionConfig>>> GetByDeviceIdAsync(Guid deviceId)
        {
            var result = new RequestResult<List<EnergyConsumptionConfig>> { Flag = false };

            try
            {
                var configs = await _configRepository.GetAll()
                    .Where(c => c.EnergyConsumptionDeviceId == deviceId && c.IsActive)
                    .ToListAsync();

                result.ResultData = configs;
                result.Flag = true;
            }
            catch (Exception ex)
            {
                result.Message = ex.Message;
                Log4Helper.Error(this.GetType(), "根据设备ID获取配置失败", ex);
            }

            return result;
        }
    }
}
