﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using YunDa.ISAS.Core.Helper;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Entities.System;

namespace YunDa.ISAS.EntityFrameworkCore.EntityFrameworkCore.Seed
{
    public class InitialHostDbBuilder
    {
        private readonly ISASDbContext _context;

        public InitialHostDbBuilder(ISASDbContext context)
        {
            _context = context;
        }

        public void Create()
        {
            CreateRolesAndUsers();
            //运维数据库不需要初始化；
            //CreateSeedEquipmentType();
            ChangeSysFunctionInfos();
            ChangeLinkStrategy();
            MigrationEquipmentViewPoint();

            MigrationEquipmentCategoryExactly();

        }

     

        private void MigrationEquipmentViewPoint()
        {
            //var equipmentViewPoints = _context.EquipmentViewPointDbSet.IgnoreQueryFilters();
            //foreach (var equipmentViewPoint in equipmentViewPoints)
            //{

            //}
        }

        private void ChangeLinkStrategy()
        {
            var linkageStrategys = _context.LinkageStrategyDbSet.IgnoreQueryFilters();
            var linkageConditions = _context.LinkageConditionDbSet.IgnoreQueryFilters();

            var dma = _context.DMAlarmCategoryDbSet.IgnoreQueryFilters().FirstOrDefault();
            var ca = _context.CameraAuthenticationDbSet.IgnoreQueryFilters().FirstOrDefault(t => t.Code == "PTXH");
            foreach (var linkageStrategy in linkageStrategys)
            {
                if (!linkageStrategy.DMAlarmCategoryId.HasValue)
                {
                    linkageStrategy.DMAlarmCategoryId = dma.Id;
                    _context.LinkageStrategyDbSet.Update(linkageStrategy);

                }
            }
            foreach (var linkageCondition in linkageConditions)
            {
                if (!linkageCondition.CameraAuthenticationId.HasValue)
                {
                    linkageCondition.CameraAuthenticationId = ca.Id;
                    _context.LinkageConditionDbSet.Update(linkageCondition);
                }
            }
            _context.SaveChanges();
        }

        private void ChangeSysFunctionInfos()
        {
            var sysFunctionvideo = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.Name == "视频监控" && u.Type == 0);
            if (sysFunctionvideo != null)
            {
                var sysFunctionCa = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.Name == "摄像机鉴权" && u.Type == 0);
                if (sysFunctionCa != null)
                {
                    sysFunctionCa.SysFunctionId = sysFunctionvideo.Id;
                    _context.SysFunctionDbSet.Update(sysFunctionCa);
                    _context.SaveChanges();
                }

            }
            var sysFunctionbaseinfo = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.Name == "基本信息" && u.Type == 0);
            if (sysFunctionbaseinfo != null)
            {
                var sysFunctionMa = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.Name == "主站信息" && u.Type == 0);
                if (sysFunctionMa != null)
                {
                    sysFunctionMa.SysFunctionId = sysFunctionbaseinfo.Id;
                    _context.SysFunctionDbSet.Update(sysFunctionMa);
                    
                    _context.SaveChanges();
                }

            }
            var sysFunctionrobot = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.Name == "机器人任务结果" && u.Type == 0);
            if (sysFunctionrobot != null)
            {
                _context.SysFunctionDbSet.Remove(sysFunctionrobot);
                _context.SaveChanges();
            }
            var sysFunctionClientRobot = _context.SysFunctionDbSet.FirstOrDefault(f => "机器人".Equals(f.Name) && f.Type == FunctionType.Client);
            if (sysFunctionClientRobot != null)
            {
                _context.SysFunctionDbSet.Remove(sysFunctionClientRobot);
            }

        }

        private void CreateRolesAndUsers()
        {
            CreateSeedUser();
            CreateSeedFunction();
            CreteSeedMultidimensionalCheckSchedule();
        }

        private void CreateSeedUser()
        {
            bool isSave = false;
            var adminUser = _context.SysUserDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.UserName == SysUser.AdminUserName);
            if (adminUser == null)
            {
                adminUser = SysUser.CreateTenantAdminUser("<EMAIL>");
                adminUser.Password = StringHelper.MD5Encrypt64(SysUser.DefaultPassword);
                adminUser.Id = Guid.NewGuid();
                adminUser.CreatorUserId = adminUser.Id;
                _context.SysUserDbSet.Add(adminUser);
                isSave = true;
            }
            if (isSave)
                _context.SaveChanges();
        }

        private void CreateSeedFunction()
        {
            IEnumerable<SysFunction> funs = _context.SysFunctionDbSet.IgnoreQueryFilters().AsEnumerable();
            #region Web端
            SysFunction fun = funs.FirstOrDefault(f => "系统维护".Equals(f.Name) && f.Type == FunctionType.Web);
            AddSysMaintenanceMenuForWeb(1, funs, fun);

            fun = funs.FirstOrDefault(f => "基本信息".Equals(f.Name) && f.Type == FunctionType.Web);
            AddBaseInfoMenuForWeb(2, funs, fun);

            //视频监控
            fun = funs.FirstOrDefault(f => "视频监控".Equals(f.Name) && f.Type == FunctionType.Web);
            AddVideoMenuForWeb(3, funs, fun);

            /// 删除巡检灯控功能
            fun = funs.FirstOrDefault(f => f.Code == "000305" && f.Name == "巡检灯控" && f.Type == FunctionType.Web);
            if (fun != null)
            {
                _context.SysFunctionDbSet.Remove(fun);
            }

            fun = funs.FirstOrDefault(f => "移动监控".Equals(f.Name) && f.Type == FunctionType.Web);
            AddMobileMenuForWeb(4, funs, fun);


            //添加通信配置模块
            AddTeleConfigurationMenuForWeb(5, funs);


            fun = funs.FirstOrDefault(f => "统计分析".Equals(f.Name) && f.Type == FunctionType.Web && f.Code == "0006");
            AddStatisticsAnalysisMenu(6, funs);


            fun = funs.FirstOrDefault(f => "巡检结果".Equals(f.Name) && f.Type == FunctionType.Web && f.Code != "0007");
            if (fun != null)
            {
                _context.SysFunctionDbSet.Remove(fun);
            }
            fun = funs.FirstOrDefault(f => "巡检结果".Equals(f.Name) && f.Type == FunctionType.Web && f.Code == "0007");
            if (fun == null)
            {
                AddInspectionResultMenu(7, funs);
            }
            fun = funs.FirstOrDefault(f => "运维配置".Equals(f.Name) && f.Type == FunctionType.Web && f.Code == "0008");
            if (fun == null)
            {
                AddMaintenanceMenu(8, funs);
            }
            #endregion
            #region 桌面客户端
            fun = funs.FirstOrDefault(f => f.Code == "0110" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddMainViewPortForClient(0);
            }
            else
            {
                fun.SeqNo = 0;
            }
            fun = funs.FirstOrDefault(f => f.Code == "0112" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddDashboardForClient(12);
            }
            else
            {
                //调整排序为0
                fun.SeqNo = 1;
            }
            fun = funs.FirstOrDefault(f =>f.Code == "0101" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddVideoMenuForClient(1);
            }
            else
            {
                fun.SeqNo = 2;
            }
           
            //客户端智能巡检
            var entities= funs.Where(f => f.Code == "0102" && f.Type == FunctionType.Client);
            if (entities.Count()==1)
            {
                fun = entities.First();
                fun.Name = "智能巡检";
                fun.SeqNo = 3;
            }
            else if (entities.Count() == 0)
            {
                AddManualInspectionMenuForClient(2);
            }
            else
            {
                _context.SysFunctionDbSet.RemoveRange(entities);
                AddManualInspectionMenuForClient(2);
            }
           
            fun = funs.FirstOrDefault(f => f.Code == "0103" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddDLHJMenuForClient(3);
            }
            else
            {
                fun.SeqNo = 4;
            }
            fun = funs.FirstOrDefault(f => f.Code == "0104" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddZXJCMenuForClient(4);
            }
            else
            {
                fun.SeqNo = 5;
            }
            fun = funs.FirstOrDefault(f => f.Code == "0105"   && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddHWCWMenuForClient(5);
            }
            else
            {
                fun.SeqNo = 6;
            }


            fun = funs.FirstOrDefault(f => f.Code == "0106"  && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddRobotMenuForClient(6);
            }
            else
            {
                fun.SeqNo = 7;

            }
            fun = funs.FirstOrDefault(f => f.Code == "0107" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddStatisticsAnalysisMenuForClient(7);
            }
            else
            {
                fun.SeqNo = 8;

            }
            fun = funs.FirstOrDefault(f => f.Code == "0108" && f.Type == FunctionType.Client);
            if (fun != null)
            {
                _context.SysFunctionDbSet.Remove(fun);
            }
           

            fun = funs.FirstOrDefault(f =>  f.Code == "0109" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddThreeDimensionalMenuForClient(9);
            }
            else
            {
                fun.SeqNo = 20;

            }
            fun = funs.FirstOrDefault(f => f.Code == "0111" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddLogScanMenuForClient(11);
            }
            else
            {
                fun.SeqNo = 11;
            }
            fun = funs.FirstOrDefault(f => f.Code == "0113" && f.Type == FunctionType.Client);
            if (fun == null)
            {
                AddAlarmListForClient(13);
            }
            else
            {
                fun.Name = "报警记录";
                fun.SeqNo = 13;
            }
            var funsGroup = funs.GroupBy(item => new { item.Name, item.Code });
            foreach (var item in funsGroup)
            {
                //移出重复的
                if (item.Count()>1)
                {
                    int index = 0;
                    foreach (var f in item)
                    {
                        if (index>0)
                        {
                            _context.SysFunctionDbSet.Remove(f);
                        }
                        index++;
                    }

                }
            }
            #endregion
            #region 运维客户端
            int seq = 0;
            string code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq,"首页", code);
            }
            seq = 01;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "一次设备列表", code);
            }
            seq = 02;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "二次设备列表", code);
            }
            seq = 03;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "二次回路校验", code);
            }
            seq = 04;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "定值管理", code);
            }
            seq = 05;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "智能故障报告", code);
            }
            seq = 06;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "版本管理与板卡诊断", code);
            }
            seq = 07;
            code = "02" + seq.ToString("00");
            fun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.MaintenanceSystemClient);
            if (fun == null)
            {
                AddMaintenanceSystemFunction(seq, "主动安全", code);
            }
            #endregion
            _context.SaveChanges();
        }
        private void AddMaintenanceSystemFunction(int seqNo,string name,string code)
        {
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = name,
                Code = code,
                Type = FunctionType.MaintenanceSystemClient,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        private void CreteSeedMultidimensionalCheckSchedule()
        {
            IEnumerable<MultidimensionalCheckSchedule> funs = _context.MultidimensionalCheckScheduleDbSet.IgnoreQueryFilters().AsEnumerable();
            if (funs.Count()!=7)
            {
                foreach (var f in funs)
                {
                    _context.MultidimensionalCheckScheduleDbSet.Remove(f);
                }
                for (int i = 1; i < 8; i++)
                {
                    _context.MultidimensionalCheckScheduleDbSet.Add(new MultidimensionalCheckSchedule
                    {
                        IsActive = true,
                        Remark = "系统自动生成",
                        IntervalMinute = 60,
                        EndTime = "23:59",
                        StartTime = "00:00",
                        Id = Guid.NewGuid(),
                        Week = (Entities.VideoSurveillance.WeekEnum)i
                    });
                }
               
            }
            _context.SaveChanges();

        }
        #region 添加系统功能
        #region Web
        /// <summary>
        /// 添加系统维护模块
        /// </summary>
        private void AddSysMaintenanceMenuForWeb(int seqNo, IEnumerable<SysFunction> funs, SysFunction funSource)
        {
            var ID = Guid.NewGuid();
            if (funSource != null)
            {
                ID = funSource.Id;
            }
            string code = "000" + seqNo;
            SysFunction sysFunction = new SysFunction
            {
                SeqNo = seqNo,
                Id = ID,
                Name = "系统维护",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                ID = fun.Id;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "用户管理",
                Code = code + "01",
                Type = FunctionType.Web,
                LoadUrl = @"/System/UserPage",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "系统功能",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/System/SysFunction",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 3,
                Name = "角色管理",
                Code = code + "03",
                Type = FunctionType.Web,
                LoadUrl = @"/System/RoleManage",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 4,
                Name = "系统日志",
                Code = code + "04",
                Type = FunctionType.Web,
                LoadUrl = @"/System/SysAuditLog",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 5,
                Name = "运维配置",
                Code = code + "05",
                Type = FunctionType.Web,
                LoadUrl = @"/System/ConfigurationSetting",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
        }
        /// <summary>
        /// 添加基本信息模块
        /// </summary>
        private void AddBaseInfoMenuForWeb(int seqNo, IEnumerable<SysFunction> funs, SysFunction funSource)
        {
            var ID = Guid.NewGuid();
            if (funSource != null)
            {
                ID = funSource.Id;
            }

            string code = "000" + seqNo;
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "基本信息",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                ID = fun.Id;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "场站信息",
                Code = code + "01",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/TranSubstation",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };

            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "厂商信息",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/ManufacturerInfo",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 3,
                Name = "安装区域",
                Code = code + "03",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentLocation",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 4,
                Name = "设备类型",
                Code = code + "04",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentType",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 5,
                Name = "设备信息",
                Code = code + "05",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentInfo",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 6,
                Name = "主站信息",
                Code = code + "06",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/MasterStation",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 7,
                Name = "设备类型部位",
                Code = code + "07",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentTypeViewPoint",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.Name = "设备类型部位";
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 8,
                Name = "设备部位",
                Code = code + "08",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentViewPoint",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };


           
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 9,
                Name = "数据分类",
                Code = code + "09",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentCategory",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.Name = "数据分类";
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 10,
                Name = "设备信息展示",
                Code = code + "10",
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentInfoView",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                //_context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                _context.SysFunctionDbSet.Remove(fun);
                //fun.Name = "设备信息展示";
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 11,
                Name = "保护装置",
                Code = code + 11,
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/ProtectionDevice",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.Name = "保护装置";
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 12,
                Name = "二次回路",
                Code = code + 12,
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/SecondaryCircuit",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.Name = "二次回路";
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 13,
                Name = "设备诊断指标",
                Code = code + 13,
                Type = FunctionType.Web,
                LoadUrl = @"/GeneralInformation/EquipmentIndicatorConfig",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            
        }
        /// <summary>
        /// 添加视频监控模块
        /// </summary>
        private void AddVideoMenuForWeb(int seqNo, IEnumerable<SysFunction> funs, SysFunction funSource)
        {
            var ID = Guid.NewGuid();
            if (funSource != null)
            {
                ID = funSource.Id;
            }
            string code = "000" + seqNo;
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "视频监控",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "视频设备",
                Code = code + "01",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/VideoDevice",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "巡检配置",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/VideoInspection",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 3,
                Name = "测温点配置",
                Code = code + "03",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/MeasureTemperaturePoint",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 4,
                Name = "摄像机鉴权",
                Code = code + "04",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/CameraAuthentication",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 5,
                Name = "识别配置",
                Code = code + "05",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/PatternRecognitionConfigutration",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 6,
                Name = "多维校核配置",
                Code = code + "06",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/MultidimensionalCheck",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Code = sysFunction.Code;
            }

        }
        /// <summary>
        /// 添加移动监控模块
        /// </summary>
        private void AddMobileMenuForWeb(int seqNo, IEnumerable<SysFunction> funs, SysFunction funSource)
        {
            var ID = Guid.NewGuid();
            if (funSource != null)
            {
                ID = funSource.Id;
            }
            string code = "000" + seqNo;
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "移动监控",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "机器人信息",
                Code = code + "01",
                Type = FunctionType.Web,
                LoadUrl = @"/MobileSurveillance/RobotInfo",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "机器人任务",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/MobileSurveillance/RobotTask",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }

        }
        /// <summary>
        /// 添加通信配置模块
        /// </summary>
        private void AddTeleConfigurationMenuForWeb(int seqNo, IEnumerable<SysFunction> funs)
        {
            var ID = Guid.NewGuid();

            string code = "000" + seqNo;
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "通信配置",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                ID = fun.Id;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "报警类型",
                Code = code + "01",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/DMAlarmCategory",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "调度数据",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/TelecomDataConfigration",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.LoadUrl = sysFunction.LoadUrl;
                fun.Name = sysFunction.Name;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 3,
                Name = "联动管理",
                Code = code + "03",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/Linkage",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 4,
                Name = "遥控计划",
                Code = code + "04",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/TeleCommandPlan",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web );
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 5,
                Name = "自检策略",
                Code = code + "05",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/SelfCheckingConfiguration",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web );
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 6,
                Name = "装置数据",
                Code = code + "06",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/DeviceTelecomDataConfigration",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
            }
            
        }
        /// <summary>
        /// 添加统计分析模块
        /// </summary>
        private void AddStatisticsAnalysisMenu(int seqNo, IEnumerable<SysFunction> funs)
        {
            var ID = Guid.NewGuid();

            string code = "000" + seqNo;
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "统计分析",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                ID = fun.Id;
                fun.Name = sysFunction.Name;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "遥测分析",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/TelemeteringAnalysis",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 3,
                Name = "遥信结果",
                Code = code + "03",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/TelesignalisationResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 4,
                Name = "遥控结果",
                Code = code + "04",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/TelecommandResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 5,
                Name = "测温结果",
                Code = code + "05",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/MeasureTemperatureResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }
            var MeasureTemperatureResults = funs.Where(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (MeasureTemperatureResults.Count()>1)
            {
                _context.SysFunctionDbSet.RemoveRange(MeasureTemperatureResults.Skip(1));
            }
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 6,
                Name = "历史报警",
                Code = code + "06",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/TeleAlarmResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 7,
                Name = "联动报警",
                Code = code + "07",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/LinkageResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }

            fun = funs.FirstOrDefault(f => f.Code == code+"08" && f.Type == FunctionType.Web);
            if (fun != null)
            {
                _context.SysFunctionDbSet.Remove(fun);
            }

            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 9,
                Name = "多维复核",
                Code = code + "09",
                Type = FunctionType.Web,
                LoadUrl = @"/DataMonitoring/MultidimensionalCheckResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                fun.SysFunctionId = ID;
                fun.Name = sysFunction.Name;
                fun.LoadUrl = sysFunction.LoadUrl;
            }

        }
        private void AddInspectionResultMenu(int seqNo, IEnumerable<SysFunction> funs)
        {
            string code = "000" + seqNo;

            var sysfuncGerIns = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => (u.Name == "巡检结果") && u.Type == FunctionType.Web && u.Code != code);
            if (sysfuncGerIns != null)
            {
                _context.SysFunctionDbSet.Remove(sysfuncGerIns);
                //_context.SaveChanges();
            }
            var sysfuncGerIns1 = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => (u.Name == "普通巡检结果") && u.Type == FunctionType.Web && u.Code != code);
            if (sysfuncGerIns1 != null)
            {
                _context.SysFunctionDbSet.Remove(sysfuncGerIns1);
                //_context.SaveChanges();
            }
            var sysfuncGerIns2 = _context.SysFunctionDbSet.IgnoreQueryFilters().FirstOrDefault(u => (u.Name == "特殊巡检结果") && u.Type == FunctionType.Web && u.Code != code);
            if (sysfuncGerIns2 != null)
            {
                _context.SysFunctionDbSet.Remove(sysfuncGerIns2);
                //_context.SaveChanges();
            }
            var ID = Guid.NewGuid();
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "巡检结果",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            var fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "普通巡检结果",
                Code = code + "01",
                Type = FunctionType.Web,
                LoadUrl = @"/VideoSurveillance/VideoInspectionResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            sysFunction = new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 2,
                Name = "特殊巡检结果",
                Code = code + "02",
                Type = FunctionType.Web,
                LoadUrl = @"/MobileSurveillance/RobotTaskResult",
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = ID
            };
            fun = funs.FirstOrDefault(f => f.Code == sysFunction.Code && f.Type == FunctionType.Web);
            if (fun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
        }


        /// <summary>
        /// 添加运维配置模块
        /// </summary>
        private void AddMaintenanceMenu(int seqNo, IEnumerable<SysFunction> funs)
        {
            // 主菜单
            var ID = Guid.NewGuid();
            string code = "000" + seqNo;
            var mainFun = funs.FirstOrDefault(f => f.Code == code && f.Type == FunctionType.Web);
            if (mainFun != null)
            {
                ID = mainFun.Id;
            }
            SysFunction sysFunction = new SysFunction
            {
                Id = ID,
                SeqNo = seqNo,
                Name = "运维配置",
                Code = code,
                Type = FunctionType.Web,
                IsActive = true,
                IsOperatorPage = false,
            };
            if (mainFun == null)
            {
                _context.SysFunctionDbSet.Add(sysFunction);
            }
            else
            {
                mainFun.Name = sysFunction.Name;
                mainFun.SeqNo = sysFunction.SeqNo;
            }

            // 子菜单定义
            var subMenus = new List<(int SeqNo, string Name, string CodeSuffix, string LoadUrl)>
            {
                (1, "能耗管理", "01", "/DataMonitoring/EnergyManagement"),
                // 可根据实际需求继续添加子菜单
            };

            foreach (var (subSeqNo, subName, codeSuffix, loadUrl) in subMenus)
            {
                var subCode = code + codeSuffix;
                var subFun = funs.FirstOrDefault(f => f.Code == subCode && f.Type == FunctionType.Web);
                if (subFun == null)
                {
                    _context.SysFunctionDbSet.Add(new SysFunction
                    {
                        Id = Guid.NewGuid(),
                        SeqNo = subSeqNo,
                        Name = subName,
                        Code = subCode,
                        Type = FunctionType.Web,
                        LoadUrl = loadUrl,
                        IsActive = true,
                        IsOperatorPage = true,
                        SysFunctionId = ID
                    });
                }
                else
                {
                    subFun.Name = subName;
                    subFun.SeqNo = subSeqNo;
                    subFun.LoadUrl = loadUrl;
                    subFun.SysFunctionId = ID;
                }
            }
        }
        #endregion

        #region Client

        private void AddMainViewPortForClient(int seqNo)
        {
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = "主页",
                Code = "0110",
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 添加通信配置模块
        /// </summary>
        private void AddVideoMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo+1,
                Name = "视频监控",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 添加手动巡检
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddManualInspectionMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = "智能巡检",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 添加动力环境
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddDLHJMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 4,
                Name = "动力环境",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 在线监测
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddZXJCMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = "在线监测",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 红外测温
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddHWCWMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = "红外测温",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 轨道巡检
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddRobotMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = "轨道巡检",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 统计分析
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddStatisticsAnalysisMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 8,
                Name = "统计分析",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 场站配置
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddSubstationConfigMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = seqNo,
                Name = "场站配置",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        /// <summary>
        /// 三维场景
        /// </summary>
        /// <param name="seqNo"></param>
        private void AddThreeDimensionalMenuForClient(int seqNo)
        {
            string code = "010" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 20,
                Name = "三维场景",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        private void AddLogScanMenuForClient(int seqNo)
        {
            string code = "01" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 11,
                Name = "日志查询",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        private void AddAlarmListForClient(int seqNo)
        {
            string code = "01" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 13,
                Name = "报警记录",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        private void AddDashboardForClient(int seqNo)
        {
            string code = "01" + seqNo;
            _context.SysFunctionDbSet.Add(new SysFunction
            {
                Id = Guid.NewGuid(),
                SeqNo = 1,
                Name = "信息看板",
                Code = code,
                Type = FunctionType.Client,
                LoadUrl = null,
                IsActive = true,
                IsOperatorPage = true,
                SysFunctionId = null
            });
        }
        #endregion
        #endregion
        private void CreateSeedRole()
        {
            var adminUser = _context.SysUserDbSet.IgnoreQueryFilters().FirstOrDefault(u => u.UserName == SysUser.AdminUserName);
            if (adminUser == null) return;
            var adminRole = _context.SysRoleDbSet.IgnoreQueryFilters().FirstOrDefault(r => r.RoleName == SysRole.AdminRole);
            if (adminRole == null)
            {
                adminRole.RoleName = SysRole.AdminRole;
                adminRole.IsActive = true;
                _context.SysRoleDbSet.Add(adminRole);
                _context.SaveChanges();
            }
        }

        /// <summary>
        /// 创建设备类型种子数据
        /// </summary>
        private void CreateSeedEquipmentType()
        {
            bool isSaveDLHJ = false;
            var isExistDLHJ = _context.EquipmentTypeDbSet.IgnoreQueryFilters().Where(eType => eType.EquipmentTypeLevel == EquipmentTypeLevelEnum.System && eType.Name == EquipmentType.DLHJ).Any();
            if (!isExistDLHJ)
            {
                EquipmentType DLHJ = new EquipmentType();
                DLHJ.Name = EquipmentType.DLHJ;
                DLHJ.SeqNo = 0;
                DLHJ.EquipmentTypeLevel = EquipmentTypeLevelEnum.System;
                DLHJ.IsActive = true;
                DLHJ.CreationTime = DateTime.Now;
                _context.EquipmentTypeDbSet.Add(DLHJ);
                isSaveDLHJ = true;
            }
            bool isSaveZXJC = false;
            var isExistZXJC = _context.EquipmentTypeDbSet.IgnoreQueryFilters().Where(eType => eType.EquipmentTypeLevel == EquipmentTypeLevelEnum.System && eType.Name == EquipmentType.ZXJC).Any();
            if (!isExistZXJC)
            {
                EquipmentType ZXJC = new EquipmentType();
                ZXJC.Name = EquipmentType.ZXJC;
                ZXJC.SeqNo = 0;
                ZXJC.EquipmentTypeLevel = EquipmentTypeLevelEnum.System;
                ZXJC.IsActive = true;
                ZXJC.CreationTime = DateTime.Now;
                _context.EquipmentTypeDbSet.Add(ZXJC);
                isSaveZXJC = true;
            }
            if (isSaveDLHJ || isSaveZXJC)
                _context.SaveChanges();
        }

        /// <summary>
        /// 把设备类型的数据分类数据关联到设备上
        /// </summary>
        /// <exception cref="NotImplementedException"></exception>
        private void MigrationEquipmentCategoryExactly()
        {
            var equipmentequipmentDataCategoryExactlys = _context.EquipmentDataCategoryExactlyDbSet.IgnoreQueryFilters().ToList();
            if (!equipmentequipmentDataCategoryExactlys.Any())
            {
                var equipmentDataCategorys = _context.EquipmentDataCategoryDbSet.IgnoreQueryFilters().ToList();
                var equipmentInfos = _context.EquipmentInfoDbSet.IgnoreQueryFilters().ToList();
                foreach (var equipmentInfo in equipmentInfos)
                {
                    var equipmentDataCategorysFilters = equipmentDataCategorys.Where(t => t.EquipmentTypeId == equipmentInfo.EquipmentTypeId);
                    foreach (var equipmentDataCategorysFilter in equipmentDataCategorysFilters)
                    {
                        EquipmentDataCategoryExactly entity = new EquipmentDataCategoryExactly()
                        {
                            EquipmentTypeId = equipmentInfo.EquipmentTypeId,
                            TransformerSubstationId = equipmentInfo.TransformerSubstationId,
                            IsActive = true,
                            EquipmentDataCategoryId = equipmentDataCategorysFilter.Id,
                            EquipmentInfoId = equipmentInfo.Id,

                        };
                        _context.EquipmentDataCategoryExactlyDbSet.Add(entity);
                    }
                  
                }
                _context.SaveChanges();

            }


        }
    }
}