﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAsdu142info
    {
        public string Companyname { get; set; }
        public string Valuetype { get; set; }
        public int Valueindex { get; set; }
        public string Valuename { get; set; }
        public string Unit { get; set; }
        public int Precise { get; set; }
        public double Coeff { get; set; }
        public string Parsemode { get; set; }
        public string Parseformat { get; set; }

        public virtual ImManufacturer CompanynameNavigation { get; set; }
    }
}
