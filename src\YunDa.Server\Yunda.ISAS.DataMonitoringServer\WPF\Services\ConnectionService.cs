using Abp.Dependency;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using ToolLibrary;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace Yunda.ISAS.DataMonitoringServer.WPF.Services
{
    public class ConnectionService : IConnectionService, ISingletonDependency
    {
        private const string CONNECTIONS_PATH = "Connections.json";
        private readonly IConfigurationService _configService;
        private readonly ILoggingService _loggingService;

        public ObservableCollection<ConnectionConfig> Connections { get; } = new ObservableCollection<ConnectionConfig>();
        private readonly IDataService _dataService;

        public ConnectionService(IConfigurationService configService,
            IDataService dataService,
            ILoggingService loggingService
            )
        {
            _configService = configService;
            _dataService = dataService;
            _loggingService = loggingService;
        }

        public void Initialize(ObservableCollection<SelectModelOutput> substations)
        {
            try
            {
                if (File.Exists(CONNECTIONS_PATH))
                {
                    var json = File.ReadAllText(CONNECTIONS_PATH);
                    var rawList = JsonConvert.DeserializeObject<List<ConnectionConfig>>(json);
                    if (rawList != null && rawList.Any())
                    {
                        foreach (var raw in rawList)
                        {
                            var connection = new ConnectionConfig()
                            {
                                Name = raw.Name,
                                Ip = raw.Ip,
                                Port = raw.Port,
                                RtuAddress = raw.RtuAddress,
                                Status = "未连接",
                                TransformerSubstation = substations.FirstOrDefault(t =>
                                    t.Key?.ToString() == raw.TransformerSubstation?.Key?.ToString()),
                                DataSourceCategoryName = raw.DataSourceCategoryName,
                                IsSelected = raw.IsSelected,
                                ShouldSaveData = raw.ShouldSaveData,
                                SaveMode = raw.SaveMode,
                                SaveInterval = raw.SaveInterval,
                                DataAccessMode =raw.DataAccessMode,
                            };

                            // 确保唯一性
                            if (IsCategoryAvailable(connection.DataSourceCategoryName, connection))
                                Connections.Add(connection);
                        }
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载连接配置失败: {ex.Message}");
            }

            // 没有任何有效配置时，创建默认连接
            if (Connections.Count == 0)
            {

                var defaultConnection = new ConnectionConfig()
                {
                    Name = "默认连接",
                    Ip = _configService.SettingModel.Dev_Ip,
                    Port = _configService.SettingModel.Dev_Port.ToString(),
                    RtuAddress = _configService.SettingModel.Dev_addr.ToString(),
                    Status = "未连接",
                    TransformerSubstation = substations.FirstOrDefault(),
                    DataSourceCategoryName = 0,
                    IsSelected = true,
                    ShouldSaveData = false,
                    SaveMode = (int)SaveModeEnum.Change,
                    SaveInterval = 60
                };
                Connections.Add(defaultConnection);
            }
        }

        public void SaveConnections()
        {
            try
            {
                var json = JsonConvert.SerializeObject(Connections, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    Formatting = Formatting.Indented
                });
                File.WriteAllText(CONNECTIONS_PATH, json);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存连接配置失败: {ex.Message}");
            }
        }

        public ConnectionConfig AddConnection(SelectModelOutput substation, int preferredCategory)
        {

            var newConnection = new ConnectionConfig()
            {
                Name = $"连接 {Connections.Count + 1}",
                Ip = "127.0.0.1",
                Port = "2404",
                RtuAddress = "1",
                Status = "未连接",
                TransformerSubstation = substation,
                DataSourceCategoryName = preferredCategory,
                IsSelected = false,
                ShouldSaveData = false,
                SaveMode = (int)SaveModeEnum.Change,
                SaveInterval = 60
            };

            Connections.Add(newConnection);
            return newConnection;
        }

        public void RemoveSelectedConnections()
        {
            var selectedConnections = Connections.Where(c => c.IsSelected).ToList();
            foreach (var connection in selectedConnections)
                Connections.Remove(connection);

        }

        public void ConnectSelectedConnections()
        {
           
        }

        public void DisconnectSelectedConnections()
        {
            foreach (var c in Connections.Where(c => c.IsSelected))
            {
                // TODO: 实现断开逻辑
                c.Status = "未连接";
            }
        }

        public void ReconnectSelectedConnections()
        {
            foreach (var c in Connections.Where(c => c.IsSelected))
            {
                c.Status = "未连接";
                // TODO: 实现连接逻辑
                c.Status = "已连接";
            }
        }

        // ===== 帮助函数 =====

        private bool IsCategoryAvailable(int category, ConnectionConfig self)
        {
            return !Connections
                .Where(c => c != self)
                .Any(c => c.DataSourceCategoryName == category);
        }

        private int? GetFirstAvailableCategory(int? preferred = null)
        {
            var used = Connections
                .Select(c => c.DataSourceCategoryName)
                .ToHashSet();

            var all = EnumHandle.GetEnumTypes<DataSourceCategoryEnum>();

            if (preferred.HasValue && !used.Contains(preferred.Value))
                return preferred;

            return all.Select(e => e.Value)
                      .FirstOrDefault(v => !used.Contains(v));
        }

    }

}
