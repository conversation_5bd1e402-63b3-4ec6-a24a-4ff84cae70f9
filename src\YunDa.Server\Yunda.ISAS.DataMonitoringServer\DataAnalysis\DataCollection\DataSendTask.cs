﻿using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Dlls;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Interfaces;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.LinkageAnalysis;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.TeleInfoSave;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.DataAnalysis.DataCollection;
using Yunda.SOMS.MongoDB.Entities.MainStationMaintenanceInfo;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Redis.Entities.AlarmCategory;
using YunDa.ISAS.Redis.Entities.DataMonitorCategory;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.SecondaryCircuitDto;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.OperationReport;
using Z.Expressions;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection
{
    /// <summary>
    /// Main coordinator class for data sending tasks
    /// </summary>
    public class DataSendTask : ISingletonDependency, IDataSendTask
    {
        private readonly Content _settingModel;
        private readonly ITelemeteringHandler _telemeteringHandler;
        private readonly ITelesignalHandler _telesignalHandler;
        private readonly IAlarmMessageManager _alarmMessageManager;
        private readonly DataRepository _dataRepository;
        private readonly WebApiRequest _webApiRequest;
        private readonly RunningDataCache _runningDataCache;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        private readonly IRedisRepository<TelesignalisationModel, string> _telesignalisationModelListRedis;
        private readonly IRedisRepository<TelemeteringModel, string> _telemeteringModelListRedis;
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoRepository;
        private readonly RedisDataRepository _redisDataRepository;
        // ActionBlocks for processing data
        public ActionBlock<RecordRECORDYXBURSTNewTaskInfo> RECORDYXBURSTActionBlock { get; set; }
        public ActionBlock<RecordYC_TYPENewTaskInfo> YCTActionBlock { get; set; }
        private Dictionary<Guid, EquipmentInfo> _equipmentInfoDic;
        // Constants for configuration
        private const int DEFAULT_PARALLELISM = 1;
        private const int DEFAULT_BOUNDED_CAPACITY = 100;
        private readonly IDeviceMonitoringHandler _deviceMonitoringHandler;
        /// <summary>
        /// Constructor for DataSendTask
        /// </summary>
        public DataSendTask(
            Content settingModel,
            ITelemeteringHandler telemeteringHandler,
            ITelesignalHandler telesignalHandler,
            IDeviceMonitoringHandler deviceMonitoringHandler,
            DataRepository dataRepository,
            WebApiRequest webApiRequest,
            RunningDataCache runningDataCache,
             IUnitOfWorkManager unitOfWorkManager,
            IRedisRepository<TelesignalisationModel, string> telesignalisationModelListRedis,
            IRedisRepository<TelemeteringModel, string> telemeteringModelListRedis,
       
            IRepository<EquipmentInfo, Guid> equipmentInfoRepository,
                 RedisDataRepository redisDataRepository,
            IAlarmMessageManager alarmMessageManager)
        {
            _settingModel = settingModel;
            _telemeteringHandler = telemeteringHandler;
            _telesignalHandler = telesignalHandler;
            _alarmMessageManager = alarmMessageManager;
            _runningDataCache = runningDataCache;
            _unitOfWorkManager = unitOfWorkManager;
            _deviceMonitoringHandler = deviceMonitoringHandler;
            //_linkageAnalysisTask = linkageAnalysisTask;
            //_telemeteringResultSaveTask = telemeteringResultSaveTask;
            _redisDataRepository = redisDataRepository;
            _dataRepository = dataRepository;
            InitializeActionBlocks();
            _alarmMessageManager.StartAlarmMessageCleanupTask();
        }

        /// <summary>
        /// Initializes the ActionBlocks for processing data
        /// </summary>
        private void InitializeActionBlocks()
        {
            RECORDYXBURSTActionBlock = new ActionBlock<RecordRECORDYXBURSTNewTaskInfo>(
                async ri => await _telesignalHandler.HandleTelesignalDataAsync(ri),
                new ExecutionDataflowBlockOptions {
                    MaxDegreeOfParallelism = DEFAULT_PARALLELISM,
                    BoundedCapacity = DEFAULT_BOUNDED_CAPACITY
                });

            YCTActionBlock = new ActionBlock<RecordYC_TYPENewTaskInfo>(
                async ri => await _telemeteringHandler.HandleTelemeteringDataAsync(ri),
                new ExecutionDataflowBlockOptions {
                    MaxDegreeOfParallelism = DEFAULT_PARALLELISM,
                    BoundedCapacity = DEFAULT_BOUNDED_CAPACITY
                });
        }

        private async Task HandleTelemeteringDataAsync(RecordYC_TYPENewTaskInfo ri)
        {
            try
            {
                var yc = ri.Record;
                var categoryValue = ri.Connection.DataSourceCategoryName;
                string redisKey = $"{_redisDataRepository.TelemeteringModelListRediskey}_{(DataSourceCategoryEnum)categoryValue}";
                string redisChannel = $"{_redisDataRepository.TelemeteringInflectionInflectionZZChannelRediskey}_{(DataSourceCategoryEnum)categoryValue}";

                string haskey = $"{yc.addr}_{0}_{yc.inf}_{categoryValue}";
                var ycData = await _redisDataRepository.TelemeteringModelListRedis.HashSetGetOneAsync(redisKey, haskey);

                if (ycData == null)
                {
                    Log4Helper.Error(GetType(), $"更新数据失败: 地址：{yc.inf} 类型：{categoryValue} 键：{haskey}");
                    return;
                }

                await UpdateTelemeteringDataAsync(ycData, yc, redisKey, redisChannel, haskey);
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler($"Error in HandleTelemeteringDataAsync: {ex}", "Error");
            }
        }

        private async Task UpdateTelemeteringDataAsync(TelemeteringModel ycData, YC_TYPE_New yc, string redisKey, string redisChannel, string haskey)
        {
            ycData.ResultTime = yc.time;
            ycData.ResultValue = yc.val * ycData.Coefficient;

            var tasks = new List<Task>
            {
                _redisDataRepository.TelemeteringModelListRedis.HashSetUpdateOneAsync(redisKey, haskey, ycData),
                CheckSecondCuirtAlarm(haskey, ycData, null),
                //HandleDeviceMonitoringData(ycData),
                Task.Run(() => _redisDataRepository.TelemeteringModelInflectionListRedis.PublishAsync(redisChannel, ycData)),
                SetEnvirmentTemp(ycData)
            };

            await Task.WhenAll(tasks);
        }

        private async Task HandleTelesignalDataAsync(RecordRECORDYXBURSTNewTaskInfo ri)
        {
            try
            {
                var yx = ri.Record;
                var categoryValue = ri.Connection.DataSourceCategoryName;
                string redisKey = $"{_redisDataRepository.TelesignalisationModelListRediskey}_{(DataSourceCategoryEnum)categoryValue}";
                string redisChannel = $"{_redisDataRepository.TelesignalisationInflectionInflectionZZChannelRediskey}_{(DataSourceCategoryEnum)categoryValue}";

                string haskey = $"{yx.dev_addr}_{yx.dev_sector}_{yx.dev_inf}_{categoryValue}";
                var yxData = await _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOneAsync(redisKey, haskey);

                if (yxData == null)
                {
                    Log4Helper.Error(GetType(), $"更新数据失败: 地址：{yx.dev_addr} 类型：{categoryValue} 键：{haskey}");
                    return;
                }

                await UpdateTelesignalDataAsync(yxData, yx, redisKey, redisChannel, haskey);
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler($"Error in HandleTelesignalDataAsync: {ex}", "Error");
            }
        }

        private async Task UpdateTelesignalDataAsync(TelesignalisationModel yxData, RECORDYXBURST_New yx, string redisKey, string redisChannel, string haskey)
        {
            yxData.ResultTime = yx.time;
            yxData.ResultValue = yx.yx_val;

            var tasks = new List<Task>
            {
                _redisDataRepository.TelesignalisationModelListRedis.HashSetUpdateOneAsync(redisKey, haskey, yxData),
                CheckSecondCuirtAlarm(haskey, null, yxData),
                Task.Run(() => _redisDataRepository.TelesignalisationModelInflectionListRedis.PublishAsync(redisChannel, yxData))
            };

            await Task.WhenAll(tasks);
        }

        private void InitEquipmentInfoDic()
        {
            using (var uow = _unitOfWorkManager.Begin())
            {
                _equipmentInfoDic = _equipmentInfoRepository.GetAll().ToDictionary(t => t.Id);
                uow.Complete();
            }
        }

        private async Task UpdateTelemeteringData(RecordYC_TYPENewTaskInfo ri)
        {
            try
            {
                var yc = ri.Record;
                var categoriyValue = ri.Connection.DataSourceCategoryName;
                string redisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + (DataSourceCategoryEnum)ri.Connection.DataSourceCategoryName;
                string redisChannel = _redisDataRepository.TelemeteringInflectionInflectionZZChannelRediskey + "_" + (DataSourceCategoryEnum)ri.Connection.DataSourceCategoryName;
                // 批量获取 Redis 数据以减少读取次数
                string haskey = $"{yc.addr}_{0}_{yc.inf}_{categoriyValue}";
                var ycData =  _redisDataRepository.TelemeteringModelListRedis.HashSetGetOne(redisKey, haskey);
                if (ycData == null)
                {
                    Log4Helper.Error(this.GetType(), $"更新数据失败: 地址：{yc.inf} 类型：{categoriyValue} 键：{haskey}");
                    return;
                }

                // 更新对象的数据
                ycData.ResultTime = yc.time;
                ycData.ResultValue = yc.val*ycData.Coefficient;
                // 并行处理多个任务以提高性能
                var tasks = new List<Task>();
                // 更新到内存数据库中
                tasks.Add(_redisDataRepository.TelemeteringModelListRedis.HashSetUpdateOneAsync(redisKey, haskey, ycData));
                tasks.Add(CheckSecondCuirtAlarm(haskey, ycData,null));
                ////处理装置监视数据
                //tasks.Add(HandleDeviceMonitoringData(ycData));
                //// 将变更的数据添加到变位库 订阅
                //tasks.Add(Task.Run(() => _redisDataRepository.TelemeteringModelInflectionListRedis.PublishAsync(redisChannel, ycData)));
                //// 异步任务处理数据保存、告警分析和缓存环境温度
                //tasks.Add(SetEnvirmentTemp(ycData));
            }
            catch (Exception ex)
            {
                // 捕获并记录异常
                MonitoringEventBus.LogHandler(ex.StackTrace, "错误信息");
            }
        }

        public async Task<TelemeteringModel> GetCPUVisualYcInfo(TelemeteringModel devYc)
        {
            string redisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);
            var ycDatas =await _redisDataRepository.TelemeteringModelListRedis.HashSetGetAllAsync(redisKey);
            if (ycDatas == null)
            {
                var ycData = ycDatas.Where(t=>t.IsVirtualDevice&&t.EquipmentInfoId == devYc.EquipmentInfoId).FirstOrDefault(t=>t.Name == "CPU温度");
                return ycData;
            }
            return null;
        }
        public async Task SetEnvirmentTemp(TelemeteringModel ycData)
        {
            // 缓存环境温度（如果适用）
            if (ycData.IsEnvironmentTemp)
            {
                var environmentTempValue = new EnvironmentTempValue(ycData.ResultValue);
                await _redisDataRepository.EnvironmentTempValueRedis.HashSetUpdateOneAsync(nameof(EnvironmentTempValue), ycData.Id.ToString(), environmentTempValue);
            }
            // 处理告警分析
            //await _alarmAnalysis.HandleTelemeteringAlarmAsync(ycData);
        }
        private async Task UpdateTelesignalDataAsync(RecordRECORDYXBURSTNewTaskInfo ri)
        {
            try
            {
                var yx = ri.Record;
                var categoriyValue = ri.Connection.DataSourceCategoryName;
                string redisKey = _redisDataRepository.TelesignalisationModelListRediskey + "_" + (DataSourceCategoryEnum)categoriyValue;
                string redisChannel = _redisDataRepository.TelesignalisationInflectionInflectionZZChannelRediskey + "_" + (DataSourceCategoryEnum)categoriyValue;
                string haskey = $"{yx.dev_addr}_{yx.dev_sector}_{yx.dev_inf}_{categoriyValue}";
                // 从 Redis 中批量获取遥信数据
                var yxData =  _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOne(redisKey, haskey);
                if (yxData == null)
                {
                    Log4Helper.Error(this.GetType(), $"更新数据失败: 地址：{yx.dev_addr} 类型：{categoriyValue} 键：{haskey}");
                    return;
                }
                // 更新遥信数据的值和时间
                yxData.ResultTime = yx.time;
                yxData.ResultValue = yx.yx_val;

                // 并行处理更新、数据插入和告警分析任务
                var tasks = new List<Task>();

                // 1. 更新 Redis 中的数据
                tasks.Add(_redisDataRepository.TelesignalisationModelListRedis.HashSetUpdateOneAsync(redisKey, haskey, yxData));
                tasks.Add(CheckSecondCuirtAlarm(haskey,null, yxData));
                // 2. 将更新的数据加入到变位库中
                tasks.Add(Task.Run(() => _redisDataRepository.TelesignalisationModelInflectionListRedis.PublishAsync(redisChannel, yxData)));

                // 3. 插入数据库中的结果数据
                int rstValue = yxData.RemoteType == RemoteTypeEnum.SinglePoint ? (yx.yx_val - 1) : yx.yx_val;
                //var yxRes = new TelesignalisationResult
                //{
                //    ResultTime = yxData.ResultTime,
                //    ResultValue = yxData.ResultValue,
                //    TelesignalisationConfigurationId = yxData.Id,
                //    SaveMethod = 2
                //};
                //tasks.Add(_dataRepository.TelesignalisationResultRepository.InsertOneAsync(yxRes));

                // 4. 处理告警分析
                //tasks.Add(_alarmAnalysis.HandleTelesignalAlarmAsync(yxData));
                // 等待所有任务完成
                //await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                // 捕获并记录异常
                MonitoringEventBus.LogHandler(ex.StackTrace, "错误信息");
            }
        }
        private Task CheckSecondCuirtAlarm(string yxycredishaskey, TelemeteringModel yc, TelesignalisationModel yx)
        {
            if (_runningDataCache.SecondaryCircuitLogicExpressionDic.ContainsKey(yxycredishaskey))
            {
                return Task.Run(() =>
                {
                    lock (_runningDataCache.SecondaryCircuitLogicExpressionDic)
                    {
                        // 获取原始数据
                        var listLogics = _runningDataCache.SecondaryCircuitLogicExpressionDic[yxycredishaskey];

                        // 创建副本列表
                        var copiedLogics = listLogics
                        .Select(logic => new LogicExpressionTelesignalisation
                        {
                            LogicExpression = logic.LogicExpression,
                            TelesignalisationAddr = logic.TelesignalisationAddr,
                            SecondaryCircuitId = logic.SecondaryCircuitId
                        })
                        .ToList();

                        foreach (var listLogic in copiedLogics)
                        {
                            string pattern = @"\{([^}]*)\}";
                            Regex regex = new Regex(pattern);
                            // 提取匹配内容
                            MatchCollection matches = regex.Matches(listLogic.LogicExpression);
                            foreach (Match match in matches)
                            {
                                var logichasKey = match.Groups[1].Value;
                                string telemeteringredisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);

                                var ycLiveData = _redisDataRepository.TelemeteringModelListRedis.HashSetGetOne(telemeteringredisKey, logichasKey);
                                if (ycLiveData != null)
                                {
                                    listLogic.LogicExpression = listLogic.LogicExpression.Replace(logichasKey, ycLiveData.ResultValue.ToString());
                                }
                                string telesignalisationredisKey = _redisDataRepository.TelesignalisationModelListRediskey + "_" + _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);

                                var yxLiveData = _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOne(telesignalisationredisKey, logichasKey);
                                if (yxLiveData != null)
                                {
                                    if (yxLiveData.RemoteType == RemoteTypeEnum.DoublePoint)
                                    {
                                        yxLiveData.ResultValue = yxLiveData.ResultValue > 2 ? 0 : yxLiveData.ResultValue - 1;
                                    }
                                    else
                                    {
                                        yxLiveData.ResultValue = yxLiveData.ResultValue > 2 ? 0 : yxLiveData.ResultValue;
                                    }
                                    listLogic.LogicExpression = listLogic.LogicExpression.Replace(logichasKey, yxLiveData.ResultValue.ToString());
                                }
                            }

                            try
                            {
                                listLogic.LogicExpression = listLogic.LogicExpression.Replace("{", "").Replace("}", "");
                                var result = Eval.Execute<bool>(listLogic.LogicExpression);
                                if (result)
                                {
                                    Guid alarmEquipmentInfoId = default;
                                    string equimpentName = default;
                                    string yxycName = default;
                                    if (yc!=null)
                                    {
                                        alarmEquipmentInfoId = yc.EquipmentInfoId.Value;
                                        equimpentName = yc.EquipmentInfo.Name;
                                        yxycName = yc.Name;
                                    }
                                    else if (yx!=null)
                                    {
                                        alarmEquipmentInfoId = yx.EquipmentInfoId.Value;
                                        //equimpentName = yx.Name;
                                        yxycName = yx.Name;

                                    }
                                    var protectionDevices = _webApiRequest.GetProtectionDevicesBySecondaryCircuitList(listLogic.SecondaryCircuitId);
                                    SecondaryCircuitComponent secondaryCircuitComponent = new SecondaryCircuitComponent()
                                    {
                                        AlarmEquipmentInfoId = alarmEquipmentInfoId,
                                        IncludeEquipmentInfoIds = protectionDevices.Select(t=>t.EquipmentInfoId).ToList(),
                                        SecondaryCircuitId = listLogic.SecondaryCircuitId,
                                        ComponentName = equimpentName,
                                        HandlingMeasures = "",
                                        Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                        AbnormalReason = $"{equimpentName}的{yxycName}的状态异常",
                                    };

                                    SendSecondaryCircuitDiagnosticsInfo(secondaryCircuitComponent);
                                    //待定
                                    string telesignalisationredisKey = _redisDataRepository.TelesignalisationModelListRediskey + "_" + _settingModel.GetDatacatgoryName("无");
                                    var yxData = _redisDataRepository.TelesignalisationModelListRedis.HashSetGetOne(telesignalisationredisKey, listLogic.TelesignalisationAddr);
                                    if (yxData!=null)
                                    {
                                        yxData.ResultValue = yxData.RemoteType == RemoteTypeEnum.DoublePoint ? 2 : 1;
                                        _webApiRequest.SendVisualYx(yxData);
                                    }

                                }
                                MonitoringEventBus.LogHandler($"判定结果为{result} 判定表达式{listLogic.LogicExpression}", "二次回路遥信判定");
                            }
                            catch (Exception ex)
                            {
                                MonitoringEventBus.LogHandler(ex.StackTrace, "错误信息");
                            }
                        }
                    }
                });
            }
            return default;
        }
        


        private async void CPUMonitorHandle(DeviceCPUMonitoring data, TelemeteringModel ycData)
        {
            EquipmentInfo equipmentInfo = default;
            if (_equipmentInfoDic== null)
            {
                InitEquipmentInfoDic();
            }
            if (_equipmentInfoDic.ContainsKey(data.EquipmentInfoId.Value))
            {
                equipmentInfo = _equipmentInfoDic[data.EquipmentInfoId.Value];
            }
            bool isDeviceCPUMonitoringData = false;
            Debug.WriteLine(ycData.Name);
            // 校验装置温度
            if (ycData.Name.Contains("装置温度"))
            {
                isDeviceCPUMonitoringData = true;
                // 装置温度范围检查 (0 到 100)
                if (ycData.ResultValue >= -20 && ycData.ResultValue <= 100)
                {
                    data.SurfaceTemperature = ycData.ResultValue;
                    if (ycData.ResultValue>ycData.UpperLimit )
                    {
                        SendDeviceSlefCheckInfo(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，超过最大门限值：{ycData.UpperLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")

                        });
                    }
                }
                else
                {
                    data.SurfaceTemperature = 40;  // 设置为默认温度 60°C
                }
                string telemeteringredisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);

                var ycdatas =await _redisDataRepository.TelemeteringModelListRedis.HashSetGetAllAsync(telemeteringredisKey);

                var cpuYcdata = ycdatas.FirstOrDefault(t => t.EquipmentInfoId == ycData.EquipmentInfoId && t.Name == "CPU温度");
                cpuYcdata.ResultTime = DateTime.Now;
                cpuYcdata.ResultValue = data.SurfaceTemperature + 20;
                _webApiRequest.SendVisualYC(cpuYcdata);
            }
            // 校验 5V 电压1
            else if (ycData.Name.ToLower().Contains("工作电压"))
            {
                isDeviceCPUMonitoringData = true;
                if (ycData.ResultValue >= 0 && ycData.ResultValue <= 10)
                {
                    data.CPU5V1 = ycData.ResultValue;
                    //报警
                    if (ycData.ResultValue > ycData.UpperLimit  )
                    {
                        SendDeviceSlefCheckInfo(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，超过最大门限值：{ycData.UpperLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                    else if (ycData.ResultValue < ycData.LowerLimit)
                    {
                        SendDeviceSlefCheckInfo(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，低于最小门限值：{ycData.LowerLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                }
                else
                {
                    Random random = new Random();
                    data.CPU5V1 = (float)(random.NextDouble() * (5.02 - 4.98) + 4.98);
                }
            }
            else if (ycData.Name.ToLower().Contains("CPU温度"))
            {
                isDeviceCPUMonitoringData = true;
                if (ycData.ResultValue >= -20&& ycData.ResultValue <= 150)
                {
                    data.CPUTemperature = ycData.ResultValue;
                    if (ycData.ResultValue > ycData.UpperLimit)
                    {
                        SendDeviceSlefCheckInfo(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，低于最小门限值：{ycData.LowerLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                }
                else
                {
                    data.CPUTemperature = 60;
                }
            }
            if (isDeviceCPUMonitoringData)
            {
                data.Time = ycData.ResultTime;
                data.EquipmentInfoId = ycData.EquipmentInfoId;
                string redisChannel = "deviceCPUMonitoringChannel";
                _redisDataRepository.DeviceCPUMonitoringRedis.PublishAsync(redisChannel, data);
                //在数据库中存储装置监测数据
                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(DeviceCPUMonitoringResult);
                DeviceCPUMonitoringResult deviceCPUMonitoringResult = new DeviceCPUMonitoringResult
                {
                    CPU5V1 = data.CPU5V1,
                    CPUTemperature = data.CPUTemperature,
                    EquipmentInfoId = data.EquipmentInfoId,
                    SurfaceTemperature = data.SurfaceTemperature,
                    Time = data.Time,
                };
               await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(deviceCPUMonitoringResult.ToBsonDocument());
            }
        }
        /// <summary>
        /// 发送装置自检信息
        /// </summary>
        /// <returns></returns>
        private async Task SendDeviceSlefCheckInfo(EquipmentInfoAbnormalComponent equipmentInfoAbnormalComponent)
        {

            var alarmMessage = new AlarmMessage
            {
                HandlingMeasures = equipmentInfoAbnormalComponent.HandlingMeasures,
                AlarmContent = equipmentInfoAbnormalComponent.AbnormalReason,
                AlarmDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                Id = $"{equipmentInfoAbnormalComponent.EquipmentInfoId}:{equipmentInfoAbnormalComponent.AbnormalReason}",
                EquipmentInfoId = equipmentInfoAbnormalComponent.EquipmentInfoId,
                EquipmentInfoName = equipmentInfoAbnormalComponent.ComponentName
            };
            var alarmMessageList = await _redisDataRepository.AlarmMessageRedis.HashSetGetAllAsync(nameof(AlarmMessage));
            var isExixtalarm = alarmMessageList.Any(t => t.EquipmentInfoId == alarmMessage.EquipmentInfoId
                                                    && t.AlarmContent == alarmMessage.AlarmContent
                                                    && t.HandlingMeasures == alarmMessage.HandlingMeasures
                                                );

            if (!isExixtalarm)
            {
                string redisChannel = "deviceSelfTestChannel";
                await _redisDataRepository.AbnormalComponentRedis.PublishAsync(redisChannel, equipmentInfoAbnormalComponent);

                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(EquipmentInfoAbnormalComponent);
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(equipmentInfoAbnormalComponent.ToBsonDocument());


                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(AlarmMessage) + DateTime.Now.Year;
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(alarmMessage.ToBsonDocument());

                _redisDataRepository.AlarmMessageRedis.PublishAsync("alarmMessageChannel", alarmMessage);

                _redisDataRepository.AlarmMessageRedis.HashSetUpdateOneAsync(nameof(AlarmMessage), alarmMessage.Id, alarmMessage);
            }
        }

        /// <summary>
        /// 发送回路诊断信息
        /// </summary>
        /// <returns></returns>
        private async Task SendSecondaryCircuitDiagnosticsInfo(SecondaryCircuitComponent secondaryCircuitComponent)
        {

            var alarmMessage = new AlarmMessage
            {
                HandlingMeasures = secondaryCircuitComponent.HandlingMeasures,
                AlarmContent = secondaryCircuitComponent.AbnormalReason,
                AlarmDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),

                EquipmentInfoId = secondaryCircuitComponent.AlarmEquipmentInfoId,
                Id = $"{secondaryCircuitComponent.AlarmEquipmentInfoId}:{secondaryCircuitComponent.AbnormalReason}"
            };
            var alarmMessageList = await _redisDataRepository.AlarmMessageRedis.HashSetGetAllAsync(nameof(AlarmMessage));
            var isExixtalarm = alarmMessageList.Any(t => t.EquipmentInfoId == alarmMessage.EquipmentInfoId
                                                    && t.AlarmContent == alarmMessage.AlarmContent
                                                    && t.HandlingMeasures == alarmMessage.HandlingMeasures
                                                );
            if (!isExixtalarm)
            {
                _redisDataRepository.AlarmMessageRedis.PublishAsync("alarmMessageChannel", alarmMessage);
                _redisDataRepository.AlarmMessageRedis.HashSetUpdateOneAsync(nameof(AlarmMessage), alarmMessage.Id, alarmMessage);
                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(AlarmMessage) + DateTime.Now.Year;
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(alarmMessage.ToBsonDocument());

                string redisChannel = "secondaryCircuitDiagnosticsChannel";
                await _redisDataRepository.SecondaryCircuitComponentRedis.PublishAsync(redisChannel, secondaryCircuitComponent);

                await _redisDataRepository.SecondaryCircuitComponentRedis.HashSetUpdateOneAsync(nameof(SecondaryCircuitComponent), secondaryCircuitComponent.SecondaryCircuitId.ToString(), secondaryCircuitComponent);
            }
        }
        /// <summary>
        /// 发送寿命预估
        /// </summary>
        /// <returns></returns>
        private async Task SendEquipmentInfoRemainingLifeAssessmentInfo()
        {
            string redisChannel = "equipmentInfoRemainingLifeAssessmentChannel";
            await _redisDataRepository.EquipmentInfoRemainingLifeAssessmentRedis.PublishAsync(redisChannel, new EquipmentInfoRemainingLifeAssessment
            {
                EquipmentInfoId = Guid.Parse("08dd0eb5-f8b7-48a4-81da-2f531d0f614a"),
                RemainingLifeInYears = "5.8年",
                Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

        }
        /// <summary>
        /// 发送遥测数据到主界面日志显示
        /// </summary>
        /// <param name="info"></param>
        public void RecordYCLogInfo(ConnectionConfig connection, YC_TYPE_New info)
        {
            if (!_settingModel.Displayyxyc)
            {
                return;
            }
            string infoStr = $"装置地址:{info.addr}，CPU扇区号：{info.sector}，编码地址：{info.inf}，遥测值：{info.val},时间：{(info.time.ToString("HH:mm:ss fff"))}";
            MonitoringEventBus.LogHandler(infoStr, $"遥测数据-{connection.DataSourceCategoryDisplay}");
        }
        /// <summary>
        /// 发送遥信数据到主界面日志显示
        /// </summary>
        /// <param name="info"></param>
        public void RecordYXLogInfo(ConnectionConfig connection, RECORDYXBURST_New info)
        {
            if (!_settingModel.Displayyxyc)
            {
                return;
            }
            string infoStr = $"装置地址：{info.dev_addr}，CPU扇区号{info.dev_sector}，编码地址：{info.dev_inf}，遥信值：{info.yx_val},时间：{(info.time.ToString("HH:mm:ss fff"))}";
             MonitoringEventBus.LogHandler(infoStr, $"遥信信息-{connection.DataSourceCategoryDisplay}");
        }



    }
}
