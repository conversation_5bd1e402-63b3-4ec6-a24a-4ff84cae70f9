﻿using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace YunDa.ISAS.MongoDB.Repositories
{
    public interface IMongoDbRepository<TEntity, TPrimaryKey> where TEntity : class
    {
        /// <summary>
        /// 表名字，默认是对象名称
        /// </summary>
        string CollectionName { get; set; }
        /// <summary>
        /// 表数据
        /// </summary>
        IMongoCollection<TEntity> Collection { get; }
        /// <summary>
        /// 数据库
        /// </summary>
        IMongoDatabase Database { get; }
        /// <summary>
        /// 根据Id获取一条数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        TEntity GetOne(TPrimaryKey id);
        /// <summary>
        /// 根据Id获取一条数据，异步
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<TEntity> GetOneAsync(TPrimaryKey id);
        /// <summary>
        /// 获取符合条件的第一条数据
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        TEntity FirstOrDefault(Expression<Func<TEntity, bool>> filter = null);
        /// <summary>
        /// 获取符合条件的第一条数据，异步
        /// </summary>
        /// <param name="filter">Lambda表达式筛选条件</param>
        /// <returns>符合条件的第一条数据或默认值</returns>
        Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> filter = null);

        /// <summary>
        /// 根据FilterDefinition条件获取符合条件的第一条数据，异步
        /// </summary>
        /// <param name="filter">MongoDB筛选条件</param>
        /// <returns>符合条件的第一条数据或默认值</returns>
        Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter);
        
        /// <summary>
        /// 插入一条数据并返回是否成功，异步
        /// </summary>
        /// <param name="entity">要插入的实体</param>
        /// <returns>插入是否成功</returns>
        Task<bool> InsertAsync(TEntity entity);

        /// <summary>
        /// 根据条件更新数据并返回更新后的实体，异步
        /// </summary>
        /// <param name="filter">MongoDB筛选条件</param>
        /// <param name="update">更新操作定义</param>
        /// <returns>更新后的实体</returns>
        Task<TEntity> UpdateAsync(FilterDefinition<TEntity> filter, UpdateDefinition<TEntity> update);
        /// <summary>
        /// 获取符合条件的所有数据
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        IQueryable<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null);
        /// <summary>
        /// 获取符合条件的所有数据
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="field"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        IEnumerable<TEntity> GetAllInclude(FilterDefinition<TEntity> filter, string[] field = null, SortDefinition<TEntity> sort = null);
        /// <summary>
        /// 获取符合条件的所有数据，返回IFindFluent
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="field"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        IFindFluent<TEntity, TEntity> GetAllIncludeToFindFluent(FilterDefinition<TEntity> filter, string[] field = null, SortDefinition<TEntity> sort = null);
        /// <summary>
        /// 插入一条数据
        /// </summary>
        /// <param name="entity"></param>
        void InsertOne(TEntity entity);
        /// <summary>
        /// 插入一条数据，异步
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task InsertOneAsync(TEntity entity);
        /// <summary>
        /// 插入多条数据
        /// </summary>
        /// <param name="entitys"></param>
        void InsertMany(IEnumerable<TEntity> entitys);
        /// <summary>
        /// 插入多条数据，异步
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        Task InsertManyAsync(IEnumerable<TEntity> entitys);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        UpdateResult UpdateOne(TEntity entity);
        /// <summary>
        /// 根据条件更新数据中指定字段
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="fieldList"></param>
        /// <returns></returns>
        UpdateResult UpdateOne(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList);
        /// <summary>
        /// 更新一条数据，异步
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<UpdateResult> UpdateOneAsync(TEntity entity);
        /// <summary>
        /// 根据条件更新数据中指定字段，异步
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="fieldList"></param>
        /// <returns></returns>
        Task<UpdateResult> UpdateOneAsync(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList);
        /// <summary>
        /// 根据条件批量修改
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="fieldList"></param>
        /// <returns></returns>
        UpdateResult UpdateMany(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList);
        /// <summary>
        /// 根据条件批量修改，异步
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="fieldList"></param>
        /// <returns></returns>
        Task<UpdateResult> UpdateManyAsync(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList);
        /// <summary>
        /// 替换一条数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        ReplaceOneResult ReplaceOne(TEntity entity);
        /// <summary>
        /// 替换一条数据，异步
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<ReplaceOneResult> ReplaceOneAsync(TEntity entity);
        /// <summary>
        /// 根据Id删除一条数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        DeleteResult DeleteOne(TPrimaryKey id);
        /// <summary>
        /// 更具id删除多条数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        DeleteResult DeleteMany(List<TPrimaryKey> ids);
        /// <summary>
        /// 根据Id删除一条数据，异步
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<DeleteResult> DeleteOneAsync(TPrimaryKey id);
       
        /// <summary>
        /// 根据条件删除多条数据，异步
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<DeleteResult> DeleteManyAsync(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据条件获取数据数量
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        long Count(Expression<Func<TEntity, bool>> filter = null);
        /// <summary>
        /// 根据条件获取数据数量，异步
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<long> CountAsync(Expression<Func<TEntity, bool>> filter = null);
        /// <summary>
        /// 判断数据是否存在
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        bool Exists(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 判断数据是否存在，异步
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 分页获取数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="sort">排序方式</param>
        /// <returns></returns>
        IFindFluent<TEntity, TEntity> GetPaged(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null);
        /// <summary>
        /// 分页获取数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="sort">排序方式</param>
        /// <returns></returns>
        Task<IAsyncCursor<TEntity>> GetPagedAsync(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null);
        /// <summary>
        /// 创建索引
        /// </summary>
        /// <param name="keys">索引键</param>
        /// <param name="options">索引选项</param>
        /// <returns></returns>
        string CreateIndex(IndexKeysDefinition<TEntity> keys, CreateIndexOptions options = null);
        /// <summary>
        /// 创建索引，异步
        /// </summary>
        /// <param name="keys">索引键</param>
        /// <param name="options">索引选项</param>
        /// <returns></returns>
        Task<string> CreateIndexAsync(IndexKeysDefinition<TEntity> keys, CreateIndexOptions options = null);
        /// <summary>
        /// 获取所有索引
        /// </summary>
        /// <returns></returns>
        List<BsonDocument> GetIndexes();
        /// <summary>
        /// 获取所有索引，异步
        /// </summary>
        /// <returns></returns>
        Task<List<BsonDocument>> GetIndexesAsync();
        /// <summary>
        /// 删除索引
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <returns></returns>
        void DropIndex(string indexName);
        /// <summary>
        /// 删除索引，异步
        /// </summary>
        /// <param name="indexName">索引名称</param>
        /// <returns></returns>
        Task DropIndexAsync(string indexName);
        /// <summary>
        /// 执行聚合操作
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="pipeline">聚合管道</param>
        /// <param name="options">聚合选项</param>
        /// <returns></returns>
        IAsyncCursor<TResult> Aggregate<TResult>(PipelineDefinition<TEntity, TResult> pipeline, AggregateOptions options = null);
        /// <summary>
        /// 执行聚合操作，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="pipeline">聚合管道</param>
        /// <param name="options">聚合选项</param>
        /// <returns></returns>
        Task<IAsyncCursor<TResult>> AggregateAsync<TResult>(PipelineDefinition<TEntity, TResult> pipeline, AggregateOptions options = null);
        /// <summary>
        /// 批量写入操作
        /// </summary>
        /// <param name="requests">写入请求列表</param>
        /// <param name="options">批量写入选项</param>
        /// <returns></returns>
        BulkWriteResult<TEntity> BulkWrite(IEnumerable<WriteModel<TEntity>> requests, BulkWriteOptions options = null);
        /// <summary>
        /// 批量写入操作，异步
        /// </summary>
        /// <param name="requests">写入请求列表</param>
        /// <param name="options">批量写入选项</param>
        /// <returns></returns>
        Task<BulkWriteResult<TEntity>> BulkWriteAsync(IEnumerable<WriteModel<TEntity>> requests, BulkWriteOptions options = null);
        /// <summary>
        /// 查找并更新
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="options">查找并更新选项</param>
        /// <returns></returns>
        TEntity FindOneAndUpdate(FilterDefinition<TEntity> filter, UpdateDefinition<TEntity> update, FindOneAndUpdateOptions<TEntity> options = null);
        /// <summary>
        /// 查找并更新，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="options">查找并更新选项</param>
        /// <returns></returns>
        Task<TEntity> FindOneAndUpdateAsync(FilterDefinition<TEntity> filter, UpdateDefinition<TEntity> update, FindOneAndUpdateOptions<TEntity> options = null);
        /// <summary>
        /// 查找并删除
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="options">查找并删除选项</param>
        /// <returns></returns>
        TEntity FindOneAndDelete(FilterDefinition<TEntity> filter, FindOneAndDeleteOptions<TEntity> options = null);
        /// <summary>
        /// 查找并删除，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="options">查找并删除选项</param>
        /// <returns></returns>
        Task<TEntity> FindOneAndDeleteAsync(FilterDefinition<TEntity> filter, FindOneAndDeleteOptions<TEntity> options = null);
        /// <summary>
        /// 查找并替换
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="replacement">替换的实体</param>
        /// <param name="options">查找并替换选项</param>
        /// <returns></returns>
        TEntity FindOneAndReplace(FilterDefinition<TEntity> filter, TEntity replacement, FindOneAndReplaceOptions<TEntity> options = null);
        /// <summary>
        /// 查找并替换，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="replacement">替换的实体</param>
        /// <param name="options">查找并替换选项</param>
        /// <returns></returns>
        Task<TEntity> FindOneAndReplaceAsync(FilterDefinition<TEntity> filter, TEntity replacement, FindOneAndReplaceOptions<TEntity> options = null);
        /// <summary>
        /// 获取所有数据列表
        /// </summary>
        /// <returns></returns>
        List<TEntity> GetAllList();
        /// <summary>
        /// 获取所有数据列表，异步
        /// </summary>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListAsync();
        /// <summary>
        /// 根据条件获取数据列表
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        List<TEntity> GetAllList(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据条件获取数据列表，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据条件获取数据列表
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sort">排序方式</param>
        /// <returns></returns>
        List<TEntity> GetAllList(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort);
        /// <summary>
        /// 根据条件获取数据列表，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sort">排序方式</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort);
        
        /// <summary>
        /// 根据条件获取第一条数据或默认值
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sort">排序方式</param>
        /// <returns></returns>
        TEntity FirstOrDefault(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort);
        /// <summary>
        /// 根据条件获取第一条数据或默认值，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sort">排序方式</param>
        /// <returns></returns>
        Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort);
        /// <summary>
        /// 根据条件获取单条数据，如果不存在则抛出异常
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        TEntity Single(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据条件获取单条数据，如果不存在则抛出异常，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        Task<TEntity> SingleAsync(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据条件获取单条数据，如果不存在或有多条则抛出异常
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        TEntity SingleOrDefault(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据条件获取单条数据，如果不存在或有多条则抛出异常，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        Task<TEntity> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 根据多个ID获取实体列表
        /// </summary>
        /// <param name="ids">ID列表</param>
        /// <returns></returns>
        List<TEntity> GetByIds(IEnumerable<TPrimaryKey> ids);
        /// <summary>
        /// 根据多个ID获取实体列表，异步
        /// </summary>
        /// <param name="ids">ID列表</param>
        /// <returns></returns>
        Task<List<TEntity>> GetByIdsAsync(IEnumerable<TPrimaryKey> ids);
        /// <summary>
        /// 获取分页数据和总数
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="sort">排序方式</param>
        /// <returns>元组(数据列表, 总数)</returns>
        (List<TEntity> Items, long TotalCount) GetPagedWithCount(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null);
        /// <summary>
        /// 获取分页数据和总数，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="sort">排序方式</param>
        /// <returns>元组(数据列表, 总数)</returns>
        Task<(List<TEntity> Items, long TotalCount)> GetPagedWithCountAsync(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null);
        /// <summary>
        /// 获取指定字段的值列表
        /// </summary>
        /// <typeparam name="TField">字段类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段选择器</param>
        /// <returns>字段值列表</returns>
        List<TField> GetFieldValues<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field);
        /// <summary>
        /// 获取指定字段的值列表，异步
        /// </summary>
        /// <typeparam name="TField">字段类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段选择器</param>
        /// <returns>字段值列表</returns>
        Task<List<TField>> GetFieldValuesAsync<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field);
        /// <summary>
        /// 获取不重复的字段值列表
        /// </summary>
        /// <typeparam name="TField">字段类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段选择器</param>
        /// <returns>不重复的字段值列表</returns>
        List<TField> GetDistinctValues<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field);
        /// <summary>
        /// 获取不重复的字段值列表，异步
        /// </summary>
        /// <typeparam name="TField">字段类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段选择器</param>
        /// <returns>不重复的字段值列表</returns>
        Task<List<TField>> GetDistinctValuesAsync<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field);
        /// <summary>
        /// 更新指定字段
        /// </summary>
        /// <typeparam name="TField">字段类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段选择器</param>
        /// <param name="value">新值</param>
        /// <returns>更新结果</returns>
        UpdateResult UpdateField<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field, TField value);
        /// <summary>
        /// 更新指定字段，异步
        /// </summary>
        /// <typeparam name="TField">字段类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段选择器</param>
        /// <param name="value">新值</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpdateFieldAsync<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field, TField value);
        /// <summary>
        /// 递增字段值
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段名</param>
        /// <param name="value">递增值</param>
        /// <returns>更新结果</returns>
        UpdateResult Increment(Expression<Func<TEntity, bool>> filter, string field, long value = 1);
        /// <summary>
        /// 递增字段值，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段名</param>
        /// <param name="value">递增值</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> IncrementAsync(Expression<Func<TEntity, bool>> filter, string field, long value = 1);
        /// <summary>
        /// 执行文本搜索
        /// </summary>
        /// <param name="text">搜索文本</param>
        /// <returns>搜索结果</returns>
        List<TEntity> TextSearch(string text);
        /// <summary>
        /// 执行文本搜索，异步
        /// </summary>
        /// <param name="text">搜索文本</param>
        /// <returns>搜索结果</returns>
        Task<List<TEntity>> TextSearchAsync(string text);
        /// <summary>
        /// 执行文本搜索
        /// </summary>
        /// <param name="text">搜索文本</param>
        /// <param name="filter">附加筛选条件</param>
        /// <returns>搜索结果</returns>
        List<TEntity> TextSearch(string text, Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 执行文本搜索，异步
        /// </summary>
        /// <param name="text">搜索文本</param>
        /// <param name="filter">附加筛选条件</param>
        /// <returns>搜索结果</returns>
        Task<List<TEntity>> TextSearchAsync(string text, Expression<Func<TEntity, bool>> filter);
        /// <summary>
        /// 获取或创建实体
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="entity">不存在时要创建的实体</param>
        /// <returns>已存在或新创建的实体</returns>
        TEntity GetOrCreate(Expression<Func<TEntity, bool>> filter, TEntity entity);
        /// <summary>
        /// 获取或创建实体，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="entity">不存在时要创建的实体</param>
        /// <returns>已存在或新创建的实体</returns>
        Task<TEntity> GetOrCreateAsync(Expression<Func<TEntity, bool>> filter, TEntity entity);
        /// <summary>
        /// 获取或创建实体
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="createEntityFactory">不存在时创建实体的工厂方法</param>
        /// <returns>已存在或新创建的实体</returns>
        TEntity GetOrCreate(Expression<Func<TEntity, bool>> filter, Func<TEntity> createEntityFactory);
        /// <summary>
        /// 获取或创建实体，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="createEntityFactory">不存在时创建实体的工厂方法</param>
        /// <returns>已存在或新创建的实体</returns>
        Task<TEntity> GetOrCreateAsync(Expression<Func<TEntity, bool>> filter, Func<TEntity> createEntityFactory);
        /// <summary>
        /// 批量更新多个文档的多个字段
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="updates">更新字段字典</param>
        /// <returns>更新结果</returns>
        UpdateResult UpdateMany(Expression<Func<TEntity, bool>> filter, Dictionary<string, object> updates);
        /// <summary>
        /// 批量更新多个文档的多个字段，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="updates">更新字段字典</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpdateManyAsync(Expression<Func<TEntity, bool>> filter, Dictionary<string, object> updates);
        /// <summary>
        /// 删除多条数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <returns></returns>
        DeleteResult DeleteMany(Expression<Func<TEntity, bool>> filter);
        

        #region 原生MongoDB操作方法

        /// <summary>
        /// 获取MongoDB集合对象
        /// </summary>
        /// <returns>MongoDB集合对象</returns>
        IMongoCollection<TEntity> GetCollection();

        /// <summary>
        /// 执行原生MongoDB命令
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="command">命令</param>
        /// <param name="readPreference">读取首选项</param>
        /// <returns>命令结果</returns>
        TResult RunCommand<TResult>(BsonDocument command, ReadPreference readPreference = null);

        /// <summary>
        /// 执行原生MongoDB命令，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="command">命令</param>
        /// <param name="readPreference">读取首选项</param>
        /// <returns>命令结果</returns>
        Task<TResult> RunCommandAsync<TResult>(BsonDocument command, ReadPreference readPreference = null);

        /// <summary>
        /// 执行原生MongoDB聚合管道
        /// </summary>
        /// <param name="pipeline">聚合管道</param>
        /// <returns>聚合结果</returns>
        List<BsonDocument> Aggregate(BsonDocument[] pipeline);

        /// <summary>
        /// 执行原生MongoDB聚合管道，异步
        /// </summary>
        /// <param name="pipeline">聚合管道</param>
        /// <returns>聚合结果</returns>
        Task<List<BsonDocument>> AggregateAsync(BsonDocument[] pipeline);

        /// <summary>
        /// 执行原生MongoDB聚合管道
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="pipeline">聚合管道</param>
        /// <returns>聚合结果</returns>
        List<TResult> Aggregate<TResult>(BsonDocument[] pipeline);

        /// <summary>
        /// 执行原生MongoDB聚合管道，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="pipeline">聚合管道</param>
        /// <returns>聚合结果</returns>
        Task<List<TResult>> AggregateAsync<TResult>(BsonDocument[] pipeline);

        /// <summary>
        /// 使用原生MongoDB查询
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>查询结果</returns>
        List<BsonDocument> Find(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB查询，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>查询结果</returns>
        Task<List<BsonDocument>> FindAsync(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB查询
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <returns>查询结果</returns>
        List<TResult> Find<TResult>(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB查询，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <returns>查询结果</returns>
        Task<List<TResult>> FindAsync<TResult>(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB查询和投影
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <param name="projection">投影</param>
        /// <returns>查询结果</returns>
        List<TResult> Find<TResult>(BsonDocument filter, BsonDocument projection);

        /// <summary>
        /// 使用原生MongoDB查询和投影，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <param name="projection">投影</param>
        /// <returns>查询结果</returns>
        Task<List<TResult>> FindAsync<TResult>(BsonDocument filter, BsonDocument projection);

        /// <summary>
        /// 使用原生MongoDB查询、排序和分页
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <param name="sort">排序</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns>查询结果</returns>
        List<TResult> Find<TResult>(BsonDocument filter, BsonDocument sort, int skip, int limit);

        /// <summary>
        /// 使用原生MongoDB查询、排序和分页，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <param name="sort">排序</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns>查询结果</returns>
        Task<List<TResult>> FindAsync<TResult>(BsonDocument filter, BsonDocument sort, int skip, int limit);

        /// <summary>
        /// 使用原生MongoDB查询、投影、排序和分页
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <param name="projection">投影</param>
        /// <param name="sort">排序</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns>查询结果</returns>
        List<TResult> Find<TResult>(BsonDocument filter, BsonDocument projection, BsonDocument sort, int skip, int limit);

        /// <summary>
        /// 使用原生MongoDB查询、投影、排序和分页，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="filter">查询条件</param>
        /// <param name="projection">投影</param>
        /// <param name="sort">排序</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns>查询结果</returns>
        Task<List<TResult>> FindAsync<TResult>(BsonDocument filter, BsonDocument projection, BsonDocument sort, int skip, int limit);

        /// <summary>
        /// 使用原生MongoDB更新
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="isUpsert">是否插入不存在的文档</param>
        /// <returns>更新结果</returns>
        UpdateResult UpdateOne(BsonDocument filter, BsonDocument update, bool isUpsert = false);

        /// <summary>
        /// 使用原生MongoDB更新，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="isUpsert">是否插入不存在的文档</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpdateOneAsync(BsonDocument filter, BsonDocument update, bool isUpsert = false);

        /// <summary>
        /// 使用原生MongoDB批量更新
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="isUpsert">是否插入不存在的文档</param>
        /// <returns>更新结果</returns>
        UpdateResult UpdateMany(BsonDocument filter, BsonDocument update, bool isUpsert = false);

        /// <summary>
        /// 使用原生MongoDB批量更新，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="isUpsert">是否插入不存在的文档</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpdateManyAsync(BsonDocument filter, BsonDocument update, bool isUpsert = false);

        /// <summary>
        /// 使用原生MongoDB删除
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>删除结果</returns>
        DeleteResult DeleteOne(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB删除，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>删除结果</returns>
        Task<DeleteResult> DeleteOneAsync(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB批量删除
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>删除结果</returns>
        DeleteResult DeleteMany(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB批量删除，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>删除结果</returns>
        Task<DeleteResult> DeleteManyAsync(BsonDocument filter);

        /// <summary>
        /// 使用原生MongoDB查找并更新
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="options">选项</param>
        /// <returns>更新前的文档</returns>
        BsonDocument FindOneAndUpdate(BsonDocument filter, BsonDocument update, FindOneAndUpdateOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用原生MongoDB查找并更新，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="update">更新操作</param>
        /// <param name="options">选项</param>
        /// <returns>更新前的文档</returns>
        Task<BsonDocument> FindOneAndUpdateAsync(BsonDocument filter, BsonDocument update, FindOneAndUpdateOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用原生MongoDB查找并删除
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="options">选项</param>
        /// <returns>删除的文档</returns>
        BsonDocument FindOneAndDelete(BsonDocument filter, FindOneAndDeleteOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用原生MongoDB查找并删除，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="options">选项</param>
        /// <returns>删除的文档</returns>
        Task<BsonDocument> FindOneAndDeleteAsync(BsonDocument filter, FindOneAndDeleteOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用原生MongoDB查找并替换
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="replacement">替换文档</param>
        /// <param name="options">选项</param>
        /// <returns>替换前的文档</returns>
        BsonDocument FindOneAndReplace(BsonDocument filter, BsonDocument replacement, FindOneAndReplaceOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用原生MongoDB查找并替换，异步
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <param name="replacement">替换文档</param>
        /// <param name="options">选项</param>
        /// <returns>替换前的文档</returns>
        Task<BsonDocument> FindOneAndReplaceAsync(BsonDocument filter, BsonDocument replacement, FindOneAndReplaceOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 创建MongoDB索引
        /// </summary>
        /// <param name="keys">索引键</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        string CreateIndex(BsonDocument keys, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB索引，异步
        /// </summary>
        /// <param name="keys">索引键</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        Task<string> CreateIndexAsync(BsonDocument keys, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB复合索引
        /// </summary>
        /// <param name="keys">索引键列表</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        string CreateCompoundIndex(List<BsonElement> keys, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB复合索引，异步
        /// </summary>
        /// <param name="keys">索引键列表</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        Task<string> CreateCompoundIndexAsync(List<BsonElement> keys, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB文本索引
        /// </summary>
        /// <param name="fields">字段列表</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        string CreateTextIndex(string[] fields, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB文本索引，异步
        /// </summary>
        /// <param name="fields">字段列表</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        Task<string> CreateTextIndexAsync(string[] fields, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB地理空间索引
        /// </summary>
        /// <param name="field">字段名</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        string CreateGeoIndex(string field, CreateIndexOptions options = null);

        /// <summary>
        /// 创建MongoDB地理空间索引，异步
        /// </summary>
        /// <param name="field">字段名</param>
        /// <param name="options">索引选项</param>
        /// <returns>索引名称</returns>
        Task<string> CreateGeoIndexAsync(string field, CreateIndexOptions options = null);

        /// <summary>
        /// 使用MapReduce执行复杂聚合查询
        /// </summary>
        /// <param name="map">Map函数</param>
        /// <param name="reduce">Reduce函数</param>
        /// <param name="options">MapReduce选项</param>
        /// <returns>MapReduce结果</returns>
        IAsyncCursor<BsonDocument> MapReduce(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用MapReduce执行复杂聚合查询，异步
        /// </summary>
        /// <param name="map">Map函数</param>
        /// <param name="reduce">Reduce函数</param>
        /// <param name="options">MapReduce选项</param>
        /// <returns>MapReduce结果</returns>
        Task<IAsyncCursor<BsonDocument>> MapReduceAsync(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, BsonDocument> options = null);

        /// <summary>
        /// 使用MapReduce执行复杂聚合查询
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="map">Map函数</param>
        /// <param name="reduce">Reduce函数</param>
        /// <param name="options">MapReduce选项</param>
        /// <returns>MapReduce结果</returns>
        IAsyncCursor<TResult> MapReduce<TResult>(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, TResult> options = null);

        /// <summary>
        /// 使用MapReduce执行复杂聚合查询，异步
        /// </summary>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="map">Map函数</param>
        /// <param name="reduce">Reduce函数</param>
        /// <param name="options">MapReduce选项</param>
        /// <returns>MapReduce结果</returns>
        Task<IAsyncCursor<TResult>> MapReduceAsync<TResult>(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, TResult> options = null);

        /// <summary>
        /// 执行MongoDB数据库统计
        /// </summary>
        /// <returns>数据库统计信息</returns>
        BsonDocument GetStats();

        /// <summary>
        /// 执行MongoDB数据库统计，异步
        /// </summary>
        /// <returns>数据库统计信息</returns>
        Task<BsonDocument> GetStatsAsync();

        /// <summary>
        /// 执行MongoDB集合统计
        /// </summary>
        /// <returns>集合统计信息</returns>
        BsonDocument GetCollectionStats();

        /// <summary>
        /// 执行MongoDB集合统计，异步
        /// </summary>
        /// <returns>集合统计信息</returns>
        Task<BsonDocument> GetCollectionStatsAsync();

        #endregion

        #region 更多FilterDefinition参数的查询方法

        /// <summary>
        /// 根据条件获取数据列表并按指定字段排序
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        List<TEntity> GetAllListSorted(FilterDefinition<TEntity> filter, string field, bool isAscending = true);

        /// <summary>
        /// 根据条件获取数据列表并按指定字段排序，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListSortedAsync(FilterDefinition<TEntity> filter, string field, bool isAscending = true);

        /// <summary>
        /// 根据条件获取数据列表并按多个字段排序
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortFields">排序字段和方向字典</param>
        /// <returns></returns>
        List<TEntity> GetAllListSorted(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields);

        /// <summary>
        /// 根据条件获取数据列表并按多个字段排序，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortFields">排序字段和方向字典</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListSortedAsync(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields);

        /// <summary>
        /// 根据条件获取指定数量的数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        List<TEntity> GetAllListLimited(FilterDefinition<TEntity> filter, int limit);

        /// <summary>
        /// 根据条件获取指定数量的数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListLimitedAsync(FilterDefinition<TEntity> filter, int limit);

        /// <summary>
        /// 根据条件获取指定数量的数据并排序
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        List<TEntity> GetAllListLimited(FilterDefinition<TEntity> filter, string field, bool isAscending, int limit);

        /// <summary>
        /// 根据条件获取指定数量的数据并排序，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListLimitedAsync(FilterDefinition<TEntity> filter, string field, bool isAscending, int limit);

        /// <summary>
        /// 根据条件获取指定范围的数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        List<TEntity> GetAllListRange(FilterDefinition<TEntity> filter, int skip, int limit);

        /// <summary>
        /// 根据条件获取指定范围的数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListRangeAsync(FilterDefinition<TEntity> filter, int skip, int limit);

        /// <summary>
        /// 根据条件获取指定范围的数据并排序
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        List<TEntity> GetAllListRange(FilterDefinition<TEntity> filter, string field, bool isAscending, int skip, int limit);

        /// <summary>
        /// 根据条件获取指定范围的数据并排序，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="skip">跳过数量</param>
        /// <param name="limit">限制数量</param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListRangeAsync(FilterDefinition<TEntity> filter, string field, bool isAscending, int skip, int limit);

        /// <summary>
        /// 根据条件获取指定字段的数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <returns></returns>
        List<BsonDocument> GetAllListProjection(FilterDefinition<TEntity> filter, string[] fields);

        /// <summary>
        /// 根据条件获取指定字段的数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <returns></returns>
        Task<List<BsonDocument>> GetAllListProjectionAsync(FilterDefinition<TEntity> filter, string[] fields);

        /// <summary>
        /// 根据条件获取指定字段的数据并排序
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        List<BsonDocument> GetAllListProjection(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending);

        /// <summary>
        /// 根据条件获取指定字段的数据并排序，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        Task<List<BsonDocument>> GetAllListProjectionAsync(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending);

        /// <summary>
        /// 根据条件获取指定字段的数据并分页
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns></returns>
        List<BsonDocument> GetAllListProjectionPaged(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取指定字段的数据并分页，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns></returns>
        Task<List<BsonDocument>> GetAllListProjectionPagedAsync(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取数据分页和总数
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>元组(数据列表, 总数)</returns>
        (List<TEntity> Items, long TotalCount) GetPagedWithCount(FilterDefinition<TEntity> filter, string sortField, bool isAscending, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取数据分页和总数，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>元组(数据列表, 总数)</returns>
        Task<(List<TEntity> Items, long TotalCount)> GetPagedWithCountAsync(FilterDefinition<TEntity> filter, string sortField, bool isAscending, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取数据分页和总数，支持多字段排序
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortFields">排序字段和方向字典</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>元组(数据列表, 总数)</returns>
        (List<TEntity> Items, long TotalCount) GetPagedWithCount(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取数据分页和总数，支持多字段排序，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortFields">排序字段和方向字典</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>元组(数据列表, 总数)</returns>
        Task<(List<TEntity> Items, long TotalCount)> GetPagedWithCountAsync(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取指定字段的数据分页和总数
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>元组(数据列表, 总数)</returns>
        (List<BsonDocument> Items, long TotalCount) GetPagedProjectionWithCount(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize);

        /// <summary>
        /// 根据条件获取指定字段的数据分页和总数，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">字段列表</param>
        /// <param name="sortField">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>元组(数据列表, 总数)</returns>
        Task<(List<BsonDocument> Items, long TotalCount)> GetPagedProjectionWithCountAsync(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize);

        /// <summary>
        /// 批量更新多个文档的单个字段
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段名</param>
        /// <param name="value">新值</param>
        /// <returns>更新结果</returns>
        UpdateResult UpdateField(FilterDefinition<TEntity> filter, string field, object value);

        /// <summary>
        /// 批量更新多个文档的单个字段，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">字段名</param>
        /// <param name="value">新值</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpdateFieldAsync(FilterDefinition<TEntity> filter, string field, object value);

        /// <summary>
        /// 条件更新，如果不存在则插入
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="entity">实体</param>
        /// <returns>更新结果</returns>
        UpdateResult Upsert(FilterDefinition<TEntity> filter, TEntity entity);

        /// <summary>
        /// 条件更新，如果不存在则插入，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="entity">实体</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpsertAsync(FilterDefinition<TEntity> filter, TEntity entity);

        /// <summary>
        /// 条件更新字段，如果不存在则插入
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="updates">更新字段字典</param>
        /// <returns>更新结果</returns>
        UpdateResult Upsert(FilterDefinition<TEntity> filter, Dictionary<string, object> updates);

        /// <summary>
        /// 条件更新字段，如果不存在则插入，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="updates">更新字段字典</param>
        /// <returns>更新结果</returns>
        Task<UpdateResult> UpsertAsync(FilterDefinition<TEntity> filter, Dictionary<string, object> updates);

        /// <summary>
        /// 根据条件和字段排序获取第一条数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        TEntity FirstOrDefault(FilterDefinition<TEntity> filter, string field, bool isAscending = true);

        /// <summary>
        /// 根据条件和字段排序获取第一条数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter, string field, bool isAscending = true);

        /// <summary>
        /// 根据条件和多字段排序获取第一条数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortFields">排序字段和方向字典</param>
        /// <returns></returns>
        TEntity FirstOrDefault(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields);

        /// <summary>
        /// 根据条件和多字段排序获取第一条数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="sortFields">排序字段和方向字典</param>
        /// <returns></returns>
        Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields);

        /// <summary>
        /// 根据条件获取第一条投影数据
        /// </summary>
        /// <typeparam name="TProjection">投影类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="projection">投影表达式</param>
        /// <returns></returns>
        TProjection FirstOrDefaultAs<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection);

        /// <summary>
        /// 根据条件获取第一条投影数据，异步
        /// </summary>
        /// <typeparam name="TProjection">投影类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="projection">投影表达式</param>
        /// <returns></returns>
        Task<TProjection> FirstOrDefaultAsAsync<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection);

        /// <summary>
        /// 根据条件和排序获取第一条投影数据
        /// </summary>
        /// <typeparam name="TProjection">投影类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="projection">投影表达式</param>
        /// <param name="sort">排序</param>
        /// <returns></returns>
        TProjection FirstOrDefaultAs<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, SortDefinition<TEntity> sort);

        /// <summary>
        /// 根据条件和排序获取第一条投影数据，异步
        /// </summary>
        /// <typeparam name="TProjection">投影类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="projection">投影表达式</param>
        /// <param name="sort">排序</param>
        /// <returns></returns>
        Task<TProjection> FirstOrDefaultAsAsync<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, SortDefinition<TEntity> sort);

        /// <summary>
        /// 根据条件和字段排序获取第一条投影数据
        /// </summary>
        /// <typeparam name="TProjection">投影类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="projection">投影表达式</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        TProjection FirstOrDefaultAs<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, string field, bool isAscending = true);

        /// <summary>
        /// 根据条件和字段排序获取第一条投影数据，异步
        /// </summary>
        /// <typeparam name="TProjection">投影类型</typeparam>
        /// <param name="filter">筛选条件</param>
        /// <param name="projection">投影表达式</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        Task<TProjection> FirstOrDefaultAsAsync<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, string field, bool isAscending = true);

        /// <summary>
        /// 根据指定字段集合查询第一条数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">返回字段列表</param>
        /// <returns></returns>
        BsonDocument FirstOrDefaultFields(FilterDefinition<TEntity> filter, string[] fields);

        /// <summary>
        /// 根据指定字段集合查询第一条数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">返回字段列表</param>
        /// <returns></returns>
        Task<BsonDocument> FirstOrDefaultFieldsAsync(FilterDefinition<TEntity> filter, string[] fields);

        /// <summary>
        /// 根据指定字段集合和排序查询第一条数据
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">返回字段列表</param>
        /// <param name="sort">排序</param>
        /// <returns></returns>
        BsonDocument FirstOrDefaultFields(FilterDefinition<TEntity> filter, string[] fields, SortDefinition<TEntity> sort);

        /// <summary>
        /// 根据指定字段集合和排序查询第一条数据，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">返回字段列表</param>
        /// <param name="sort">排序</param>
        /// <returns></returns>
        Task<BsonDocument> FirstOrDefaultFieldsAsync(FilterDefinition<TEntity> filter, string[] fields, SortDefinition<TEntity> sort);

        /// <summary>
        /// 根据条件和字段排序获取第一条数据指定字段
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">返回字段列表</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        BsonDocument FirstOrDefaultFields(FilterDefinition<TEntity> filter, string[] fields, string field, bool isAscending = true);

        /// <summary>
        /// 根据条件和字段排序获取第一条数据指定字段，异步
        /// </summary>
        /// <param name="filter">筛选条件</param>
        /// <param name="fields">返回字段列表</param>
        /// <param name="field">排序字段</param>
        /// <param name="isAscending">是否升序</param>
        /// <returns></returns>
        Task<BsonDocument> FirstOrDefaultFieldsAsync(FilterDefinition<TEntity> filter, string[] fields, string field, bool isAscending = true);

        

        #endregion
    }
}