using Abp.Application.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsResultDto;

namespace YunDa.ISAS.Application.DataMonitoring
{
    /// <summary>
    /// 遥测统计结果服务接口
    /// </summary>
    public interface ITelemeteringStatisticsResultAppService : IApplicationService
    {
        /// <summary>
        /// 查询遥测统计结果
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>查询结果</returns>
        Task<RequestPageResult<TelemeteringStatisticsResultOutput>> GetPagedAsync(TelemeteringStatisticsResultSearchConditionInput input);

        /// <summary>
        /// 查询遥测统计数据点
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>数据点列表</returns>
        Task<RequestResult<List<TelemeteringStatisticsPointOutput>>> GetStatisticsPointsAsync(TelemeteringStatisticsResultSearchConditionInput input);

        /// <summary>
        /// 导出遥测统计结果
        /// </summary>
        /// <param name="input">查询条件</param>
        /// <returns>导出结果</returns>
        Task<RequestResult<string>> ExportAsync(TelemeteringStatisticsResultSearchConditionInput input);

        /// <summary>
        /// 手动触发统计
        /// </summary>
        /// <param name="telemeteringConfigurationId">遥测配置ID</param>
        /// <param name="statisticsType">统计类型</param>
        /// <param name="intervalType">时间间隔类型</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>触发结果</returns>
        Task<RequestEasyResult> TriggerStatisticsAsync(Guid telemeteringConfigurationId, int statisticsType, int intervalType, DateTime startTime, DateTime endTime);
    }
} 