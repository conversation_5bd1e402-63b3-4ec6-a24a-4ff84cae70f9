﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class VwTableMap
    {
        public string TableMapId { get; set; } = null!;
        public string SrcTableId { get; set; } = null!;
        public string DstTableId { get; set; } = null!;
        public int MapSeqNo { get; set; }
        public string MapCtgy { get; set; } = null!;
        public string? BeiZhu { get; set; }
        public string SrcTableCtgy { get; set; } = null!;
        public string SrcTableName { get; set; } = null!;
        public string SrcPrcname { get; set; } = null!;
        public string SrcFullName { get; set; } = null!;
        public string DstTableCtgy { get; set; } = null!;
        public string DstTableName { get; set; } = null!;
        public string DstPrcname { get; set; } = null!;
        public string DstFullName { get; set; } = null!;
    }
}
