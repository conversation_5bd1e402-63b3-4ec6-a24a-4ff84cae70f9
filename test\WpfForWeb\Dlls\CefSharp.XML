<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CefSharp</name>
    </assembly>
    <members>
        <member name="T:CefSharp.AsyncExtensions">
            <summary>
            Async extensions for different interfaces
            </summary>
        </member>
        <member name="M:CefSharp.AsyncExtensions.DeleteCookiesAsync(CefSharp.ICookieManager,System.String,System.String)">
            <summary>
            Deletes all cookies that matches all the provided parameters asynchronously.
            If both <paramref name="url"/> and <paramref name="name"/> are empty, all cookies will be deleted.
            </summary>
            <param name="cookieManager">cookie manager</param>
            <param name="url">The cookie URL. If an empty string is provided, any URL will be matched.</param>
            <param name="name">The name of the cookie. If an empty string is provided, any URL will be matched.</param>
            <returns>Returns -1 if a non-empty invalid URL is specified, or if cookies cannot be accessed;
            otherwise, a task that represents the delete operation. The value of the TResult will be the number of cookies that were deleted or -1 if unknown.</returns>
        </member>
        <member name="M:CefSharp.AsyncExtensions.SetCookieAsync(CefSharp.ICookieManager,System.String,CefSharp.Cookie)">
            <summary>
            Sets a cookie given a valid URL and explicit user-provided cookie attributes.
            This function expects each attribute to be well-formed. It will check for disallowed
            characters (e.g. the ';' character is disallowed within the cookie value attribute) and will return false without setting
            </summary>
            <param name="cookieManager">cookie manager</param>
            <param name="url">The cookie URL. If an empty string is provided, any URL will be matched.</param>
            <param name="cookie">the cookie to be set</param>
            <returns>returns false if the cookie cannot be set (e.g. if illegal charecters such as ';' are used);
            otherwise task that represents the set operation. The value of the TResult parameter contains a bool to indicate success.</returns>
        </member>
        <member name="M:CefSharp.AsyncExtensions.VisitAllCookiesAsync(CefSharp.ICookieManager)">
            <summary>
            Visits all cookies. The returned cookies are sorted by longest path, then by earliest creation date.
            </summary>
            <param name="cookieManager">cookie manager</param>
            <returns>A task that represents the VisitAllCookies operation. The value of the TResult parameter contains a List of cookies
            or null if cookies cannot be accessed.</returns>
        </member>
        <member name="M:CefSharp.AsyncExtensions.VisitUrlCookiesAsync(CefSharp.ICookieManager,System.String,System.Boolean)">
            <summary>
            Visits a subset of the cookies. The results are filtered by the given url scheme, host, domain and path. 
            If <paramref name="includeHttpOnly"/> is true, HTTP-only cookies will also be included in the results. The returned cookies 
            are sorted by longest path, then by earliest creation date.
            </summary>
            <param name="cookieManager">cookie manager</param>
            <param name="url">The URL to use for filtering a subset of the cookies available.</param>
            <param name="includeHttpOnly">A flag that determines whether HTTP-only cookies will be shown in results.</param>
            <returns>A task that represents the VisitUrlCookies operation. The value of the TResult parameter contains a List of cookies.
            or null if cookies cannot be accessed.</returns>
        </member>
        <member name="M:CefSharp.AsyncExtensions.FlushStoreAsync(CefSharp.ICookieManager)">
            <summary>
            Flush the backing store (if any) to disk.
            </summary>
            <param name="cookieManager">cookieManager instance</param>
            <returns>A task that represents the FlushStore operation. Result indicates if the flush completed successfully.
            Will return false if the cookikes cannot be accessed.</returns>
        </member>
        <member name="T:CefSharp.BindingOptions">
            <summary>
            Javascript binding options
            </summary>
        </member>
        <member name="P:CefSharp.BindingOptions.DefaultBinder">
            <summary>
            Set of options with the default binding
            </summary>
        </member>
        <member name="P:CefSharp.BindingOptions.CamelCaseJavascriptNames">
            <summary>
            Camel case the javascript names of properties/methods, defaults to true
            </summary>
        </member>
        <member name="P:CefSharp.BindingOptions.Binder">
            <summary>
            Model binder used for passing complex classes as params to methods
            </summary>
        </member>
        <member name="P:CefSharp.BindingOptions.MethodInterceptor">
            <summary>
            Interceptor used for intercepting calls to the target object methods. For instance, can be used 
            for logging calls (from js) to .net methods.
            </summary>
        </member>
        <member name="T:CefSharp.IAuthCallback">
            <summary>
            Callback interface used for asynchronous continuation of authentication requests.
            </summary>
        </member>
        <member name="M:CefSharp.IAuthCallback.Continue(System.String,System.String)">
            <summary>
            Continue the authentication request.
            </summary>
            <param name="username">requested username</param>
            <param name="password">requested password</param>
        </member>
        <member name="M:CefSharp.IAuthCallback.Cancel">
            <summary>
            Cancel the authentication request.
            </summary>
        </member>
        <member name="P:CefSharp.IAuthCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IBeforeDownloadCallback">
            <summary>
            Callback interface used to asynchronously continue a download.
            </summary>
        </member>
        <member name="M:CefSharp.IBeforeDownloadCallback.Continue(System.String,System.Boolean)">
            <summary>
            Call to continue the download.
            </summary>
            <param name="downloadPath">full file path for the download including the file name
            or leave blank to use the suggested name and the default temp directory</param>
            <param name="showDialog">Set to true if you do wish to show the default "Save As" dialog</param>
        </member>
        <member name="P:CefSharp.IBeforeDownloadCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.ICallback">
            <summary>
            Generic callback interface used for asynchronous continuation.
            </summary>
        </member>
        <member name="M:CefSharp.ICallback.Continue">
            <summary>
            Continue processing.
            </summary>
        </member>
        <member name="M:CefSharp.ICallback.Cancel">
            <summary>
            Cancel processing.
            </summary>
        </member>
        <member name="P:CefSharp.ICallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.ICompletionCallback">
            <summary>
            Generic callback interface used for asynchronous completion. 
            </summary>
        </member>
        <member name="M:CefSharp.ICompletionCallback.OnComplete">
            <summary>
            Method that will be called once the task is complete. 
            </summary>
        </member>
        <member name="T:CefSharp.IDeleteCookiesCallback">
            <summary>
            Interface to implement to be notified of asynchronous completion via ICookieManager.DeleteCookies().
            It will be executed asnychronously on the CEF IO thread after the cookie has been deleted
            </summary>
        </member>
        <member name="M:CefSharp.IDeleteCookiesCallback.OnComplete(System.Int32)">
            <summary>
            Method that will be called upon completion. 
            </summary>
            <param name="numDeleted">will be the number of cookies that were deleted or -1 if unknown.</param>
        </member>
        <member name="P:CefSharp.IDeleteCookiesCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IDownloadItemCallback">
            <summary>
            Callback interface used to asynchronously cancel a download.
            </summary>
        </member>
        <member name="M:CefSharp.IDownloadItemCallback.Cancel">
            <summary>
            Call to cancel the download.
            </summary>
        </member>
        <member name="M:CefSharp.IDownloadItemCallback.Pause">
            <summary>
            Call to pause the download.
            </summary>
        </member>
        <member name="M:CefSharp.IDownloadItemCallback.Resume">
            <summary>
            Call to resume the download.
            </summary>
        </member>
        <member name="P:CefSharp.IDownloadItemCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IFileDialogCallback">
            <summary>
            Callback interface for asynchronous continuation of file dialog requests.
            </summary>
        </member>
        <member name="M:CefSharp.IFileDialogCallback.Continue(System.Int32,System.Collections.Generic.List{System.String})">
            <summary>
            Continue the file selection.
            </summary>
            <param name="selectedAcceptFilter">should be the 0-based index of the value selected from the accept filters
            array passed to <see cref="M:CefSharp.IDialogHandler.OnFileDialog(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefFileDialogMode,System.String,System.String,System.Collections.Generic.List{System.String},System.Int32,CefSharp.IFileDialogCallback)"/></param>
            <param name="filePaths">should be a single value or a list of values depending on the dialog mode.
            An empty value is treated the same as calling Cancel().</param>
        </member>
        <member name="M:CefSharp.IFileDialogCallback.Cancel">
            <summary>
            Cancel the file selection.
            </summary>
        </member>
        <member name="P:CefSharp.IFileDialogCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IGeolocationCallback">
            <summary>
            Callback interface used for asynchronous continuation of geolocation permission requests.
            </summary>
        </member>
        <member name="M:CefSharp.IGeolocationCallback.Continue(System.Boolean)">
            <summary>
            Call to allow or deny geolocation access.
            </summary>
            <param name="allow">true to allow</param>
        </member>
        <member name="P:CefSharp.IGeolocationCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IGetGeolocationCallback">
            <summary>
            Implement this interface to receive geolocation updates. The methods of this
            class will be called on the CEF UI thread.
            </summary>
        </member>
        <member name="M:CefSharp.IGetGeolocationCallback.OnLocationUpdate(CefSharp.Geoposition)">
            <summary>
            Called with the 'best available' location information or,
            if the location update failed, with error information.
            </summary>
            <param name="position">geo position</param>
        </member>
        <member name="P:CefSharp.IGetGeolocationCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="P:CefSharp.IJavascriptCallback.Id">
            <summary>
            Callback Id
            </summary>
        </member>
        <member name="P:CefSharp.IJavascriptCallback.CanExecute">
            <summary>
            Check to see if the underlying resource are still available to execute the callback
            </summary>
        </member>
        <member name="P:CefSharp.IJavascriptCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="M:CefSharp.IJsDialogCallback.Continue(System.Boolean,System.String)">
            <summary>
            Continue the Javascript dialog request.
            </summary>
            <param name="success">Set to true if the OK button was pressed.</param>
            <param name="userInput">value should be specified for prompt dialogs.</param>
        </member>
        <member name="M:CefSharp.IJsDialogCallback.Continue(System.Boolean)">
            <summary>
            Continue the Javascript dialog request.
            </summary>
            <param name="success">Set to true if the OK button was pressed.</param>
        </member>
        <member name="P:CefSharp.IJsDialogCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IPrintToPdfCallback">
            <summary>
            Callback interface for <see cref="M:CefSharp.IBrowserHost.PrintToPdf(System.String,CefSharp.PdfPrintSettings,CefSharp.IPrintToPdfCallback)"/>. The methods of this interface
            will be called on the CEF UI thread.
            </summary>
        </member>
        <member name="M:CefSharp.IPrintToPdfCallback.OnPdfPrintFinished(System.String,System.Boolean)">
            <summary>
            Method that will be executed when the PDF printing has completed.
            </summary>
            <param name="path">The output path.</param>
            <param name="ok">Will be true if the printing completed
            successfully or false otherwise.</param>
        </member>
        <member name="T:CefSharp.IRegisterCdmCallback">
            <summary>
            Content Decryption Module (CDM) registration callback used for asynchronous completion.
            </summary>
        </member>
        <member name="M:CefSharp.IRegisterCdmCallback.OnRegistrationComplete(CefSharp.CdmRegistration)">
            <summary>
            Method that will be called once CDM registration is complete
            </summary>
            <param name="registration">The result of the CDM registration process</param>
        </member>
        <member name="M:CefSharp.IRequestCallback.Continue(System.Boolean)">
            <summary>
            Continue the url request. 
            </summary>
            <param name="allow">If is true the request will be continued, otherwise, the request will be canceled.</param>
        </member>
        <member name="M:CefSharp.IRequestCallback.Cancel">
            <summary>
            Cancel the url request.
            </summary>
        </member>
        <member name="P:CefSharp.IRequestCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="M:CefSharp.IResolveCallback.OnResolveCompleted(CefSharp.CefErrorCode,System.Collections.Generic.IList{System.String})">
            <summary>
            Called after the ResolveHost request has completed.
            </summary>
            <param name="result">The result code</param>
            <param name="resolvedIpAddresses">will be the list of resolved IP addresses or
            empty if the resolution failed.</param>
        </member>
        <member name="M:CefSharp.IRunContextMenuCallback.Continue(CefSharp.CefMenuCommand,CefSharp.CefEventFlags)">
            <summary>
            Complete context menu display by selecting the specified commandId and eventFlags;
            </summary>
            <param name="commandId">the command Id</param>
            <param name="eventFlags">the event flags</param>
        </member>
        <member name="M:CefSharp.IRunContextMenuCallback.Cancel">
            <summary>
            Cancel context menu display.
            </summary>
        </member>
        <member name="P:CefSharp.IRunContextMenuCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.ISelectClientCertificateCallback">
            <summary>
            Callback interface used to select a client certificate for authentication.
            </summary>
        </member>
        <member name="M:CefSharp.ISelectClientCertificateCallback.Select(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Callback interface used to select a client certificate for authentication.
            </summary>
            <param name="selectedCert">selected certificate</param>
        </member>
        <member name="P:CefSharp.ISelectClientCertificateCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.ISetCookieCallback">
            <summary>
            Interface to implement to be notified of asynchronous completion via ICookieManager.SetCookie().
            It will be executed asnychronously on the CEF IO thread after the cookie has been set
            </summary>
        </member>
        <member name="M:CefSharp.ISetCookieCallback.OnComplete(System.Boolean)">
            <summary>
            Method that will be called upon completion. 
            </summary>
            <param name="success">success will be true if the cookie was set successfully.</param>
        </member>
        <member name="P:CefSharp.ISetCookieCallback.IsDisposed">
            <summary>
            Gets a value indicating whether the callback has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.TaskGetGeolocationCallback">
            <summary>
            Provides a callback implementation of <see cref="T:CefSharp.IGetGeolocationCallback"/> for use with geo positioning
            </summary>
        </member>
        <member name="T:CefSharp.TaskDeleteCookiesCallback">
            <summary>
            Provides a callback implementation of <see cref="T:CefSharp.IDeleteCookiesCallback"/>.
            </summary>
        </member>
        <member name="T:CefSharp.TaskSetCookieCallback">
            <summary>
            Provides a callback implementation of <see cref="T:CefSharp.ISetCookieCallback"/>.
            </summary>
        </member>
        <member name="T:CefSharp.TaskRegisterCdmCallback">
            <summary>
            Provides a callback implementation of <see cref="T:CefSharp.IRegisterCdmCallback"/> for use with asynchronous Widevine CDM registration.
            </summary>
        </member>
        <member name="T:CefSharp.CdmRegistration">
            <summary>
            Represents the response to an attempt to register the Widevine Content Decryption Module (CDM)
            </summary>
        </member>
        <member name="P:CefSharp.CdmRegistration.ErrorCode">
            <summary>
            If CDM registration succeeded then value will be <see cref="F:CefSharp.CdmRegistrationErrorCode.None"/>, for other values see the enumeration <see cref="T:CefSharp.CdmRegistrationErrorCode"/>.
            </summary>
        </member>
        <member name="P:CefSharp.CdmRegistration.ErrorMessage">
            <summary>
            Contains an error message containing additional information if <see cref="P:CefSharp.CdmRegistration.ErrorCode"/> is not <see cref="F:CefSharp.CdmRegistrationErrorCode.None"/>.
            </summary>
        </member>
        <member name="T:CefSharp.Event.JavascriptBindingEventArgs">
            <summary>
            Event arguments for the <see cref="E:CefSharp.IJavascriptObjectRepository.ResolveObject"/> and
            <see cref="E:CefSharp.IJavascriptObjectRepository.ObjectBoundInJavascript"/> events
            </summary>
        </member>
        <member name="P:CefSharp.Event.JavascriptBindingEventArgs.ObjectRepository">
            <summary>
            The javascript object repository, used to register objects
            </summary>
        </member>
        <member name="P:CefSharp.Event.JavascriptBindingEventArgs.ObjectName">
            <summary>
            Name of the object
            </summary>
        </member>
        <member name="T:CefSharp.Handler.DefaultRequestHandler">
            <summary>
            Default implementation of <see cref="T:CefSharp.IRequestHandler"/>.
            This class provides default implementations of the methods from <see cref="T:CefSharp.IRequestHandler"/>,
            therefore providing a convenience base class for any custom request handler.
            </summary>
        </member>
        <member name="T:CefSharp.IRequestHandler">
            <summary>
            Implement this interface to handle events related to browser requests.
            The methods of this class will be called on the thread indicated. 
            </summary>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnBeforeBrowse(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,System.Boolean)">
            <summary>
            Called before browser navigation.
            If the navigation is allowed <see cref="E:CefSharp.IWebBrowser.FrameLoadStart"/> and <see cref="E:CefSharp.IWebBrowser.FrameLoadEnd"/>
            will be called. If the navigation is canceled <see cref="E:CefSharp.IWebBrowser.LoadError"/> will be called with an ErrorCode
            value of <see cref="F:CefSharp.CefErrorCode.Aborted"/>. 
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame the request is coming from</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="isRedirect">has the request been redirected</param>
            <returns>Return true to cancel the navigation or false to allow the navigation to proceed.</returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnOpenUrlFromTab(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,System.String,CefSharp.WindowOpenDisposition,System.Boolean)">
            <summary>
            Called on the UI thread before OnBeforeBrowse in certain limited cases
            where navigating a new or different browser might be desirable. This
            includes user-initiated navigation that might open in a special way (e.g.
            links clicked via middle-click or ctrl + left-click) and certain types of
            cross-origin navigation initiated from the renderer process (e.g.
            navigating the top-level frame to/from a file URL).
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object</param>
            <param name="targetUrl">target url</param>
            <param name="targetDisposition">The value indicates where the user intended to navigate the browser based
            on standard Chromium behaviors (e.g. current tab, new tab, etc). </param>
            <param name="userGesture">The value will be true if the browser navigated via explicit user gesture
            (e.g. clicking a link) or false if it navigated automatically (e.g. via the DomContentLoaded event).</param>
            <returns>Return true to cancel the navigation or false to allow the navigation
            to proceed in the source browser's top-level frame.</returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnCertificateError(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefErrorCode,System.String,CefSharp.ISslInfo,CefSharp.IRequestCallback)">
            <summary>
            Called to handle requests for URLs with an invalid SSL certificate.
            Return true and call <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> either
            in this method or at a later time to continue or cancel the request.  
            If <see cref="!:CefSettings.IgnoreCertificateErrors"/> is set all invalid certificates
            will be accepted without calling this method.
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="errorCode">the error code for this invalid certificate</param>
            <param name="requestUrl">the url of the request for the invalid certificate</param>
            <param name="sslInfo">ssl certificate information</param>
            <param name="callback">Callback interface used for asynchronous continuation of url requests.
            If empty the error cannot be recovered from and the request will be canceled automatically.</param>
            <returns>Return false to cancel the request immediately. Return true and use <see cref="T:CefSharp.IRequestCallback"/> to
            execute in an async fashion.</returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnPluginCrashed(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String)">
            <summary>
            Called when a plugin has crashed
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="pluginPath">path of the plugin that crashed</param>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnBeforeResourceLoad(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IRequestCallback)">
            <summary>
            Called before a resource request is loaded. For async processing return <see cref="F:CefSharp.CefReturnValue.ContinueAsync"/> 
            and execute <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> or <see cref="M:CefSharp.IRequestCallback.Cancel"/>
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object</param>
            <param name="request">the request object - can be modified in this callback.</param>
            <param name="callback">Callback interface used for asynchronous continuation of url requests.</param>
            <returns>To cancel loading of the resource return <see cref="F:CefSharp.CefReturnValue.Cancel"/>
            or <see cref="F:CefSharp.CefReturnValue.Continue"/> to allow the resource to load normally. For async
            return <see cref="F:CefSharp.CefReturnValue.ContinueAsync"/></returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.GetAuthCredentials(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,System.Boolean,System.String,System.Int32,System.String,System.String,CefSharp.IAuthCallback)">
            <summary>
            Called when the browser needs credentials from the user.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object that needs credentials (This will contain the URL that is being requested.)</param>
            <param name="isProxy">indicates whether the host is a proxy server</param>
            <param name="host">hostname</param>
            <param name="port">port number</param>
            <param name="realm">realm</param>
            <param name="scheme">scheme</param>
            <param name="callback">Callback interface used for asynchronous continuation of authentication requests.</param>
            <returns>Return true to continue the request and call CefAuthCallback::Continue() when the authentication information is available. Return false to cancel the request. </returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnSelectClientCertificate(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Boolean,System.String,System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2Collection,CefSharp.ISelectClientCertificateCallback)">
            <summary>
            Called when the browser needs user to select Client Certificate for authentication requests (eg. PKI authentication).
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="isProxy">indicates whether the host is a proxy server</param>
            <param name="host">hostname</param>
            <param name="port">port number</param>
            <param name="certificates">List of Client certificates for selection</param>
            <param name="callback">Callback interface used for asynchronous continuation of client certificate selection for authentication requests.</param>
            <returns>Return true to continue the request and call ISelectClientCertificateCallback.Select() with the selected certificate for authentication. 
            Return false to use the default behavior where the browser selects the first certificate from the list. </returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnRenderProcessTerminated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefTerminationStatus)">
            <summary>
            Called when the render process terminates unexpectedly.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="status">indicates how the process terminated.</param>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnQuotaRequest(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,System.Int64,CefSharp.IRequestCallback)">
            <summary>
            Called when JavaScript requests a specific storage quota size via the webkitStorageInfo.requestQuota function.
            For async processing return true and execute <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> at a later time to 
            grant or deny the request or <see cref="M:CefSharp.IRequestCallback.Cancel"/> to cancel.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="originUrl">the origin of the page making the request</param>
            <param name="newSize">is the requested quota size in bytes</param>
            <param name="callback">Callback interface used for asynchronous continuation of url requests.</param>
            <returns>Return false to cancel the request immediately. Return true to continue the request
            and call <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> either in this method or at a later time to
            grant or deny the request.</returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnResourceRedirect(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse,System.String@)">
            <summary>
            Called on the IO thread when a resource load is redirected. The <see cref="P:CefSharp.IRequest.Url"/>
            parameter will contain the old URL and other request-related information.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="response">the response object</param>
            <param name="newUrl">the new URL and can be changed if desired</param>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnProtocolExecution(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String)">
            <summary>
            Called on the UI thread to handle requests for URLs with an unknown protocol component. 
            SECURITY WARNING: YOU SHOULD USE THIS METHOD TO ENFORCE RESTRICTIONS BASED ON SCHEME, HOST OR OTHER URL ANALYSIS BEFORE ALLOWING OS EXECUTION.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="url">the request url</param>
            <returns>return to true to attempt execution via the registered OS protocol handler, if any. Otherwise return false.</returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnRenderViewReady(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called on the CEF UI thread when the render view associated
            with browser is ready to receive/handle IPC messages in the render
            process.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnResourceResponse(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse)">
            <summary>
            Called on the CEF IO thread when a resource response is received.
            To allow the resource to load normally return false.
            To redirect or retry the resource modify request (url, headers or post body) and return true.
            The response object cannot be modified in this callback. 
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object</param>
            <param name="response">the response object - cannot be modified in this callback</param>
            <returns>
            To allow the resource to load normally return false.
            To redirect or retry the resource modify request (url, headers or post body) and return true.
            </returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.GetResourceResponseFilter(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse)">
            <summary>
            Called on the CEF IO thread to optionally filter resource response content.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="response">the response object - cannot be modified in this callback</param>
            <returns>Return an IResponseFilter to intercept this response, otherwise return null</returns>
        </member>
        <member name="M:CefSharp.IRequestHandler.OnResourceLoadComplete(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse,CefSharp.UrlRequestStatus,System.Int64)">
            <summary>
            Called on the CEF IO thread when a resource load has completed.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="response">the response object - cannot be modified in this callback</param>
            <param name="status">indicates the load completion status</param>
            <param name="receivedContentLength">is the number of response bytes actually read.</param>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.GetAuthCredentials(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,System.Boolean,System.String,System.Int32,System.String,System.String,CefSharp.IAuthCallback)">
            <summary>
            Called when the browser needs credentials from the user.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object that needs credentials (This will contain the URL that is being requested.)</param>
            <param name="isProxy">indicates whether the host is a proxy server</param>
            <param name="host">hostname</param>
            <param name="port">port number</param>
            <param name="realm">realm</param>
            <param name="scheme">scheme</param>
            <param name="callback">Callback interface used for asynchronous continuation of authentication requests.</param>
            <returns>Return true to continue the request and call CefAuthCallback::Continue() when the authentication information is available. Return false to cancel the request. </returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.GetResourceResponseFilter(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse)">
            <summary>
            Called on the CEF IO thread to optionally filter resource response content.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="response">the response object - cannot be modified in this callback</param>
            <returns>Return an IResponseFilter to intercept this response, otherwise return null</returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnBeforeBrowse(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,System.Boolean)">
            <summary>
            Called before browser navigation.
            If the navigation is allowed <see cref="E:CefSharp.IWebBrowser.FrameLoadStart"/> and <see cref="E:CefSharp.IWebBrowser.FrameLoadEnd"/>
            will be called. If the navigation is canceled <see cref="E:CefSharp.IWebBrowser.LoadError"/> will be called with an ErrorCode
            value of <see cref="F:CefSharp.CefErrorCode.Aborted"/>. 
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame the request is coming from</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="isRedirect">has the request been redirected</param>
            <returns>Return true to cancel the navigation or false to allow the navigation to proceed.</returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnBeforeResourceLoad(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IRequestCallback)">
            <summary>
            Called before a resource request is loaded. For async processing return <see cref="F:CefSharp.CefReturnValue.ContinueAsync"/> 
            and execute <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> or <see cref="M:CefSharp.IRequestCallback.Cancel"/>
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object</param>
            <param name="request">the request object - can be modified in this callback.</param>
            <param name="callback">Callback interface used for asynchronous continuation of url requests.</param>
            <returns>To cancel loading of the resource return <see cref="F:CefSharp.CefReturnValue.Cancel"/>
            or <see cref="F:CefSharp.CefReturnValue.Continue"/> to allow the resource to load normally. For async
            return <see cref="F:CefSharp.CefReturnValue.ContinueAsync"/></returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnCertificateError(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefErrorCode,System.String,CefSharp.ISslInfo,CefSharp.IRequestCallback)">
            <summary>
            Called to handle requests for URLs with an invalid SSL certificate.
            Return true and call <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> either
            in this method or at a later time to continue or cancel the request.  
            If <see cref="!:CefSettings.IgnoreCertificateErrors"/> is set all invalid certificates
            will be accepted without calling this method.
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="errorCode">the error code for this invalid certificate</param>
            <param name="requestUrl">the url of the request for the invalid certificate</param>
            <param name="sslInfo">ssl certificate information</param>
            <param name="callback">Callback interface used for asynchronous continuation of url requests.
            If empty the error cannot be recovered from and the request will be canceled automatically.</param>
            <returns>Return false to cancel the request immediately. Return true and use <see cref="T:CefSharp.IRequestCallback"/> to
            execute in an async fashion.</returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnOpenUrlFromTab(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,System.String,CefSharp.WindowOpenDisposition,System.Boolean)">
            <summary>
            Called on the UI thread before OnBeforeBrowse in certain limited cases
            where navigating a new or different browser might be desirable. This
            includes user-initiated navigation that might open in a special way (e.g.
            links clicked via middle-click or ctrl + left-click) and certain types of
            cross-origin navigation initiated from the renderer process (e.g.
            navigating the top-level frame to/from a file URL).
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object</param>
            <param name="targetUrl">target url</param>
            <param name="targetDisposition">The value indicates where the user intended to navigate the browser based
            on standard Chromium behaviors (e.g. current tab, new tab, etc). </param>
            <param name="userGesture">The value will be true if the browser navigated via explicit user gesture
            (e.g. clicking a link) or false if it navigated automatically (e.g. via the DomContentLoaded event).</param>
            <returns>Return true to cancel the navigation or false to allow the navigation
            to proceed in the source browser's top-level frame.</returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnPluginCrashed(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String)">
            <summary>
            Called when a plugin has crashed
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="pluginPath">path of the plugin that crashed</param>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnProtocolExecution(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String)">
            <summary>
            Called on the UI thread to handle requests for URLs with an unknown protocol component. 
            SECURITY WARNING: YOU SHOULD USE THIS METHOD TO ENFORCE RESTRICTIONS BASED ON SCHEME, HOST OR OTHER URL ANALYSIS BEFORE ALLOWING OS EXECUTION.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="url">the request url</param>
            <returns>return to true to attempt execution via the registered OS protocol handler, if any. Otherwise return false.</returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnQuotaRequest(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,System.Int64,CefSharp.IRequestCallback)">
            <summary>
            Called when JavaScript requests a specific storage quota size via the webkitStorageInfo.requestQuota function.
            For async processing return true and execute <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> at a later time to 
            grant or deny the request or <see cref="M:CefSharp.IRequestCallback.Cancel"/> to cancel.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="originUrl">the origin of the page making the request</param>
            <param name="newSize">is the requested quota size in bytes</param>
            <param name="callback">Callback interface used for asynchronous continuation of url requests.</param>
            <returns>Return false to cancel the request immediately. Return true to continue the request
            and call <see cref="M:CefSharp.IRequestCallback.Continue(System.Boolean)"/> either in this method or at a later time to
            grant or deny the request.</returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnRenderProcessTerminated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefTerminationStatus)">
            <summary>
            Called when the render process terminates unexpectedly.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="status">indicates how the process terminated.</param>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnRenderViewReady(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called on the CEF UI thread when the render view associated
            with browser is ready to receive/handle IPC messages in the render
            process.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnResourceLoadComplete(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse,CefSharp.UrlRequestStatus,System.Int64)">
            <summary>
            Called on the CEF IO thread when a resource load has completed.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="response">the response object - cannot be modified in this callback</param>
            <param name="status">indicates the load completion status</param>
            <param name="receivedContentLength">is the number of response bytes actually read.</param>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnResourceRedirect(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse,System.String@)">
            <summary>
            Called on the IO thread when a resource load is redirected. The <see cref="P:CefSharp.IRequest.Url"/>
            parameter will contain the old URL and other request-related information.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <param name="response">the response object</param>
            <param name="newUrl">the new URL and can be changed if desired</param>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnResourceResponse(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest,CefSharp.IResponse)">
            <summary>
            Called on the CEF IO thread when a resource response is received.
            To allow the resource to load normally return false.
            To redirect or retry the resource modify request (url, headers or post body) and return true.
            The response object cannot be modified in this callback. 
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame that is being redirected.</param>
            <param name="request">the request object</param>
            <param name="response">the response object - cannot be modified in this callback</param>
            <returns>
            To allow the resource to load normally return false.
            To redirect or retry the resource modify request (url, headers or post body) and return true.
            </returns>
        </member>
        <member name="M:CefSharp.Handler.DefaultRequestHandler.OnSelectClientCertificate(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Boolean,System.String,System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2Collection,CefSharp.ISelectClientCertificateCallback)">
            <summary>
            Called when the browser needs user to select Client Certificate for authentication requests (eg. PKI authentication).
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="isProxy">indicates whether the host is a proxy server</param>
            <param name="host">hostname</param>
            <param name="port">port number</param>
            <param name="certificates">List of Client certificates for selection</param>
            <param name="callback">Callback interface used for asynchronous continuation of client certificate selection for authentication requests.</param>
            <returns>Return true to continue the request and call ISelectClientCertificateCallback.Select() with the selected certificate for authentication. 
            Return false to use the default behavior where the browser selects the first certificate from the list. </returns>
        </member>
        <member name="T:CefSharp.UrlRequestFlags">
            <summary>
            Flags used to customize the behavior of CefURLRequest.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestFlags.None">
            <summary>
            Default behavior.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestFlags.SkipCache">
            <summary>
            If set the cache will be skipped when handling the request.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestFlags.AllowCachedCredentials">
            <summary>
            If set user name, password, and cookies may be sent with the request, and
            cookies may be saved from the response.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestFlags.ReportUploadProgress">
            <summary>
             If set upload progress events will be generated when a request has a body.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestFlags.NoDownloadData">
            <summary>
            If set the CefURLRequestClient::OnDownloadData method will not be called.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestFlags.NoRetryOn5XX">
            <summary>
            If set 5XX redirect errors will be propagated to the observer instead of
            automatically re-tried. This currently only applies for requests
            originated in the browser process.
            </summary>
        </member>
        <member name="T:CefSharp.IJavascriptObjectRepository">
            <summary>
            Javascript object repository, object are registered for binding
            One repository per ChromiumWebBrowser instance
            </summary>
        </member>
        <member name="M:CefSharp.IJavascriptObjectRepository.Register(System.String,System.Object,System.Boolean,CefSharp.BindingOptions)">
            <summary>
            Register an object for binding in Javascript. You can either
            register an object in advance or as part of the <see cref="E:CefSharp.IJavascriptObjectRepository.ResolveObject"/>
            event that will be called if no object matching object is found in the registry.
            Objects binding is now initiated in Javascript through the CefSharp.BindObjectAsync
            function (returns a Promose).
            For more detailed examples see https://github.com/cefsharp/CefSharp/issues/2246
            The equivilient to RegisterJsObject is isAsync = false
            The equivilient RegisterAsyncJsObject is isAsync = true
            </summary>
            <param name="name">object name</param>
            <param name="objectToBind">the object that will be bound in javascript</param>
            <param name="isAsync">
            if true the object will be registered for async communication,
            only methods will be exposed and when called from javascript will return a Promise to be awaited. 
            This method is newer and recommended for everyone starting out as it is faster and more reliable.
            If false then methods and properties will be registered, this method relies on a WCF service to communicate.
            </param>
            <param name="options">binding options, by default method/property names are camelCased, you can control this
            and other advanced options though this class.</param>
        </member>
        <member name="M:CefSharp.IJavascriptObjectRepository.IsBound(System.String)">
            <summary>
            Is object bound
            </summary>
            <param name="name">name</param>
            <returns>true if object with matching name bound</returns>
        </member>
        <member name="P:CefSharp.IJavascriptObjectRepository.HasBoundObjects">
            <summary>
            Has bound objects
            </summary>
        </member>
        <member name="E:CefSharp.IJavascriptObjectRepository.ResolveObject">
            <summary>
            Event handler is called when an object with a given name is requested for binding and is not yet
            registered with the repository. Use <see cref="P:CefSharp.Event.JavascriptBindingEventArgs.ObjectRepository"/>
            to register objects (using 
            </summary>
        </member>
        <member name="E:CefSharp.IJavascriptObjectRepository.ObjectBoundInJavascript">
            <summary>
            Event handler is triggered when a object has been successfully bound on javascript
            </summary>
        </member>
        <member name="T:CefSharp.ByteArrayResourceHandler">
            <summary>
            ByteArrayResourceHandler is used as a placeholder class which uses native CEF implementations.
            CefStreamReader::CreateForData(); reads the byte array that is passed to a new instance
            of CefStreamResourceHandler 
            TODO: Move this class into Handler namespace 
            </summary>
        </member>
        <member name="T:CefSharp.IResourceHandler">
            <summary>
            Class used to implement a custom resource handler. The methods of this class will always be called on the CEF IO thread.
            Blocking the CEF IO thread will adversely affect browser performance. We suggest you execute your code in a Task (or similar).
            To implement async handling, spawn a new Task (or similar), keep a reference to the callback. When you have a 
            fully populated stream, execute the callback. Once the callback Executes, GetResponseHeaders will be called where you
            can modify the response including headers, or even redirect to a new Url. Set your responseLength and headers
            Populate the dataOut stream in ReadResponse. For those looking for a sample implementation or upgrading from 
            a previous version <see cref="T:CefSharp.ResourceHandler"/>. For those upgrading, inherit from ResourceHandler instead of IResourceHandler
            add the override keywoard to existing methods e.g. ProcessRequestAsync.
            </summary>
        </member>
        <member name="M:CefSharp.IResourceHandler.ProcessRequest(CefSharp.IRequest,CefSharp.ICallback)">
            <summary>
            Begin processing the request.  
            </summary>
            <param name="request">The request object.</param>
            <param name="callback">The callback used to Continue or Cancel the request (async).</param>
            <returns>To handle the request return true and call
            <see cref="M:CefSharp.ICallback.Continue"/> once the response header information is available
            <see cref="M:CefSharp.ICallback.Continue"/> can also be called from inside this method if
            header information is available immediately).
            To cancel the request return false.</returns>
        </member>
        <member name="M:CefSharp.IResourceHandler.GetResponseHeaders(CefSharp.IResponse,System.Int64@,System.String@)">
            <summary>
            Retrieve response header information. If the response length is not known
            set responseLength to -1 and ReadResponse() will be called until it
            returns false. If the response length is known set responseLength
            to a positive value and ReadResponse() will be called until it returns
            false or the specified number of bytes have been read. 
            If an error occured while setting up the request you can set <see cref="P:CefSharp.IResponse.ErrorCode"/>
            to indicate the error condition.
            </summary>
            <param name="response">Use the response object to set the mime type, http status code and other optional header values.</param>
            <param name="responseLength">If the response length is not known set responseLength to -1</param>
            <param name="redirectUrl">To redirect the request to a new URL set redirectUrl to the new Url.</param>
        </member>
        <member name="M:CefSharp.IResourceHandler.ReadResponse(System.IO.Stream,System.Int32@,CefSharp.ICallback)">
            <summary>
            Read response data. If data is available immediately copy to
            dataOut, set bytesRead to the number of bytes copied, and return true.
            To read the data at a later time set bytesRead to 0, return true and call ICallback.Continue() when the
            data is available. To indicate response completion return false.
            </summary>
            <param name="dataOut">Stream to write to</param>
            <param name="bytesRead">Number of bytes copied to the stream</param>
            <param name="callback">The callback used to Continue or Cancel the request (async).</param>
            <returns>If data is available immediately copy to dataOut, set bytesRead to the number of bytes copied,
            and return true.To indicate response completion return false.</returns>
            <remarks>Depending on this size of your response this method may be called multiple times</remarks>
        </member>
        <member name="M:CefSharp.IResourceHandler.CanGetCookie(CefSharp.Cookie)">
            <summary>
            Return true if the specified cookie can be sent with the request or false
            otherwise. If false is returned for any cookie then no cookies will be sent
            with the request.
            </summary>
            <param name="cookie">cookie</param>
            <returns>Return true if the specified cookie can be sent with the request or false
            otherwise. If false is returned for any cookie then no cookies will be sent
            with the request.</returns>
        </member>
        <member name="M:CefSharp.IResourceHandler.CanSetCookie(CefSharp.Cookie)">
            <summary>
            Return true if the specified cookie returned with the response can be set or false otherwise.
            </summary>
            <param name="cookie">cookie</param>
            <returns>Return true if the specified cookie returned with the response can be set or false otherwise.</returns>
        </member>
        <member name="M:CefSharp.IResourceHandler.Cancel">
            <summary>
            Request processing has been canceled.
            </summary>
        </member>
        <member name="M:CefSharp.ByteArrayResourceHandler.#ctor(System.String,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.ByteArrayResourceHandler"/> class.
            </summary>
            <param name="mimeType">mimeType</param>
            <param name="filePath">filePath</param>
        </member>
        <member name="P:CefSharp.ByteArrayResourceHandler.Data">
            <summary>
            Underlying byte array that represents the data
            </summary>
        </member>
        <member name="P:CefSharp.ByteArrayResourceHandler.MimeType">
            <summary>
            Gets or sets the Mime Type.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.DateTimeUtils">
            <summary>
            Mapping to/from CefTime
            </summary>
        </member>
        <member name="M:CefSharp.Internals.DateTimeUtils.FromCefTime(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts a cef
            </summary>
            <param name="year">year</param>
            <param name="month">month</param>
            <param name="day">day</param>
            <param name="hour">hour</param>
            <param name="minute">minute</param>
            <param name="second">second</param>
            <param name="millisecond">millisecond</param>
            <returns>DateTime</returns>
        </member>
        <member name="M:CefSharp.Internals.DateTimeUtils.ToCefTime(System.DateTime)">
            <summary>
            Returns epoch (different from 01/01/1970)
            </summary>
            <param name="dateTime">datetime</param>
            <returns>epoch</returns>
        </member>
        <member name="T:CefSharp.FileResourceHandler">
            <summary>
            FileResourceHandler  is used as a placeholder class which uses native CEF implementations.
            CefStreamReader::CreateForFile is used to create a CefStreamReader instance which is passed to
            a new instance of CefStreamResourceHandler
            (Was previously ResourceHandlerType::File to differentiate, going for a more flexible approach now)
            TODO: Move this class into Handler namespace 
            </summary>
        </member>
        <member name="M:CefSharp.FileResourceHandler.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.FileResourceHandler"/> class.
            </summary>
            <param name="mimeType">mimeType</param>
            <param name="filePath">filePath</param>
        </member>
        <member name="P:CefSharp.FileResourceHandler.FilePath">
            <summary>
            Path of the underlying file
            </summary>
        </member>
        <member name="P:CefSharp.FileResourceHandler.MimeType">
            <summary>
            Gets or sets the Mime Type.
            </summary>
        </member>
        <member name="T:CefSharp.CefExtension">
            <summary>
            Represents a new V8 extension to be registered.
            </summary>
        </member>
        <member name="M:CefSharp.CefExtension.#ctor(System.String,System.String)">
            <summary>
            Creates a new CwefExtension instance with a given name.
            </summary>
            <param name="name">Name of the CefExtension</param>
            <param name="javascriptCode">The javascript extension code.</param>
        </member>
        <member name="P:CefSharp.CefExtension.Name">
            <summary>
            Gets the name of the extension.
            </summary>
        </member>
        <member name="P:CefSharp.CefExtension.JavascriptCode">
            <summary>
            Gets the javascript extension code
            </summary>
        </member>
        <member name="T:CefSharp.CefLibraryHandle">
            <summary>
            CefLibraryHandle is a SafeHandle that Loads libcef.dll and relesases it when disposed/finalized
            Calls LoadLibraryEx with LoadLibraryFlags.LOAD_WITH_ALTERED_SEARCH_PATH
            Make sure to set settings.BrowserSubprocessPath and settings.LocalesDirPath
            </summary>
            <remarks>Adapted from http://www.pinvoke.net/default.aspx/kernel32.loadlibraryex</remarks>
        </member>
        <member name="T:CefSharp.CefLibraryHandle.LoadLibraryFlags">
            <summary>
            In general not a fan of having inline classes/enums
            In this case it's not something that I'd like to see exposed
            as it's just a helper and outside the scope of the project
            </summary>
        </member>
        <member name="T:CefSharp.CompositionUnderline">
            <summary>
            Represents an IME composition underline.
            </summary>
        </member>
        <member name="P:CefSharp.CompositionUnderline.Range">
            <summary>
            Underline character range.
            </summary>
        </member>
        <member name="P:CefSharp.CompositionUnderline.Color">
            <summary>
            Text color. 32-bit ARGB color value, not premultiplied. The color components are always
            in a known order. Equivalent to the SkColor type.
            </summary>
        </member>
        <member name="P:CefSharp.CompositionUnderline.BackgroundColor">
            <summary>
            Background color. 32-bit ARGB color value, not premultiplied. The color components are always
            in a known order. Equivalent to the SkColor type.
            </summary>
        </member>
        <member name="P:CefSharp.CompositionUnderline.Thick">
            <summary>
            true for thickunderline
            </summary>
        </member>
        <member name="T:CefSharp.DomNode">
            <summary>
            Represents a node in the browser's DOM.
            </summary>
        </member>
        <member name="T:CefSharp.IDomNode">
            <summary>
            Represents the tag name and attribute data belonging to a node in the
            browser's DOM.
            </summary>
        </member>
        <member name="M:CefSharp.IDomNode.HasAttribute(System.String)">
            <summary>
            Determine if the DomNode has the requested attribute.
            </summary>
            <param name="attributeName">
            The name of the attribute value.
            </param>
            <returns>
            True if the attribute exists in the DomNode, false if it does not.
            </returns>
        </member>
        <member name="P:CefSharp.IDomNode.Item(System.String)">
            <summary>
            Get the value of an attribute.
            </summary>
            <param name="attributeName">
            The name of the attribute value to get.
            </param>
            <returns>
            The attribute value if the name exists in the DomNode's attributes.
            Null if the name does not exist.
            </returns>
        </member>
        <member name="P:CefSharp.IDomNode.TagName">
            <summary>
            The name of the HTML element.
            </summary>
        </member>
        <member name="P:CefSharp.IDomNode.AttributeNames">
            <summary>
            Get a read only list of the attribute names.
            </summary>
        </member>
        <member name="T:CefSharp.DraggableRegion">
            <summary>
            Representing a draggable region.
            </summary>
        </member>
        <member name="M:CefSharp.DraggableRegion.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Creates a new DraggableRegion
            </summary>
            <param name="width">width</param>
            <param name="height">height</param>
            <param name="x">x coordinate</param>
            <param name="y">y coordinate</param>
            <param name="draggable">is draggable?</param>
        </member>
        <member name="P:CefSharp.DraggableRegion.Width">
            <summary>
            Width
            </summary>
        </member>
        <member name="P:CefSharp.DraggableRegion.Height">
            <summary>
            Height
            </summary>
        </member>
        <member name="P:CefSharp.DraggableRegion.X">
            <summary>
            X coordinate
            </summary>
        </member>
        <member name="P:CefSharp.DraggableRegion.Y">
            <summary>
            Y coordinate
            </summary>
        </member>
        <member name="P:CefSharp.DraggableRegion.Draggable">
            <summary>
            Is this region draggable
            </summary>
        </member>
        <member name="T:CefSharp.CdmRegistrationErrorCode">
            <summary>
            Lists the errors that can be reported during Widevine Content Decryption Module (CDM) registration.
            </summary>
        </member>
        <member name="F:CefSharp.CdmRegistrationErrorCode.None">
            <summary>
            No error. Registration completed successfully.
            </summary>
        </member>
        <member name="F:CefSharp.CdmRegistrationErrorCode.IncorrectContents">
            <summary>
            Required files or manifest contents are missing.
            </summary>
        </member>
        <member name="F:CefSharp.CdmRegistrationErrorCode.Incompatible">
            <summary>
            The CDM is incompatible with the current Chromium version.
            </summary>
        </member>
        <member name="F:CefSharp.CdmRegistrationErrorCode.NotSupported">
            <summary>
            CDM registration is not supported at this time.
            </summary>
        </member>
        <member name="T:CefSharp.CursorType">
            <summary>
            Cursor type values.
            </summary>
        </member>
        <member name="T:CefSharp.CefErrorCode">
            <summary>
            Lists some of the error codes that can be reported by CEF.
            
            For a complete up-to-date list, see the CEF source code
            (cef_errorcode_t in include/internal/cef_types.h)
            and the Chromium source code (net/base/net_error_list.h).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.None">
            <summary>
            No error occurred.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.IoPending">
            <summary>
            An asynchronous IO operation is not yet complete.  This usually does not
            indicate a fatal error.  Typically this error will be generated as a
            notification to wait for some external notification that the IO operation
            finally completed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Failed">
            <summary>
            A generic failure occurred.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Aborted">
            <summary>
            A request was aborted, possibly by the user.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidArgument">
            <summary>
            An argument to the function is incorrect.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidHandle">
            <summary>
            The handle or file descriptor is invalid.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FileNotFound">
            <summary>
            The file or directory cannot be found.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.TimedOut">
            <summary>
            An operation timed out.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FileTooBig">
            <summary>
            The file is too large.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Unexpected">
            <summary>
            An unexpected error. This may be caused by a programming mistake or an invalid assumption
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.AccessDenied">
            <summary>
            Permission to access a resource, other than the network, was denied.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NotImplemented">
            <summary>
            The operation failed because of unimplemented functionality.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InsufficientResources">
            <summary>
            There were not enough resources to complete the operation.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.OutOfMemory">
            <summary>
            Memory allocation failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UploadFileChanged">
            <summary>
            The file upload failed because the file's modification time was different
            from the expectation.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocketNotConnected">
            <summary>
            The socket is not connected.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FileExists">
            <summary>
            The file already exists.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FilePathTooLong">
            <summary>
            The path or file name is too long.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FileNoSpace">
            <summary>
            Not enough room left on the disk.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FileVirusInfected">
            <summary>
            The file has a virus.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.BlockedByClient">
            <summary>
            The client chose to block the request.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NetworkChanged">
            <summary>
            The network changed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.BlockedByAdministrator">
            <summary>
            The request was blocked by the URL blacklist configured by the domain
            administrator.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocketIsConnected">
            <summary>
            The socket is already connected.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.BlockedEnrollmentCheckPending">
            <summary>
            The request was blocked because the forced reenrollment check is still
            pending. This error can only occur on ChromeOS.
            The error can be emitted by code in chrome/browser/policy/policy_helpers.cc.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ConnectionClosed">
            <summary>
            A connection was closed (corresponding to a TCP FIN).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ConnectionReset">
            <summary>
            A connection was reset (corresponding to a TCP RST).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ConnectionRefused">
            <summary>
            A connection attempt was refused.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ConnectionAborted">
            <summary>
            A connection timed out as a result of not receiving an ACK for data sent.
            This can include a FIN packet that did not get ACK'd.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ConnectionFailed">
            <summary>
            A connection attempt failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NameNotResolved">
            <summary>
            The host name could not be resolved.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InternetDisconnected">
            <summary>
            The Internet connection has been lost.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslProtocolError">
            <summary>
            An SSL protocol error occurred.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.AddressInvalid">
            <summary>
            The IP address or port number is invalid (e.g., cannot connect to the IP address 0 or the port 0).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.AddressUnreachable">
            <summary>
            The IP address is unreachable.  This usually means that there is no route to the specified host or network.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslClientAuthCertNeeded">
            <summary>
            The server requested a client certificate for SSL client authentication.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.TunnelConnectionFailed">
            <summary>
            A tunnel connection through the proxy could not be established.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NoSslVersionsEnabled">
            <summary>
            No SSL protocol versions are enabled.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslVersionOrCipherMismatch">
            <summary>
            The client and server don't support a common SSL protocol version or cipher suite.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslRenegotiationRequested">
            <summary>
            The server requested a renegotiation (rehandshake).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ProxyAuthUnsupported">
            <summary>
            The proxy requested authentication (for tunnel establishment) with an
            unsupported method.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertErrorInSslRenegotiation">
             <summary>
             During SSL renegotiation (rehandshake), the server sent a certificate with
             an error.
            
             Note: this error is not in the -2xx range so that it won't be handled as a
             certificate error.
             </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.BadSslClientAuthCert">
            <summary>
            The SSL handshake failed because of a bad or missing client certificate.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ConnectionTimedOut">
            <summary>
            A connection attempt timed out.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.HostResolverQueueTooLarge">
            <summary>
            There are too many pending DNS resolves, so a request in the queue was
            aborted.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocksConnectionFailed">
            <summary>
            Failed establishing a connection to the SOCKS proxy server for a target host.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocksConnectionHostUnreachable">
            <summary>
            The SOCKS proxy server failed establishing connection to the target host
            because that host is unreachable.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NpnNegotiationFailed">
            <summary>
            The request to negotiate an alternate protocol failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslNoRenegotiation">
            <summary>
            The peer sent an SSL no_renegotiation alert message.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.WinsockUnexpectedWrittenBytes">
            <summary>
            Winsock sometimes reports more data written than passed.  This is probably
            due to a broken LSP.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslDecompressionFailureAlert">
            <summary>
            An SSL peer sent us a fatal decompression_failure alert. This typically
            occurs when a peer selects DEFLATE compression in the mistaken belief that
            it supports it.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslBadRecordMacAlert">
            <summary>
            An SSL peer sent us a fatal bad_record_mac alert. This has been observed
            from servers with buggy DEFLATE support.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ProxyAuthRequested">
            <summary>
            The proxy requested authentication (for tunnel establishment).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslUnsafeNegotiation">
            <summary>
            A known TLS strict server didn't offer the renegotiation extension.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslWeakServerEphemeralDhKey">
            <summary>
            The SSL server attempted to use a weak ephemeral Diffie-Hellman key.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ProxyConnectionFailed">
            <summary>
            Could not create a connection to the proxy server. An error occurred
            either in resolving its name, or in connecting a socket to it.
            Note that this does NOT include failures during the actual "CONNECT" method
            of an HTTP proxy.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.MandatoryProxyConfigurationFailed">
            <summary>
            A mandatory proxy configuration could not be used. Currently this means
            that a mandatory PAC script could not be fetched, parsed or executed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.PreconnectMaxSocketLimit">
            <summary>
            We've hit the max socket limit for the socket pool while preconnecting.  We
            don't bother trying to preconnect more sockets.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslClientAuthPrivateKeyAccessDenied">
            <summary>
            The permission to use the SSL client certificate's private key was denied.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslClientAuthCertNoPrivateKey">
            <summary>
            The SSL client certificate has no private key.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ProxyCertificateInvalid">
            <summary>
            The certificate presented by the HTTPS Proxy was invalid.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NameResolutionFailed">
            <summary>
            An error occurred when trying to do a name resolution (DNS).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NetworkAccessDenied">
            <summary>
            Permission to access the network was denied. This is used to distinguish
            errors that were most likely caused by a firewall from other access denied
            errors. See also ERR_ACCESS_DENIED.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.TemporarilyThrottled">
            <summary>
            The request throttler module cancelled this request to avoid DDOS.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.HttpsProxyTunnelResponse">
            <summary>
            A request to create an SSL tunnel connection through the HTTPS proxy
            received a non-200 (OK) and non-407 (Proxy Auth) response.  The response
            body might include a description of why the request failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslClientAuthSignatureFailed">
             <summary>
             We were unable to sign the CertificateVerify data of an SSL client auth
             handshake with the client certificate's private key.
            
             Possible causes for this include the user implicitly or explicitly
             denying access to the private key, the private key may not be valid for
             signing, the key may be relying on a cached handle which is no longer
             valid, or the CSP won't allow arbitrary data to be signed.
             </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.MsgTooBig">
            <summary>
            The message was too large for the transport.  (for example a UDP message
            which exceeds size threshold).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdySessionAlreadyExists">
            <summary>
            A SPDY session already exists, and should be used instead of this connection.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.WsProtocolError">
            <summary>
            Websocket protocol error. Indicates that we are terminating the connection
            due to a malformed frame or other protocol violation.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ProtocolSwitched">
            <summary>
            Connection was aborted for switching to another ptotocol.
            WebSocket abort SocketStream connection when alternate protocol is found.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.AddressInUse">
            <summary>
            Returned when attempting to bind an address that is already in use.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslHandshakeNotCompleted">
            <summary>
            An operation failed because the SSL handshake has not completed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslBadPeerPublicKey">
            <summary>
            SSL peer's public key is invalid.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslPinnedKeyNotInCertChain">
            <summary>
            The certificate didn't match the built-in public key pins for the host name.
            The pins are set in net/http/transport_security_state.cc and require that
            one of a set of public keys exist on the path from the leaf to the root.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ClientAuthCertTypeUnsupported">
            <summary>
            Server request for client certificate did not contain any types we support.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.OriginBoundCertGenerationTypeMismatch">
            <summary>
            Server requested one type of cert, then requested a different type while the
            first was still being generated.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslDecryptErrorAlert">
            <summary>
            An SSL peer sent us a fatal decrypt_error alert. This typically occurs when
            a peer could not correctly verify a signature (in CertificateVerify or
            ServerKeyExchange) or validate a Finished message.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.WsThrottleQueueTooLarge">
            <summary>
            There are too many pending WebSocketJob instances, so the new job was not
            pushed to the queue.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.TooManySocketStreams">
            <summary>
            There are too many active SocketStream instances, so the new connect request
            was rejected.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslServerCertChanged">
            <summary>
            The SSL server certificate changed in a renegotiation.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslInappropriateFallback">
            <summary>
            The SSL server indicated that an unnecessary TLS version fallback was
            performed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CtNoSctsVerifiedOk">
            <summary>
            Certificate Transparency: All Signed Certificate Timestamps failed to verify.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslUnrecognizedNameAlert">
            <summary>
            The SSL server sent us a fatal unrecognized_name alert.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocketSetReceiveBufferSizeError">
            <summary>
            Failed to set the socket's receive buffer size as requested.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocketSetSendBufferSizeError">
            <summary>
            Failed to set the socket's send buffer size as requested.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocketReceiveBufferSizeUnchangeable">
            <summary>
            Failed to set the socket's receive buffer size as requested, despite success
            return code from setsockopt.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SocketSendBufferSizeUnchangeable">
            <summary>
            Failed to set the socket's send buffer size as requested, despite success
            return code from setsockopt.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SslClientAuthCertBadFormat">
            <summary>
            Failed to import a client certificate from the platform store into the SSL
            library.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertCommonNameInvalid">
            <summary>
            The server responded with a certificate whose common name did not match the host name.
            This could mean:
            1. An attacker has redirected our traffic to his server and is presenting a certificate
            for which he knows the private key.
            2. The server is misconfigured and responding with the wrong cert.
            3. The user is on a wireless network and is being redirected to the network's login page.
            4. The OS has used a DNS search suffix and the server doesn't have a certificate for the
            abbreviated name in the address bar.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertDateInvalid">
            <summary>
            The server responded with a certificate that, by our clock, appears to either not yet be valid or to have expired.
            This could mean:
            1. An attacker is presenting an old certificate for which he has managed to obtain the private key
            2. The server is misconfigured and is not presenting a valid cert.
            3. Our clock is wrong.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertAuthorityInvalid">
            <summary>
            The server responded with a certificate that is signed by an authority we don't trust.
            The could mean:
            1. An attacker has substituted the real certificate for a cert that
            contains his public key and is signed by his cousin.
            2. The server operator has a legitimate certificate from a CA we don't know about, but should trust.
            3. The server is presenting a self-signed certificate, providing no defense against active attackers (but foiling passive attackers).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertContainsErrors">
            <summary>
            The server responded with a certificate that contains errors. This error is not recoverable.
            MSDN describes this error as follows:
            "The SSL certificate contains errors."
            NOTE: It's unclear how this differs from ERR_CERT_INVALID. For consistency,
            use that code instead of this one from now on.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertNoRevocationMechanism">
            <summary>
            The certificate has no mechanism for determining if it is revoked.  In effect, this certificate cannot be revoked.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertUnableToCheckRevocation">
            <summary>
            Revocation information for the security certificate for this site is not available.
            This could mean:
            1. An attacker has compromised the private key in the certificate and is blocking our attempt to
            find out that the cert was revoked.
            2. The certificate is unrevoked, but the revocation server is busy or unavailable.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertRevoked">
            <summary>
            The server responded with a certificate has been revoked.
            We have the capability to ignore this error, but it is probably not the thing to do.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertInvalid">
            <summary>
            The server responded with a certificate that is invalid. This error is not recoverable.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertWeakSignatureAlgorithm">
            <summary>
            The server responded with a certificate that is signed using a weak
            signature algorithm.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertNonUniqueName">
            <summary>
            The host name specified in the certificate is not unique.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertWeakKey">
            <summary>
            The server responded with a certificate that contains a weak key (e.g.
            a too-small RSA key).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertNameConstraintViolation">
            <summary>
            The certificate claimed DNS names that are in violation of name constraints.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertEnd">
             <summary>
             Add new certificate error codes here.
            
             Update the value of CERT_END whenever you add a new certificate error
             code.
            
             The value immediately past the last certificate error code.
             </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidUrl">
            <summary>
            The URL is invalid.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DisallowedUrlScheme">
            <summary>
            The scheme of the URL is disallowed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnknownUrlScheme">
            <summary>
            The scheme of the URL is unknown.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.TooManyRedirects">
            <summary>
            Attempting to load an URL resulted in too many redirects.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnsafeRedirect">
            <summary>
            Attempting to load an URL resulted in an unsafe redirect (e.g., a redirect to file:// is considered unsafe).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnsafePort">
            <summary>
            Attempting to load an URL with an unsafe port number.  These are port
            numbers that correspond to services, which are not robust to spurious input
            that may be constructed as a result of an allowed web construct (e.g., HTTP
            looks a lot like SMTP, so form submission to port 25 is denied).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidResponse">
            <summary>
            The server's response was invalid.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidChunkedEncoding">
            <summary>
            Error in chunked transfer encoding.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.MethodNotSupported">
            <summary>
            The server did not support the request method.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnexpectedProxyAuth">
            <summary>
            The response was 407 (Proxy Authentication Required), yet we did not send the request to a proxy.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.EmptyResponse">
            <summary>
            The server closed the connection without sending any data.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ResponseHeadersTooBig">
            <summary>
            The headers section of the response is too large.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.PacStatusNotOk">
            <summary>
            The PAC requested by HTTP did not have a valid status code (non-200).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.PacScriptFailed">
            <summary>
            The evaluation of the PAC script failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.RequestRangeNotSatisfiable">
            <summary>
            The response was 416 (Requested range not satisfiable) and the server cannot
            satisfy the range requested.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.MalformedIdentity">
            <summary>
            The identity used for authentication is invalid.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ContentDecodingFailed">
            <summary>
            Content decoding of the response body failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NetworkIoSuspended">
            <summary>
            An operation could not be completed because all network IO
            is suspended.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SynReplyNotReceived">
            <summary>
            FLIP data received without receiving a SYN_REPLY on the stream.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.EncodingConversionFailed">
            <summary>
            Converting the response to target encoding failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnrecognizedFtpDirectoryListingFormat">
            <summary>
            The server sent an FTP directory listing in a format we do not understand.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidSpdyStream">
            <summary>
            Attempted use of an unknown SPDY stream id.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NoSupportedProxies">
            <summary>
            There are no supported proxies in the provided list.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyProtocolError">
            <summary>
            There is a SPDY protocol error.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InvalidAuthCredentials">
            <summary>
            Credentials could not be established during HTTP Authentication.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnsupportedAuthScheme">
            <summary>
            An HTTP Authentication scheme was tried which is not supported on this
            machine.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.EncodingDetectionFailed">
            <summary>
            Detecting the encoding of the response failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.MissingAuthCredentials">
            <summary>
            (GSSAPI) No Kerberos credentials were available during HTTP Authentication.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UnexpectedSecurityLibraryStatus">
            <summary>
            An unexpected, but documented, SSPI or GSSAPI status code was returned.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.MisconfiguredAuthEnvironment">
            <summary>
            The environment was not set up correctly for authentication (for
            example, no KDC could be found or the principal is unknown.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.UndocumentedSecurityLibraryStatus">
            <summary>
            An undocumented SSPI or GSSAPI status code was returned.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ResponseBodyTooBigToDrain">
            <summary>
            The HTTP response was too big to drain.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ResponseHeadersMultipleContentLength">
            <summary>
            The HTTP response contained multiple distinct Content-Length headers.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.IncompleteSpdyHeaders">
            <summary>
            SPDY Headers have been received, but not all of them - status or version
            headers are missing, so we're expecting additional frames to complete them.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.PacNotInDhcp">
            <summary>
            No PAC URL configuration could be retrieved from DHCP. This can indicate
            either a failure to retrieve the DHCP configuration, or that there was no
            PAC URL configured in DHCP.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ResponseHeadersMultipleContentDisposition">
            <summary>
            The HTTP response contained multiple Content-Disposition headers.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ResponseHeadersMultipleLocation">
            <summary>
            The HTTP response contained multiple Location headers.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyServerRefusedStream">
            <summary>
            SPDY server refused the stream. Client should retry. This should never be a
            user-visible error.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyPingFailed">
            <summary>
            SPDY server didn't respond to the PING message.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ContentLengthMismatch">
            <summary>
            The HTTP response body transferred fewer bytes than were advertised by the
            Content-Length header when the connection is closed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.IncompleteChunkedEncoding">
            <summary>
            The HTTP response body is transferred with Chunked-Encoding, but the
            terminating zero-length chunk was never sent when the connection is closed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.QuicProtocolError">
            <summary>
            There is a QUIC protocol error.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ResponseHeadersTruncated">
            <summary>
            The HTTP headers were truncated by an EOF.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.QuicHandshakeFailed">
            <summary>
            The QUIC crytpo handshake failed.  This means that the server was unable
            to read any requests sent, so they may be resent.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.RequestForSecureResourceOverInsecureQuic">
            <summary>
            An https resource was requested over an insecure QUIC connection.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyInadequateTransportSecurity">
            <summary>
            Transport security is inadequate for the SPDY version.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyFlowControlError">
            <summary>
            The peer violated SPDY flow control.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyFrameSizeError">
            <summary>
            The peer sent an improperly sized SPDY frame.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SpdyCompressionError">
            <summary>
            Decoding or encoding of compressed SPDY headers failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ProxyAuthRequestedWithNoConnection">
            <summary>
            Proxy Auth Requested without a valid Client Socket Handle.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheMiss">
            <summary>
            The cache does not have the requested entry.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheReadFailure">
            <summary>
            Unable to read from the disk cache.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheWriteFailure">
            <summary>
            Unable to write to the disk cache.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheOperationNotSupported">
            <summary>
            The operation is not supported for this entry.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheOpenFailure">
            <summary>
            The disk cache is unable to open this entry.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheCreateFailure">
            <summary>
            The disk cache is unable to create this entry.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheRace">
            <summary>
            Multiple transactions are racing to create disk cache entries. This is an
            internal error returned from the HttpCache to the HttpCacheTransaction that
            tells the transaction to restart the entry-creation logic because the state
            of the cache has changed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheChecksumReadFailure">
            <summary>
            The cache was unable to read a checksum record on an entry. This can be
            returned from attempts to read from the cache. It is an internal error,
            returned by the SimpleCache backend, but not by any URLRequest methods
            or members.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheChecksumMismatch">
            <summary>
            The cache found an entry with an invalid checksum. This can be returned from
            attempts to read from the cache. It is an internal error, returned by the
            SimpleCache backend, but not by any URLRequest methods or members.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CacheLockTimeout">
            <summary>
            Internal error code for the HTTP cache. The cache lock timeout has fired.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.InsecureResponse">
            <summary>
            The server's response was insecure (e.g. there was a cert error).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.NoPrivateKeyForCert">
            <summary>
            The server responded to a &lt;keygen&gt; with a generated client cert that we
            don't have the matching private key for.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.AddUserCertFailed">
            <summary>
            An error adding to the OS certificate database (e.g. OS X Keychain).
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpFailed">
            <summary>
            A generic error for failed FTP control connection command.
            If possible, please use or add a more specific error code.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpServiceUnavailable">
            <summary>
            The server cannot fulfill the request at this point. This is a temporary
            error.
            FTP response code 421.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpTransferAborted">
            <summary>
            The server has aborted the transfer.
            FTP response code 426.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpFileBusy">
            <summary>
            The file is busy, or some other temporary error condition on opening
            the file.
            FTP response code 450.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpSyntaxError">
            <summary>
            Server rejected our command because of syntax errors.
            FTP response codes 500, 501.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpCommandNotSupported">
            <summary>
            Server does not support the command we issued.
            FTP response codes 502, 504.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.FtpBadCommandSequence">
            <summary>
            Server rejected our command because we didn't issue the commands in right
            order.
            FTP response code 503.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Pkcs12ImportBadPassword">
            <summary>
            PKCS #12 import failed due to incorrect password.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Pkcs12ImportFailed">
            <summary>
            PKCS #12 import failed due to other error.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ImportCaCertNotCa">
            <summary>
            CA import failed - not a CA cert.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ImportCertAlreadyExists">
            <summary>
            Import failed - certificate already exists in database.
            Note it's a little weird this is an error but reimporting a PKCS12 is ok
            (no-op).  That's how Mozilla does it, though.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ImportCaCertFailed">
            <summary>
            CA import failed due to some other error.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ImportServerCertFailed">
            <summary>
            Server certificate import failed due to some internal error.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Pkcs12ImportInvalidMac">
            <summary>
            PKCS #12 import failed due to invalid MAC.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Pkcs12ImportInvalidFile">
            <summary>
            PKCS #12 import failed due to invalid/corrupt file.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.Pkcs12ImportUnsupported">
            <summary>
            PKCS #12 import failed due to unsupported features.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.KeyGenerationFailed">
            <summary>
            Key generation failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.OriginBoundCertGenerationFailed">
            <summary>
            Server-bound certificate generation failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.PrivateKeyExportFailed">
            <summary>
            Failure to export private key.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.SelfSignedCertGenerationFailed">
            <summary>
            Self-signed certificate generation failed.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.CertDatabaseChanged">
            <summary>
            The certificate database changed in some way.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.ChannelIdImportFailed">
            <summary>
            Failure to import Channel ID.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsMalformedResponse">
            <summary>
            DNS resolver received a malformed response.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsServerRequiresTcp">
            <summary>
            DNS server requires TCP
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsServerFailed">
            <summary>
            DNS server failed.  This error is returned for all of the following
            error conditions:
            1 - Format error - The name server was unable to interpret the query.
            2 - Server failure - The name server was unable to process this query
                due to a problem with the name server.
            4 - Not Implemented - The name server does not support the requested
                kind of query.
            5 - Refused - The name server refuses to perform the specified
                operation for policy reasons.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsTimedOut">
            <summary>
            DNS transaction timed out.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsCacheMiss">
            <summary>
            The entry was not found in cache, for cache-only lookups.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsSearchEmpty">
            <summary>
            Suffix search list rules prevent resolution of the given host name.
            </summary>
        </member>
        <member name="F:CefSharp.CefErrorCode.DnsSortError">
            <summary>
            Failed to sort addresses according to RFC3484.
            </summary>
        </member>
        <member name="T:CefSharp.CefEventFlags">
            <summary>
            Supported event bit flags.
            </summary>
        </member>
        <member name="F:CefSharp.CefEventFlags.CommandDown">
            <summary>
            Mac OS-X command key.
            </summary>
        </member>
        <member name="T:CefSharp.CefFileDialogMode">
            <summary>
            CefFileDialogMode (Based on cef_file_dialog_mode_t)
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.Open">
            <summary>
            Requires that the file exists before allowing the user to pick it.
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.OpenMultiple">
            <summary>
            Like Open, but allows picking multiple files to open.
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.OpenFolder">
            <summary>
            Like Open, but selects a folder to open.
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.Save">
            <summary>
            Allows picking a nonexistent file, and prompts to overwrite if the file already exists.
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.TypeMask">
            <summary>
            General mask defining the bits used for the type values.
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.OverwritePrompt">
            <summary>
            Prompt to overwrite if the user selects an existing file with the Save dialog.
            </summary>
        </member>
        <member name="F:CefSharp.CefFileDialogMode.HideReadOnly">
            <summary>
            Do not display read-only files.
            </summary>
        </member>
        <member name="T:CefSharp.CefFocusSource">
            <summary>
            Focus Source
            </summary>
        </member>
        <member name="F:CefSharp.CefFocusSource.FocusSourceNavigation">
            <summary>
            The source is explicit navigation via the API (LoadURL(), etc).
            </summary>
        </member>
        <member name="F:CefSharp.CefFocusSource.FocusSourceSystem">
            <summary>
            The source is a system-generated focus event.
            </summary>
        </member>
        <member name="T:CefSharp.CefGeoPositionErrorCode">
            <summary>
            Geoposition error codes. 
            </summary>
        </member>
        <member name="T:CefSharp.CefJsDialogType">
            <summary>
            Supported JavaScript dialog types.
            </summary>
        </member>
        <member name="F:CefSharp.CefMenuCommand.CustomFirst">
            <summary>
            Custom menu items originating from the renderer process. For example, plugin placeholder menu items or Flash menu items.
            This is the first entry
            </summary>
        </member>
        <member name="F:CefSharp.CefMenuCommand.CustomLast">
            <summary>
            Custom menu items originating from the renderer process. For example, plugin placeholder menu items or Flash menu items.
            This is the last entry
            </summary>
        </member>
        <member name="T:CefSharp.CefPdfPrintMarginType">
            <summary>
            Margin type for PDF printing.
            </summary>
        </member>
        <member name="F:CefSharp.CefPdfPrintMarginType.Default">
            <summary>
            Default margins.
            </summary>
        </member>
        <member name="F:CefSharp.CefPdfPrintMarginType.None">
            <summary>
            No margins.
            </summary>
        </member>
        <member name="F:CefSharp.CefPdfPrintMarginType.Minimum">
            <summary>
            Minimum margins
            </summary>
        </member>
        <member name="F:CefSharp.CefPdfPrintMarginType.Custom">
            <summary>
            Custom margins.
            </summary>
        </member>
        <member name="T:CefSharp.CefReturnValue">
            <summary>
            Return value types.
            </summary>
        </member>
        <member name="F:CefSharp.CefReturnValue.Cancel">
            <summary>
            Cancel immediately.
            </summary>
        </member>
        <member name="F:CefSharp.CefReturnValue.Continue">
            <summary>
            Continue immediately.
            </summary>
        </member>
        <member name="F:CefSharp.CefReturnValue.ContinueAsync">
            <summary>
            Continue asynchronously (usually via a callback).
            </summary>
        </member>
        <member name="T:CefSharp.CefState">
            <summary>
            Represents the state of a setting.
            </summary>
        </member>
        <member name="F:CefSharp.CefState.Default">
            <summary>
            Use the default state for the setting.
            </summary>
        </member>
        <member name="F:CefSharp.CefState.Enabled">
            <summary>
            Enable or allow the setting.
            </summary>
        </member>
        <member name="F:CefSharp.CefState.Disabled">
            <summary>
            Disable or disallow the setting.
            </summary>
        </member>
        <member name="T:CefSharp.CefTerminationStatus">
            <summary>
            Process termination status values. 
            </summary>
        </member>
        <member name="F:CefSharp.CefTerminationStatus.AbnormalTermination">
            <summary>
            Non-zero exit status.
            </summary>
        </member>
        <member name="F:CefSharp.CefTerminationStatus.ProcessWasKilled">
            <summary>
            SIGKILL or task manager kill.
            </summary>
        </member>
        <member name="F:CefSharp.CefTerminationStatus.ProcessCrashed">
            <summary>
            Segmentation fault.
            </summary>
        </member>
        <member name="T:CefSharp.CefThreadIds">
            <summary>
            Managed enum for cef_thread_id_t/CefThreadId
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_UI">
            <summary>
            The CEF UI thread in the browser. In CefSharp this is ALWAYS
            separate from the application's main thread (and thus the main 
            WinForm UI thread).
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_DB">
            <summary>
            Used to interact with the database.
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_FILE">
            <summary>
            Used to interact with the file system.
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_FILE_USER_BLOCKING">
            <summary>
            Used for file system operations that block user interactions.
            Responsiveness of this thread affects users.
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_PROCESS_LAUNCHER">
            <summary>
            Used to launch and terminate browser processes.
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_CACHE">
            <summary>
            Used to handle slow HTTP cache operations.
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_IO">
            <summary>
            Used to process IPC and network messages.
            </summary>
        </member>
        <member name="F:CefSharp.CefThreadIds.TID_RENDERER">
            <summary>
            The main thread in the renderer. Used for all WebKit and V8 interaction.
            </summary>
        </member>
        <member name="T:CefSharp.CertStatus">
             <summary>
            
             Supported certificate status code values. See net\cert\cert_status_flags.h
             for more information. CERT_STATUS_NONE is new in CEF because we use an
             enum while cert_status_flags.h uses a typedef and static const variables.
             </summary>
        </member>
        <member name="T:CefSharp.ContextMenuEditState">
            <summary>
            Supported context menu edit state bit flags.
            </summary>
        </member>
        <member name="T:CefSharp.ContextMenuMediaState">
            <summary>
            Supported context menu media state bit flags.
            </summary>
        </member>
        <member name="T:CefSharp.ContextMenuMediaType">
            <summary>
            Supported context menu media types.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuMediaType.None">
            <summary>
            No special node is in context.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuMediaType.Image">
            <summary>
            An image node is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuMediaType.Video">
            <summary>
            A video node is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuMediaType.Audio">
            <summary>
            An audio node is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuMediaType.File">
            <summary>
            A file node is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuMediaType.Plugin">
            <summary>
            A plugin node is selected.
            </summary>
        </member>
        <member name="T:CefSharp.ContextMenuType">
            <summary>
            ContextMenuType
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.None">
            <summary>
            No node is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.Page">
            <summary>
            The top page is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.Frame">
            <summary>
            A subframe page is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.Link">
            <summary>
            A link is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.Media">
            <summary>
            A media node is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.Selection">
            <summary>
            There is a textual or mixed selection that is selected.
            </summary>
        </member>
        <member name="F:CefSharp.ContextMenuType.Editable">
            <summary>
            An editable element is selected.
            </summary>
        </member>
        <member name="T:CefSharp.DragOperationsMask">
            <summary>
            "Verb" of a drag-and-drop operation as negotiated between the source and destination.
            </summary>
        </member>
        <member name="T:CefSharp.FilterStatus">
            <summary>
            Return values for IResponseFilter
            </summary>
        </member>
        <member name="F:CefSharp.FilterStatus.NeedMoreData">
            <summary>
            Some or all of the pre-filter data was read successfully but more data is
            needed in order to continue filtering (filtered output is pending).
            </summary>
        </member>
        <member name="F:CefSharp.FilterStatus.Done">
            <summary>
            Some or all of the pre-filter data was read successfully and all available filtered output has been written.
            </summary>
        </member>
        <member name="F:CefSharp.FilterStatus.Error">
            <summary>
            An error occurred during filtering.
            </summary>
        </member>
        <member name="F:CefSharp.KeyEventType.RawKeyDown">
            <summary>
            Notification that a key transitioned from "up" to "down".
            </summary>
        </member>
        <member name="F:CefSharp.KeyEventType.KeyDown">
            <summary>
            Notification that a key was pressed. This does not necessarily correspond
            to a character depending on the key and language. Use KEYEVENT_CHAR for
            character input.
            </summary>
        </member>
        <member name="F:CefSharp.KeyEventType.KeyUp">
            <summary>
            Notification that a key was released.
            </summary>
        </member>
        <member name="F:CefSharp.KeyEventType.Char">
            <summary>
            Notification that a character was typed. Use this for text input. Key
            down events may generate 0, 1, or more than one character event depending
            on the key, locale, and operating system.
            </summary>
        </member>
        <member name="F:CefSharp.LogSeverity.Default">
            <summary>
            Default logging (currently Info logging)
            </summary>
        </member>
        <member name="F:CefSharp.LogSeverity.Verbose">
            <summary>
            Verbose logging.
            </summary>
        </member>
        <member name="F:CefSharp.LogSeverity.Info">
            <summary>
            Info logging
            </summary>
        </member>
        <member name="F:CefSharp.LogSeverity.Warning">
            <summary>
            Warning logging
            </summary>
        </member>
        <member name="F:CefSharp.LogSeverity.Error">
            <summary>
            Error logging
            </summary>
        </member>
        <member name="F:CefSharp.LogSeverity.Disable">
            <summary>
            Completely disable logging
            </summary>
        </member>
        <member name="T:CefSharp.PaintElementType">
            <summary>
            Paint element types.
            </summary>
        </member>
        <member name="T:CefSharp.PluginPolicy">
            <summary>
            Plugin policies supported by IPluginHandler.OnBeforePluginLoad.
            </summary>
        </member>
        <member name="F:CefSharp.PluginPolicy.Allow">
            <summary>
            Allow the content
            </summary>
        </member>
        <member name="F:CefSharp.PluginPolicy.DetectImportant">
            <summary>
            Allow important content and block unimportant content based on heuristics. The user can manually load blocked content.
            </summary>
        </member>
        <member name="F:CefSharp.PluginPolicy.Block">
            <summary>
            Block the content. The user can manually load blocked content.
            </summary>
        </member>
        <member name="F:CefSharp.PluginPolicy.Disable">
            <summary>
            Disable the content. The user cannot load disabled content.
            </summary>
        </member>
        <member name="T:CefSharp.PostDataElementType">
            <summary>
            Post data elements may represent either bytes or files.
            </summary>
        </member>
        <member name="F:CefSharp.ReferrerPolicy.Always">
            <summary>
            Always send the complete Referrer value.
            </summary>
        </member>
        <member name="F:CefSharp.ReferrerPolicy.Default">
            <summary>
            Use the default policy. This is OriginWhenCrossOrigin
            when the `--reduced-referrer-granularity` command-line flag is specified
            and NoReferrerWhenDowngrade otherwise.
            </summary>
        </member>
        <member name="F:CefSharp.ReferrerPolicy.NoReferrerWhenDowngrade">
            <summary>
            When navigating from HTTPS to HTTP do not send the Referrer value.
            Otherwise, send the complete Referrer value.
            </summary>
        </member>
        <member name="F:CefSharp.ReferrerPolicy.Never">
            <summary>
            Never send the Referrer value.
            </summary>
        </member>
        <member name="F:CefSharp.ReferrerPolicy.Origin">
            <summary>
            Only send the origin component of the Referrer value.
            </summary>
        </member>
        <member name="F:CefSharp.ReferrerPolicy.OriginWhenCrossOrigin">
            <summary>
            When navigating cross-origin only send the origin component of the Referrer value. Otherwise, send the complete Referrer value.
            </summary>
        </member>
        <member name="T:CefSharp.ResourceType">
            <summary>
            Resource type for a request.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.MainFrame">
            <summary>
            Top level page.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.SubFrame">
            <summary>
            Frame or iframe.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Stylesheet">
            <summary>
            CSS stylesheet.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Script">
            <summary>
            External script.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Image">
            <summary>
            Image (jpg/gif/png/etc).
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.FontResource">
            <summary>
            Font.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.SubResource">
            <summary>
            Some other subresource. This is the default type if the actual type is unknown.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Object">
            <summary>
            Object (or embed) tag for a plugin, or a resource that a plugin requested.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Media">
            <summary>
            Media resource.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Worker">
            <summary>
            Main resource of a dedicated worker.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.SharedWorker">
            <summary>
            Main resource of a shared worker.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Prefetch">
            <summary>
            Explicitly requested prefetch.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Favicon">
            <summary>
            Favicon.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Xhr">
            <summary>
            XMLHttpRequest.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.Ping">
            <summary>
            A request for a ping
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.ServiceWorker">
            <summary>
            Main resource of a service worker.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.CspReport">
            <summary>
            A report of Content Security Policy violations.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceType.PluginResource">
            <summary>
            A resource that a plugin requested.
            </summary>
        </member>
        <member name="T:CefSharp.SslContentStatus">
            <summary>
            Supported SSL content status flags. See content/public/common/ssl_status.h
            for more information.
            </summary>
        </member>
        <member name="T:CefSharp.SslVersion">
            <summary>
            Supported SSL version values. See net/ssl/ssl_connection_status_flags.h
            for more information.
            </summary>
        </member>
        <member name="T:CefSharp.TransitionType">
            <summary>
            Transition type for a request. Made up of one source value and 0 or more qualifiers.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.LinkClicked">
            <summary>
            Source is a link click or the JavaScript window.open function. This is
            also the default value for requests like sub-resource loads that are not navigations.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.Explicit">
            <summary>
            Source is some other "explicit" navigation action such as creating a new 
            browser or using the LoadURL function. This is also the default value
            for navigations where the actual type is unknown.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.AutoSubFrame">
            <summary>
            Source is a subframe navigation. This is any content that is automatically
            loaded in a non-toplevel frame. For example, if a page consists of several
            frames containing ads, those ad URLs will have this transition type.
            The user may not even realize the content in these pages is a separate
            frame, so may not care about the URL.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.ManualSubFrame">
            <summary>
            Source is a subframe navigation explicitly requested by the user that will
            generate new navigation entries in the back/forward list. These are
            probably more important than frames that were automatically loaded in
            the background because the user probably cares about the fact that this
            link was loaded.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.FormSubmit">
            <summary>
            Source is a form submission by the user. NOTE: In some situations
            submitting a form does not result in this transition type. This can happen
            if the form uses a script to submit the contents.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.Reload">
            <summary>
            Source is a "reload" of the page via the Reload function or by re-visiting
            the same URL. NOTE: This is distinct from the concept of whether a
            particular load uses "reload semantics" (i.e. bypasses cached data).
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.SourceMask">
            <summary>
            General mask defining the bits used for the source values.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.Blocked">
            <summary>
            Attempted to visit a URL but was blocked.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.ForwardBack">
            <summary>
            Used the Forward or Back function to navigate among browsing history.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.ChainStart">
            <summary>
            The beginning of a navigation chain.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.ChainEnd">
            <summary>
            The last transition in a redirect chain.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.ClientRedirect">
            <summary>
            Redirects caused by JavaScript or a meta refresh tag on the page.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.ServerRedirect">
            <summary>
            Redirects sent from the server by HTTP headers.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.IsRedirect">
            <summary>
            Used to test whether a transition involves a redirect.
            </summary>
        </member>
        <member name="F:CefSharp.TransitionType.QualifierMask">
            <summary>
            General mask defining the bits used for the qualifiers.
            </summary>
        </member>
        <member name="T:CefSharp.UrlRequestStatus">
            <summary>
            Flags that represent CefURLRequest status.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestStatus.Unknown">
            <summary>
            Unknown status.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestStatus.Success">
            <summary>
            Request succeeded.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestStatus.IoPending">
            <summary>
            An IO request is pending, and the caller will be informed when it is completed.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestStatus.Canceled">
            <summary>
            Request was canceled programatically.
            </summary>
        </member>
        <member name="F:CefSharp.UrlRequestStatus.Failed">
            <summary>
            Request failed for some reason.
            </summary>
        </member>
        <member name="T:CefSharp.WindowOpenDisposition">
            <summary>
            The manner in which a link click should be opened.
            </summary>
        </member>
        <member name="T:CefSharp.AddressChangedEventArgs">
            <summary>
            Event arguments for the AddressChanged event handler.
            </summary>
        </member>
        <member name="M:CefSharp.AddressChangedEventArgs.#ctor(CefSharp.IBrowser,System.String)">
            <summary>
            Creates a new AddressChangedEventArgs event argument.
            </summary>
            <param name="browser">the browser object</param>
            <param name="address">the address</param>
        </member>
        <member name="P:CefSharp.AddressChangedEventArgs.Browser">
            <summary>
            Access to the underlying <see cref="T:CefSharp.IBrowser"/> object
            </summary>
        </member>
        <member name="P:CefSharp.AddressChangedEventArgs.Address">
            <summary>
            The new address
            </summary>
        </member>
        <member name="T:CefSharp.ConsoleMessageEventArgs">
            <summary>
            Event arguments for the ConsoleMessage event handler set up in IWebBrowser.
            </summary>
        </member>
        <member name="M:CefSharp.ConsoleMessageEventArgs.#ctor(System.String,System.String,System.Int32)">
            <summary>
            Creates a new ConsoleMessageEventArgs event argument.
            </summary>
            <param name="message">message</param>
            <param name="source">source</param>
            <param name="line">line number</param>
        </member>
        <member name="P:CefSharp.ConsoleMessageEventArgs.Message">
            <summary>
            The message text of the console message.
            </summary>
        </member>
        <member name="P:CefSharp.ConsoleMessageEventArgs.Source">
            <summary>
            The source of the console message.
            </summary>
        </member>
        <member name="P:CefSharp.ConsoleMessageEventArgs.Line">
            <summary>
            The line number that generated the console message.
            </summary>
        </member>
        <member name="T:CefSharp.FrameLoadEndEventArgs">
            <summary>
            Event arguments to the FrameLoadEnd event handler set up in IWebBrowser.
            </summary>
        </member>
        <member name="M:CefSharp.FrameLoadEndEventArgs.#ctor(CefSharp.IBrowser,CefSharp.IFrame,System.Int32)">
            <summary>
            Creates a new FrameLoadEnd event args
            </summary>
            <param name="browser">browser</param>
            <param name="frame">frame</param>
            <param name="httpStatusCode">http statusCode</param>
        </member>
        <member name="P:CefSharp.FrameLoadEndEventArgs.Browser">
            <summary>
            The browser that contains the frame that finished loading.
            </summary>
        </member>
        <member name="P:CefSharp.FrameLoadEndEventArgs.Frame">
            <summary>
            The frame that finished loading.
            </summary>
        </member>
        <member name="P:CefSharp.FrameLoadEndEventArgs.Url">
            <summary>
            The URL that was loaded.
            </summary>
        </member>
        <member name="P:CefSharp.FrameLoadEndEventArgs.HttpStatusCode">
            <summary>
            Http Status Code
            </summary>
        </member>
        <member name="T:CefSharp.FrameLoadStartEventArgs">
            <summary>
            Event arguments to the FrameLoadStart event handler set up in IWebBrowser.
            </summary>
        </member>
        <member name="M:CefSharp.FrameLoadStartEventArgs.#ctor(CefSharp.IBrowser,CefSharp.IFrame,CefSharp.TransitionType)">
            <summary>
            Creates a new FrameLoadStart event args
            </summary>
            <param name="browser">browser</param>
            <param name="frame">frame</param>
        </member>
        <member name="P:CefSharp.FrameLoadStartEventArgs.Browser">
            <summary>
            The browser object
            </summary>
        </member>
        <member name="P:CefSharp.FrameLoadStartEventArgs.Frame">
            <summary>
            The frame that just started loading.
            </summary>
        </member>
        <member name="P:CefSharp.FrameLoadStartEventArgs.Url">
            <summary>
            The URL that was loaded.
            </summary>
        </member>
        <member name="P:CefSharp.FrameLoadStartEventArgs.TransitionType">
            <summary>
            TransitionType provides information about the source of the navigation.
            </summary>
        </member>
        <member name="T:CefSharp.IsBrowserInitializedChangedEventArgs">
            <summary>
            Event arguments for the IsBrowserInitializedChanged event handler.
            </summary>
        </member>
        <member name="T:CefSharp.LoadErrorEventArgs">
            <summary>
            Event arguments to the LoadError event handler set up in IWebBrowser.
            </summary>
        </member>
        <member name="P:CefSharp.LoadErrorEventArgs.Browser">
            <summary>
            The browser object
            </summary>
        </member>
        <member name="P:CefSharp.LoadErrorEventArgs.Frame">
            <summary>
            The frame that failed to load.
            </summary>
        </member>
        <member name="P:CefSharp.LoadErrorEventArgs.FailedUrl">
            <summary>
            The URL that failed to load.
            </summary>
        </member>
        <member name="P:CefSharp.LoadErrorEventArgs.ErrorCode">
            <summary>
            The error code.
            </summary>
        </member>
        <member name="P:CefSharp.LoadErrorEventArgs.ErrorText">
            <summary>
            The error text.
            </summary>
        </member>
        <member name="T:CefSharp.LoadingStateChangedEventArgs">
            <summary>
            Event arguments to the LoadingStateChanged event handler set up in IWebBrowser.
            </summary>
        </member>
        <member name="P:CefSharp.LoadingStateChangedEventArgs.CanGoForward">
            <summary>
            Returns true if the browser can navigate forwards. 
            </summary>
        </member>
        <member name="P:CefSharp.LoadingStateChangedEventArgs.CanGoBack">
            <summary>
            Returns true if the browser can navigate backwards. 
            </summary>
        </member>
        <member name="P:CefSharp.LoadingStateChangedEventArgs.CanReload">
            <summary>
            Returns true if the browser can reload. 
            </summary>
        </member>
        <member name="P:CefSharp.LoadingStateChangedEventArgs.IsLoading">
            <summary>
            Returns true if the browser is loading. 
            </summary>
        </member>
        <member name="P:CefSharp.LoadingStateChangedEventArgs.Browser">
            <summary>
            Access to the underlying <see cref="T:CefSharp.IBrowser"/> object
            </summary>
        </member>
        <member name="T:CefSharp.StatusMessageEventArgs">
            <summary>
            Event arguments to the StatusMessage event handler set up in IWebBrowser.
            </summary>
        </member>
        <member name="P:CefSharp.StatusMessageEventArgs.Browser">
            <summary>
            The browser object
            </summary>
        </member>
        <member name="P:CefSharp.StatusMessageEventArgs.Value">
            <summary>
            The value of the status message.
            </summary>
        </member>
        <member name="T:CefSharp.TitleChangedEventArgs">
            <summary>
            Event arguments to the TitleChanged event handler.
            </summary>
        </member>
        <member name="M:CefSharp.TitleChangedEventArgs.#ctor(System.String)">
            <summary>
            Creates a new TitleChanged event arg
            </summary>
            <param name="title">the new title</param>
        </member>
        <member name="P:CefSharp.TitleChangedEventArgs.Title">
            <summary>
            The new title
            </summary>
        </member>
        <member name="T:CefSharp.Geoposition">
            <summary>
            Structure representing geoposition information. The properties of this
            structure correspond to those of the JavaScript Position object although
            their types may differ.
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.Latitude">
            <summary>
            Latitude in decimal degrees north (WGS84 coordinate frame).
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.Longitude">
            <summary>
            Longitude in decimal degrees west (WGS84 coordinate frame).
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.Altitude">
            <summary>
            Altitude in meters (above WGS84 datum).
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.AltitudeAccuracy">
            <summary>
            Accuracy of altitude in meters.
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.Heading">
            <summary>
            Heading in decimal degrees clockwise from true north.
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.Speed">
            <summary>
            Horizontal component of device velocity in meters per second.
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.Timestamp">
            <summary>
            Time of position measurement in miliseconds since Epoch in UTC time. This
            is taken from the host computer's system clock.
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.ErrorCode">
            <summary>
            Error code, see enum above.
            </summary>
        </member>
        <member name="P:CefSharp.Geoposition.ErrorMessage">
            <summary>
            Human-readable error message.
            </summary>
        </member>
        <member name="T:CefSharp.IBrowserProcessHandler">
            <summary>
            Implement this interface to handle events related to browser process callbacks.
            The methods of this class will be called on the CEF UI thread unless otherwise indicated. . 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserProcessHandler.OnContextInitialized">
            <summary>
            Called on the CEF UI thread immediately after the CEF context has been initialized.
            You can now access the Global RequestContext through Cef.GetGlobalRequestContext() - this is the
            first place you can set Preferences (e.g. proxy settings, spell check dictionaries).
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserProcessHandler.OnScheduleMessagePumpWork(System.Int64)">
            <summary>
            Called from any thread when work has been scheduled for the browser process
            main (UI) thread. This callback is used in combination with CefSettings.
            ExternalMessagePump and Cef.DoMessageLoopWork() in cases where the CEF
            message loop must be integrated into an existing application message loop
            (see additional comments and warnings on Cef.DoMessageLoopWork). This
            callback should schedule a Cef.DoMessageLoopWork() call to happen on the
            main (UI) thread. 
            </summary>
            <param name="delay">is the requested delay in milliseconds. If
            delay is less than or equal to 0 then the call should happen reasonably soon. If
            delay is greater than 0 then the call should be scheduled to happen after the
            specified delay and any currently pending scheduled call should be
            cancelled.</param>
        </member>
        <member name="T:CefSharp.IContextMenuHandler">
            <summary>
            Implement this interface to handle context menu events. 
            </summary>
        </member>
        <member name="M:CefSharp.IContextMenuHandler.OnBeforeContextMenu(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IContextMenuParams,CefSharp.IMenuModel)">
            <summary>
            Called before a context menu is displayed. The model can be cleared to show no context menu or
            modified to show a custom menu.
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame the request is coming from</param>
            <param name="parameters">provides information about the context menu state</param>
            <param name="model">initially contains the default context menu</param>
        </member>
        <member name="M:CefSharp.IContextMenuHandler.OnContextMenuCommand(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IContextMenuParams,CefSharp.CefMenuCommand,CefSharp.CefEventFlags)">
            <summary>
            Called to execute a command selected from the context menu. See
            cef_menu_id_t for the command ids that have default implementations. All
            user-defined command ids should be between MENU_ID_USER_FIRST and
            MENU_ID_USER_LAST.
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame the request is coming from</param>
            <param name="parameters">will have the same values as what was passed to</param>
            <param name="commandId">menu command id</param>
            <param name="eventFlags">event flags</param>
            <returns>Return true if the command was handled or false for the default implementation.</returns>
        </member>
        <member name="M:CefSharp.IContextMenuHandler.OnContextMenuDismissed(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)">
            <summary>
            Called when the context menu is dismissed irregardless of whether the menu
            was empty or a command was selected.
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame the request is coming from</param>
        </member>
        <member name="M:CefSharp.IContextMenuHandler.RunContextMenu(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IContextMenuParams,CefSharp.IMenuModel,CefSharp.IRunContextMenuCallback)">
            <summary>
            Called to allow custom display of the context menu.
            For custom display return true and execute callback either synchronously or asynchronously with the selected command Id.
            For default display return false. Do not keep references to parameters or model outside of this callback. 
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame the request is coming from</param>
            <param name="parameters">provides information about the context menu state</param>
            <param name="model">contains the context menu model resulting from OnBeforeContextMenu</param>
            <param name="callback">the callback to execute for custom display</param>
            <returns>For custom display return true and execute callback either synchronously or asynchronously with the selected command ID.</returns>
        </member>
        <member name="T:CefSharp.IDialogHandler">
            <summary>
            Implement this interface to handle dialog events. The methods of this class will be called on the CEF UI thread.
            </summary>
        </member>
        <member name="M:CefSharp.IDialogHandler.OnFileDialog(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefFileDialogMode,System.String,System.String,System.Collections.Generic.List{System.String},System.Int32,CefSharp.IFileDialogCallback)">
            <summary>
            Runs a file chooser dialog. 
            </summary>
            <example>
            To test assign something like TempFileDialogHandler (from CefSharp.Example) to DialogHandler e.g.
            <code>
            browser.DialogHandler = new TempFileDialogHandler();
            </code>
            Example URL to use for file browsing http://www.cs.tut.fi/~jkorpela/forms/file.html#example
            Simply click browse, the space next to the browse button should be populated with a randomly generated filename.
            </example>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
            <param name="mode">represents the type of dialog to display</param>
            <param name="title">the title to be used for the dialog. It may be empty to show the default title ("Open" or "Save" 
            depending on the mode).</param>
            <param name="defaultFilePath">is the path with optional directory and/or file name component that
            should be initially selected in the dialog.</param>
            <param name="acceptFilters">are used to restrict the selectable file types and may any combination of
            (a) valid lower-cased MIME types (e.g. "text/*" or "image/*"),
            (b) individual file extensions (e.g. ".txt" or ".png"),
            (c) combined description and file extension delimited using "|" and ";" (e.g. "Image Types|.png;.gif;.jpg").</param>
            <param name="selectedAcceptFilter">is the 0-based index of the filter that should be selected by default.</param>
            <param name="callback">Callback interface for asynchronous continuation of file dialog requests.</param>
            <returns>To display a custom dialog return true. To display the default dialog return false.</returns>
        </member>
        <member name="T:CefSharp.IDisplayHandler">
            <summary>
            Handle events related to browser display state.
            </summary>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnAddressChanged(CefSharp.IWebBrowser,CefSharp.AddressChangedEventArgs)">
            <summary>
            Called when a frame's address has changed. 
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="addressChangedArgs">args</param>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnAutoResize(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.Structs.Size)">
            <summary>
            Called when auto-resize is enabled via IBrowserHost.SetAutoResizeEnabled and the contents have auto-resized.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="newSize">will be the desired size in view coordinates</param>
            <returns>Return true if the resize was handled or false for default handling. </returns>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnTitleChanged(CefSharp.IWebBrowser,CefSharp.TitleChangedEventArgs)">
            <summary>
            Called when the page title changes.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="titleChangedArgs">args</param>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnFaviconUrlChange(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Collections.Generic.IList{System.String})">
            <summary>
            Called when the page icon changes.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="urls">list of urls where the favicons can be downloaded</param>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnFullscreenModeChange(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Boolean)">
            <summary>
            Called when web content in the page has toggled fullscreen mode. The client is
            responsible for resizing the browser if desired.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="fullscreen">If true the content will automatically be sized to fill the browser content area.
            If false the content will automatically return to its original size and position.</param>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnTooltipChanged(CefSharp.IWebBrowser,System.String@)">
            <summary>
            Called when the browser is about to display a tooltip. text contains the
            text that will be displayed in the tooltip. You can optionally modify text
            and then return false to allow the browser to display the tooltip.
            When window rendering is disabled the application is responsible for
            drawing tooltips and the return value is ignored.
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="text">the text that will be displayed in the tooltip</param>
            <returns>To handle the display of the tooltip yourself return true otherwise return false
            to allow the browser to display the tooltip.</returns>
            <remarks>Only called when using Off-screen rendering (WPF and OffScreen)</remarks>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnStatusMessage(CefSharp.IWebBrowser,CefSharp.StatusMessageEventArgs)">
            <summary>
            Called when the browser receives a status message.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="statusMessageArgs">args</param>
        </member>
        <member name="M:CefSharp.IDisplayHandler.OnConsoleMessage(CefSharp.IWebBrowser,CefSharp.ConsoleMessageEventArgs)">
            <summary>
            Called to display a console message. 
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="consoleMessageArgs">args</param>
            <returns>Return true to stop the message from being output to the console.</returns>
        </member>
        <member name="T:CefSharp.IDownloadHandler">
            <summary>
            Class used to handle file downloads.
            The methods of this class will called on the CEF UI thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IDownloadHandler.OnBeforeDownload(CefSharp.IBrowser,CefSharp.DownloadItem,CefSharp.IBeforeDownloadCallback)">
            <summary>
            Called before a download begins.
            </summary>
            <param name="browser">The browser instance</param>
            <param name="downloadItem">Represents the file being downloaded.</param>
            <param name="callback">Callback interface used to asynchronously continue a download.</param>
        </member>
        <member name="M:CefSharp.IDownloadHandler.OnDownloadUpdated(CefSharp.IBrowser,CefSharp.DownloadItem,CefSharp.IDownloadItemCallback)">
            <summary>
            Called when a download's status or progress information has been updated. This may be called multiple times before and after <see cref="M:CefSharp.IDownloadHandler.OnBeforeDownload(CefSharp.IBrowser,CefSharp.DownloadItem,CefSharp.IBeforeDownloadCallback)"/>.
            </summary>
            <param name="browser">The browser instance</param>
            <param name="downloadItem">Represents the file being downloaded.</param>
            <param name="callback">The callback used to Cancel/Pause/Resume the process</param>
        </member>
        <member name="T:CefSharp.IDragHandler">
            <summary>
            Implement this interface to handle events related to dragging.
            The methods of this class will be called on the UI thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IDragHandler.OnDragEnter(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IDragData,CefSharp.DragOperationsMask)">
            <summary>
            Called when an external drag event enters the browser window.
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="dragData">contains the drag event data</param>
            <param name="mask">represents the type of drag operation</param>
            <returns>Return false for default drag handling behavior or true to cancel the drag event. </returns>
        </member>
        <member name="M:CefSharp.IDragHandler.OnDraggableRegionsChanged(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Collections.Generic.IList{CefSharp.DraggableRegion})">
            <summary>
            Called whenever draggable regions for the browser window change.
            These can be specified using the '-webkit-app-region: drag/no-drag' CSS-property.
            If draggable regions are never defined in a document this method will also never be called.
            If the last draggable region is removed from a document this method will be called with an empty IList. 
            </summary>
            <param name="browserControl">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="regions">List of <see cref="T:CefSharp.DraggableRegion"/> objects or null if last region was removed.</param>
        </member>
        <member name="T:CefSharp.IFindHandler">
            <summary>
            Implement this interface to handle events related to find results.
            The methods of this class will be called on the CEF UI thread.
            </summary>
        </member>
        <member name="M:CefSharp.IFindHandler.OnFindResult(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Int32,System.Int32,CefSharp.Rect,System.Int32,System.Boolean)">
            <summary>
            Called to report find results returned by <see cref="M:CefSharp.IBrowserHost.Find(System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean)"/>
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
            <param name="identifier">is the identifier passed to Find()</param>
            <param name="count">is the number of matches currently identified</param>
            <param name="selectionRect">is the location of where the match was found (in window coordinates)</param>
            <param name="activeMatchOrdinal">is the current position in the search results</param>
            <param name="finalUpdate">is true if this is the last find notification.</param>
        </member>
        <member name="T:CefSharp.IFocusHandler">
            <summary>
            Implement this interface to handle events related to focus.
            The methods of this class will be called on the CEF UI thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IFocusHandler.OnGotFocus">
            <summary>
            Called when the browser component has received focus.
            </summary>
        </member>
        <member name="M:CefSharp.IFocusHandler.OnSetFocus(CefSharp.CefFocusSource)">
            <summary>
            Called when the browser component is requesting focus.
            </summary>
            <param name="source">Indicates where the focus request is originating from.</param>
            <returns>Return false to allow the focus to be set or true to cancel setting the focus.</returns>
        </member>
        <member name="M:CefSharp.IFocusHandler.OnTakeFocus(System.Boolean)">
            <summary>
            Called when the browser component is about to lose focus.
            For instance, if focus was on the last HTML element and the user pressed the TAB key.
            </summary>
            <param name="next">Will be true if the browser is giving focus to the next component
            and false if the browser is giving focus to the previous component.</param>
        </member>
        <member name="T:CefSharp.IGeolocationHandler">
            <summary>
            Implement this interface to handle events related to geolocation permission requests.
            The methods of this class will be called on the CEF UI thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IGeolocationHandler.OnRequestGeolocationPermission(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,System.Int32,CefSharp.IGeolocationCallback)">
            <summary>
            Called when a page requests permission to access geolocation information.
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
            <param name="requestingUrl">the URL requesting permission</param>
            <param name="requestId">the unique ID for the permission request</param>
            <param name="callback">Callback interface used for asynchronous continuation of geolocation permission requests.</param>
            <returns>Return true and call IGeolocationCallback.Continue() either in this method or at a later time to continue or cancel the request. Return false to cancel the request immediately.</returns>
        </member>
        <member name="M:CefSharp.IGeolocationHandler.OnCancelGeolocationPermission(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Int32)">
            <summary>
            Called when a geolocation access request is canceled.
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
            <param name="requestId">the unique ID for the permission request, as seen in <see cref="M:CefSharp.IGeolocationHandler.OnRequestGeolocationPermission(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,System.Int32,CefSharp.IGeolocationCallback)"/></param>
        </member>
        <member name="T:CefSharp.IJsDialogHandler">
            <summary>
            Implement this interface to handle events related to JavaScript dialogs.
            The methods of this class will be called on the CEF UI thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IJsDialogHandler.OnJSDialog(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,CefSharp.CefJsDialogType,System.String,System.String,CefSharp.IJsDialogCallback,System.Boolean@)">
            <summary>
            Called to run a JavaScript dialog.
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
            <param name="originUrl">originating url</param>
            <param name="dialogType">Dialog Type</param>
            <param name="messageText">Message Text</param>
            <param name="defaultPromptText">value will be specified for prompt dialogs only</param>
            <param name="callback">Callback can be executed inline or in an async fashion</param>
            <param name="suppressMessage">Set suppressMessage to true and return false to suppress the message (suppressing messages is preferable to immediately executing the callback as this is used to detect presumably malicious behavior like spamming alert messages in onbeforeunload). Set suppressMessage to false and return false to use the default implementation (the default implementation will show one modal dialog at a time and suppress any additional dialog requests until the displayed dialog is dismissed).</param>
            <returns>Return true if the application will use a custom dialog or if the callback has been executed immediately. Custom dialogs may be either modal or modeless. If a custom dialog is used the application must execute |callback| once the custom dialog is dismissed.</returns>
        </member>
        <member name="M:CefSharp.IJsDialogHandler.OnJSBeforeUnload(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,System.Boolean,CefSharp.IJsDialogCallback)">
            <summary>
            When leaving the page a Javascript dialog is displayed asking for user confirmation.
            Returning True allows you to implement a custom dialog or programatically handle.
            To cancel the unload return True and set allowUnload to False.
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
            <param name="message">message (optional)</param>
            <param name="isReload">indicates a page reload</param>
            <param name="callback">Callback can be executed inline or in an async fashion</param>
            <returns>Return false to use the default dialog implementation otherwise return true to handle</returns>
        </member>
        <member name="M:CefSharp.IJsDialogHandler.OnResetDialogState(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called to cancel any pending dialogs and reset any saved dialog state. Will
            be called due to events like page navigation irregardless of whether any
            dialogs are currently pending.
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
        </member>
        <member name="M:CefSharp.IJsDialogHandler.OnDialogClosed(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called when the default implementation dialog is closed.
            </summary>
            <param name="browserControl">the browser control</param>
            <param name="browser">the browser object</param>
        </member>
        <member name="T:CefSharp.IKeyboardHandler">
            <summary>
            Implement this interface to handle events related to keyboard input.
            </summary>
        </member>
        <member name="M:CefSharp.IKeyboardHandler.OnPreKeyEvent(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.KeyType,System.Int32,System.Int32,CefSharp.CefEventFlags,System.Boolean,System.Boolean@)">
            <summary>
            Called before a keyboard event is sent to the renderer.
            Return true if the event was handled or false
            otherwise. If the event will be handled in <see cref="M:CefSharp.IKeyboardHandler.OnKeyEvent(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.KeyType,System.Int32,System.Int32,CefSharp.CefEventFlags,System.Boolean)"/> as a keyboard
            shortcut set isKeyboardShortcut to true and return false.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this request is for.</param>
            <param name="browser">The browser instance.</param>
            <param name="type">Whether this was a key up/down/raw/etc...</param>
            <param name="windowsKeyCode">
            The Windows key code for the key event. This value is used by the DOM
            specification. Sometimes it comes directly from the event (i.e. on
            Windows) and sometimes it's determined using a mapping function. See
            WebCore/platform/chromium/KeyboardCodes.h for the list of values.
            </param>
            <param name="nativeKeyCode">The native key code. On Windows this appears to be in the format of WM_KEYDOWN/WM_KEYUP/etc... lParam data.</param>
            <param name="modifiers">What other modifier keys are currently down: Shift/Control/Alt/OS X Command/etc...</param>
            <param name="isSystemKey">
            Indicates whether the event is considered a "system key" event (see
            http://msdn.microsoft.com/en-us/library/ms646286(VS.85).aspx for details).
            </param>
            <param name="isKeyboardShortcut">See the summary for an explanation of when to set this to true.</param>
            <returns>Returns true if the event was handled or false otherwise.</returns>
        </member>
        <member name="M:CefSharp.IKeyboardHandler.OnKeyEvent(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.KeyType,System.Int32,System.Int32,CefSharp.CefEventFlags,System.Boolean)">
            <summary>
            Called after the renderer and JavaScript in the page has had a chance to
            handle the event. Return true if the keyboard event was handled or false otherwise.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this request is for.</param>
            <param name="browser">The browser instance.</param>
            <param name="type">Whether this was a key up/down/raw/etc...</param>
            <param name="windowsKeyCode">
            The Windows key code for the key event. This value is used by the DOM
            specification. Sometimes it comes directly from the event (i.e. on
            Windows) and sometimes it's determined using a mapping function. See
            WebCore/platform/chromium/KeyboardCodes.h for the list of values.
            </param>
            <param name="nativeKeyCode">The native key code. On Windows this appears to be in the format of WM_KEYDOWN/WM_KEYUP/etc... lParam data.</param>
            <param name="modifiers">What other modifier keys are currently down: Shift/Control/Alt/OS X Command/etc...</param>
            <param name="isSystemKey">
            Indicates whether the event is considered a "system key" event (see
            http://msdn.microsoft.com/en-us/library/ms646286(VS.85).aspx for details).
            </param>
            <returns>Return true if the keyboard event was handled or false otherwise.</returns>
        </member>
        <member name="T:CefSharp.ILifeSpanHandler">
            <summary>
            Implement this interface to handle events related to browser life span.
            The methods of this class will be called on the CEF UI thread unless otherwise indicated. 
            </summary>
        </member>
        <member name="M:CefSharp.ILifeSpanHandler.OnBeforePopup(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,System.String,System.String,CefSharp.WindowOpenDisposition,System.Boolean,CefSharp.IPopupFeatures,CefSharp.IWindowInfo,CefSharp.IBrowserSettings,System.Boolean@,CefSharp.IWebBrowser@)">
            <summary>
            Called before a popup window is created.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this request is for.</param>
            <param name="browser">The browser instance that launched this popup.</param>
            <param name="frame">The HTML frame that launched this popup.</param>
            <param name="targetUrl">The URL of the popup content. (This may be empty/null)</param>
            <param name="targetFrameName">The name of the popup. (This may be empty/null)</param>
            <param name="targetDisposition">The value indicates where the user intended to
            open the popup (e.g. current tab, new tab, etc)</param>
            <param name="userGesture">The value will be true if the popup was opened via explicit user gesture
            (e.g. clicking a link) or false if the popup opened automatically (e.g. via the DomContentLoaded event).</param>
            <param name="popupFeatures"> structure contains additional information about the requested popup window</param>
            <param name="windowInfo">window information</param>
            <param name="browserSettings">browser settings, defaults to source browsers</param>
            <param name="noJavascriptAccess">value indicates whether the new browser window should be scriptable
            and in the same process as the source browser.</param>
            <param name="newBrowser">EXPERIMENTAL - A newly created browser that will host the popup. Set to null
            for default behaviour.</param>
            <returns>To cancel creation of the popup window return true otherwise return false.</returns>
            <remarks>
            CEF documentation:
            
            Called on the IO thread before a new popup window is created. The |browser|
            and |frame| parameters represent the source of the popup request. The
            |target_url| and |target_frame_name| values may be empty if none were
            specified with the request. The |popupFeatures| structure contains
            information about the requested popup window. To allow creation of the
            popup window optionally modify |windowInfo|, |client|, |settings| and
            |no_javascript_access| and return false. To cancel creation of the popup
            window return true. The |client| and |settings| values will default to the
            source browser's values. The |no_javascript_access| value indicates whether
            the new browser window should be scriptable and in the same process as the
            source browser.
            </remarks>
        </member>
        <member name="M:CefSharp.ILifeSpanHandler.OnAfterCreated(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called after a new browser is created.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control that is realted to the window is closing.</param>
            <param name="browser">The browser instance</param>
        </member>
        <member name="M:CefSharp.ILifeSpanHandler.DoClose(CefSharp.IWebBrowser,CefSharp.IBrowser)">
             <summary>
             Called when a browser has recieved a request to close. This may result
             directly from a call to CefBrowserHost::CloseBrowser() or indirectly if the
             browser is a top-level OS window created by CEF and the user attempts to
             close the window. This method will be called after the JavaScript
             'onunload' event has been fired. It will not be called for browsers after
             the associated OS window has been destroyed (for those browsers it is no
             longer possible to cancel the close).
            
             If CEF created an OS window for the browser returning false will send an OS
             close notification to the browser window's top-level owner (e.g. WM_CLOSE
             on Windows, performClose: on OS-X and "delete_event" on Linux). If no OS
             window exists (window rendering disabled) returning false will cause the
             browser object to be destroyed immediately. Return true if the browser is
             parented to another window and that other window needs to receive close
             notification via some non-standard technique.
            
             If an application provides its own top-level window it should handle OS
             close notifications by calling CefBrowserHost::CloseBrowser(false) instead
             of immediately closing (see the example below). This gives CEF an
             opportunity to process the 'onbeforeunload' event and optionally cancel the
             close before DoClose() is called.
            
             The CefLifeSpanHandler::OnBeforeClose() method will be called immediately
             before the browser object is destroyed. The application should only exit
             after OnBeforeClose() has been called for all existing browsers.
            
             If the browser represents a modal window and a custom modal loop
             implementation was provided in CefLifeSpanHandler::RunModal() this callback
             should be used to restore the opener window to a usable state.
            
             By way of example consider what should happen during window close when the
             browser is parented to an application-provided top-level OS window.
             1.  User clicks the window close button which sends an OS close
                 notification (e.g. WM_CLOSE on Windows, performClose: on OS-X and
                 "delete_event" on Linux).
             2.  Application's top-level window receives the close notification and:
                 A. Calls CefBrowserHost::CloseBrowser(false).
                 B. Cancels the window close.
             3.  JavaScript 'onbeforeunload' handler executes and shows the close
                 confirmation dialog (which can be overridden via
                 CefJSDialogHandler::OnBeforeUnloadDialog()).
             4.  User approves the close.
             5.  JavaScript 'onunload' handler executes.
             6.  Application's DoClose() handler is called. Application will:
                 A. Set a flag to indicate that the next close attempt will be allowed.
                 B. Return false.
             7.  CEF sends an OS close notification.
             8.  Application's top-level window receives the OS close notification and
                 allows the window to close based on the flag from #6B.
             9.  Browser OS window is destroyed.
             10. Application's CefLifeSpanHandler::OnBeforeClose() handler is called and
                 the browser object is destroyed.
             11. Application exits by calling CefQuitMessageLoop() if no other browsers
                 exist.
             </summary>
             <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control that is realted to the window is closing.</param>
             <param name="browser">The browser instance - check if IsDisposed as it's possible when the browser is disposing</param>
             <returns>For default behaviour return false</returns>
        </member>
        <member name="M:CefSharp.ILifeSpanHandler.OnBeforeClose(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called before a CefBrowser window (either the main browser for <see cref="T:CefSharp.IWebBrowser"/>, 
            or one of its children)
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control that is realted to the window is closing.</param>
            <param name="browser">The browser instance</param>
        </member>
        <member name="T:CefSharp.ILoadHandler">
            <summary>
            Implement this interface to handle events related to browser load status.
            The methods of this interface will be called on the CEF UI thread. Blocking in these methods
            will likely cause your UI to become unresponsive and/or hang.
            </summary>
        </member>
        <member name="M:CefSharp.ILoadHandler.OnLoadingStateChange(CefSharp.IWebBrowser,CefSharp.LoadingStateChangedEventArgs)">
            <summary>
            Called when the loading state has changed. This callback will be executed twice
            once when loading is initiated either programmatically or by user action,
            and once when loading is terminated due to completion, cancellation of failure.
            This method will be called on the CEF UI thread.
            Blocking this thread will likely cause your UI to become unresponsive and/or hang.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="loadingStateChangedArgs">args</param>
        </member>
        <member name="M:CefSharp.ILoadHandler.OnFrameLoadStart(CefSharp.IWebBrowser,CefSharp.FrameLoadStartEventArgs)">
            <summary>
            Called when the browser begins loading a frame.
            The <see cref="P:CefSharp.FrameLoadEndEventArgs.Frame"/> value will never be empty
            Check the <see cref="P:CefSharp.IFrame.IsMain"/> method to see if this frame is the main frame.
            Multiple frames may be loading at the same time. Sub-frames may start or continue loading after the main frame load has ended.
            This method may not be called for a particular frame if the load request for that frame fails.
            For notification of overall browser load status use <see cref="M:CefSharp.ILoadHandler.OnLoadingStateChange(CefSharp.IWebBrowser,CefSharp.LoadingStateChangedEventArgs)"/> instead. 
            This method will be called on the CEF UI thread.
            Blocking this thread will likely cause your UI to become unresponsive and/or hang.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="frameLoadStartArgs">args</param>
            <remarks>Whilst thist may seem like a logical place to execute js, it's called before the DOM has been loaded, implement
            <see cref="M:CefSharp.IRenderProcessMessageHandler.OnContextCreated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)"/> as it's called when the underlying V8Context is created
            (Only called for the main frame at this stage)</remarks>
        </member>
        <member name="M:CefSharp.ILoadHandler.OnFrameLoadEnd(CefSharp.IWebBrowser,CefSharp.FrameLoadEndEventArgs)">
            <summary>
            Called when the browser is done loading a frame.
            The <see cref="P:CefSharp.FrameLoadEndEventArgs.Frame"/> value will never be empty
            Check the <see cref="P:CefSharp.IFrame.IsMain"/> method to see if this frame is the main frame.
            Multiple frames may be loading at the same time. Sub-frames may start or continue loading after the main frame load has ended.
            This method will always be called for all frames irrespective of whether the request completes successfully. 
            This method will be called on the CEF UI thread.
            Blocking this thread will likely cause your UI to become unresponsive and/or hang.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="frameLoadEndArgs">args</param>
        </member>
        <member name="M:CefSharp.ILoadHandler.OnLoadError(CefSharp.IWebBrowser,CefSharp.LoadErrorEventArgs)">
            <summary>
            Called when the resource load for a navigation fails or is canceled.
            <see cref="P:CefSharp.LoadErrorEventArgs.ErrorCode"/> is the error code number, <see cref="P:CefSharp.LoadErrorEventArgs.ErrorText"/> is the error text and
            <see cref="P:CefSharp.LoadErrorEventArgs.FailedUrl"/> is the URL that failed to load. See net\base\net_error_list.h
            for complete descriptions of the error codes.
            This method will be called on the CEF UI thread.
            Blocking this thread will likely cause your UI to become unresponsive and/or hang.
            </summary>
            <param name="browserControl">The <see cref="T:CefSharp.IWebBrowser"/> control this popup is related to.</param>
            <param name="loadErrorArgs">args</param>
        </member>
        <member name="T:CefSharp.IRenderProcessMessageHandler">
            <summary>
            Messages sent by the render process can be handled by implementing this
            interface.
            </summary>
        </member>
        <member name="M:CefSharp.IRenderProcessMessageHandler.OnContextCreated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)">
            <summary>
            OnContextCreated is called in the Render process immediately after a CefV8Context is created.
            An IPC message is immediately sent to notify the context has been created
            (should be safe to execute javascript). If the page has no javascript then no V8Context will be created
            and as a result this method will not be called. Currently only called for the Main frame <see cref="P:CefSharp.IFrame.IsMain"/>
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame.</param>
        </member>
        <member name="M:CefSharp.IRenderProcessMessageHandler.OnContextReleased(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)">
            <summary>
            OnContextReleased is called in the Render process immediately before the CefV8Context is released.
            An IPC message is immediately sent to notify the context has been released
            (cannot execute javascript this point). If the page had no javascript then the context would not have been created
            and as a result this method will not be called. Currently only called for the Main frame <see cref="P:CefSharp.IFrame.IsMain"/>
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame.</param>
        </member>
        <member name="M:CefSharp.IRenderProcessMessageHandler.OnFocusedNodeChanged(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IDomNode)">
            <summary>
            Invoked when an element in the UI gains focus (or possibly no
            element gains focus; i.e. an element lost focus).
            </summary>
            <param name="browserControl">The ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="frame">The frame object</param>
            <param name="node">An object with information about the node (if any) that has focus.</param>
        </member>
        <member name="T:CefSharp.IRequestContextHandler">
            <summary>
            Implement this interface to provide handler implementations. The handler
            instance will not be released until all objects related to the context have
            been destroyed. Implement this interface to cancel loading of specific plugins
            </summary>
        </member>
        <member name="M:CefSharp.IRequestContextHandler.GetCookieManager">
            <summary>
            Called on the browser process IO thread to retrieve the cookie manager. If
            this method returns NULL the default cookie manager retrievable via
            IRequestContext.GetDefaultCookieManager() will be used.
            </summary>
            <returns>If
            this method returns null the default cookie manager retrievable via
            IRequestContext.GetDefaultCookieManager() will be used..</returns>
        </member>
        <member name="M:CefSharp.IRequestContextHandler.OnBeforePluginLoad(System.String,System.String,System.Boolean,System.String,CefSharp.WebPluginInfo,CefSharp.PluginPolicy@)">
            <summary>
            Called on the CEF IO thread before a plugin instance is loaded.
            The default plugin policy can be set at runtime using the `--plugin-policy=[allow|detect|block]` command-line flag.
            </summary>
            <param name="mimeType">is the mime type of the plugin that will be loaded</param>
            <param name="url">is the content URL that the plugin will load and may be empty</param>
            <param name="isMainFrame">will be true if the plugin is being loaded in the main (top-level) frame</param>
            <param name="topOriginUrl">is the URL for the top-level frame that contains the plugin</param>
            <param name="pluginInfo">includes additional information about the plugin that will be loaded</param>
            <param name="pluginPolicy">Modify and return true to change the policy.</param>
            <returns>Return false to use the recommended policy. Modify and return true to change the policy.</returns>
        </member>
        <member name="T:CefSharp.IBrowserSettings">
            <summary>
            Interface representing browser initialization settings. 
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.StandardFontFamily">
            <summary>
            StandardFontFamily
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.FixedFontFamily">
            <summary>
            FixedFontFamily
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.SerifFontFamily">
            <summary>
            SerifFontFamily
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.SansSerifFontFamily">
            <summary>
            SansSerifFontFamily
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.CursiveFontFamily">
            <summary>
            CursiveFontFamily
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.FantasyFontFamily">
            <summary>
            FantasyFontFamily
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.DefaultFontSize">
            <summary>
            DefaultFontSize
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.DefaultFixedFontSize">
            <summary>
            DefaultFixedFontSize
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.MinimumFontSize">
            <summary>
            MinimumFontSize
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.MinimumLogicalFontSize">
            <summary>
            MinimumLogicalFontSize
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.DefaultEncoding">
            <summary>
            Default encoding for Web content. If empty "ISO-8859-1" will be used. Also
            configurable using the "default-encoding" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.RemoteFonts">
            <summary>
            Controls the loading of fonts from remote sources. Also configurable using
            the "disable-remote-fonts" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.Javascript">
            <summary>
            Controls whether JavaScript can be executed.
            (Disable javascript)
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.JavascriptCloseWindows">
            <summary>
            Controls whether JavaScript can be used to close windows that were not
            opened via JavaScript. JavaScript can still be used to close windows that
            were opened via JavaScript. Also configurable using the
            "disable-javascript-close-windows" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.JavascriptAccessClipboard">
            <summary>
            Controls whether JavaScript can access the clipboard. Also configurable
            using the "disable-javascript-access-clipboard" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.JavascriptDomPaste">
            <summary>
            Controls whether DOM pasting is supported in the editor via
            execCommand("paste"). The |javascript_access_clipboard| setting must also
            be enabled. Also configurable using the "disable-javascript-dom-paste"
            command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.Plugins">
            <summary>
            Controls whether any plugins will be loaded. Also configurable using the
            "disable-plugins" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.UniversalAccessFromFileUrls">
            <summary>
            Controls whether file URLs will have access to all URLs. Also configurable
            using the "allow-universal-access-from-files" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.FileAccessFromFileUrls">
            <summary>
            Controls whether file URLs will have access to other file URLs. Also
            configurable using the "allow-access-from-files" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.WebSecurity">
            <summary>
            Controls whether web security restrictions (same-origin policy) will be
            enforced. Disabling this setting is not recommend as it will allow risky
            security behavior such as cross-site scripting (XSS). Also configurable
            using the "disable-web-security" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.ImageLoading">
            <summary>
            Controls whether image URLs will be loaded from the network. A cached image
            will still be rendered if requested. Also configurable using the
            "disable-image-loading" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.ImageShrinkStandaloneToFit">
            <summary>
            Controls whether standalone images will be shrunk to fit the page. Also
            configurable using the "image-shrink-standalone-to-fit" command-line
            switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.TextAreaResize">
            <summary>
            Controls whether text areas can be resized. Also configurable using the
            "disable-text-area-resize" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.TabToLinks">
            <summary>
            Controls whether the tab key can advance focus to links. Also configurable
            using the "disable-tab-to-links" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.LocalStorage">
            <summary>
            Controls whether local storage can be used. Also configurable using the
            "disable-local-storage" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.Databases">
            <summary>
            Controls whether databases can be used. Also configurable using the
            "disable-databases" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.ApplicationCache">
            <summary>
            Controls whether the application cache can be used. Also configurable using
            the "disable-application-cache" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.WebGl">
            <summary>
            Controls whether WebGL can be used. Note that WebGL requires hardware
            support and may not work on all systems even when enabled. Also
            configurable using the "disable-webgl" command-line switch.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.BackgroundColor">
            <summary>
            Opaque background color used for the browser before a document is loaded
            and when no document color is specified. By default the background color
            will be the same as CefSettings.BackgroundColor. Only the RGB compontents
            of the specified value will be used. The alpha component must greater than
            0 to enable use of the background color but will be otherwise ignored.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.AcceptLanguageList">
            <summary>
            Comma delimited ordered list of language codes without any whitespace that
            will be used in the "Accept-Language" HTTP header. May be overridden on a
            per-browser basis using the CefBrowserSettings.AcceptLanguageList value.
            If both values are empty then "en-US,en" will be used. Can be overridden
            for individual RequestContext instances via the
            RequestContextSettings.AcceptLanguageList value.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserSettings.WindowlessFrameRate">
            <summary>
            The maximum rate in frames per second (fps) that CefRenderHandler::OnPaint
            will be called for a windowless browser. The actual fps may be lower if
            the browser cannot generate frames at the requested rate. The minimum
            value is 1 and the maximum value is 60 (default 30). This value can also be
            changed dynamically via IBrowserHost.SetWindowlessFrameRate.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.CommandLineArgsParser">
            <summary>
            Simple helper class used for checking/parsing command line arguments
            </summary>
        </member>
        <member name="T:CefSharp.IPopupFeatures">
            <summary>
            Class representing popup window features. 
            </summary>
        </member>
        <member name="T:CefSharp.IRequestContext">
            <summary>
            A request context provides request handling for a set of related browser or URL request objects.
            A request context can be specified when creating a new browser by setting the 
            <see cref="P:CefSharp.IWebBrowser.RequestContext"/> property (Passing in via the constructor for the OffScreen
            control is preferred).
            Browser objects with different request contexts will never be hosted in the same render process.
            Browser objects with the same request context may or may not be hosted in the same render process
            depending on the process model.Browser objects created indirectly via the JavaScript window.open
            function or targeted links will share the same render process and the same request context as
            the source browser.
            </summary>
        </member>
        <member name="M:CefSharp.IRequestContext.IsSame(CefSharp.IRequestContext)">
            <summary>
            Returns true if this object is pointing to the same context object.
            </summary>
            <param name="context">context to compare</param>
            <returns>Returns true if the same</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.IsSharingWith(CefSharp.IRequestContext)">
            <summary>
            Returns true if this object is sharing the same storage as the specified context.
            </summary>
            <param name="context">context to compare</param>
            <returns>Returns true if same storage</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.GetDefaultCookieManager(CefSharp.ICompletionCallback)">
            <summary>
            Returns the default cookie manager for this object. This will be the global
            cookie manager if this object is the global request context. Otherwise,
            this will be the default cookie manager used when this request context does
            not receive a value via IRequestContextHandler.GetCookieManager(). 
            </summary>
            <param name="callback">If callback is non-NULL it will be executed asnychronously on the CEF IO thread
            after the manager's storage has been initialized.</param>
            <returns>Returns the default cookie manager for this object</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.RegisterSchemeHandlerFactory(System.String,System.String,CefSharp.ISchemeHandlerFactory)">
            <summary>
            Register a scheme handler factory for the specified schemeName and optional domainName.
            An empty domainName value for a standard scheme will cause the factory to match all domain
            names. The domainName value will be ignored for non-standard schemes. If schemeName is
            a built-in scheme and no handler is returned by factory then the built-in scheme handler
            factory will be called. If schemeName is a custom scheme then you must also implement the
            CefApp::OnRegisterCustomSchemes() method in all processes. This function may be called multiple
            times to change or remove the factory that matches the specified schemeName and optional
            domainName.
            </summary>
            <param name="schemeName">Scheme Name</param>
            <param name="domainName">Optional domain name</param>
            <param name="factory">Scheme handler factory</param>
            <returns>Returns false if an error occurs.</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.ClearSchemeHandlerFactories">
            <summary>
            Clear all registered scheme handler factories. 
            </summary>
            <returns>Returns false on error.</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.PurgePluginListCache(System.Boolean)">
            <summary>
            Tells all renderer processes associated with this context to throw away
            their plugin list cache. If reloadPages is true they will also reload
            all pages with plugins. RequestContextHandler.OnBeforePluginLoad may
            be called to rebuild the plugin list cache.
            </summary>
            <param name="reloadPages">reload any pages with pluginst</param>
        </member>
        <member name="M:CefSharp.IRequestContext.HasPreference(System.String)">
            <summary>
            Returns true if a preference with the specified name exists. This method
            must be called on the CEF UI thread.
            </summary>
            <param name="name">name of preference</param>
            <returns>bool if the preference exists</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
            Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
            executed on the CEF UI thread, so can be called directly.
            When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
            application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.IRequestContext.GetPreference(System.String)">
            <summary>
            Returns the value for the preference with the specified name. Returns
            NULL if the preference does not exist. The returned object contains a copy
            of the underlying preference value and modifications to the returned object
            will not modify the underlying preference value. This method must be called
            on the CEF UI thread.
            </summary>
            <param name="name">preference name</param>
            <returns>Returns the value for the preference with the specified name</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
            Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
            executed on the CEF UI thread, so can be called directly.
            When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
            application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.IRequestContext.GetAllPreferences(System.Boolean)">
            <summary>
            Returns all preferences as a dictionary. The returned
            object contains a copy of the underlying preference values and
            modifications to the returned object will not modify the underlying
            preference values. This method must be called on the browser process UI
            thread.
            </summary>
            <param name="includeDefaults">If true then
            preferences currently at their default value will be included.</param>
            <returns>Preferences (dictionary can have sub dictionaries)</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.CanSetPreference(System.String)">
            <summary>
            Returns true if the preference with the specified name can be modified
            using SetPreference. As one example preferences set via the command-line
            usually cannot be modified. This method must be called on the CEF UI thread.
            </summary>
            <param name="name">preference key</param>
            <returns>Returns true if the preference with the specified name can be modified
            using SetPreference</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
            Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
            executed on the CEF UI thread, so can be called directly.
            When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
            application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.IRequestContext.SetPreference(System.String,System.Object,System.String@)">
            <summary>
            Set the value associated with preference name. If value is null the
            preference will be restored to its default value. If setting the preference
            fails then error will be populated with a detailed description of the
            problem. This method must be called on the CEF UI thread.
            Preferences set via the command-line usually cannot be modified.
            </summary>
            <param name="name">preference key</param>
            <param name="value">preference value</param>
            <param name="error">out error</param>
            <returns>Returns true if the value is set successfully and false otherwise.</returns>
            /// <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
            Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
            executed on the CEF UI thread, so can be called directly.
            When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
            application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.IRequestContext.ClearCertificateExceptions(CefSharp.ICompletionCallback)">
            <summary>
            Clears all certificate exceptions that were added as part of handling
            <see cref="M:CefSharp.IRequestHandler.OnCertificateError(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefErrorCode,System.String,CefSharp.ISslInfo,CefSharp.IRequestCallback)"/>. If you call this it is
            recommended that you also call <see cref="M:CefSharp.IRequestContext.CloseAllConnections(CefSharp.ICompletionCallback)"/> or you risk not
            being prompted again for server certificates if you reconnect quickly.
            </summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
            completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.IRequestContext.CloseAllConnections(CefSharp.ICompletionCallback)">
            <summary>
            Clears all active and idle connections that Chromium currently has.
            This is only recommended if you have released all other CEF objects but
            don't yet want to call Cef.Shutdown().
            </summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
            completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.IRequestContext.ResolveHostAsync(System.Uri)">
            <summary>
            Attempts to resolve origin to a list of associated IP addresses.
            </summary>
            <param name="origin">host name to resolve</param>
            <returns>A task that represents the Resoolve Host operation. The value of the TResult parameter contains ResolveCallbackResult.</returns>
        </member>
        <member name="M:CefSharp.IRequestContext.ResolveHostCached(System.Uri,System.Collections.Generic.IList{System.String}@)">
            <summary>
            Attempts to resolve origin to a list of associated IP addresses using
            cached data. This method must be called on the CEF IO thread. Use
            Cef.IOThreadTaskFactory to execute on that thread.
            </summary>
            <param name="origin">host name to resolve</param>
            <param name="resolvedIpAddresses">list of resolved IP
            addresses or empty list if no cached data is available.</param>
            <returns> Returns <see cref="F:CefSharp.CefErrorCode.None"/> on success</returns>
        </member>
        <member name="P:CefSharp.IRequestContext.IsGlobal">
            <summary>
            Returns true if this object is the global context. The global context is
            used by default when creating a browser or URL request with a NULL context
            argument.
            </summary>
        </member>
        <member name="P:CefSharp.IRequestContext.CachePath">
            <summary>
            Returns the cache path for this object. If empty an "incognito mode"
            in-memory cache is being used.
            </summary>
        </member>
        <member name="T:CefSharp.IResponseFilter">
            <summary>
            Implement this interface to filter resource response content.
            The methods of this class will be called on the CEF IO thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IResponseFilter.InitFilter">
            <summary>
            Initialize the response filter. Will only be called a single time.
            The filter will not be installed if this method returns false.
            </summary>
            <returns>The filter will not be installed if this method returns false.</returns>
        </member>
        <member name="M:CefSharp.IResponseFilter.Filter(System.IO.Stream,System.Int64@,System.IO.Stream,System.Int64@)">
            <summary>
            Called to filter a chunk of data.
            This method will be called repeatedly until there is no more data to filter (resource response is complete),
            dataInRead matches dataIn.Length (all available pre-filter bytes have been read), and the method
            returns FilterStatus.Done or FilterStatus.Error.
            </summary>
            <param name="dataIn">is a Stream wrapping the underlying input buffer containing pre-filter data. Can be null.</param>
            <param name="dataInRead">Set to the number of bytes that were read from dataIn</param>
            <param name="dataOut">is a Stream wrapping the underlying output buffer that can accept filtered output data.
            Check dataOut.Length for maximum buffer size</param>
            <param name="dataOutWritten">Set to the number of bytes that were written into dataOut</param>
            <returns>If some or all of the pre-filter data was read successfully but more data is needed in order
            to continue filtering (filtered output is pending) return FilterStatus.NeedMoreData. If some or all of the pre-filter
            data was read successfully and all available filtered output has been written return FilterStatus.Done. If an error
            occurs during filtering return FilterStatus.Error. </returns>
            <remarks>Do not keep a reference to the buffers(Streams) passed to this method.</remarks>
        </member>
        <member name="T:CefSharp.ModelBinding.BindingContext">
            <summary>
            Model binding context object
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingContext.DestinationType">
            <summary>
            Binding destination type
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingContext.GenericType">
            <summary>
            The generic type of a collection is only used when DestinationType is a enumerable.
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingContext.Model">
            <summary>
            The current model object (or null for body deserialization)
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingContext.ValidModelBindingMembers">
            <summary>
            DestinationType properties that are not black listed
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingContext.Object">
            <summary>
            The incoming data fields
            </summary>
        </member>
        <member name="T:CefSharp.ModelBinding.BindingMemberInfo">
            <summary>
            Represents a bindable member of a type, which can be a property or a field.
            </summary>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.#ctor(System.Reflection.PropertyInfo)">
            <summary>
            Constructs a BindingMemberInfo instance for a property.
            </summary>
            <param name="propertyInfo">The bindable property to represent.</param>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.#ctor(System.Reflection.FieldInfo)">
            <summary>
            Constructs a BindingMemberInfo instance for a field.
            </summary>
            <param name="fieldInfo">The bindable field to represent.</param>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.SetValue(System.Object,System.Object)">
            <summary>
            Sets the value from a specified object associated with the property or field represented by this BindingMemberInfo.
            </summary>
            <param name="destinationObject">The object whose property or field should be assigned.</param>
            <param name="newValue">The value to assign in the specified object to this BindingMemberInfo's property or field.</param>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.Equals(System.Object)">
            <inherit-doc/>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.Equals(CefSharp.ModelBinding.BindingMemberInfo)">
            <summary>
            Compares two BindingMemberInfo's with eachother on their respective values rather then their reference
            </summary>
            <param name="obj">the other BindingMemberInfo</param>
            <returns>true when they are equal and false otherwise</returns>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.GetHashCode">
            <inherit-doc/>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.Collect``1">
            <summary>
            Returns an enumerable sequence of bindable properties for the specified type.
            </summary>
            <typeparam name="T">The type to enumerate.</typeparam>
            <returns>Bindable properties.</returns>
        </member>
        <member name="M:CefSharp.ModelBinding.BindingMemberInfo.Collect(System.Type)">
            <summary>
            Returns an enumerable sequence of bindable properties for the specified type.
            </summary>
            <param name="type">The type to enumerate.</param>
            <returns>Bindable properties.</returns>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingMemberInfo.MemberInfo">
            <summary>
            Gets a reference to the MemberInfo that this BindingMemberInfo represents. This can be a property or a field.
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingMemberInfo.Name">
            <summary>
            Gets the name of the property or field represented by this BindingMemberInfo.
            </summary>
        </member>
        <member name="P:CefSharp.ModelBinding.BindingMemberInfo.PropertyType">
            <summary>
            Gets the data type of the property or field represented by this BindingMemberInfo.
            </summary>
        </member>
        <member name="T:CefSharp.ModelBinding.DefaultBinder">
            <summary>
            Default binder - used as a fallback when a specific modelbinder
            is not available.
            </summary>
        </member>
        <member name="T:CefSharp.ModelBinding.IBinder">
            <summary>
            Binds incoming request data to a model type
            </summary>
        </member>
        <member name="M:CefSharp.ModelBinding.IBinder.Bind(System.Object,System.Type)">
            <summary>
            Bind to the given model type
            </summary>
            <param name="obj">object to be converted into a model</param>
            <param name="modelType">Model type to bind to</param>
            <returns>Bound model</returns>
        </member>
        <member name="M:CefSharp.ModelBinding.DefaultBinder.#ctor(CefSharp.ModelBinding.IFieldNameConverter)">
            <summary>
            DefaultBinder constructor
            </summary>
            <param name="fieldNameConverter">used to convert field names to property names</param>
        </member>
        <member name="M:CefSharp.ModelBinding.DefaultBinder.Bind(System.Object,System.Type)">
            <summary>
            Bind to the given model type
            </summary>
            <param name="obj">object to be converted into a model</param>
            <param name="modelType">Model type to bind to</param>
            <returns>Bound model</returns>
        </member>
        <member name="P:CefSharp.ModelBinding.DefaultBinder.BlackListedPropertyNames">
            <summary>
            List of property names to be ignored
            </summary>
        </member>
        <member name="T:CefSharp.ModelBinding.DefaultFieldNameConverter">
            <summary>
            Default field name converter
            Converts camel case to pascal case
            </summary>
        </member>
        <member name="T:CefSharp.ModelBinding.IFieldNameConverter">
            <summary>
            Provides the capability to supply a convention to
            convert form field names to property names if required.
            </summary>
        </member>
        <member name="M:CefSharp.ModelBinding.IFieldNameConverter.Convert(System.String)">
            <summary>
            Converts a field name to a property name
            </summary>
            <param name="fieldName">Field name</param>
            <returns>Property name</returns>
        </member>
        <member name="M:CefSharp.ModelBinding.DefaultFieldNameConverter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.ModelBinding.DefaultFieldNameConverter"/> class.
            </summary>
        </member>
        <member name="M:CefSharp.ModelBinding.DefaultFieldNameConverter.Convert(System.String)">
            <summary>
            Converts a field name to a property name
            </summary>
            <param name="fieldName">Field name</param>
            <returns>Property name</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:CefSharp.ModelBinding.IMethodInterceptor" -->
        <member name="M:CefSharp.ModelBinding.IMethodInterceptor.Intercept(System.Func{System.Object},System.String)">
            <summary>
            Called before the method is invokved. You are now responsible for evaluating
            the function and returning the result.
            </summary>
            <param name="method">A Func that represents the method to be called</param>
            <param name="methodName">Name of the method to be called</param>
            <returns>The method result</returns>
            <example>
            object IMethodInterceptor.Intercept(Func&lt;object&gt; method, string methodName)
            {
              object result = method();
              Debug.WriteLine("Called " + methodName);
              return result;
             }
            </example>
        </member>
        <member name="T:CefSharp.ModelBinding.ModelBindingExtensions">
            <summary>
            Containing extensions for the <see cref="T:System.Type"/> object.
            </summary>
        </member>
        <member name="M:CefSharp.ModelBinding.ModelBindingExtensions.IsArray(System.Type)">
            <summary>
            Checks if a type is an array or not
            </summary>
            <param name="source">The type to check.</param>
            <returns><see langword="true" /> if the type is an array, otherwise <see langword="false" />.</returns>
        </member>
        <member name="M:CefSharp.ModelBinding.ModelBindingExtensions.IsCollection(System.Type)">
            <summary>
            Checks if a type is an collection or not
            </summary>
            <param name="source">The type to check.</param>
            <returns><see langword="true" /> if the type is a collection, otherwise <see langword="false" />.</returns>
        </member>
        <member name="M:CefSharp.ModelBinding.ModelBindingExtensions.IsEnumerable(System.Type)">
            <summary>
            Checks if a type is enumerable or not
            </summary>
            <param name="source">The type to check.</param>
            <returns><see langword="true" /> if the type is an enumerable, otherwise <see langword="false" />.</returns>
        </member>
        <member name="T:CefSharp.NavigationEntry">
            <summary>
            Represents an entry in navigation history.
            </summary>
        </member>
        <member name="M:CefSharp.NavigationEntry.#ctor(System.Boolean,System.DateTime,System.String,System.Int32,System.String,System.String,CefSharp.TransitionType,System.String,System.Boolean,System.Boolean,CefSharp.SslStatus)">
            <summary>
            NavigationEntry
            </summary>
            <param name="completionTime">completionTime</param>
            <param name="displayUrl">displayUrl</param>
            <param name="httpStatusCode">httpStatusCode</param>
            <param name="originalUrl">originalUrl</param>
            <param name="title">title</param>
            <param name="transitionType">transitionType</param>
            <param name="url">url</param>
            <param name="hasPostData">hasPostData</param>
            <param name="isValid">isValid</param>
            <param name="isCurrent">is the current entry</param>
            <param name="sslStatus">the ssl status</param>
        </member>
        <member name="P:CefSharp.NavigationEntry.CompletionTime">
            <summary>
            Returns the time for the last known successful navigation completion.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.DisplayUrl">
            <summary>
            Returns a display-friendly version of the URL.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.HttpStatusCode">
            <summary>
            Returns the HTTP status code for the last known successful navigation response.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.OriginalUrl">
            <summary>
            Returns the original URL that was entered by the user before any redirects.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.Title">
            <summary>
            Returns the title set by the page.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.TransitionType">
            <summary>
            Returns the transition type which indicates what the user did to move to this page from the previous page.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.Url">
            <summary>
            Returns the actual URL of the page.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.HasPostData">
            <summary>
            Returns true if this navigation includes post data.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.IsValid">
            <summary>
            Returns true if this object is valid.
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.IsCurrent">
            <summary>
            If true if this entry is the currently loaded navigation entry
            </summary>
        </member>
        <member name="P:CefSharp.NavigationEntry.SslStatus">
            <summary>
            Returns the SSL information for this navigation entry.
            </summary>
        </member>
        <member name="T:CefSharp.PdfPrintSettings">
            <summary>
            Print to Pdf Settings
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.HeaderFooterTitle">
            <summary>
            Page title to display in the header. Only used if <see cref="P:CefSharp.PdfPrintSettings.HeaderFooterEnabled"/>
            is set to true.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.HeaderFooterUrl">
            <summary>
            URL to display in the footer. Only used if <see cref="P:CefSharp.PdfPrintSettings.HeaderFooterEnabled"/> is set
            to true.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.PageWidth">
            <summary>
            Output page size in microns. If either of these values is less than or
            equal to zero then the default paper size (A4) will be used.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.PageHeight">
            <summary>
            Output page size in microns. If either of these values is less than or
            equal to zero then the default paper size (A4) will be used.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.MarginLeft">
            <summary>
            Margin in millimeters. Only used if MarginType is set to Custom.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.MarginTop">
            <summary>
            Margin in millimeters. Only used if MarginType is set to Custom.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.MarginRight">
            <summary>
            Margin in millimeters. Only used if MarginType is set to Custom.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.MarginBottom">
            <summary>
            Margin in millimeters. Only used if MarginType is set to Custom.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.MarginType">
            <summary>
            Margin type.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.ScaleFactor">
            <summary>
            Scale the PDF by the specified amount, defaults to 100%.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.HeaderFooterEnabled">
            <summary>
            Set to true to print headers and footers or false to not print
            headers and footers.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.SelectionOnly">
            <summary>
            Set to true to print the selection only or false to print all.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.Landscape">
            <summary>
            Set to true for landscape mode or false for portrait mode.
            </summary>
        </member>
        <member name="P:CefSharp.PdfPrintSettings.BackgroundsEnabled">
            <summary>
            Set to true to print background graphics or false to not print
            background graphics.
            </summary>
        </member>
        <member name="T:CefSharp.ICookieManager">
            <summary>
            Used for managing cookies. The methods may be called on any thread unless otherwise indicated.
            </summary>
        </member>
        <member name="M:CefSharp.ICookieManager.DeleteCookies(System.String,System.String,CefSharp.IDeleteCookiesCallback)">
            <summary>
            Deletes all cookies that matches all the provided parameters. If both <paramref name="url"/> and <paramref name="name"/> are empty, all cookies will be deleted.
            Cookies can alternately be deleted using the Visit*Cookies() methods. 
            This method will be executed on the CEF IO thread in an async fashion, to be notified upon completion implement <see cref="T:CefSharp.IDeleteCookiesCallback"/>
            and pass in as <paramref name="callback"/>
            </summary>
            <param name="url">The cookie URL. If an empty string is provided, any URL will be matched.</param>
            <param name="name">The name of the cookie. If an empty string is provided, any URL will be matched.</param>
            <param name="callback">If non-NULL it will be executed asnychronously on the CEF IO thread after the cookies have been deleted.</param>
            <returns>Returns false if a non-empty invalid URL is specified, or if cookies cannot be accessed; otherwise, true.</returns>
        </member>
        <member name="M:CefSharp.ICookieManager.SetCookie(System.String,CefSharp.Cookie,CefSharp.ISetCookieCallback)">
            <summary>
            Sets a cookie given a valid URL and explicit user-provided cookie attributes. This function expects each attribute to be well-formed. It will check for disallowed
            characters (e.g. the ';' character is disallowed within the cookie value attribute) and will return false without setting
            the cookie if such characters are found.
            This method will be executed on the CEF IO thread in an async fashion, to be notified upon completion implement <see cref="T:CefSharp.ISetCookieCallback"/>
            and pass in as <paramref name="callback"/>
            </summary>
            <param name="url">The cookie URL</param>
            <param name="cookie">The cookie</param>
            <param name="callback">If non-NULL it will be executed asnychronously on the CEF IO thread after the cookie has been set.</param>
            <returns>returns false if the cookie cannot be set (e.g. if illegal charecters such as ';' are used); otherwise true.</returns>
        </member>
        <member name="M:CefSharp.ICookieManager.SetStoragePath(System.String,System.Boolean,CefSharp.ICompletionCallback)">
            <summary>
            Sets the directory path that will be used for storing cookie data. If <paramref name="path"/> is empty data will be stored in 
            memory only. Otherwise, data will be stored at the specified path. To persist session cookies (cookies without an expiry 
            date or validity interval) set <paramref name="persistSessionCookies"/> to true. Session cookies are generally intended to be transient and 
            most Web browsers do not persist them.
            </summary>
            <param name="path">The file path to write cookies to.</param>
            <param name="persistSessionCookies">A flag that determines whether session cookies will be persisted or not.</param>
            <param name="callback">If non-NULL it will be executed asnychronously on the CEF IO thread after the
            manager's storage has been initialized</param>
            <returns>Returns false if cookies cannot be accessed</returns>
        </member>
        <member name="M:CefSharp.ICookieManager.SetSupportedSchemes(System.String[],CefSharp.ICompletionCallback)">
            <summary>
            Set the schemes supported by this manager. By default only "http" and "https" schemes are supported. Must be called before any cookies are accessed.
            </summary>
            <param name="schemes">The list of supported schemes.</param>
            <param name="callback">If non-NULL it will be executed asnychronously on the CEF IO thread after the change has been applied.</param>
        </member>
        <member name="M:CefSharp.ICookieManager.VisitAllCookies(CefSharp.ICookieVisitor)">
            <summary>
            Visits all cookies using the provided Cookie Visitor. The returned cookies are sorted by longest path, then by earliest creation date.
            </summary>
            <param name="visitor">A user-provided Cookie Visitor implementation.</param>
            <returns>Returns false if cookies cannot be accessed; otherwise, true.</returns>
        </member>
        <member name="M:CefSharp.ICookieManager.VisitUrlCookies(System.String,System.Boolean,CefSharp.ICookieVisitor)">
            <summary>
            Visits a subset of the cookies. The results are filtered by the given url scheme, host, domain and path. 
            If <paramref name="includeHttpOnly"/> is true, HTTP-only cookies will also be included in the results. The returned cookies 
            are sorted by longest path, then by earliest creation date.
            </summary>
            <param name="url">The URL to use for filtering a subset of the cookies available.</param>
            <param name="includeHttpOnly">A flag that determines whether HTTP-only cookies will be shown in results.</param>
            <param name="visitor">A user-provided Cookie Visitor implementation.</param>
            <returns>Returns false if cookies cannot be accessed; otherwise, true.</returns>
        </member>
        <member name="M:CefSharp.ICookieManager.FlushStore(CefSharp.ICompletionCallback)">
            <summary>
            Flush the backing store (if any) to disk
            This method will be executed on the CEF IO thread in an async fashion, to be notified upon completion implement <see cref="T:CefSharp.ICompletionCallback"/>
            and pass in as <paramref name="callback"/>
            </summary>
            <param name="callback">If non-NULL it will be executed asnychronously on the CEF IO thread after the flush is complete.</param>
            <returns>Returns false if cookies cannot be accessed.</returns>
        </member>
        <member name="P:CefSharp.ICookieManager.IsDisposed">
            <summary>
            Returns true if disposed
            </summary>
        </member>
        <member name="M:CefSharp.IMenuModel.Clear">
            <summary>
            Remove all menu items. Can be used to disable the context menu. Returns true on success.
            </summary>
            <returns>Returns true on success</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetLabelAt(System.Int32)">
            <summary>
            Returns the label at the specified index or empty if not found due to
            invalid range or the index being a separator.
            </summary>
            <param name="index">specified index</param>
            <returns>Label or empty if not found due to invalid range or the index being a separator.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetCommandIdAt(System.Int32)">
            <summary>
            Returns the command id at the specified index or -1 if not found due to invalid range or the index being a separator.
            </summary>
            <param name="index">the index</param>
            <returns>Command or -1 if not found due to invalid range or the index being a separator.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.Remove(CefSharp.CefMenuCommand)">
            <summary>
            Removes the item with the specified commandId.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns true on success</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.AddItem(CefSharp.CefMenuCommand,System.String)">
            <summary>
            Add an item to the menu. 
            </summary>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.AddSeparator">
            <summary>
            Add a separator to the menu. 
            </summary>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.AddCheckItem(CefSharp.CefMenuCommand,System.String)">
            <summary>
            Add a check item to the menu.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.AddRadioItem(CefSharp.CefMenuCommand,System.String,System.Int32)">
            <summary>
            Add a radio item to the menu. Only a single item with the specified groupId can be checked at a time.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <param name="groupId">the group id</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.AddSubMenu(CefSharp.CefMenuCommand,System.String)">
            <summary>
            Add a sub-menu to the menu. The new sub-menu is returned.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <returns>Returns the newly created <see cref="T:CefSharp.IMenuModel"/>.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.InsertSeparatorAt(System.Int32)">
            <summary>
            Insert a separator in the menu at the specified index. 
            </summary>
            <param name="index">index</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.InsertItemAt(System.Int32,CefSharp.CefMenuCommand,System.String)">
            <summary>
            Insert an item in the menu at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <returns>Returns true on success.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.InsertCheckItemAt(System.Int32,CefSharp.CefMenuCommand,System.String)">
            <summary>
            Insert a check item in the menu at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.InsertRadioItemAt(System.Int32,CefSharp.CefMenuCommand,System.String,System.Int32)">
            <summary>
            Insert a radio item in the menu at the specified index.
            Only a single item with the specified groupId can be checked at a time.
            </summary>
            <param name="index">index</param>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <param name="groupId">the group id</param>
            <returns>Returns true on success.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.InsertSubMenuAt(System.Int32,CefSharp.CefMenuCommand,System.String)">
            <summary>
            Insert a sub-menu in the menu at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="commandId">the command Id</param>
            <param name="label">the label of the item</param>
            <returns>Returns the newly created <see cref="T:CefSharp.IMenuModel"/>.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.RemoveAt(System.Int32)">
            <summary>
            Removes the item at the specified index.
            </summary>
            <param name="index">index</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetIndexOf(CefSharp.CefMenuCommand)">
            <summary>
            Returns the index associated with the specified commandId or -1 if not found due to the command id not existing in the menu.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns the index associated with the specified commandId or -1 if not found due to the command id not existing in the menu.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetCommandIdAt(System.Int32,CefSharp.CefMenuCommand)">
            <summary>
            Sets the command id at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="commandId">the command Id</param>
            <returns>Returns true on success.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.GetLabel(CefSharp.CefMenuCommand)">
            <summary>
            Returns the label for the specified commandId or empty if not found.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns the label for the specified commandId or empty if not found.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetLabel(CefSharp.CefMenuCommand,System.String)">
            <summary>
            Sets the label for the specified commandId. 
            </summary>
            <param name="commandId">the command Id</param>
            <param name="label">the label</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetLabelAt(System.Int32,System.String)">
            <summary>
            Set the label at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="label">the label</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetType(CefSharp.CefMenuCommand)">
            <summary>
            Returns the item type for the specified commandId.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns the item type for the specified commandId.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetTypeAt(System.Int32)">
            <summary>
            Returns the item type at the specified index.
            </summary>
            <param name="index">index</param>
            <returns>Returns the item type at the specified index.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetGroupId(CefSharp.CefMenuCommand)">
            <summary>
            Returns the group id for the specified commandId or -1 if invalid.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns the group id for the specified commandId or -1 if invalid.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.GetGroupIdAt(System.Int32)">
            <summary>
            Returns the group id at the specified index or -1 if invalid.
            </summary>
            <param name="index">index</param>
            <returns>Returns the group id at the specified index or -1 if invalid.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.SetGroupId(CefSharp.CefMenuCommand,System.Int32)">
            <summary>
            Sets the group id for the specified commandId.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="groupId">the group id</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetGroupIdAt(System.Int32,System.Int32)">
            <summary>
            Sets the group id at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="groupId">the group id</param>
            <returns>Returns true on success.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.GetSubMenu(CefSharp.CefMenuCommand)">
            <summary>
            Returns the <see cref="T:CefSharp.IMenuModel"/> for the specified commandId or null if invalid.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns the <see cref="T:CefSharp.IMenuModel"/> for the specified commandId or null if invalid.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.GetSubMenuAt(System.Int32)">
            <summary>
            Returns the <see cref="T:CefSharp.IMenuModel"/> at the specified index or empty if invalid.
            </summary>
            <param name="index">index</param>
            <returns>Returns the <see cref="T:CefSharp.IMenuModel"/> for the specified commandId or null if invalid.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.IsVisible(CefSharp.CefMenuCommand)">
            <summary>
            Returns true if the specified commandId is visible.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns true if the specified commandId is visible.</returns>        
        </member>
        <member name="M:CefSharp.IMenuModel.IsVisibleAt(System.Int32)">
            <summary>
            Returns true if the specified index is visible.
            </summary>
            <param name="index">index</param>
            <returns>Returns true if the specified index is visible.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetVisible(CefSharp.CefMenuCommand,System.Boolean)">
            <summary>
            Change the visibility of the specified commandId.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="visible">visible</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetVisibleAt(System.Int32,System.Boolean)">
            <summary>
            Change the visibility at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="visible">visible</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.IsEnabled(CefSharp.CefMenuCommand)">
            <summary>
            Returns true if the specified commandId is enabled.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns true if the specified commandId is enabled.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.IsEnabledAt(System.Int32)">
            <summary>
            Returns true if the specified index is enabled.
            </summary>
            <param name="index">index</param>
            <returns>Returns true if the specified index is enabled.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetEnabled(CefSharp.CefMenuCommand,System.Boolean)">
            <summary>
            Change the enabled status of the specified commandId.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="enabled">is enabled</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetEnabledAt(System.Int32,System.Boolean)">
            <summary>
            Change the enabled status at the specified index.
            </summary>
            <param name="index">index</param>
            <param name="enabled">is enabled</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.IsChecked(CefSharp.CefMenuCommand)">
            <summary>
            Returns true if the specified commandId is checked. Only applies to check and radio items.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns true if the specified commandId is checked. Only applies to check and radio items.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.IsCheckedAt(System.Int32)">
            <summary>
            Returns true if the specified index is checked. Only applies to check and radio items.
            </summary>
            <param name="index">index</param>
            <returns>Returns true if the specified index is checked. Only applies to check and radio items.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetChecked(CefSharp.CefMenuCommand,System.Boolean)">
            <summary>
            Check the specified commandId. Only applies to check and radio items.
            </summary>
            <param name="commandId">the command Id</param>
            <param name="isChecked">set checked</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetCheckedAt(System.Int32,System.Boolean)">
            <summary>
            Check the specified index. Only applies to check and radio items.
            </summary>
            <param name="index">index</param>
            <param name="isChecked">set checked</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.HasAccelerator(CefSharp.CefMenuCommand)">
            <summary>
            Returns true if the specified commandId has a keyboard accelerator assigned.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns true if the specified commandId has a keyboard accelerator assigned.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.HasAcceleratorAt(System.Int32)">
            <summary>
            Returns true if the specified index has a keyboard accelerator assigned.
            </summary>
            <param name="index">index</param>
            <returns>Returns true if the specified index has a keyboard accelerator assigned.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetAccelerator(CefSharp.CefMenuCommand,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Set the keyboard accelerator for the specified commandId. 
            </summary>
            <param name="commandId">the command Id</param>
            <param name="keyCode">keyCode can be any key or character value. </param>
            <param name="shiftPressed">shift key pressed</param>
            <param name="ctrlPressed">ctrl key pressed</param>
            <param name="altPressed">alt key pressed</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.SetAcceleratorAt(System.Int32,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Set the keyboard accelerator at the specified index. keyCode can be any key or character value.
            </summary>
            <param name="index">index</param>
            <param name="keyCode">keyCode can be any key or character value. </param>
            <param name="shiftPressed">shift key pressed</param>
            <param name="ctrlPressed">ctrl key pressed</param>
            <param name="altPressed">alt key pressed</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.RemoveAccelerator(CefSharp.CefMenuCommand)">
            <summary>
            Remove the keyboard accelerator for the specified commandId.
            </summary>
            <param name="commandId">the command Id</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.RemoveAcceleratorAt(System.Int32)">
            <summary>
            Remove the keyboard accelerator at the specified index. 
            </summary>
            <param name="index">index</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetAccelerator(CefSharp.CefMenuCommand,System.Int32@,System.Boolean@,System.Boolean@,System.Boolean@)">
            <summary>
            Retrieves the keyboard accelerator for the specified commandId. 
            </summary>
            <param name="commandId">the command Id</param>
            <param name="keyCode">keyCode can be any key or character value. </param>
            <param name="shiftPressed">shift key pressed</param>
            <param name="ctrlPressed">ctrl key pressed</param>
            <param name="altPressed">alt key pressed</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="M:CefSharp.IMenuModel.GetAcceleratorAt(System.Int32,System.Int32@,System.Boolean@,System.Boolean@,System.Boolean@)">
            <summary>
            Retrieves the keyboard accelerator for the specified index.
            </summary>
            <param name="index">index</param>
            <param name="keyCode">keyCode can be any key or character value. </param>
            <param name="shiftPressed">shift key pressed</param>
            <param name="ctrlPressed">ctrl key pressed</param>
            <param name="altPressed">alt key pressed</param>
            <returns>Returns true on success.</returns>
        </member>
        <member name="P:CefSharp.IMenuModel.Count">
            <summary>
            Returns the number of items in this menu.
            </summary>
        </member>
        <member name="M:CefSharp.ProxyOptions.HasUsernameAndPassword">
            <summary>
            Checks if username and password is set
            </summary>
            <returns>Returns true if both username and password is set, otherwise false</returns>
        </member>
        <member name="M:CefSharp.ProxyOptions.#ctor(System.String,System.String,System.String,System.String,System.String)">
            <param name="ip">The IP address for the proxy</param>
            <param name="port">The port for the proxy</param>
            <param name="username">The username required for authentication</param>
            <param name="password">The password required for authentication</param>
            <param name="bypassList">The list of domains that shouldn't be affected by the proxy, Format: example.com;example2.com</param>
        </member>
        <member name="P:CefSharp.ProxyOptions.IP">
            <summary>
            The IP address for the proxy
            </summary>
        </member>
        <member name="P:CefSharp.ProxyOptions.Port">
            <summary>
            The port for the proxy
            </summary>
        </member>
        <member name="P:CefSharp.ProxyOptions.Username">
            <summary>
            The username for authentication
            </summary>
        </member>
        <member name="P:CefSharp.ProxyOptions.Password">
            <summary>
            The password for authentication
            </summary>
        </member>
        <member name="P:CefSharp.ProxyOptions.BypassList">
            <summary>
            The list of domains that shouldn't be affected by the proxy, Format: example.com;example2.com
            </summary>
        </member>
        <member name="M:CefSharp.DefaultResourceHandlerFactoryItem.#ctor(CefSharp.IResourceHandler,System.Boolean)">
            <param name="handler">The handler for a specific Url</param>
            <param name="oneTimeUse">Whether or not the handler should be used once (true) or until manually unregistered (false)</param>
        </member>
        <member name="P:CefSharp.DefaultResourceHandlerFactoryItem.Handler">
            <summary>
            The handler for a specific Url
            </summary>
        </member>
        <member name="P:CefSharp.DefaultResourceHandlerFactoryItem.OneTimeUse">
            <summary>
            Whether or not the handler should be used once (true) or until manually unregistered (false)
            </summary>
        </member>
        <member name="T:CefSharp.Structs.Size">
            <summary>
            Structure representing a size. 
            </summary>
        </member>
        <member name="P:CefSharp.Structs.Size.Width">
            <summary>
            Width
            </summary>
        </member>
        <member name="P:CefSharp.Structs.Size.Height">
            <summary>
            Height
            </summary>
        </member>
        <member name="T:CefSharp.ISslInfo">
            <summary>
            Class representing SSL information. 
            </summary>
        </member>
        <member name="P:CefSharp.ISslInfo.CertStatus">
            <summary>
            Returns a bitmask containing any and all problems verifying the server
            certificate.
            </summary>
        </member>
        <member name="P:CefSharp.ISslInfo.X509Certificate">
            <summary>
            Returns the X.509 certificate.
            </summary>
        </member>
        <member name="T:CefSharp.KeyEvent">
            <summary>
            Class representing a a keyboard event.
            </summary>
        </member>
        <member name="P:CefSharp.KeyEvent.Type">
            <summary>
            The type of keyboard event.
            </summary>
        </member>
        <member name="P:CefSharp.KeyEvent.Modifiers">
            <summary>
            Bit flags describing any pressed modifier keys. See
            cef_event_flags_t for values.
            </summary>
        </member>
        <member name="P:CefSharp.KeyEvent.WindowsKeyCode">
            <summary>
            The Windows key code for the key event. This value is used by the DOM
            specification. Sometimes it comes directly from the event (i.e. on
            Windows) and sometimes it's determined using a mapping function. See
            WebCore/platform/chromium/KeyboardCodes.h for the list of values.
            </summary>
        </member>
        <member name="P:CefSharp.KeyEvent.NativeKeyCode">
            <summary>
            The actual key code genenerated by the platform.
            </summary>
        </member>
        <member name="P:CefSharp.KeyEvent.IsSystemKey">
            <summary>
            Indicates whether the event is considered a "system key" event (see
            http://msdn.microsoft.com/en-us/library/ms646286(VS.85).aspx for details).
            This value will always be false on non-Windows platforms.
            </summary>
        </member>
        <member name="P:CefSharp.KeyEvent.FocusOnEditableField">
            <summary>
            True if the focus is currently on an editable field on the page. This is useful for determining if standard key events should be intercepted.
            </summary>
        </member>
        <member name="T:CefSharp.IPostData">
            <summary>
            Class used to represent post data for a web request. The methods of this class may be called on any thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IPostData.AddElement(CefSharp.IPostDataElement)">
            <summary>
            Add the specified <see cref="T:CefSharp.IPostDataElement"/>.
            </summary>
            <param name="element">element to be added.</param>
            <returns> Returns true if the add succeeds.</returns>
        </member>
        <member name="M:CefSharp.IPostData.RemoveElement(CefSharp.IPostDataElement)">
            <summary>
            Remove  the specified <see cref="T:CefSharp.IPostDataElement"/>.
            </summary>
            <param name="element">element to be removed.</param>
            <returns> Returns true if the add succeeds.</returns>
        </member>
        <member name="M:CefSharp.IPostData.RemoveElements">
            <summary>
            Remove all existing post data elements.
            </summary>
        </member>
        <member name="M:CefSharp.IPostData.CreatePostDataElement">
            <summary>
            Create a new <see cref="T:CefSharp.IPostDataElement"/> instance
            </summary>
            <returns></returns>
        </member>
        <member name="P:CefSharp.IPostData.Elements">
            <summary>
            Retrieve the post data elements.
            </summary>
        </member>
        <member name="P:CefSharp.IPostData.IsReadOnly">
            <summary>
            Returns true if this object is read-only.
            </summary>
        </member>
        <member name="P:CefSharp.IPostData.IsDisposed">
            <summary>
            Gets a value indicating whether the object has been disposed of.
            </summary>
        </member>
        <member name="P:CefSharp.IPostData.HasExcludedElements">
            <summary>
            Returns true if the underlying POST data includes elements that are not
            represented by this IPostData object (for example, multi-part file upload
            data). Modifying IPostData objects with excluded elements may result in
            the request failing.
            </summary>
        </member>
        <member name="T:CefSharp.IPostDataElement">
            <summary>
            Class used to represent a single element in the request post data.
            The methods of this class may be called on any thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IPostDataElement.SetToEmpty">
            <summary>
            Remove all contents from the post data element.
            </summary>
        </member>
        <member name="P:CefSharp.IPostDataElement.File">
            <summary>
            Gets or sets the file name.
            </summary>
        </member>
        <member name="P:CefSharp.IPostDataElement.IsReadOnly">
            <summary>
            Gets if the object is read-only.
            </summary>
        </member>
        <member name="P:CefSharp.IPostDataElement.Type">
            <summary>
            Gets the type of this <see cref="T:CefSharp.IPostDataElement"/>.
            </summary>
        </member>
        <member name="P:CefSharp.IPostDataElement.Bytes">
            <summary>
            Gets or sets the bytes of this <see cref="T:CefSharp.IPostDataElement"/>.
            </summary>
        </member>
        <member name="T:CefSharp.CefSharpSettings">
            <summary>
            Use this static class to configure some CefSharp specific settings like WcfTimeout
            </summary>
        </member>
        <member name="M:CefSharp.CefSharpSettings.#cctor">
            <summary>
            Set default values for CefSharpSettings
            </summary>
        </member>
        <member name="P:CefSharp.CefSharpSettings.LegacyJavascriptBindingEnabled">
            <summary>
            Objects registered using RegisterJsObject and RegisterAsyncJsObject
            will be automatically bound in the first render process that's created
            for a ChromiumWebBrowser instance. If you perform a cross-site
            navigation a process switch will occur and bound objects will no longer
            be automatically avaliable. For those upgrading from version 57 or below
            that do no perform cross-site navigation (e.g. Single Page applications or
            applications that only refer to a single domain) can set this property to 
            true and use the old behaviour.Defaults to false
            NOTE: Set this before your first call to RegisterJsObject or RegisterAsyncJsObject
            </summary>
            <remarks>
            Javascript binding in CefSharp version 57 and below used the
            --process-per-tab Process Model to limit the number of render
            processes to 1 per ChromiumWebBrowser instance, this allowed
            us to communicate bound javascript objects when the process was
            initially created (OnRenderViewReady is only called for the first
            process creation or after a crash), subsiquently all bound objects
            were registered in ever V8Context in OnContextCreated (executed in the render process).
            Chromium has made changes and --process-per-tab is not currently working.
            Performing a cross-site navigation (from one domain to a different domain)
            will cause a new render process to be created, subsiquent render processes 
            won't have access to the bound object information by default.
            </remarks>
        </member>
        <member name="P:CefSharp.CefSharpSettings.WcfEnabled">
            <summary>
            WCF is used by RegisterJsObject feature for Javascript Binding
            It's reccomended that anyone developing a new application use 
            the RegisterAsyncJsObject version which communicates using native
            Chromium IPC.
            </summary>
        </member>
        <member name="P:CefSharp.CefSharpSettings.WcfTimeout">
            <summary>
            Change the Close timeout for the WCF channel used by the sync JSB binding.
            The default value is currently 2 seconds. Changing this to <see cref="F:System.TimeSpan.Zero"/>
            will result on Abort() being called on the WCF Channel Host
            </summary>
        </member>
        <member name="P:CefSharp.CefSharpSettings.ShutdownOnExit">
            <summary>
            For the WinForms and WPF instances of ChromiumWebBrowser the relevant Application Exit event
            is hooked and Cef.Shutdown() called by default. Set this to false to disable this behaviour.
            This value needs to be set before the first instance of ChromiumWebBrowser is created as
            the event handlers are hooked in the static constructor for the ChromiumWebBrowser class
            </summary>
        </member>
        <member name="P:CefSharp.CefSharpSettings.Proxy">
            <summary>
            The proxy options that will be used for all connections
            
            If set before the call to Cef.Initialize, command line arguments will be set for you
            If a username and password is provided and the IPs match authentication is done automatically
            
            NOTE: GetAuthCredentials won't be called for a proxy server that matches the IP
            NOTE: It isn't possble to change the proxy after the call to Cef.Initialize
            </summary>
        </member>
        <member name="P:CefSharp.CefSharpSettings.ConcurrentTaskExecution">
            <summary>
            This influences the behavior of RegisterAsyncJsObject and how method calls are made.
            By default the <see cref="!:MethodRunnerQueue"/> executes Tasks in a sync fashion.
            Setting this property to true will allocate new Tasks on TaskScheduler.Default for execution.
            </summary>
        </member>
        <member name="T:CefSharp.Cookie">
            <summary>
            Class used to Represent a cookie the built in .Net Cookie
            class isn't used as some of it's properties have internal setters
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Name">
            <summary>
            The cookie name
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Value">
            <summary>
            The cookie value. 
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Domain">
            <summary>
            If domain is empty a host cookie will be created instead of a domain cookie. Domain cookies are stored with a leading "."
            and are visible to sub-domains whereas host cookies are not. 
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Path">
            <summary>
            Ss non-empty only URLs at or below the path will get the cookie value. 
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Secure">
            <summary>
            If true the cookie will only be sent for HTTPS requests. 
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.HttpOnly">
            <summary>
            Ss true the cookie will only be sent for HTTP requests. 
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Expires">
            <summary>
            Expires or null of no expiry
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.Creation">
            <summary>
            The cookie creation date. This is automatically populated by the system on cookie creation. 
            </summary>
        </member>
        <member name="P:CefSharp.Cookie.LastAccess">
            <summary>
            The cookie last access date. This is automatically populated by the system on access. 
            </summary>		
        </member>
        <member name="T:CefSharp.DefaultResourceHandlerFactory">
            <summary>
            Default implementation of <see cref="T:CefSharp.IResourceHandlerFactory"/> it's used
            internally for the LoadHtml implementation - basically a resource handler is
            registered for a specific Url.
            </summary>
        </member>
        <member name="T:CefSharp.IResourceHandlerFactory">
            <summary>
            Class that creates <see cref="T:CefSharp.IResourceHandler"/> instances for handling custom requests.
            The methods of this class will always be called on the CEF IO thread. This interface
            maps to the  CefRequestHandler::GetResourceHandler method. It was split out to allow for
            the <see cref="T:CefSharp.DefaultResourceHandlerFactory"/> implementation that provides support
            for the LoadHtml extension method.
            </summary>
        </member>
        <member name="M:CefSharp.IResourceHandlerFactory.GetResourceHandler(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest)">
            <summary>
            Called before a resource is loaded. To specify a handler for the resource return a <see cref="T:CefSharp.ResourceHandler"/> object
            </summary>
            <param name="browserControl">The browser UI control</param>
            <param name="browser">the browser object</param>
            <param name="frame">the frame object</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <returns>To allow the resource to load normally return NULL otherwise return an instance of ResourceHandler with a valid stream</returns>
        </member>
        <member name="P:CefSharp.IResourceHandlerFactory.HasHandlers">
            <summary>
            Are there any <see cref="T:CefSharp.ResourceHandler"/>'s registered?
            </summary>
        </member>
        <member name="M:CefSharp.DefaultResourceHandlerFactory.#ctor(System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>
            Create a new instance of DefaultResourceHandlerFactory
            </summary>
            <param name="comparer">string equality comparer</param>
        </member>
        <member name="M:CefSharp.DefaultResourceHandlerFactory.RegisterHandler(System.String,CefSharp.IResourceHandler,System.Boolean)">
            <summary>
            Register a handler for the specified Url
            </summary>
            <param name="url">url</param>
            <param name="handler">handler</param>
            <param name="oneTimeUse">Whether or not the handler should be used once (true) or until manually unregistered (false)</param>
            <returns>returns true if the Url was successfully parsed into a Uri otherwise false</returns>
        </member>
        <member name="M:CefSharp.DefaultResourceHandlerFactory.UnregisterHandler(System.String)">
            <summary>
            Unregister a handler for the specified Url
            </summary>
            <param name="url">Url</param>
            <returns>returns true if successfully removed</returns>
        </member>
        <member name="M:CefSharp.DefaultResourceHandlerFactory.GetResourceHandler(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame,CefSharp.IRequest)">
            <summary>
            Called before a resource is loaded. To specify a handler for the resource return a <see cref="T:CefSharp.ResourceHandler"/> object
            </summary>
            <param name="browserControl">The browser UI control</param>
            <param name="browser">the browser object</param>
            <param name="frame">the frame object</param>
            <param name="request">the request object - cannot be modified in this callback</param>
            <returns>To allow the resource to load normally return NULL otherwise return an instance of ResourceHandler with a valid stream</returns>
        </member>
        <member name="P:CefSharp.DefaultResourceHandlerFactory.Handlers">
            <summary>
            Resource handler thread safe dictionary
            </summary>
        </member>
        <member name="P:CefSharp.DefaultResourceHandlerFactory.HasHandlers">
            <summary>
            Are there any <see cref="T:CefSharp.ResourceHandler"/>'s registered?
            </summary>
        </member>
        <member name="T:CefSharp.DependencyChecker">
            <summary>
            DependencyChecker provides a known list of Cef/CefSharp dependencies and 
            provides helper methods to check for their existance.
            </summary>
        </member>
        <member name="F:CefSharp.DependencyChecker.LocalesPackFile">
            <summary>
            en-US Locales pak file location
            </summary>
        </member>
        <member name="F:CefSharp.DependencyChecker.CefDependencies">
            <summary>
            List of Cef Dependencies
            </summary>
        </member>
        <member name="F:CefSharp.DependencyChecker.CefResources">
            <summary>
            List of Cef Resources (pack files)
            </summary>
        </member>
        <member name="F:CefSharp.DependencyChecker.CefSharpDependencies">
            <summary>
            List of CefSharp Dependencies
            </summary>
        </member>
        <member name="F:CefSharp.DependencyChecker.BrowserSubprocessDependencies">
            <summary>
            List of CefSharp.BrowserSubprocess.exe dependencies.
            </summary>
        </member>
        <member name="M:CefSharp.DependencyChecker.CheckDependencies(System.Boolean,System.Boolean,System.String,System.String,System.String,System.String)">
            <summary>
            CheckDependencies iterates through the list of Cef and CefSharp dependencines
            relative to the path provided and returns a list of missing ones
            </summary>
            <param name="checkOptional">check to see if optional dependencies are present</param>
            <param name="packLoadingDisabled">Is loading of pack files disabled?</param>
            <param name="path">path to check for dependencies</param>
            <param name="resourcesDirPath">The path to the resources directory, if empty the Executing Assembly path is used.</param>
            <param name="browserSubProcessPath">The path to a separate executable that will be launched for sub-processes.</param>
            <param name="localePackFile">The locale pack file e.g. <see cref="F:CefSharp.DependencyChecker.LocalesPackFile"/> </param>
            <returns>List of missing dependencies, if all present an empty List will be returned</returns>
        </member>
        <member name="M:CefSharp.DependencyChecker.CheckDependencyList(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loop through dependencies and add to the returned missing dependency list if not found.
            </summary>
            <param name="dir">The directory of the dependencies, or the current directory if null.</param>
            <param name="files">The dependencies to check.</param>
            <returns>List of missing dependencies, if all present an empty List will be returned</returns>
        </member>
        <member name="M:CefSharp.DependencyChecker.AssertAllDependenciesPresent(System.String,System.String,System.String,System.Boolean,System.String)">
            <summary>
            Checks if all Cef and CefSharp dependencies were found relative to the Executing Assembly.
            Shortcut method that calls <see cref="M:CefSharp.DependencyChecker.CheckDependencies(System.Boolean,System.Boolean,System.String,System.String,System.String,System.String)"/>, throws an Exception if not files are missing.
            </summary>
            <param name="locale">The locale, if empty then en-US will be used.</param>
            <param name="localesDirPath">The path to the locales directory, if empty locales\ will be used.</param>
            <param name="resourcesDirPath">The path to the resources directory, if empty the Executing Assembly path is used.</param>
            <param name="packLoadingDisabled">Is loading of pack files disabled?</param>
            <param name="browserSubProcessPath">The path to a separate executable that will be launched for sub-processes.</param>
            <exception cref="T:System.Exception">Throw when not all dependencies are present</exception>
        </member>
        <member name="T:CefSharp.IBrowser">
            <summary>
            CefSharp interface for CefBrowser.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowser.GetHost">
            <summary>
            Returns the browser host object. This method can only be called in the browser process.
            </summary>
            <returns>the browser host object</returns>
        </member>
        <member name="M:CefSharp.IBrowser.GoBack">
            <summary>
            Navigate backwards.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowser.GoForward">
            <summary>
            Navigate forwards.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowser.CloseBrowser(System.Boolean)">
            <summary>
            Request that the browser close. The JavaScript 'onbeforeunload' event will
            be fired. If |forceClose| is false the event handler, if any, will be
            allowed to prompt the user and the user can optionally cancel the close.
            If |force_close| is true the prompt will not be displayed and the close
            will proceed. Results in a call to CefLifeSpanHandler::DoClose() if the
            event handler allows the close or if |force_close| is true. See
            CefLifeSpanHandler::DoClose() documentation for additional usage
            information.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowser.Reload(System.Boolean)">
            <summary>
            Reload the current page.
            </summary>
            <param name="ignoreCache">
            <c>true</c> a reload is performed ignoring browser cache; <c>false</c> a reload is
            performed using files from the browser cache, if available.
            </param>
        </member>
        <member name="M:CefSharp.IBrowser.StopLoad">
            <summary>
            Stop loading the page.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowser.IsSame(CefSharp.IBrowser)">
            <summary>
            Returns true if this object is pointing to the same handle as that object.
            </summary>
            <param name="that">compare browser instances</param>
            <returns>returns true if the same instance</returns>
        </member>
        <member name="M:CefSharp.IBrowser.GetFrame(System.Int64)">
            <summary>
            Returns the frame with the specified identifier, or NULL if not found.
            </summary>
            <param name="identifier">identifier</param>
            <returns>frame or null</returns>
        </member>
        <member name="M:CefSharp.IBrowser.GetFrame(System.String)">
            <summary>
            Returns the frame with the specified name, or NULL if not found.
            </summary>
            <param name="name">name of frame</param>
            <returns>frame or null</returns>
        </member>
        <member name="M:CefSharp.IBrowser.GetFrameCount">
            <summary>
            Returns the number of frames that currently exist.
            </summary>
            <returns>the number of frames</returns>
        </member>
        <member name="M:CefSharp.IBrowser.GetFrameIdentifiers">
            <summary>
            Returns the identifiers of all existing frames.
            </summary>
            <returns>list of frame identifiers</returns>
        </member>
        <member name="M:CefSharp.IBrowser.GetFrameNames">
            <summary>
            Returns the names of all existing frames.
            </summary>
            <returns>frame names</returns>
        </member>
        <member name="P:CefSharp.IBrowser.CanGoBack">
            <summary>
            Returns true if the browser can navigate backwards.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.CanGoForward">
            <summary>
            Returns true if the browser can navigate forwards.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.IsLoading">
            <summary>
            Returns true if the browser is currently loading.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.Identifier">
            <summary>
            Returns the globally unique identifier for this browser.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.IsPopup">
            <summary>
            Returns true if the window is a popup window.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.HasDocument">
            <summary>
            Returns true if a document has been loaded in the browser.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.MainFrame">
            <summary>
            Returns the main (top-level) frame for the browser window.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.FocusedFrame">
            <summary>
            Returns the focused frame for the browser window.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowser.IsDisposed">
            <summary>
            Gets a value indicating whether the browser has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IBrowserHost">
            <summary>
            Interface used to represent the browser process aspects of a browser window.
            They may be called on any thread in that process unless otherwise indicated in the comments. 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.AddWordToDictionary(System.String)">
            <summary>
            Add the specified word to the spelling dictionary.
            </summary>
            <param name="word"></param>
        </member>
        <member name="M:CefSharp.IBrowserHost.CloseBrowser(System.Boolean)">
            <summary>
            Request that the browser close. The JavaScript 'onbeforeunload' event will be fired.
            </summary>
            <param name="forceClose">
            If forceClose is false the event handler, if any, will be allowed to prompt the user and the
            user can optionally cancel the close. If forceClose is true the prompt will not be displayed
            and the close will proceed. Results in a call to <see cref="M:CefSharp.ILifeSpanHandler.DoClose(CefSharp.IWebBrowser,CefSharp.IBrowser)"/> if
            the event handler allows the close or if forceClose is true
            See <see cref="M:CefSharp.ILifeSpanHandler.DoClose(CefSharp.IWebBrowser,CefSharp.IBrowser)"/> documentation for additional usage information.
            </param>
        </member>
        <member name="M:CefSharp.IBrowserHost.CloseDevTools">
            <summary>
            Explicitly close the developer tools window if one exists for this browser instance.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.DragTargetDragEnter(CefSharp.IDragData,CefSharp.MouseEvent,CefSharp.DragOperationsMask)">
            <summary>
            Call this method when the user drags the mouse into the web view (before calling <see cref="M:CefSharp.IBrowserHost.DragTargetDragOver(CefSharp.MouseEvent,CefSharp.DragOperationsMask)"/>/<see cref="M:CefSharp.IBrowserHost.DragTargetDragLeave"/>/<see cref="M:CefSharp.IBrowserHost.DragTargetDragDrop(CefSharp.MouseEvent)"/>).
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.DragTargetDragOver(CefSharp.MouseEvent,CefSharp.DragOperationsMask)">
            <summary>
            Call this method each time the mouse is moved across the web view during a drag operation (after calling <see cref="M:CefSharp.IBrowserHost.DragTargetDragEnter(CefSharp.IDragData,CefSharp.MouseEvent,CefSharp.DragOperationsMask)"/> and before calling <see cref="M:CefSharp.IBrowserHost.DragTargetDragLeave"/>/<see cref="M:CefSharp.IBrowserHost.DragTargetDragDrop(CefSharp.MouseEvent)"/>). 
            This method is only used when window rendering is disabled.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.DragTargetDragDrop(CefSharp.MouseEvent)">
            <summary>
            Call this method when the user completes the drag operation by dropping the object onto the web view (after calling <see cref="M:CefSharp.IBrowserHost.DragTargetDragEnter(CefSharp.IDragData,CefSharp.MouseEvent,CefSharp.DragOperationsMask)"/>). 
            The object being dropped is <see cref="T:CefSharp.IDragData"/>, given as an argument to the previous <see cref="M:CefSharp.IBrowserHost.DragTargetDragEnter(CefSharp.IDragData,CefSharp.MouseEvent,CefSharp.DragOperationsMask)"/> call. 
            This method is only used when window rendering is disabled.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.DragSourceEndedAt(System.Int32,System.Int32,CefSharp.DragOperationsMask)">
            <summary>
            Call this method when the drag operation started by a <see cref="M:CefSharp.Internals.IRenderWebBrowser.StartDragging(CefSharp.IDragData,CefSharp.DragOperationsMask,System.Int32,System.Int32)"/> call has ended either in a drop or by being cancelled.
            If the web view is both the drag source and the drag target then all DragTarget* methods should be called before DragSource* methods.
            This method is only used when window rendering is disabled. 
            </summary>
            <param name="x">x mouse coordinate relative to the upper-left corner of the view.</param>
            <param name="y">y mouse coordinate relative to the upper-left corner of the view.</param>
            <param name="op">Drag Operations mask</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.DragTargetDragLeave">
            <summary>
            Call this method when the user drags the mouse out of the web view (after calling <see cref="M:CefSharp.IBrowserHost.DragTargetDragEnter(CefSharp.IDragData,CefSharp.MouseEvent,CefSharp.DragOperationsMask)"/>).
            This method is only used when window rendering is disabled.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.DragSourceSystemDragEnded">
            <summary>
            Call this method when the drag operation started by a <see cref="M:CefSharp.Internals.IRenderWebBrowser.StartDragging(CefSharp.IDragData,CefSharp.DragOperationsMask,System.Int32,System.Int32)"/> call has completed.
            This method may be called immediately without first calling DragSourceEndedAt to cancel a drag operation.
            If the web view is both the drag source and the drag target then all DragTarget* methods should be called before DragSource* mthods.
            This method is only used when window rendering is disabled. 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.Find(System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Search for text
            </summary>
            <param name="identifier">can be used to have multiple searches running simultaniously</param>
            <param name="searchText">text to search for</param>
            <param name="forward">indicates whether to search forward or backward within the page</param>
            <param name="matchCase">indicates whether the search should be case-sensitive</param>
            <param name="findNext">indicates whether this is the first request or a follow-up</param>
            <remarks>The IFindHandler instance, if any, will be called to report find results. </remarks>
        </member>
        <member name="M:CefSharp.IBrowserHost.GetOpenerWindowHandle">
            <summary>
            Retrieve the window handle of the browser that opened this browser.
            </summary>
            <returns>The handler</returns>
        </member>
        <member name="M:CefSharp.IBrowserHost.GetWindowHandle">
            <summary>
            Retrieve the window handle for this browser. 
            </summary>
            <returns>The handler</returns>
        </member>
        <member name="M:CefSharp.IBrowserHost.GetZoomLevelAsync">
            <summary>
            Get the current zoom level. The default zoom level is 0.0. This method can only be called on the CEF UI thread. 
            </summary>
            <returns> a <see cref="T:System.Threading.Tasks.Task`1"/> that when executed returns the zoom level as a double.</returns>
        </member>
        <member name="M:CefSharp.IBrowserHost.Invalidate(CefSharp.PaintElementType)">
            <summary>
            Invalidate the view. The browser will call CefRenderHandler::OnPaint asynchronously.
            This method is only used when window rendering is disabled (OSR). 
            </summary>
            <param name="type">indicates which surface to re-paint either View or Popup.</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.ImeSetComposition(System.String,CefSharp.CompositionUnderline[],System.Nullable{CefSharp.Range})">
             <summary>
             Begins a new composition or updates the existing composition. Blink has a
             special node (a composition node) that allows the input method to change
             text without affecting other DOM nodes. 
            
             This method may be called multiple times as the composition changes. When
             the client is done making changes the composition should either be canceled
             or completed. To cancel the composition call ImeCancelComposition. To
             complete the composition call either ImeCommitText or
             ImeFinishComposingText. Completion is usually signaled when:
             The client receives a WM_IME_COMPOSITION message with a GCS_RESULTSTR
             flag (on Windows).
             This method is only used when window rendering is disabled. (WPF and OffScreen) 
             </summary>
             <param name="text">is the optional text that
             will be inserted into the composition node</param>
             <param name="underlines">is an optional set
             of ranges that will be underlined in the resulting text.</param>
             <param name="selectionRange"> is an optional range of the resulting text that
             will be selected after insertion or replacement. </param>
        </member>
        <member name="M:CefSharp.IBrowserHost.ImeCommitText(System.String)">
            <summary>
            Completes the existing composition by optionally inserting the specified
            text into the composition node.
            This method is only used when window rendering is disabled. (WPF and OffScreen) 
            </summary>
            <param name="text">text that will be committed</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.ImeFinishComposingText(System.Boolean)">
            <summary>
            Completes the existing composition by applying the current composition node
            contents. See comments on ImeSetComposition for usage.
            This method is only used when window rendering is disabled. (WPF and OffScreen) 
            </summary>
            <param name="keepSelection">If keepSelection is false the current selection, if any, will be discarded.</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.ImeCancelComposition">
            <summary>
            Cancels the existing composition and discards the composition node
            contents without applying them. See comments on ImeSetComposition for
            usage.
            This method is only used when window rendering is disabled. (WPF and OffScreen) 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.NotifyMoveOrResizeStarted">
            <summary>
            Notify the browser that the window hosting it is about to be moved or resized.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.NotifyScreenInfoChanged">
            <summary>
            Send a notification to the browser that the screen info has changed.
            The browser will then call CefRenderHandler::GetScreenInfo to update the screen information with the new values.
            This simulates moving the webview window from one display to another, or changing the properties of the current display.
            This method is only used when window rendering is disabled. 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.Print">
            <summary>
            Print the current browser contents. 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.PrintToPdf(System.String,CefSharp.PdfPrintSettings,CefSharp.IPrintToPdfCallback)">
            <summary>
            Asynchronously prints the current browser contents to the Pdf file specified.
            The caller is responsible for deleting the file when done.
            </summary>
            <param name="path">Output file location.</param>
            <param name="settings">Print Settings, can be null</param>
            <param name="callback">Callback executed when printing complete</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.ReplaceMisspelling(System.String)">
            <summary>
            If a misspelled word is currently selected in an editable node calling this method will replace it with the specified word.
            </summary>
            <param name="word">word to be replaced</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendCaptureLostEvent">
            <summary>
            Send a capture lost event to the browser.
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendFocusEvent(System.Boolean)">
            <summary>
            Send a focus event to the browser. . (Used for OSR Rendering e.g. WPF or OffScreen)
            </summary>
            <param name="setFocus">set focus</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendKeyEvent(CefSharp.KeyEvent)">
            <summary>
             Send a key event to the browser.
            </summary>
            <param name="keyEvent">represents keyboard event</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendKeyEvent(System.Int32,System.Int32,System.Int32)">
            <summary>
            Send key event to browser based on operating system message
            </summary>
            <param name="message">message</param>
            <param name="wParam">wParam</param>
            <param name="lParam">lParam</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendMouseClickEvent(CefSharp.MouseEvent,CefSharp.MouseButtonType,System.Boolean,System.Int32)">
            <summary>
            Send a mouse click event to the browser.
            </summary>
            <param name="mouseEvent">mouse event - x, y and modifiers</param>
            <param name="mouseButtonType">Mouse ButtonType</param>
            <param name="mouseUp">mouse up</param>
            <param name="clickCount">click count</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendMouseWheelEvent(CefSharp.MouseEvent,System.Int32,System.Int32)">
            <summary>
            Send a mouse wheel event to the browser.
            </summary>
            <param name="mouseEvent">mouse event - x, y and modifiers</param>
            <param name="deltaX">Movement delta for X direction.</param>
            <param name="deltaY">movement delta for Y direction.</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SetAccessibilityState(CefSharp.CefState)">
            <summary>
            Set accessibility state for all frames.  If accessibilityState is Default then accessibility will be disabled by default
            and the state may be further controlled with the "force-renderer-accessibility" and "disable-renderer-accessibility"
            command-line switches. If accessibilityState is STATE_ENABLED then accessibility will be enabled.
            If accessibilityState is STATE_DISABLED then accessibility will be completely disabled. For windowed browsers
            accessibility will be enabled in Complete mode (which corresponds to kAccessibilityModeComplete in Chromium).
            In this mode all platform accessibility objects will be created and managed by Chromium's internal implementation.
            The client needs only to detect the screen reader and call this method appropriately. For example, on Windows the
            client can handle WM_GETOBJECT with OBJID_CLIENT to detect accessibility readers. For windowless browsers accessibility
            will be enabled in TreeOnly mode (which corresponds to kAccessibilityModeWebContentsOnly in Chromium). In this mode
            renderer accessibility is enabled, the full tree is computed, and events are passed to IAccessibiltyHandler,
            but platform accessibility objects are not created. The client may implement platform accessibility objects using
            IAccessibiltyHandler callbacks if desired. 
            </summary>
            <param name="accessibilityState">may be default, enabled or disabled.</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SetAutoResizeEnabled(System.Boolean,CefSharp.Structs.Size,CefSharp.Structs.Size)">
            <summary>
            Enable notifications of auto resize via IDisplayHandler.OnAutoResize. Notifications are disabled by default.
            </summary>
            <param name="enabled">enable auto resize</param>
            <param name="minSize">minimum size</param>
            <param name="maxSize">maximum size</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SetFocus(System.Boolean)">
            <summary>
            Set whether the browser is focused. (Used for Normal Rendering e.g. WinForms)
            </summary>
            <param name="focus">set focus</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SetZoomLevel(System.Double)">
            <summary>
            Change the zoom level to the specified value. Specify 0.0 to reset the zoom level.
            If called on the CEF UI thread the change will be applied immediately.
            Otherwise, the change will be applied asynchronously on the UI thread. 
            </summary>
            <param name="zoomLevel">zoom level</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.ShowDevTools(CefSharp.IWindowInfo,System.Int32,System.Int32)">
            <summary>
            Open developer tools in its own window. If inspectElementAtX and/or inspectElementAtY  are specified then
            the element at the specified (x,y) location will be inspected.
            </summary>
            <param name="windowInfo">window info used for showing dev tools</param>
            <param name="inspectElementAtX">x coordinate (used for inspectElement)</param>
            <param name="inspectElementAtY">y coordinate (used for inspectElement)</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.StartDownload(System.String)">
            <summary>
            Download the file at url using IDownloadHandler. 
            </summary>
            <param name="url">url to download</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.StopFinding(System.Boolean)">
            <summary>
            Cancel all searches that are currently going on. 
            </summary>
            <param name="clearSelection">clear the selection</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.SendMouseMoveEvent(CefSharp.MouseEvent,System.Boolean)">
            <summary>
            Send a mouse move event to the browser, coordinates, 
            </summary>
            <param name="x">x coordinate - relative to upper-left corner of view</param>
            <param name="y">y coordinate - relative to upper-left corner of view</param>
            <param name="mouseLeave">mouse leave</param>
            <param name="modifiers">click modifiers .e.g Ctrl</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.WasHidden(System.Boolean)">
            <summary>
            Notify the browser that it has been hidden or shown.
            Layouting and rendering notification will stop when the browser is hidden.
            This method is only used when window rendering is disabled (WPF/OffScreen). 
            </summary>
            <param name="hidden"></param>
        </member>
        <member name="M:CefSharp.IBrowserHost.WasResized">
            <summary>
            Notify the browser that the widget has been resized.
            The browser will first call CefRenderHandler::GetViewRect to get the new size and then call CefRenderHandler::OnPaint asynchronously with the updated regions.
            This method is only used when window rendering is disabled. 
            </summary>
        </member>
        <member name="M:CefSharp.IBrowserHost.GetNavigationEntries(CefSharp.INavigationEntryVisitor,System.Boolean)">
            <summary>
            Retrieve a snapshot of current navigation entries as values sent to the
            specified visitor. 
            </summary>
            <param name="visitor">visitor</param>
            <param name="currentOnly">If true only the current navigation
            entry will be sent, otherwise all navigation entries will be sent.</param>
        </member>
        <member name="M:CefSharp.IBrowserHost.GetVisibleNavigationEntry">
            <summary>
            Returns the current visible navigation entry for this browser. This method
            can only be called on the CEF UI thread.
            </summary>
            <returns>the current navigation entry</returns>
        </member>
        <member name="P:CefSharp.IBrowserHost.HasDevTools">
            <summary>
            Returns true if this browser currently has an associated DevTools browser.
            Must be called on the CEF UI thread.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserHost.MouseCursorChangeDisabled">
            <summary>
            Get/Set Mouse cursor change disabled
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserHost.RequestContext">
            <summary>
            Returns the request context for this browser.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserHost.WindowlessFrameRate">
            <summary>
            Gets/sets the maximum rate in frames per second (fps) that CefRenderHandler::
            OnPaint will be called for a windowless browser. The actual fps may be
            lower if the browser cannot generate frames at the requested rate. The
            minimum value is 1 and the maximum value is 60 (default 30). This method
            can only be called on the UI thread. Can also be set at browser creation
            via BrowserSettings.WindowlessFrameRate.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserHost.WindowRenderingDisabled">
            <summary>
            Returns true if window rendering is disabled.
            </summary>
        </member>
        <member name="P:CefSharp.IBrowserHost.IsDisposed">
            <summary>
            Gets a value indicating whether the browserHost has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.IFrame">
            <summary>
            This interface represents a CefFrame object (i.e. a HTML frame)
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.Undo">
            <summary>
            Execute undo in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.Redo">
            <summary>
            Execute redo in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.Cut">
            <summary>
            Execute cut in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.Copy">
            <summary>
            Execute copy in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.Paste">
            <summary>
            Execute paste in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.Delete">
            <summary>
            Execute delete in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.SelectAll">
            <summary>
            Execute select all in this frame.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.ViewSource">
            <summary>
            Save this frame's HTML source to a temporary file and open it in the
            default text viewing application. This method can only be called from the
            browser process.
            </summary>
        </member>
        <member name="M:CefSharp.IFrame.GetSourceAsync">
            <summary>
            Retrieve this frame's HTML source as a string sent to the specified visitor.
            </summary>
            <returns>
            a <see cref="T:System.Threading.Tasks.Task`1"/> that when executed returns this frame's HTML source as a string.
            </returns>
        </member>
        <member name="M:CefSharp.IFrame.GetSource(CefSharp.IStringVisitor)">
            <summary>
            Retrieve this frame's HTML source as a string sent to the specified visitor. 
            Use the <see cref="M:CefSharp.IFrame.GetSourceAsync"/> method for a Task based async wrapper
            </summary>
            <param name="visitor">visitor will recieve string values asynchronously</param>
        </member>
        <member name="M:CefSharp.IFrame.GetTextAsync">
            <summary>
            Retrieve this frame's display text as a string sent to the specified visitor.
            </summary>
            <returns>
            a <see cref="T:System.Threading.Tasks.Task`1"/> that when executed returns the frame's display text as a string.
            </returns>
        </member>
        <member name="M:CefSharp.IFrame.GetText(CefSharp.IStringVisitor)">
            <summary>
            Retrieve this frame's display text as a string sent to the specified visitor. 
            Use the <see cref="M:CefSharp.IFrame.GetTextAsync"/> method for a Task based async wrapper
            </summary>
            <param name="visitor">visitor will recieve string values asynchronously</param>
        </member>
        <member name="M:CefSharp.IFrame.LoadRequest(CefSharp.IRequest)">
            <summary>
            Load the custom request.
            </summary>
            <param name="request">request to be loaded in the frame</param>
        </member>
        <member name="M:CefSharp.IFrame.LoadUrl(System.String)">
            <summary>
            Load the specified url.
            </summary>
            <param name="url">url to be loaded in the frame</param>
        </member>
        <member name="M:CefSharp.IFrame.LoadStringForUrl(System.String,System.String)">
            <summary>
            Load the contents of html with the specified dummy url.
            </summary>
            <param name="html">html to be loaded</param>
            <param name="url"> should have a standard scheme (for example, http scheme) or behaviors like 
            link clicks and web security restrictions may not behave as expected.</param>
        </member>
        <member name="M:CefSharp.IFrame.ExecuteJavaScriptAsync(System.String,System.String,System.Int32)">
            <summary>
            Execute a string of JavaScript code in this frame.
            </summary>
            <param name="code">Javascript to execute</param>
            <param name="scriptUrl">is the URL where the script in question can be found, if any.
            The renderer may request this URL to show the developer the source of the error.</param>
            <param name="startLine">is the base line number to use for error reporting.</param>
        </member>
        <member name="M:CefSharp.IFrame.EvaluateScriptAsync(System.String,System.String,System.Int32,System.Nullable{System.TimeSpan})">
            <summary>
            Execute some Javascript code in the context of this WebBrowser, and return the result of the evaluation
            in an Async fashion
            </summary>
            <param name="script">The Javascript code that should be executed.</param>
            <param name="scriptUrl">is the URL where the script in question can be found, if any.</param>
            <param name="startLine">is the base line number to use for error reporting.</param>
            <param name="timeout">The timeout after which the Javascript code execution should be aborted.</param>
            <returns>A Task that can be awaited to perform the script execution</returns>
        </member>
        <member name="M:CefSharp.IFrame.CreateRequest(System.Boolean)">
            <summary>
            Create a custom request for use with <see cref="M:CefSharp.IFrame.LoadRequest(CefSharp.IRequest)"/>
            </summary>
            <param name="initializePostData">Initialize the PostData object when creating this request</param>
            <returns>A new instance of the request</returns>
        </member>
        <member name="P:CefSharp.IFrame.IsValid">
            <summary>
            True if this object is currently attached to a valid frame.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.IsMain">
            <summary>
            Returns true if this is the main (top-level) frame.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.IsFocused">
            <summary>
            Returns true if this is the focused frame.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.Name">
            <summary>
            Returns the name for this frame. If the frame has an assigned name (for
            example, set via the iframe "name" attribute) then that value will be
            returned. Otherwise a unique name will be constructed based on the frame
            parent hierarchy. The main (top-level) frame will always have an empty name
            value.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.Identifier">
            <summary>
            Returns the globally unique identifier for this frame or &lt; 0 if the underlying frame does not yet exist.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.Parent">
            <summary>
            Returns the parent of this frame or NULL if this is the main (top-level) frame.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.Url">
            <summary>
            Returns the URL currently loaded in this frame.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.Browser">
            <summary>
            Returns the browser that this frame belongs to.
            </summary>
        </member>
        <member name="P:CefSharp.IFrame.IsDisposed">
            <summary>
            Gets a value indicating whether the frame has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.IBrowserAdapter">
            <summary>
            Interface used to break reference cycles in CefSharp.Core C++ code.
            This will ALWAYS be a ManagedCefBrowserAdapter instance.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.IJavascriptCallbackFactory">
            <summary>
            Interface to convert a JavascriptCallback dto to a callable implementation.
            </summary>
        </member>
        <member name="M:CefSharp.Internals.IntPtrExtensions.CastToInt32(System.IntPtr)">
            <summary>
            Do an unchecked conversion from IntPtr to int
            so overflow exceptions don't get thrown.
            </summary>
            <param name="intPtr">the IntPtr to cast</param>
            <returns>a 32-bit signed integer</returns>
        </member>
        <member name="T:CefSharp.Internals.PendingTaskRepository`1">
            <summary>
            Class to store TaskCompletionSources indexed by a unique id.
            </summary>
            <typeparam name="TResult">The type of the result produced by the tasks held.</typeparam>
        </member>
        <member name="M:CefSharp.Internals.PendingTaskRepository`1.CreatePendingTask(System.Nullable{System.TimeSpan})">
            <summary>
            Creates a new pending task with a timeout.
            </summary>
            <param name="timeout">The maximum running time of the task.</param>
            <returns>The unique id of the newly created pending task and the newly created <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/>.</returns>
        </member>
        <member name="M:CefSharp.Internals.PendingTaskRepository`1.RemovePendingTask(System.Int64)">
            <summary>
            Gets and removed pending task by id.
            </summary>
            <param name="id">Unique id of the pending task.</param>
            <returns>
            The <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/> associated with the given id.
            </returns>
        </member>
        <member name="T:CefSharp.IResponse">
            <summary>
            Class used to represent a web response. The methods of this class may be called on any thread. 
            </summary>
        </member>
        <member name="P:CefSharp.IResponse.MimeType">
            <summary>
            MimeType
            </summary>
        </member>
        <member name="P:CefSharp.IResponse.ResponseHeaders">
            <summary>
            Response Headers
            </summary>
        </member>
        <member name="P:CefSharp.IResponse.IsReadOnly">
            <summary>
            Returns true if this object is read-only.
            </summary>
        </member>
        <member name="P:CefSharp.IResponse.ErrorCode">
            <summary>
            Get/set the response error code.
            </summary>
        </member>
        <member name="P:CefSharp.IResponse.StatusCode">
            <summary>
            The status code of the response. Unless set, the default value used is 200
            (corresponding to HTTP status OK).
            </summary>
        </member>
        <member name="P:CefSharp.IResponse.StatusText">
            <summary>
            Status Text
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CefSharp.IWindowInfo.SetAsWindowless(System.IntPtr)" -->
        <member name="T:CefSharp.MouseEvent">
            <summary>
            Struct representing a mouse event.
            </summary>
        </member>
        <member name="P:CefSharp.MouseEvent.X">
            <summary>
            x coordinate - relative to upper-left corner of view
            </summary>
        </member>
        <member name="P:CefSharp.MouseEvent.Y">
            <summary>
            y coordinate - relative to upper-left corner of view
            </summary>
        </member>
        <member name="P:CefSharp.MouseEvent.Modifiers">
            <summary>
            Bit flags describing any pressed modifier keys.
            </summary>
        </member>
        <member name="T:CefSharp.IBitmapFactory">
            <summary>
            Factory class used to generate a BitmapInfo object for OSR rendering (WPF and OffScreen projects)
            Implement this interface if you wish to render the underlying Bitmap to a custom type
            e.g. a GDI Bitmap in the WPF Control
            </summary>
        </member>
        <member name="M:CefSharp.IBitmapFactory.CreateBitmap(System.Boolean,System.Double)">
            <summary>
            Create an instance of BitmapInfo based on the params
            </summary>
            <param name="isPopup">create bitmap info for a popup (typically just a bool flag used internally)</param>
            <param name="dpiScale">DPI scale</param>
            <returns>newly created BitmapInfo</returns>
        </member>
        <member name="T:CefSharp.IDragData">
            <summary>
            Used to represent drag data.
            </summary>
        </member>
        <member name="M:CefSharp.IDragData.AddFile(System.String,System.String)">
            <summary>
            Add a file that is being dragged into the webview.
            </summary>
            <param name="path">File Path</param>
            <param name="displayName">Optional Display Name</param>
        </member>
        <member name="M:CefSharp.IDragData.ResetFileContents">
            <summary>
            Reset the file contents. You should do this before calling
            CefBrowserHost::DragTargetDragEnter as the web view does not allow us to
            drag in this kind of data.
            </summary>
        </member>
        <member name="M:CefSharp.IDragData.GetFileContents">
            <summary>
            Gets the contents of the File as a <see cref="T:System.IO.Stream"/>
            For a suggested filename check the <see cref="P:CefSharp.IDragData.FileName"/> property
            </summary>
            <returns>the contents of the file</returns>
        </member>
        <member name="P:CefSharp.IDragData.IsReadOnly">
            <summary>
            Returns true if this object is read-only.
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.FileName">
            <summary>
            Return the name of the file being dragged out of the browser window.
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.FileNames">
            <summary>
            Retrieve the list of file names that are being dragged into the browser window
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.FragmentBaseUrl">
            <summary>
            Return the base URL that the fragment came from. This value is used for resolving relative URLs and may be empty. 
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.FragmentHtml">
            <summary>
            Return the text/html fragment that is being dragged. 
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.FragmentText">
            <summary>
            Return the plain text fragment that is being dragged.
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.LinkMetaData">
            <summary>
            Return the metadata, if any, associated with the link being dragged. 
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.LinkTitle">
            <summary>
            Return the title associated with the link being dragged.
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.LinkUrl">
            <summary>
            Return the link URL that is being dragged. 
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.IsFile">
            <summary>
            Returns true if the drag data is a file.
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.IsFragment">
            <summary>
            Returns true if the drag data is a text or html fragment.
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.IsLink">
            <summary>
            Returns true if the drag data is a link
            </summary>
        </member>
        <member name="P:CefSharp.IDragData.IsDisposed">
            <summary>
            Gets a value indicating whether the object has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.DownloadItem">
            <summary>
            Class used to represent a download item.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.IsValid">
            <summary>
            Returns true if this object is valid. Do not call any other methods if this function returns false.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.IsInProgress">
            <summary>
            Returns true if the download is in progress.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.IsComplete">
            <summary>
            Returns true if the download is complete.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.IsCancelled">
            <summary>
            Returns true if the download has been canceled or interrupted.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.CurrentSpeed">
            <summary>
            Returns a simple speed estimate in bytes/s.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.PercentComplete">
            <summary>
            Returns the rough percent complete or -1 if the receive total size is unknown.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.TotalBytes">
            <summary>
            Returns the total number of bytes.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.ReceivedBytes">
            <summary>
            Returns the number of received bytes.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.StartTime">
            <summary>
            Returns the time that the download started
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.EndTime">
            <summary>
            Returns the time that the download ended
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.FullPath">
            <summary>
            Returns the full path to the downloaded or downloading file.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.Id">
            <summary>
            Returns the unique identifier for this download.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.Url">
            <summary>
            Returns the URL.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.OriginalUrl">
            <summary>
            Returns the URL as it was before any redirects.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.SuggestedFileName">
            <summary>
            Returns the suggested file name.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.ContentDisposition">
            <summary>
            Returns the content disposition.
            </summary>
        </member>
        <member name="P:CefSharp.DownloadItem.MimeType">
            <summary>
            Returns the mime type.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.TaskExtensions">
            <summary>
            TaskExtension based on the following
            https://github.com/ChadBurggraf/parallel-extensions-extras/blob/master/Extensions/TaskExtrasExtensions.cs
            https://github.com/ChadBurggraf/parallel-extensions-extras/blob/ec803e58eee28c698e44f55f49c5ad6671b1aa58/Extensions/TaskCompletionSourceExtensions.cs
            </summary>
        </member>
        <member name="M:CefSharp.Internals.TaskExtensions.WithTimeout``1(System.Threading.Tasks.Task{``0},System.TimeSpan)">
            <summary>Creates a new Task that mirrors the supplied task but that will be canceled after the specified timeout.</summary>
            <typeparam name="TResult">Specifies the type of data contained in the task.</typeparam>
            <param name="task">The task.</param>
            <param name="timeout">The timeout.</param>
            <returns>The new Task that may time out.</returns>
        </member>
        <member name="M:CefSharp.Internals.TaskExtensions.TrySetFromTask``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Threading.Tasks.Task)">
            <summary>Attempts to transfer the result of a Task to the TaskCompletionSource.</summary>
            <typeparam name="TResult">Specifies the type of the result.</typeparam>
            <param name="resultSetter">The TaskCompletionSource.</param>
            <param name="task">The task whose completion results should be transfered.</param>
            <returns>Whether the transfer could be completed.</returns>
        </member>
        <member name="M:CefSharp.Internals.TaskExtensions.TrySetFromTask``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Threading.Tasks.Task{``0})">
            <summary>Attempts to transfer the result of a Task to the TaskCompletionSource.</summary>
            <typeparam name="TResult">Specifies the type of the result.</typeparam>
            <param name="resultSetter">The TaskCompletionSource.</param>
            <param name="task">The task whose completion results should be transfered.</param>
            <returns>Whether the transfer could be completed.</returns>
        </member>
        <member name="M:CefSharp.Internals.TaskExtensions.TrySetResultAsync``1(System.Threading.Tasks.TaskCompletionSource{``0},``0)">
            <summary>
            Set the TaskCompletionSource in an async fashion. This prevents the Task Continuation being executed sync on the same thread
            This is required otherwise contintinuations will happen on CEF UI threads
            </summary>
            <typeparam name="TResult">Generic param</typeparam>
            <param name="taskCompletionSource">tcs</param>
            <param name="result">result</param>
        </member>
        <member name="T:CefSharp.IContextMenuParams">
            <summary>
            Wrapper for the CefContextMenuParams
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.YCoord">
            <summary>
            Returns the Y coordinate of the mouse where the context menu was invoked.
            Coords are relative to the associated RenderView's origin.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.XCoord">
            <summary>
            Returns the X coordinate of the mouse where the context menu was invoked.
            Coords are relative to the associated RenderView's origin.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.TypeFlags">
            <summary>
            Returns flags representing the type of node that the context menu was invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.LinkUrl">
            <summary>
            Returns the URL of the link, if any, that encloses the node that the
            context menu was invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.UnfilteredLinkUrl">
            <summary>
            Returns the link URL, if any, to be used ONLY for "copy link address". We
            don't validate this field in the frontend process.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.SourceUrl">
            <summary>
            Returns the source URL, if any, for the element that the context menu was
            invoked on. Example of elements with source URLs are img, audio, and video.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.HasImageContents">
            <summary>
            Returns true if the context menu was invoked on an image which has
            non-empty contents.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.PageUrl">
            <summary>
            Returns the URL of the top level page that the context menu was invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.FrameUrl">
            <summary>
            Returns the URL of the subframe that the context menu was invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.FrameCharset">
            <summary>
            Returns the character encoding of the subframe that the context menu was
            invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.MediaType">
            <summary>
            Returns the type of context node that the context menu was invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.MediaStateFlags">
            <summary>
            Returns flags representing the actions supported by the media element, if
            any, that the context menu was invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.SelectionText">
            <summary>
            Returns the text of the selection, if any, that the context menu was
            invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.MisspelledWord">
            <summary>
            Returns the text of the misspelled word, if any, that the context menu was
            invoked on.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.DictionarySuggestions">
            <summary>
            Returns a list of strings from the spell check service for the misspelled word if there is one.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.IsEditable">
            <summary>
            Returns true if the context menu was invoked on an editable node.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.IsSpellCheckEnabled">
            <summary>
            Returns true if the context menu was invoked on an editable node where
            spell-check is enabled.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.EditStateFlags">
            <summary>
            Returns flags representing the actions supported by the editable node, if
            any, that the context menu was invoked on.
            </summary>
            <returns>Returns ContextMenuEditState as flags</returns>
        </member>
        <member name="P:CefSharp.IContextMenuParams.IsCustomMenu">
            <summary>
            Returns true if the context menu contains items specified by the renderer
            process (for example, plugin placeholder or pepper plugin menu items).
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.IsPepperMenu">
            <summary>
            Returns true if the context menu was invoked from a pepper plugin.
            </summary>
        </member>
        <member name="P:CefSharp.IContextMenuParams.IsDisposed">
            <summary>
            Gets a value indicating whether the object has been disposed of.
            </summary>
        </member>
        <member name="T:CefSharp.JavascriptIgnoreAttribute">
            <summary>
            JavascriptIgnoreAttribute - Methods and Properties marked with this attribute
            will be excluded from Javascript Binding
            </summary>
        </member>
        <member name="M:CefSharp.PostDataExtensions.GetCharSet(CefSharp.IRequest)">
            <summary>
            A convenience extension method that extracts the Character set from
            the content-type header. Can be used in conjuncation with <see cref="M:CefSharp.PostDataExtensions.GetBody(CefSharp.IPostDataElement,System.String)"/>
            </summary>
            <param name="request">the request</param>
            <returns>character set e.g. UTF-8</returns>
        </member>
        <member name="M:CefSharp.PostDataExtensions.GetBody(CefSharp.IPostDataElement,System.String)">
            <summary>
            Converts the <see cref="P:CefSharp.IPostDataElement.Bytes"/> property into a string
            using the specified charset (Encoding) or if unable to parse then uses
            the <see cref="P:System.Text.Encoding.Default"/>
            </summary>
            <param name="postDataElement">post data</param>
            <param name="charSet">character set</param>
            <returns>encoded string</returns>
        </member>
        <member name="M:CefSharp.PostDataExtensions.AddFile(CefSharp.IPostData,System.String)">
            <summary>
            Add a new <see cref="T:CefSharp.IPostDataElement"/> that represents the specified file
            </summary>
            <param name="postData">post data instance</param>
            <param name="fileName">file name</param>
        </member>
        <member name="M:CefSharp.PostDataExtensions.AddData(CefSharp.IPostData,System.String,System.Text.Encoding)">
            <summary>
            Add a new <see cref="T:CefSharp.IPostDataElement"/> that represents the key and value
            The data is encoded using
            </summary>
            <param name="postData">Post Data</param>
            <param name="data">Data to be encoded for the post data element</param>
            <param name="encoding">Specified Encoding. If null then <see cref="P:System.Text.Encoding.Default"/> will be used</param>
        </member>
        <member name="M:CefSharp.PostDataExtensions.AddData(CefSharp.IPostData,System.Byte[])">
            <summary>
            Add a new <see cref="T:CefSharp.IPostDataElement"/> that represents the key and value
            </summary>
            <param name="postData">Post Data</param>
            <param name="bytes">byte array that represents the post data</param>
        </member>
        <member name="T:CefSharp.Range">
            <summary>
            Represents a range
            </summary>
        </member>
        <member name="P:CefSharp.Range.From">
            <summary>
            From
            </summary>
        </member>
        <member name="P:CefSharp.Range.To">
            <summary>
            To
            </summary>
        </member>
        <member name="T:CefSharp.Rect">
            <summary>
            Represents a rectangle
            </summary>
        </member>
        <member name="P:CefSharp.Rect.X">
            <summary>
            X coordinate
            </summary>
        </member>
        <member name="P:CefSharp.Rect.Y">
            <summary>
            Y coordinate
            </summary>
        </member>
        <member name="P:CefSharp.Rect.Width">
            <summary>
            Width
            </summary>
        </member>
        <member name="P:CefSharp.Rect.Height">
            <summary>
            Height
            </summary>
        </member>
        <member name="P:CefSharp.ResolveCallbackResult.Result">
            <summary>
            The result code - <see cref="F:CefSharp.CefErrorCode.None"/> on success
            </summary>
        </member>
        <member name="P:CefSharp.ResolveCallbackResult.ResolvedIpAddresses">
            <summary>
            List of resolved IP addresses or empty if the resolution failed.
            </summary>
        </member>
        <member name="T:CefSharp.ResourceHandler">
            <summary>
            Default implementation of <see cref="T:CefSharp.IResourceHandler"/>. This latest implementation provides some simplification, at
            a minimum you only need to override ProcessRequestAsync. See the project source on GitHub for working examples.
            used to implement a custom request handler interface. The methods of this class will always be called on the IO thread. 
            Static helper methods are included like FromStream and FromString that make dealing with fixed resources easy.
            </summary>
        </member>
        <member name="F:CefSharp.ResourceHandler.DefaultMimeType">
            <summary>
            MimeType to be used if none provided
            </summary>
        </member>
        <member name="M:CefSharp.ResourceHandler.#ctor(System.String,System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.ResourceHandler"/> class.
            </summary>
            <param name="mimeType">Optional mimeType defaults to <see cref="F:CefSharp.ResourceHandler.DefaultMimeType"/></param>
            <param name="stream">Optional Stream - must be set at some point to provide a valid response</param>
        </member>
        <member name="M:CefSharp.ResourceHandler.ProcessRequestAsync(CefSharp.IRequest,CefSharp.ICallback)">
            <summary>
            Begin processing the request. If you have the data in memory you can execute the callback
            immediately and return true. For Async processing you would typically spawn a Task to perform processing,
            then return true. When the processing is complete execute callback.Continue(); In your processing Task, simply set
            the StatusCode, StatusText, MimeType, ResponseLength and Stream
            </summary>
            <param name="request">The request object.</param>
            <param name="callback">The callback used to Continue or Cancel the request (async).</param>
            <returns>To handle the request return true and call
            <see cref="M:CefSharp.ICallback.Continue"/> once the response header information is available
            <see cref="M:CefSharp.ICallback.Continue"/> can also be called from inside this method if
            header information is available immediately).
            To cancel the request return false.</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.GetResponse(CefSharp.IResponse,System.Int64@,System.String@)">
            <summary>
            Populate the response stream, response length. When this method is called
            the response should be fully populated with data.
            It is possible to redirect to another url at this point in time.
            NOTE: It's no longer manditory to implement this method, you can simply populate the
            properties of this instance and they will be set by the default implementation. 
            </summary>
            <param name="response">The response object used to set Headers, StatusCode, etc</param>
            <param name="responseLength">length of the response</param>
            <param name="redirectUrl">If set the request will be redirect to specified Url</param>
            <returns>The response stream</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.Cancel">
            <summary>
            Called if the request is cancelled
            </summary>
        </member>
        <member name="M:CefSharp.ResourceHandler.FromFilePath(System.String,System.String)">
            <summary>
            Gets the resource from the file path specified. Use the <see cref="M:CefSharp.ResourceHandler.GetMimeType(System.String)"/>
            helper method to lookup the mimeType if required. Uses CefStreamResourceHandler for reading the data
            </summary>
            <param name="filePath">Location of the file.</param>
            <param name="mimeType">The mimeType if null then text/html is used.</param>
            <returns>IResourceHandler.</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.FromByteArray(System.Byte[],System.String)">
            <summary>
            Creates a IResourceHandler that represents a Byte[], uses CefStreamResourceHandler for reading the data
            </summary>
            <param name="data">data</param>
            <param name="mimeType">mimeType</param>
            <returns>IResourceHandler</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.FromString(System.String,System.String)">
            <summary>
            Gets the resource from the string.
            </summary>
            <param name="text">The text.</param>
            <param name="fileExtension">The file extension.</param>
            <returns>ResourceHandler.</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.FromString(System.String,System.Text.Encoding,System.Boolean,System.String)">
            <summary>
            Gets a <see cref="T:CefSharp.ResourceHandler"/> that represents a string.
            Without a Preamble, Cef will use BrowserSettings.DefaultEncoding to load the html.
            </summary>
            <param name="text">The html string</param>
            <param name="encoding">Character Encoding</param>
            <param name="includePreamble">Include encoding preamble</param>
            <param name="mimeType">Mime Type</param>
            <returns>ResourceHandler</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.ForErrorMessage(System.String,System.Net.HttpStatusCode)">
            <summary>
            Generates a ResourceHandler that has it's StatusCode set
            </summary>
            <param name="errorMessage">Body the response to be displayed</param>
            <param name="statusCode">StatusCode</param>
            <returns>ResourceHandler</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.FromStream(System.IO.Stream,System.String)">
            <summary>
            Gets the resource from a stream.
            </summary>
            <param name="stream">A stream of the resource.</param>
            <param name="mimeType">Type of MIME.</param>
            <returns>ResourceHandler.</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.GetMemoryStream(System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Gets a MemoryStream from the given string using the provided encoding
            </summary>
            <param name="text">string to be converted to a stream</param>
            <param name="encoding">encoding</param>
            <param name="includePreamble">if true a BOM will be written to the beginning of the stream</param>
            <returns>A memory stream from the given string</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.GetByteArray(System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Gets a byteArray from the given string using the provided encoding
            </summary>
            <param name="text">string to be converted to a stream</param>
            <param name="encoding">encoding</param>
            <param name="includePreamble">if true a BOM will be written to the beginning of the stream</param>
            <returns>A memory stream from the given string</returns>
        </member>
        <member name="M:CefSharp.ResourceHandler.GetMimeType(System.String)">
            <summary>
            Gets the MIME type of the content.
            </summary>
            <param name="extension">The extension.</param>
            <returns>System.String.</returns>
            <exception cref="T:System.ArgumentNullException">extension</exception>
        </member>
        <member name="M:CefSharp.ResourceHandler.Dispose">
            <summary>
            Dispose of resources here
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.MimeType">
            <summary>
            Gets or sets the Mime Type.
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.Stream">
            <summary>
            Gets or sets the resource stream.
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.StatusCode">
            <summary>
            Gets or sets the http status code.
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.StatusText">
            <summary>
            Gets or sets the status text.
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.ResponseLength">
            <summary>
            Gets or sets ResponseLength, when you know the size of your
            Stream (Response) set this property. This is optional.
            If you use a MemoryStream and don't provide a value
            here then it will be cast and it's size used
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.Headers">
            <summary>
            Gets or sets the headers.
            </summary>
            <value>The headers.</value>
        </member>
        <member name="P:CefSharp.ResourceHandler.AutoDisposeStream">
            <summary>
            When true the Stream will be Disposed when
            this instance is Disposed. The default value for
            this property is false.
            </summary>
        </member>
        <member name="P:CefSharp.ResourceHandler.ErrorCode">
            <summary>
            If the ErrorCode is set then the response will be ignored and
            the errorCode returned.
            </summary>
        </member>
        <member name="T:CefSharp.SchemeHandler.FolderSchemeHandlerFactory">
            <summary>
            FolderSchemeHandlerFactory is a very simple scheme handler that allows you
            to map requests for urls to a folder on your file system. For example
            creating a setting the rootFolder to c:\projects\CefSharp\CefSharp.Example\Resources
            registering the scheme handler
            </summary>
        </member>
        <member name="T:CefSharp.ISchemeHandlerFactory">
            <summary>
            Class that creates <see cref="T:CefSharp.IResourceHandler"/> instances for handling scheme requests.
            The methods of this class will always be called on the CEF IO thread.
            </summary>
        </member>
        <member name="M:CefSharp.ISchemeHandlerFactory.Create(CefSharp.IBrowser,CefSharp.IFrame,System.String,CefSharp.IRequest)">
            <summary>
            Return a new <see cref="T:CefSharp.IResourceHandler"/> instance to handle the request or an empty
            reference to allow default handling of the request.
            </summary>
            <param name="browser">the browser window that originated the
            request or null if the request did not originate from a browser window
            (for example, if the request came from CefURLRequest).</param>
            <param name="frame">frame that originated the request
            or null if the request did not originate from a browser window
            (for example, if the request came from CefURLRequest).</param>
            <param name="schemeName">the scheme name</param>
            <param name="request">The request. (will not contain cookie data)</param>
            <returns>
            Return a new <see cref="T:CefSharp.IResourceHandler"/> instance to handle the request or an empty
            reference to allow default handling of the request
            </returns>
        </member>
        <member name="M:CefSharp.SchemeHandler.FolderSchemeHandlerFactory.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Initialize a new instance of FolderSchemeHandlerFactory
            </summary>
            <param name="rootFolder">Root Folder where all your files exist, requests cannot be made outside of this folder</param>
            <param name="schemeName">if not null then schemeName checking will be implemented</param>
            <param name="hostName">if not null then hostName checking will be implemented</param>
            <param name="defaultPage">default page if no page specified, defaults to index.html</param>
        </member>
        <member name="M:CefSharp.SchemeHandler.FolderSchemeHandlerFactory.CefSharp#ISchemeHandlerFactory#Create(CefSharp.IBrowser,CefSharp.IFrame,System.String,CefSharp.IRequest)">
            <summary>
            If the file requested is within the rootFolder then a IResourceHandler reference to the file requested will be returned
            otherwise a 404 ResourceHandler will be returned.
            </summary>
            <param name="browser">the browser window that originated the
            request or null if the request did not originate from a browser window
            (for example, if the request came from CefURLRequest).</param>
            <param name="frame">frame that originated the request
            or null if the request did not originate from a browser window
            (for example, if the request came from CefURLRequest).</param>
            <param name="schemeName">the scheme name</param>
            <param name="request">The request. (will not contain cookie data)</param>
            <returns>
            A IResourceHandler
            </returns>
        </member>
        <member name="T:CefSharp.SslStatus">
            <summary>
            Class representing the SSL information for a navigation entry.
            </summary>
        </member>
        <member name="P:CefSharp.SslStatus.IsSecureConnection">
            <summary>
            Returns true if the status is related to a secure SSL/TLS connection.
            </summary>
        </member>
        <member name="P:CefSharp.SslStatus.CertStatus">
            <summary>
            Returns a bitmask containing any and all problems verifying the server
            certificate.
            </summary>
            <returns></returns>
        </member>
        <member name="P:CefSharp.SslStatus.SslVersion">
            <summary>
            Returns the SSL version used for the SSL connection.
            </summary>
            <returns></returns>
        </member>
        <member name="P:CefSharp.SslStatus.ContentStatus">
            <summary>
            Returns a bitmask containing the page security content status.
            </summary>
        </member>
        <member name="P:CefSharp.SslStatus.X509Certificate">
            <summary>
            Returns the X.509 certificate.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.BitmapInfo">
            <summary>
            Information about the bitmap that should be rendered
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptMethod.Function">
            <summary>
            Gets or sets a delegate which is used to invoke the method if the member is a method. 
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptMethod.Id">
            <summary>
            Identifies the <see cref="T:CefSharp.Internals.JavascriptMethod"/> for BrowserProcess to RenderProcess communication
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptMethod.ManagedName">
            <summary>
            Gets or sets the name of the managed property.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptMethod.JavascriptName">
            <summary>
            Gets or sets the name of the property in the JavaScript runtime.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptMethod.Parameters">
            <summary>
            Params this method expects
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptMethod.ParameterCount">
            <summary>
            Number of Params this function exepects
            </summary>
        </member>
        <member name="T:CefSharp.Internals.JavascriptObject">
            <summary>
            This maps the registered objects in the browser process
            to the reflection data necessary to update the objects,
            and mapping information to how the object/method/proprerty
            will be exposed to JavaScript.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObject.Id">
            <summary>
            Identifies the <see cref="T:CefSharp.Internals.JavascriptObject"/> for BrowserProcess to RenderProcess communication
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObject.IsAsync">
            <summary>
            Indicate if this object bound as async
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObject.CamelCaseJavascriptNames">
            <summary>
            Indicate if JavascriptName is camel case or not
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObject.Methods">
            <summary>
            Gets the methods of the <see cref="T:CefSharp.Internals.JavascriptObject"/>.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObject.Properties">
            <summary>
            Gets the properties of the <see cref="T:CefSharp.Internals.JavascriptObject"/>.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObject.Value">
            <summary>
            Gets or sets the value.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.JavascriptObjectRepository">
            <summary>
            This class manages the registration of objects in the browser
            process to be exposed to JavaScript in the renderer process.
            Registration performs method, parameter, property type analysis
            of the registered objects into meta-data tied to reflection data 
            for later use.
            
            This class also is the adaptation layer between the BrowserProcessService
            and the registered objects. This means when the renderer wants to call an 
            exposed method, get a property of an object, or
            set a property of an object in the browser process, that this
            class does deals with the previously created meta-data and invokes the correct
            behavior via reflection APIs.
            
            All of the registered objects are tracked via meta-data for the objects 
            expressed starting with the JavaScriptObject type.
            </summary>
        </member>
        <member name="F:CefSharp.Internals.JavascriptObjectRepository.objects">
            <summary>
            A hash from assigned object ids to the objects,
            this is done to speed up finding the object in O(1) time
            instead of traversing the JavaScriptRootObject tree.
            </summary>
        </member>
        <member name="M:CefSharp.Internals.JavascriptObjectRepository.AnalyseObjectForBinding(CefSharp.Internals.JavascriptObject,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Analyse the object and generate metadata which will
            be used by the browser subprocess to interact with Cef.
            Method is called recursively
            </summary>
            <param name="obj">Javascript object</param>
            <param name="analyseMethods">Analyse methods for inclusion in metadata model</param>
            <param name="analyseProperties">Analyse properties for inclusion in metadata model</param>
            <param name="readPropertyValue">When analysis is done on a property, if true then get it's value for transmission over WCF</param>
            <param name="camelCaseJavascriptNames">camel case the javascript names of properties/methods</param>
        </member>
        <member name="P:CefSharp.Internals.JavascriptObjectRepository.IsBrowserInitialized">
            <summary>
            Has the browser this repository is associated with been initilized (set in OnAfterCreated)
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.SetValue">
            <summary>
            Gets or sets a delegate which is used to set the property / field value in the managed object.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.GetValue">
            <summary>
            Gets or sets a delegate which is used to get the property / field value from the managed object.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.Id">
            <summary>
            Identifies the <see cref="T:CefSharp.Internals.JavascriptProperty"/> for BrowserProcess to RenderProcess communication
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.ManagedName">
            <summary>
            Gets or sets the name of the managed property.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.JavascriptName">
            <summary>
            Gets or sets the name of the property in the JavaScript runtime.
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.IsComplexType">
            <summary>
            Gets or sets if this property represents a complex type
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.IsReadOnly">
            <summary>
            Gets or sets if this property is read-only
            </summary>
        </member>
        <member name="P:CefSharp.Internals.JavascriptProperty.PropertyValue">
            <summary>
            Gets or sets the property value
            Only primative types can be stored in this property
            </summary>
        </member>
        <member name="T:CefSharp.CefCustomScheme">
            <summary>
            Used in conjunction with CefSettings.RegisterScheme to register a scheme.
            You can register your own custom scheme e.g. custom:// or use an existing
            scheme e.g. http://
            </summary>
        </member>
        <member name="M:CefSharp.CefCustomScheme.#ctor">
            <summary>
            Creates a new CefCustomScheme.
            </summary>
        </member>
        <member name="M:CefSharp.CefCustomScheme.ParseCommandLineArguments(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Method used internally
            </summary>
            <param name="args">command line arguments</param>
            <returns>list of scheme objects</returns>
        </member>
        <member name="P:CefSharp.CefCustomScheme.SchemeName">
            <summary>
            Schema Name e.g. custom
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.DomainName">
            <summary>
            Optional Domain Name. An empty value for a standard scheme
            will cause the factory to match all domain names. The |domain_name| value
            will be ignored for non-standard schemes.
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.IsStandard">
             <summary>
             If true the scheme will be treated as a standard scheme.
             Standard schemes are subject to URL canonicalization and parsing rules as
             defined in the Common Internet Scheme Syntax RFC 1738 Section 3.1 available
             at http://www.ietf.org/rfc/rfc1738.txt
            
             In particular, the syntax for standard scheme URLs must be of the form:
             <pre>
              [scheme]://[username]:[password]@[host]:[port]/[url-path]
             </pre>
             Standard scheme URLs must have a host component that is a fully qualified
             domain name as defined in Section 3.5 of RFC 1034 [13] and Section 2.1 of
             RFC 1123. These URLs will be canonicalized to "scheme://host/path" in the
             simplest case and "scheme://username:password@host:port/path" in the most
             explicit case. For example, "scheme:host/path" and "scheme:///host/path"
             will both be canonicalized to "scheme://host/path". The origin of a
             standard scheme URL is the combination of scheme, host and port (i.e.,
             "scheme://host:port" in the most explicit case).
            
             For non-standard scheme URLs only the "scheme:" component is parsed and
             canonicalized. The remainder of the URL will be passed to the handler
             as-is. For example, "scheme:///some%20text" will remain the same.
             Non-standard scheme URLs cannot be used as a target for form submission.
             </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.IsLocal">
            <summary>
            If true the scheme will be treated as local (i.e. with the
            same security rules as those applied to "file" URLs). Normal pages cannot
            link to or access local URLs. Also, by default, local URLs can only perform
            XMLHttpRequest calls to the same URL (origin + path) that originated the
            request. To allow XMLHttpRequest calls from a local URL to other URLs with
            the same origin set the CefSettings.file_access_from_file_urls_allowed
            value to true. To allow XMLHttpRequest calls from a local URL to all
            origins set the CefSettings.universal_access_from_file_urls_allowed value
            to true.
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.IsDisplayIsolated">
            <summary>
            If true the scheme will be treated as display-isolated.
            This means that pages cannot display these URLs unless they are
            from the same scheme. For example, pages in another origin cannot create
            iframes or hyperlinks to URLs with this scheme.
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.IsSecure">
            <summary>
            If true the scheme will be treated with the same security
            rules as those applied to "https" URLs. For example, loading this scheme
            from other secure schemes will not trigger mixed content warnings.
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.IsCorsEnabled">
            <summary>
            If true the scheme can be sent CORS requests.
            This value should be true in most cases where IsStandard is true.
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.IsCSPBypassing">
            <summary>
            If true the scheme can bypass Content-Security-Policy(CSP) checks. 
            This value should be false in most cases where IsStandard is true.
            </summary>
        </member>
        <member name="P:CefSharp.CefCustomScheme.SchemeHandlerFactory">
            <summary>
            Factory Class that creates <see cref="T:CefSharp.IResourceHandler"/> instances
            for handling scheme requests.
            </summary>
        </member>
        <member name="T:CefSharp.Internals.IWebBrowserInternal">
            <summary>
            Interface implemented by UI control that contains
            a ManagedCefBrowserAdapter instance.
            </summary>
        </member>
        <member name="T:CefSharp.IWebBrowser">
            <summary>
            ChromiumWebBrowser implementations implement this interface. Can be cast to
            the concrete implementation to access UI specific features.
            </summary>
            <seealso cref="T:System.IDisposable"/>
        </member>
        <member name="M:CefSharp.IWebBrowser.Load(System.String)">
            <summary>
            Loads the specified URL.
            </summary>
            <param name="url">The URL to be loaded.</param>
        </member>
        <member name="M:CefSharp.IWebBrowser.RegisterJsObject(System.String,System.Object,CefSharp.BindingOptions)">
            <summary>
            Registers a Javascript object in this specific browser instance.
            </summary>
            <param name="name">The name of the object. (e.g. "foo", if you want the object to be accessible as window.foo).</param>
            <param name="objectToBind">The object to be made accessible to Javascript.</param>
            <param name="options">binding options - camelCaseJavascriptNames default to true </param>
        </member>
        <member name="M:CefSharp.IWebBrowser.RegisterAsyncJsObject(System.String,System.Object,CefSharp.BindingOptions)">
            <summary>
            <para>Asynchronously registers a Javascript object in this specific browser instance.</para>
            <para>Only methods of the object will be available.</para>
            </summary>
            <param name="name">The name of the object. (e.g. "foo", if you want the object to be accessible as window.foo).</param>
            <param name="objectToBind">The object to be made accessible to Javascript.</param>
            <param name="options">binding options - camelCaseJavascriptNames default to true </param>
            <remarks>The registered methods can only be called in an async way, they will all return immeditaly and the resulting
            object will be a standard javascript Promise object which is usable to wait for completion or failure.</remarks>
        </member>
        <member name="M:CefSharp.IWebBrowser.Focus">
            <summary>
            Attempts to give focus to the IWebBrowser control.
            </summary>
            <returns><c>true</c> if keyboard focus and logical focus were set to this element; <c>false</c> if only logical focus
            was set to this element, or if the call to this method did not force the focus to change.</returns>
        </member>
        <member name="M:CefSharp.IWebBrowser.GetBrowser">
            <summary>
            Returns the current CEF Browser Instance
            </summary>
            <returns>browser instance or null</returns>
        </member>
        <member name="E:CefSharp.IWebBrowser.ConsoleMessage">
            <summary>
            Event handler for receiving Javascript console messages being sent from web pages.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            (The exception to this is when your running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.IWebBrowser.StatusMessage">
            <summary>
            Event handler for changes to the status message.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang.
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            (The exception to this is when your running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.IWebBrowser.FrameLoadStart">
            <summary>
            Event handler that will get called when the browser begins loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method may not be called for a
            particular frame if the load request for that frame fails. For notification of overall browser load status use
            OnLoadingStateChange instead.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
            <remarks>Whilst this may seem like a logical place to execute js, it's called before the DOM has been loaded, implement
            <see cref="M:CefSharp.IRenderProcessMessageHandler.OnContextCreated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)"/> as it's called when the underlying V8Context is created
            (Only called for the main frame at this stage)</remarks>
        </member>
        <member name="E:CefSharp.IWebBrowser.FrameLoadEnd">
            <summary>
            Event handler that will get called when the browser is done loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method will always be called
            for all frames irrespective of whether the request completes successfully.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="E:CefSharp.IWebBrowser.LoadError">
            <summary>
            Event handler that will get called when the resource load for a navigation fails or is canceled.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="E:CefSharp.IWebBrowser.LoadingStateChanged">
            <summary>
            Event handler that will get called when the Loading state has changed.
            This event will be fired twice. Once when loading is initiated either programmatically or
            by user action, and once when loading is terminated due to completion, cancellation of failure. 
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="P:CefSharp.IWebBrowser.JavascriptObjectRepository">
            <summary>
            The javascript object repository, one repository per ChromiumWebBrowser instance.
            </summary>
        </member>
        <member name="P:CefSharp.IWebBrowser.DialogHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDialogHandler"/> and assign to handle dialog events.
            </summary>
            <value>The dialog handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.RequestHandler">
            <summary>
            Implement <see cref="T:CefSharp.IRequestHandler"/> and assign to handle events related to browser requests.
            </summary>
            <value>The request handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.DisplayHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDisplayHandler"/> and assign to handle events related to browser display state.
            </summary>
            <value>The display handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.LoadHandler">
            <summary>
            Implement <see cref="T:CefSharp.ILoadHandler"/> and assign to handle events related to browser load status.
            </summary>
            <value>The load handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.LifeSpanHandler">
            <summary>
            Implement <see cref="T:CefSharp.ILifeSpanHandler"/> and assign to handle events related to popups.
            </summary>
            <value>The life span handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.KeyboardHandler">
            <summary>
            Implement <see cref="T:CefSharp.IKeyboardHandler"/> and assign to handle events related to key press.
            </summary>
            <value>The keyboard handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.JsDialogHandler">
            <summary>
            Implement <see cref="T:CefSharp.IJsDialogHandler"/> and assign to handle events related to JavaScript Dialogs.
            </summary>
            <value>The js dialog handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.DragHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDragHandler"/> and assign to handle events related to dragging.
            </summary>
            <value>The drag handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.DownloadHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDownloadHandler"/> and assign to handle events related to downloading files.
            </summary>
            <value>The download handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.MenuHandler">
            <summary>
            Implement <see cref="T:CefSharp.IContextMenuHandler"/> and assign to handle events related to the browser context menu
            </summary>
            <value>The menu handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.FocusHandler">
            <summary>
            Implement <see cref="T:CefSharp.IFocusHandler"/> and assign to handle events related to the browser component's focus
            </summary>
            <value>The focus handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.ResourceHandlerFactory">
            <summary>
            Implement <see cref="T:CefSharp.IResourceHandlerFactory"/> and control the loading of resources
            </summary>
            <value>The resource handler factory.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.GeolocationHandler">
            <summary>
            Implement <see cref="T:CefSharp.IGeolocationHandler"/> and assign to handle requests for permission to use geolocation.
            </summary>
            <value>The geolocation handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.RenderProcessMessageHandler">
            <summary>
            Implement <see cref="T:CefSharp.IRenderProcessMessageHandler"/> and assign to handle messages from the render process.
            </summary>
            <value>The render process message handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.FindHandler">
            <summary>
            Implement <see cref="T:CefSharp.IFindHandler"/> to handle events related to find results.
            </summary>
            <value>The find handler.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.IsBrowserInitialized">
            <summary>
            A flag that indicates whether the WebBrowser is initialized (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is browser initialized; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.IWebBrowser.IsLoading">
            <summary>
            A flag that indicates whether the control is currently loading one or more web pages (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is loading; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.IWebBrowser.CanGoBack">
            <summary>
            A flag that indicates whether the state of the control current supports the GoBack action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go back; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.IWebBrowser.CanGoForward">
            <summary>
            A flag that indicates whether the state of the control currently supports the GoForward action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go forward; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.IWebBrowser.Address">
            <summary>
            The address (URL) which the browser control is currently displaying.
            Will automatically be updated as the user navigates to another page (e.g. by clicking on a link).
            </summary>
            <value>The address.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.IWebBrowser.TooltipText">
            <summary>
            The text that will be displayed as a ToolTip
            </summary>
            <value>The tooltip text.</value>
        </member>
        <member name="P:CefSharp.IWebBrowser.CanExecuteJavascriptInMainFrame">
            <summary>
            A flag that indicates if you can execute javascript in the main frame.
            Flag is set to true in IRenderProcessMessageHandler.OnContextCreated.
            and false in IRenderProcessMessageHandler.OnContextReleased
            </summary>
        </member>
        <member name="P:CefSharp.IWebBrowser.RequestContext">
            <summary>
            Gets the custom request context assigned to this browser instance
            If no instance was assigned this will be null and the global
            request context will have been used for this browser. 
            You can access the global request context through Cef.GetGlobalRequestContext()
            </summary>
        </member>
        <member name="M:CefSharp.Internals.IRenderWebBrowser.GetScreenPoint(System.Int32,System.Int32,System.Int32@,System.Int32@)">
            <summary>
            Called to retrieve the translation from view coordinates to actual screen coordinates. 
            </summary>
            <param name="viewX">x</param>
            <param name="viewY">y</param>
            <param name="screenX">screen x</param>
            <param name="screenY">screen y</param>
            <returns>Return true if the screen coordinates were provided.</returns>
        </member>
        <member name="M:CefSharp.Internals.IRenderWebBrowser.OnPaint(CefSharp.Internals.BitmapInfo)">
            <summary>
            Called when an element should be painted.
            Pixel values passed to this method are scaled relative to view coordinates based on the value of
            ScreenInfo.DeviceScaleFactor returned from GetScreenInfo. bitmapInfo.IsPopup indicates whether the element is the view
            or the popup widget. BitmapInfo.DirtyRect contains the set of rectangles in pixel coordinates that need to be
            repainted. The bitmap will be will be  width * height *4 bytes in size and represents a BGRA image with an upper-left origin.
            The underlying buffer is copied into the back buffer and is accessible via BackBufferHandle
            </summary>
            <param name="bitmapInfo">information about the bitmap to be rendered</param>
        </member>
        <member name="M:CefSharp.Internals.IRenderWebBrowser.OnImeCompositionRangeChanged(CefSharp.Range,CefSharp.Rect[])">
            <summary>
            Called when the IME composition range has changed.
            </summary>
            <param name="selectedRange">is the range of characters that have been selected</param>
            <param name="characterBounds">is the bounds of each character in view coordinates.</param>
        </member>
        <member name="T:CefSharp.ICookieVisitor">
            <summary>
            Interface to implement for visiting cookie values. 
            The methods of this class will always be called on the IO thread.
            If there are no cookies then Visit will never be called, you must implement
            Dispose to handle this scenario.
            </summary>
        </member>
        <member name="M:CefSharp.ICookieVisitor.Visit(CefSharp.Cookie,System.Int32,System.Int32,System.Boolean@)">
            <summary>
            Method that will be called once for each cookie. This method may never be called if no cookies are found. 
            </summary>
            <param name="cookie">cookie</param>
            <param name="count">is the 0-based index for the current cookie.</param>
            <param name="total">is the total number of cookies.</param>
            <param name="deleteCookie">Set to true to delete the cookie currently being visited.</param>
            <returns>Return false to stop visiting cookies otherwise true</returns>
        </member>
        <member name="T:CefSharp.INavigationEntryVisitor">
            <summary>
            Callback interface for IBrowserHost.GetNavigationEntries.
            The methods of this class will be called on the CEF UI thread. 
            </summary>
        </member>
        <member name="M:CefSharp.INavigationEntryVisitor.Visit(CefSharp.NavigationEntry,System.Boolean,System.Int32,System.Int32)">
            <summary>
            Method that will be executed.
            </summary>
            <param name="entry">if the navigationEntry will be invalid then </param>
            <param name="current">is true if this entry is the currently loaded navigation entry</param>
            <param name="index">is the 0-based index of this entry</param>
            <param name="total">is the total number of entries.</param>
            <returns>Return true to continue visiting entries or false to stop.</returns>
        </member>
        <member name="T:CefSharp.IStringVisitor">
            <summary>
            Implement this interface to receive string values asynchronously.
            </summary>
        </member>
        <member name="M:CefSharp.IStringVisitor.Visit(System.String)">
            <summary>
             Method that will be executed.
            </summary>
            <param name="str">string (result of async execution)</param>
        </member>
        <member name="T:CefSharp.IWebPluginInfoVisitor">
            <summary>
            Interface to implement for visiting web plugin information.
            The methods of this class will be called on the CEF UI thread,
            which by default is not the same as your application UI 
            </summary>
        </member>
        <member name="M:CefSharp.IWebPluginInfoVisitor.Visit(CefSharp.WebPluginInfo,System.Int32,System.Int32)">
            <summary>
            Method that will be called once for each plugin. 
            This method may never be called if no plugins are found.
            </summary>
            <param name="plugin">plugin information</param>
            <param name="count">is the 0-based index for the current plugin</param>
            <param name="total">total is the total number of plugins.</param>
            <returns>Return false to stop visiting plugins otherwise true</returns>
        </member>
        <member name="T:CefSharp.TaskCookieVisitor">
            <summary>
            Cookie Visitor implementation that uses a TaskCompletionSource
            to return a List of cookies
            </summary>
        </member>
        <member name="M:CefSharp.TaskCookieVisitor.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:CefSharp.TaskCookieVisitor.Task">
            <summary>
            Task that can be awaited for the result to be retrieved async
            </summary>
        </member>
        <member name="T:CefSharp.TaskNavigationEntryVisitor">
            <summary>
            A <see cref="T:CefSharp.INavigationEntryVisitor"/> that uses a TaskCompletionSource
            to simplify things
            </summary>
        </member>
        <member name="M:CefSharp.TaskNavigationEntryVisitor.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:CefSharp.TaskNavigationEntryVisitor.Task">
            <summary>
            Task that can be awaited for the result to be retrieved async
            </summary>
        </member>
        <member name="P:CefSharp.TaskWebPluginInfoVisitor.Task">
            <summary>
            Task that can be awaited for the result to be retrieved async
            </summary>
        </member>
        <member name="T:CefSharp.TaskStringVisitor">
            <summary>
            A <see cref="T:CefSharp.IStringVisitor"/> that uses a TaskCompletionSource
            to simplify things
            </summary>
        </member>
        <member name="M:CefSharp.TaskStringVisitor.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:CefSharp.TaskStringVisitor.CefSharp#IStringVisitor#Visit(System.String)">
            <summary>
             Method that will be executed.
            </summary>
            <param name="str">string (result of async execution)</param>
        </member>
        <member name="P:CefSharp.TaskStringVisitor.Task">
            <summary>
            Task that can be awaited for the result to be retrieved async
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetMainFrame(CefSharp.IWebBrowser)">
            <summary>
            Returns the main (top-level) frame for the browser window.
            </summary>
            <returns>Frame</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetFocusedFrame(CefSharp.IWebBrowser)">
            <summary>
            Returns the focused frame for the browser window.
            </summary>
            <returns>Frame</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Undo(CefSharp.IWebBrowser)">
            <summary>
            Execute Undo on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Redo(CefSharp.IWebBrowser)">
            <summary>
            Execute Redo on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Cut(CefSharp.IWebBrowser)">
            <summary>
            Execute Cut on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Copy(CefSharp.IWebBrowser)">
            <summary>
            Execute Copy on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Paste(CefSharp.IWebBrowser)">
            <summary>
            Execute Paste on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Delete(CefSharp.IWebBrowser)">
            <summary>
            Execute Delete on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.SelectAll(CefSharp.IWebBrowser)">
            <summary>
            Execute SelectAll on the focused frame
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ViewSource(CefSharp.IWebBrowser)">
            <summary>
            Opens up a new program window (using the default text editor) where the source code of the currently displayed web
            page is shown.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetSourceAsync(CefSharp.IWebBrowser)">
            <summary>
            Retrieve the main frame's HTML source using a <see cref="T:System.Threading.Tasks.Task`1"/>.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <returns><see cref="T:System.Threading.Tasks.Task`1"/> that when executed returns the frame source as a string</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetTextAsync(CefSharp.IWebBrowser)">
            <summary>
            Retrieve the main frame's display text using a <see cref="T:System.Threading.Tasks.Task`1"/>.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <returns><see cref="T:System.Threading.Tasks.Task`1"/> that when executed returns the frame display text as a string.</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ExecuteScriptAsync(CefSharp.IWebBrowser,System.String,System.Object[])">
            <summary>
            Execute some Javascript code in the context of this WebBrowser. As the method name implies, the script will be
            executed asynchronously, and the method therefore returns before the script has actually been executed.
            This simple helper extension will encapsulate params in single quotes (unless int, uint, etc)
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="methodName">The javascript method name to execute</param>
            <param name="args">the arguments to be passed as params to the method</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ExecuteScriptAsync(CefSharp.IWebBrowser,System.String)">
            <summary>
            Execute some Javascript code in the context of this WebBrowser. As the method name implies, the script will be
            executed asynchronously, and the method therefore returns before the script has actually been executed.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="script">The Javascript code that should be executed.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.LoadUrlWithPostData(CefSharp.IWebBrowser,System.String,System.Byte[],System.String)">
            <summary>
            Loads 
            Creates a new instance of IRequest with the specified Url and Method = POST
            </summary>
            <param name="browser"></param>
            <param name="url"></param>
            <param name="postDataBytes"></param>
            <param name="contentType"></param>
            <remarks>This is an extension method</remarks>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.LoadString(CefSharp.IWebBrowser,System.String,System.String)">
            <summary>
            Load the string contents with the specified dummy url. Web security restrictions may not behave as expected. 
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="html">html string to load</param>
            <param name="url">the url should have a standard scheme (for example, http scheme) or behaviors like link clicks</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.LoadHtml(CefSharp.IWebBrowser,System.String,System.String)">
            <summary>
            Registers and loads a <see cref="T:CefSharp.ResourceHandler"/> that represents the HTML content.
            </summary>
            <remarks>
            `Cef` Native `LoadHtml` is unpredictable and only works sometimes, this method wraps
            the provided HTML in a <see cref="T:CefSharp.ResourceHandler"/> and loads the provided url using
            the <see cref="M:CefSharp.IWebBrowser.Load(System.String)"/> method.
            Defaults to using <see cref="P:System.Text.Encoding.UTF8"/> for character encoding 
            The url must start with a valid schema, other uri's such as about:blank are invalid
            A valid example looks like http://test/page
            </remarks>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="html">The HTML content.</param>
            <param name="url">The URL that will be treated as the address of the content.</param>
            <returns>returns false if the Url was not successfully parsed into a Uri</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.LoadHtml(CefSharp.IWebBrowser,System.String,System.Boolean)">
            <summary>
            Loads html as Data Uri
            See https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URIs for details
            If base64Encode is false then html will be Uri encoded
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="html">Html to load as data uri.</param>
            <param name="base64Encode">if true the html string will be base64 encoded using UTF8 encoding.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.LoadHtml(CefSharp.IWebBrowser,System.String,System.String,System.Text.Encoding,System.Boolean)">
            <summary>
            Registers and loads a <see cref="T:CefSharp.ResourceHandler"/> that represents the HTML content.
            </summary>
            <remarks>
            `Cef` Native `LoadHtml` is unpredictable and only works sometimes, this method wraps
            the provided HTML in a <see cref="T:CefSharp.ResourceHandler"/> and loads the provided url using
            the <see cref="M:CefSharp.IWebBrowser.Load(System.String)"/> method.
            </remarks>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="html">The HTML content.</param>
            <param name="url">The URL that will be treated as the address of the content.</param>
            <param name="encoding">Character Encoding</param>
            <param name="oneTimeUse">Whether or not the handler should be used once (true) or until manually unregistered (false)</param>
            <returns>returns false if the Url was not successfully parsed into a Uri</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.RegisterResourceHandler(CefSharp.IWebBrowser,System.String,System.IO.Stream,System.String)">
            <summary>
            Register a ResourceHandler. Can only be used when browser.ResourceHandlerFactory is an instance of DefaultResourceHandlerFactory
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="url">the url of the resource to unregister</param>
            <param name="stream">Stream to be registered, the stream should not be shared with any other instances of DefaultResourceHandlerFactory</param>
            <param name="mimeType">the mimeType</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.UnRegisterResourceHandler(CefSharp.IWebBrowser,System.String)">
            <summary>
            Unregister a ResourceHandler. Can only be used when browser.ResourceHandlerFactory is an instance of DefaultResourceHandlerFactory
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="url">the url of the resource to unregister</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Stop(CefSharp.IWebBrowser)">
            <summary>
            Stops loading the current page.
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Back(CefSharp.IWebBrowser)">
            <summary>
            Navigates back, must check <see cref="P:CefSharp.IWebBrowser.CanGoBack"/> before calling this method.
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Forward(CefSharp.IWebBrowser)">
            <summary>
            Navigates forward, must check <see cref="P:CefSharp.IWebBrowser.CanGoForward"/> before calling this method.
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Reload(CefSharp.IWebBrowser)">
            <summary>
            Reloads the page being displayed. This method will use data from the browser's cache, if available.
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Reload(CefSharp.IWebBrowser,System.Boolean)">
            <summary>
            Reloads the page being displayed, optionally ignoring the cache (which means the whole page including all .css, .js
            etc. resources will be re-fetched).
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="ignoreCache"><c>true</c> A reload is performed ignoring browser cache; <c>false</c> A reload is
            performed using files from the browser cache, if available.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetZoomLevelAsync(CefSharp.IBrowser)">
            <summary>
            Asynchronously gets the current Zoom Level.
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetZoomLevelAsync(CefSharp.IWebBrowser)">
            <summary>
            Asynchronously gets the current Zoom Level.
            </summary>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.SetZoomLevel(CefSharp.IBrowser,System.Double)">
            <summary>
            Change the ZoomLevel to the specified value. Can be set to 0.0 to clear the zoom level.
            </summary>
            <remarks>
            If called on the CEF UI thread the change will be applied immediately.
            Otherwise, the change will be applied asynchronously on the CEF UI thread.
            The CEF UI thread is different to the WPF/WinForms UI Thread
            </remarks>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
            <param name="zoomLevel">zoom level</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.SetZoomLevel(CefSharp.IWebBrowser,System.Double)">
            <summary>
            Change the ZoomLevel to the specified value. Can be set to 0.0 to clear the zoom level.
            </summary>
            <remarks>
            If called on the CEF UI thread the change will be applied immediately.
            Otherwise, the change will be applied asynchronously on the CEF UI thread.
            The CEF UI thread is different to the WPF/WinForms UI Thread
            </remarks>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="zoomLevel">zoom level</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Find(CefSharp.IBrowser,System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Search for text within the current page.
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
            <param name="identifier">Can be used in can conjunction with searchText to have multiple
            searches running simultaneously.</param>
            <param name="searchText">search text</param>
            <param name="forward">indicates whether to search forward or backward within the page.</param>
            <param name="matchCase">indicates whether the search should be case-sensitive. </param>
            <param name="findNext">indicates whether this is the first request or a follow-up.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Find(CefSharp.IWebBrowser,System.Int32,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Search for text within the current page.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="identifier">Can be used in can conjunction with searchText to have multiple
            searches running simultaneously.</param>
            <param name="searchText">search text</param>
            <param name="forward">indicates whether to search forward or backward within the page.</param>
            <param name="matchCase">indicates whether the search should be case-sensitive. </param>
            <param name="findNext">indicates whether this is the first request or a follow-up.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.StopFinding(CefSharp.IBrowser,System.Boolean)">
            <summary>
            Cancel all searches that are currently going on.
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
            <param name="clearSelection">clear the current search selection</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.StopFinding(CefSharp.IWebBrowser,System.Boolean)">
            <summary>
            Cancel all searches that are currently going on.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="clearSelection">clear the current search selection</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Print(CefSharp.IBrowser)">
            <summary>
            Opens a Print Dialog which if used (can be user cancelled) will print the browser contents.
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.PrintToPdfAsync(CefSharp.IBrowser,System.String,CefSharp.PdfPrintSettings)">
            <summary>
            Asynchronously prints the current browser contents to the PDF file specified.
            The caller is responsible for deleting the file when done.
            </summary>
            <param name="cefBrowser">The <see cref="T:CefSharp.IBrowser"/> object this method extends.</param>
            <param name="path">Output file location.</param>
            <param name="settings">Print Settings.</param>
            <returns>A task that represents the asynchronous print operation.
            The result is true on success or false on failure to generate the Pdf.</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.Print(CefSharp.IWebBrowser)">
            <summary>
            Opens a Print Dialog which if used (can be user cancelled) will print the browser contents.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.PrintToPdfAsync(CefSharp.IWebBrowser,System.String,CefSharp.PdfPrintSettings)">
            <summary>
            Asynchronously prints the current browser contents to the PDF file specified.
            The caller is responsible for deleting the file when done.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="path">Output file location.</param>
            <param name="settings">Print Settings.</param>
            <returns>A task that represents the asynchronous print operation.
            The result is true on success or false on failure to generate the Pdf.</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ShowDevTools(CefSharp.IBrowser)">
            <summary>
            Open developer tools in its own window. 
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ShowDevTools(CefSharp.IWebBrowser)">
            <summary>
            Open developer tools in its own window. 
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.CloseDevTools(CefSharp.IBrowser)">
            <summary>
            Explicitly close the developer tools window if one exists for this browser instance.
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.CloseDevTools(CefSharp.IWebBrowser)">
            <summary>
            Explicitly close the developer tools window if one exists for this browser instance.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ReplaceMisspelling(CefSharp.IBrowser,System.String)">
            <summary>
            If a misspelled word is currently selected in an editable node calling
            this method will replace it with the specified word. 
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
            <param name="word">The new word that will replace the currently selected word.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.ReplaceMisspelling(CefSharp.IWebBrowser,System.String)">
            <summary>
            If a misspelled word is currently selected in an editable node calling
            this method will replace it with the specified word. 
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="word">The new word that will replace the currently selected word.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.AddWordToDictionary(CefSharp.IBrowser,System.String)">
            <summary>
            Add the specified word to the spelling dictionary.
            </summary>
            <param name="cefBrowser">The ChromiumWebBrowser instance this method extends</param>
            <param name="word">The new word that will be added to the dictionary.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetBrowserHost(CefSharp.IWebBrowser)">
            <summary>
            Shortcut method to get the browser IBrowserHost
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <returns>browserHost or null</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.AddWordToDictionary(CefSharp.IWebBrowser,System.String)">
            <summary>
            Add the specified word to the spelling dictionary.
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="word">The new word that will be added to the dictionary.</param>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.EvaluateScriptAsync(CefSharp.IWebBrowser,System.String,System.Object[])">
            <summary>
            Evaluate some Javascript code in the context of this WebBrowser. The script will be executed asynchronously and the
            method returns a Task encapsulating the response from the Javascript 
            This simple helper extension will encapsulate params in single quotes (unless int, uint, etc)
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="methodName">The javascript method name to execute</param>
            <param name="args">the arguments to be passed as params to the method</param>
            <returns><see cref="T:System.Threading.Tasks.Task`1"/> that can be awaited to perform the script execution</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.EvaluateScriptAsync(CefSharp.IWebBrowser,System.Nullable{System.TimeSpan},System.String,System.Object[])">
            <summary>
            Evaluate some Javascript code in the context of this WebBrowser using the specified timeout. The script will be executed asynchronously and the
            method returns a Task encapsulating the response from the Javascript 
            This simple helper extension will encapsulate params in single quotes (unless int, uint, etc)
            </summary>
            <param name="browser">The ChromiumWebBrowser instance this method extends</param>
            <param name="timeout">The timeout after which the Javascript code execution should be aborted.</param>
            <param name="methodName">The javascript method name to execute</param>
            <param name="args">the arguments to be passed as params to the method</param>
            <returns><see cref="T:System.Threading.Tasks.Task`1"/> that can be awaited to perform the script execution</returns>
        </member>
        <member name="M:CefSharp.WebBrowserExtensions.GetScript(System.String,System.Object[])">
            <summary>
            Transforms the methodName and arguments into valid Javascript code. Will encapsulate params in single quotes (unless int, uint, etc)
            </summary>
            <param name="methodName">The javascript method name to execute</param>
            <param name="args">the arguments to be passed as params to the method</param>
            <returns>The Javascript code</returns>
        </member>
        <member name="T:CefSharp.WebPluginInfo">
            <summary>
            Information about a specific web plugin.
            </summary>
        </member>
        <member name="P:CefSharp.WebPluginInfo.Name">
            <summary>
            Gets or sets the plugin name (i.e. Flash).
            </summary>
        </member>
        <member name="P:CefSharp.WebPluginInfo.Description">
            <summary>
            Gets or sets tge description of the plugin from the version information.
            </summary>
        </member>
        <member name="P:CefSharp.WebPluginInfo.Path">
            <summary>
            Gets or sets the plugin file path (DLL/bundle/library).
            </summary>
        </member>
        <member name="P:CefSharp.WebPluginInfo.Version">
            <summary>
            Gets or sets the version of the plugin (may be OS-specific).
            </summary>
        </member>
        <member name="T:CefSharp.IRequest">
            <summary>
            Class used to represent a web request. The methods of this class may be called on any thread. 
            </summary>
        </member>
        <member name="M:CefSharp.IRequest.SetReferrer(System.String,CefSharp.ReferrerPolicy)">
            <summary>
            Set the referrer URL and policy. If non-empty the referrer URL must be
            fully qualified with an HTTP or HTTPS scheme component. Any username,
            password or ref component will be removed.
            </summary>
            <param name="referrerUrl">the referrer url</param>
            <param name="policy">referrer policy</param>
        </member>
        <member name="M:CefSharp.IRequest.InitializePostData">
            <summary>
            Initialize a new instance of <see cref="T:CefSharp.IPostData"/>.
            Make sure to check if the <see cref="P:CefSharp.IRequest.PostData"/> is null
            before calling otherwise the existing data will be overridden. 
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.Flags">
            <summary>
            Get/Set request flags, can be used to control caching policy
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.Url">
            <summary>
            Request Url
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.Identifier">
            <summary>
            Returns the globally unique identifier for this request or 0 if not specified.
            Can be used by <see cref="T:CefSharp.IRequestHandler"/> implementations in the browser process to track a
            single request across multiple callbacks.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.Method">
            <summary>
            Request Method GET/POST etc
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.ReferrerUrl">
            <summary>
            Get the referrer URL.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.ResourceType">
            <summary>
            Get the resource type for this request.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.ReferrerPolicy">
            <summary>
            Get the referrer policy.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.Headers">
            <summary>
            Header Collection
            NOTE: This collection is a copy of the underlying type, to make changes, take a reference to the collection,
            make your changes, then reassign the collection. At some point this will be replaced with a proper wrapper.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.PostData">
            <summary>
            Post data
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.TransitionType">
            <summary>
            Get the transition type for this request.
            Applies to requests that represent a main frame or sub-frame navigation.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.IsDisposed">
            <summary>
            Gets a value indicating whether the request has been disposed of.
            </summary>
        </member>
        <member name="P:CefSharp.IRequest.IsReadOnly">
            <summary>
            Returns true if this object is read-only.
            </summary>
        </member>
    </members>
</doc>
