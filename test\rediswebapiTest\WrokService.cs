﻿using Microsoft.Extensions.Hosting;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace rediswebapiTest
{
    public class WrokService : BackgroundService
    {
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (true)
            {
                Task.Delay(100).Wait();
                Debug.WriteLine("BackgroundService");
            }
        }
    }
}
