﻿using System;
using System.IO;
using ToolLibrary;

namespace XmlTest
{
    internal class Program
    {
        private static void Main(string[] args)
        {
            Console.WriteLine("Hello World!");
            Person p = new Person
            {
                ID = Guid.NewGuid(),
                Name = "张三",
                Age = 18
            };
            XmlHelperForObject<Person>.CreateXmlByObject(p, Path.GetFullPath("./Xml/test.xml"));
            Person p2 = new Person();
            XmlHelperForObject<Person>.ReadXmlToObject(Path.GetFullPath("./Xml/test.xml"), ref p2);
            Console.WriteLine(p2.Name);
            Console.ReadLine();
        }
    }

    public class Person
    {
        public Guid ID { get; set; }
        public string Name { get; set; }
        public int Age { get; set; }
    }
}