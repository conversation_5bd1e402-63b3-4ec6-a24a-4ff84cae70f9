﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImNoticeboard
    {
        public string NbId { get; set; }
        public string Statcode { get; set; }
        public string YkId { get; set; }
        public string Description { get; set; }
        public string Operusername { get; set; }
        public string Operwholename { get; set; }
        public DateTime Executetime { get; set; }

        public virtual ImStation StatcodeNavigation { get; set; }
        public virtual ImDeviceyk Yk { get; set; }
    }
}
