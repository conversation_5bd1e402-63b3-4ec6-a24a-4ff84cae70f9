﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImGlyph
    {
        public ImGlyph()
        {
            ImGlyphhots = new HashSet<ImGlyphhots>();
        }

        public string Id { get; set; }
        public string Parentid { get; set; }
        public string Diagid { get; set; }
        public int Glyphtype { get; set; }
        public double? Left { get; set; }
        public double? Top { get; set; }
        public double? Right { get; set; }
        public double? Bottom { get; set; }
        public short? Linewidth { get; set; }
        public int? Linecolor { get; set; }
        public int? Bkcolor { get; set; }
        public short? Linestyle { get; set; }
        public short? Transparent { get; set; }
        public short? Direction { get; set; }
        public string Name { get; set; }
        public short? ZOrder { get; set; }
        public short? State { get; set; }
        public string Linkdiagid { get; set; }
        public int? Pwroncolor { get; set; }
        public int? Pwroffcolor { get; set; }
        public int? Switchoncolor { get; set; }
        public int? Switchoffcolor { get; set; }
        public int? Switchunknowncolor { get; set; }
        public int? Locked { get; set; }
        public double? Pwronthreshold { get; set; }
        public string Reserved1 { get; set; }
        public string Reserved2 { get; set; }

        public virtual Im3posswitch Im3posswitch { get; set; }
        public virtual ImDataviewer ImDataviewer { get; set; }
        public virtual ImDiagram ImDiagram { get; set; }
        public virtual ImGlyphdevdata ImGlyphdevdata { get; set; }
        public virtual ImGrid ImGrid { get; set; }
        public virtual ImLabel ImLabel { get; set; }
        public virtual ICollection<ImGlyphhots> ImGlyphhots { get; set; }
    }
}
