﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceDzenum
    {
        public int EnumTypeId { get; set; }
        public int EnumIndex { get; set; }
        public string? EnumComment { get; set; }
        public string Manufacturer { get; set; } = null!;

        public virtual ImManufacturer ManufacturerNavigation { get; set; } = null!;
    }
}
