﻿using Abp.Dependency;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.TcpSocket.Server;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.Commdb.Models;
using YunDa.SOMS.DataTransferObject.GeneralInformation.ProtectionDeviceInfoDto;
namespace Yunda.SOMS.DataMonitoringServer.ProtectionDeviceHandle
{
    public class ProtectionDeviceDataCenter: ISingletonDependency
    {
        public static List<ProtectionDeviceCommInfoOutput> _devices;
        WebApiRequest _webApiRequest;
        ProtectionDeviceDZDataHandle _protectionDeviceDZDataHandle;
        ProtectionDeviceVersionHandle _protectionDeviceVersionHandle;
        ProtectionDeviceIOInfoHandle _protectionDeviceIOInfoHandle;
        ProtectionDeviceRunInfoHandle _protectionDeviceRunInfoHandle;
        ProtectionDeviceSelfCheckHandle _protectionDeviceSelfCheckHandle;
        ProtectionDeviceBCodeHandle _protectionDeviceBCodeHandle;
        IRedisRepository<int[], string> _deviceBoardStatesRedis;
        IRedisRepository<ProtectionDeviceCommInfoOutput, string> _protectionDeviceCommInfoRedis;

        DotNettyTcpServer _dotNettyTcpServer;
        string deviceBoardStatesRedisKey = "deviceBoardStates";
        ConcurrentBag<Process> _processes = new ConcurrentBag<Process>();
        public ProtectionDeviceDataCenter(WebApiRequest webApiRequest
            , ProtectionDeviceDZDataHandle protectionDeviceDZDataHandle
            , ProtectionDeviceVersionHandle protectionDeviceVersionHandle
            , ProtectionDeviceIOInfoHandle protectionDeviceIOInfoHandle
            , DotNettyTcpServer dotNettyTcpServer
           , ProtectionDeviceSelfCheckHandle protectionDeviceSelfCheckHandle
            , ProtectionDeviceRunInfoHandle protectionDeviceRunInfoHandle
            ,ProtectionDeviceBCodeHandle protectionDeviceBCodeHandle
            , IRedisRepository<int[], string> deviceBoardStatesRedis
            , IRedisRepository<ProtectionDeviceCommInfoOutput, string> protectionDeviceCommInfoRedis
            )
        {
            _webApiRequest = webApiRequest;
            _protectionDeviceDZDataHandle = protectionDeviceDZDataHandle;
            _protectionDeviceVersionHandle = protectionDeviceVersionHandle;
            _protectionDeviceIOInfoHandle = protectionDeviceIOInfoHandle;
            _dotNettyTcpServer = dotNettyTcpServer;
            _protectionDeviceRunInfoHandle = protectionDeviceRunInfoHandle;
            _deviceBoardStatesRedis = deviceBoardStatesRedis;
            _protectionDeviceSelfCheckHandle = protectionDeviceSelfCheckHandle;
            _protectionDeviceCommInfoRedis = protectionDeviceCommInfoRedis;
        }
        
  
       

        public  async Task InitProtectionDeviceComms(ConnectionConfig connection)
        {
            MonitoringEventBus.LogHandler("开始启动103客户端", "装置信息");
            await  Task.Run(async () =>
            {
                _devices = _webApiRequest.GetProtectionDeviceCommInfos("神池南");
               if (_devices == null)
               {
                   MonitoringEventBus.LogHandler("没有获取到装置数据", "获取信息");
                   return;
               }
                if (connection.DataAccessMode == (int)DataAccessModeEnum.JingHu)
                {
                    _protectionDeviceDZDataHandle.Init(_devices);
                }
                MonitoringEventBus.LogHandler($"共获取{_devices.Count}条装置数据", "装置信息");
                string currentDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string sourceDirectory = Path.Combine(currentDirectory, "DeviceComm");
                string destDirectoryBase = Path.Combine(currentDirectory, "DeviceComms", "DeviceComm");
            

               // 定义一个并行处理每个设备的过程
               Parallel.ForEach(_devices, device =>
               {
                   try
                   {
                       var online = IsDeviceOnline(device.GatewayIP1);
                       if (online)
                       {

                           string destDirectory = $"{destDirectoryBase}_{device.DeviceAddr}";
                           CopyDirectory(sourceDirectory, destDirectory);
                           string configFilePath = Path.Combine(destDirectory, "cfg", "scmgateway.ini");
                           ModifyDeviceAddrInConfig(configFilePath, device.GatewayIP1, device.DeviceAddr);
                           string exePath = Path.Combine(destDirectory, "scmgateway.exe");
                           var process = StartExe(exePath);
                           if (process != null)
                           {
                               MonitoringEventBus.LogHandler($"装置地址：{device.DeviceAddr}，ip:{device.GatewayIP1} 启动成功", "装置定值");
                               _processes.Add(process);
                           }
                           device.IsOnline = true;

                       }
                       else if(IsDeviceOnline(device.GatewayIP2))
                       {
                           string destDirectory = $"{destDirectoryBase}_{device.DeviceAddr}";
                           CopyDirectory(sourceDirectory, destDirectory);
                           string configFilePath = Path.Combine(destDirectory, "cfg", "scmgateway.ini");
                           ModifyDeviceAddrInConfig(configFilePath, device.GatewayIP2, device.DeviceAddr);
                           string exePath = Path.Combine(destDirectory, "scmgateway.exe");
                           var process = StartExe(exePath);
                           if (process != null)
                           {
                               MonitoringEventBus.LogHandler($"装置地址：{device.DeviceAddr}，ip:{device.GatewayIP2} 启动成功", "装置定值");
                               _processes.Add(process);
                           }
                           device.IsOnline = true;

                       }
                       else 
                       {
                           device.IsOnline = false;
                           MonitoringEventBus.LogHandler($"装置地址：{device.DeviceAddr}，ip1:{device.GatewayIP1}，ip2:{device.GatewayIP2} {(online ? "在线" : "离线")}", "装置状态");
                       }
                       if (device.GatewayIP1 == "127.0.0.1" )
                       {
                           device.IsOnline = true;
                       }

                   }
                   catch (Exception ex)
                   {
                       MonitoringEventBus.LogHandler($"{ex.StackTrace}", "错误信息");
                   }

               });
               string protectionDeviceCommInfoRedisKey = "protectionDeviceCommInfo";
               _protectionDeviceCommInfoRedis.HashSetUpdateManyAsync(protectionDeviceCommInfoRedisKey, _devices.Select(t => t.DeviceAddr.ToString()).ToList(), _devices);
                string protectionDeviceCommInfoRedisChannel = "protectionDeviceCommInfosChannel";
                foreach (var device in _devices)
                {
                    await _protectionDeviceCommInfoRedis.PublishAsync(protectionDeviceCommInfoRedisChannel, device);
                }

                MonitoringEventBus.LogHandler("开始启动TCP服务端", "装置定值");
               await _dotNettyTcpServer.RunServerAsync();
           });
           
        }
         Process StartExe(string exePath)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        WorkingDirectory = Path.GetDirectoryName(exePath), // 设置工作目录为exe所在目录
                        FileName = exePath,
                        CreateNoWindow = true,
                        UseShellExecute = false
                    }
                };
                process.Start();
                return process;
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler($"启动 {exePath} 失败: {ex.StackTrace}", "错误信息");
                return null;
            }
        }
        public void StopAllProcesses()
        {
            foreach (var process in _processes)
            {
                try
                {
                    if (!process.HasExited)
                    {
                        process.Kill();
                        process.WaitForExit();
                    }
                }
                catch (Exception ex)
                {
                    MonitoringEventBus.LogHandler($"停止进程失败: {ex.StackTrace}", "错误信息");
                }
            }
        }
        // 通过 Ping 判断设备是否在线
        private bool IsDeviceOnline(string ipAddress)
        {
            try
            {
                if (ipAddress == "127.0.0.1" || ipAddress == "localhost" || string.IsNullOrWhiteSpace(ipAddress))
                {
                    return false;
                }

                using (var tcpClient = new System.Net.Sockets.TcpClient())
                {
                    // 设置超时为 1000 毫秒 (1秒)
                    var result = tcpClient.BeginConnect(ipAddress, 2403, null, null);
                    bool success = result.AsyncWaitHandle.WaitOne(1000);

                    // 判断连接是否成功
                    return success && tcpClient.Connected;
                }
            }
            catch (Exception)
            {
                // 如果发生异常，端口不可达（例如网络断开或连接失败）
                return false;
            }
        }

        private void CopyDirectory(string sourceDir, string destDir)
        {
            var dir = new DirectoryInfo(sourceDir);
            if (!dir.Exists)
            {
                throw new DirectoryNotFoundException($"源目录不存在或无法找到: {sourceDir}");
            }
            if (Directory.Exists(destDir))
            {
                //如果目标目录存在，则直接跳过复制
                return;
            }
            DirectoryInfo[] dirs = dir.GetDirectories();
            Directory.CreateDirectory(destDir);

            foreach (FileInfo file in dir.GetFiles())
            {
                string targetFilePath = Path.Combine(destDir, file.Name);
                file.CopyTo(targetFilePath, true);
            }

            foreach (DirectoryInfo subDir in dirs)
            {
                string newDestinationDir = Path.Combine(destDir, subDir.Name);
                CopyDirectory(subDir.FullName, newDestinationDir);
            }
        }

        private  void ModifyDeviceAddrInConfig(string configFilePath, string gatewayIP1,int protectAddr)
        {
            if (File.Exists(configFilePath))
            {
                var lines = File.ReadAllLines(configFilePath);
                for (int i = 0; i < lines.Length; i++)
                {
                    if (lines[i].StartsWith("[Device]"))
                    {
                        for (int j = i + 1; j < lines.Length; j++)
                        {
                            if (lines[j].StartsWith("deviceAddr="))
                            {
                                lines[j] = $"deviceAddr={gatewayIP1}";
                            }
                            if (lines[j].StartsWith("protectAddr="))
                            {
                                lines[j] = $"protectAddr={protectAddr}";
                            }
                        }
                        
                        break;
                    }
                }
                File.WriteAllLines(configFilePath, lines);
            }
            else
            {
                Debug.WriteLine($"文件未找到: {configFilePath}");
            }
        }
    }
    
}





