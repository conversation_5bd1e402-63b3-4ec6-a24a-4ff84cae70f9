using Abp.Application.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YunDa.ISAS.DataTransferObject;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    /// <summary>
    /// 变压器监视服务接口，提供获取变压器电压、电流等数据的功能
    /// </summary>
    public interface ITransformerMonitoringAppService : IApplicationService
    {
        /// <summary>
        /// 获取高压侧A、B、C相相电压
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="realTimePowerType">数据时间类型</param>
        /// <returns>含有时间序列的高压侧相电压数据</returns>
        Task<RequestResult<HighSideVoltageData>> GetHighSidePhaseVoltage(Guid deviceId, RealTimePowerTypeEnum realTimePowerType);

        /// <summary>
        /// 获取高压侧A、B、C相相电流
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="realTimePowerType">数据时间类型</param>
        /// <returns>含有时间序列的高压侧相电流数据</returns>
        Task<RequestResult<HighSideCurrentData>> GetHighSidePhaseCurrent(Guid deviceId, RealTimePowerTypeEnum realTimePowerType);

        /// <summary>
        /// 获取低压侧F1、F2线电压
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="realTimePowerType">数据时间类型</param>
        /// <returns>含有时间序列的低压侧线电压数据</returns>
        Task<RequestResult<LowSideVoltageData>> GetLowSideLineVoltage(Guid deviceId, RealTimePowerTypeEnum realTimePowerType);

        /// <summary>
        /// 获取低压侧F1、F2线电流
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="realTimePowerType">数据时间类型</param>
        /// <returns>含有时间序列的低压侧线电流数据</returns>
        Task<RequestResult<LowSideCurrentData>> GetLowSideLineCurrent(Guid deviceId, RealTimePowerTypeEnum realTimePowerType);
    }

    /// <summary>
    /// 高压侧相电压数据
    /// </summary>
    public class HighSideVoltageData
    {
        /// <summary>
        /// A相电压数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> PhaseAVoltage { get; set; }

        /// <summary>
        /// B相电压数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> PhaseBVoltage { get; set; }

        /// <summary>
        /// C相电压数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> PhaseCVoltage { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum TimeIntervalType { get; set; }
    }

    /// <summary>
    /// 高压侧相电流数据
    /// </summary>
    public class HighSideCurrentData
    {
        /// <summary>
        /// A相电流数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> PhaseACurrent { get; set; }

        /// <summary>
        /// B相电流数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> PhaseBCurrent { get; set; }

        /// <summary>
        /// C相电流数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> PhaseCCurrent { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum TimeIntervalType { get; set; }
    }

    /// <summary>
    /// 低压侧线电压数据
    /// </summary>
    public class LowSideVoltageData
    {
        /// <summary>
        /// F1线电压数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> F1Voltage { get; set; }

        /// <summary>
        /// F2线电压数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> F2Voltage { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum TimeIntervalType { get; set; }
    }

    /// <summary>
    /// 低压侧线电流数据
    /// </summary>
    public class LowSideCurrentData
    {
        /// <summary>
        /// F1线电流数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> F1Current { get; set; }

        /// <summary>
        /// F2线电流数据
        /// </summary>
        public IEnumerable<FloatTimeOutput> F2Current { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum TimeIntervalType { get; set; }
    }
}
