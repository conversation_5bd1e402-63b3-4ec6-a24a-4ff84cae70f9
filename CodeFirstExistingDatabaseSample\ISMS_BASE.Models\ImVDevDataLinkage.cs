﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVDevDataLinkage
    {
        public string Id { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string DataName { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int AutoSave { get; set; }
        public int Visible { get; set; }
        public string? BeiZhu { get; set; }
        public string? SrcTempDataId { get; set; }
        public string Domain { get; set; } = null!;
        public int DeviceAddr { get; set; }
        public string DeviceName { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string GateWayId { get; set; } = null!;
        public string StatName { get; set; } = null!;
        public string LnkId { get; set; } = null!;
        public string SrcObjId { get; set; } = null!;
        public string SrcObjType { get; set; } = null!;
        public string LnkType { get; set; } = null!;
        public string Resource { get; set; } = null!;
        public string? Action { get; set; }
        public string? ActParam { get; set; }
        public string State { get; set; } = null!;
    }
}
