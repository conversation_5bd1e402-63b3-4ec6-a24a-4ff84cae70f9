﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.Server
{
    public class CommunicationFrame
    {
        public byte StartByte { get; set; } = 0x16; // 启动字符
        public ushort Length { get; set; }          // 总长度
        public byte Address { get; set; }           // 地址
        public byte ControlWord { get; set; }       // 应用控制字
        public byte FunctionType { get; set; }      // 功能类型
        public byte[] Data { get; set; }            // 数据

        public CommunicationFrame(byte address, byte controlWord, byte functionType, byte[] data)
        {
            Address = address;
            ControlWord = controlWord;
            FunctionType = functionType;
            Data = data ?? Array.Empty<byte>();
            Length = (ushort)(3 + Data.Length); // 2字节长度+1字节地址+1字节标志位
        }

        public byte[] ToByteArray()
        {
            using (var ms = new MemoryStream())
            {
                ms.WriteByte(StartByte);
                ms.Write(BitConverter.GetBytes(Length), 0, 2);
                ms.WriteByte(Address);
                ms.WriteByte(ControlWord);
                ms.WriteByte(FunctionType);
                ms.Write(Data, 0, Data.Length);
                return ms.ToArray();
            }
        }
    }

}
