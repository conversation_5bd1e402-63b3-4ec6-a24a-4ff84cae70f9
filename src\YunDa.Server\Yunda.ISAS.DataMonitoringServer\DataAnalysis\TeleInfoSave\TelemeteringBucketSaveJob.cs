using MongoDB.Bson;
using MongoDB.Driver;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Model;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.MongoDB.Repositories;

namespace Yunda.SOMS.DataMonitoringServer.DataAnalysis.TeleInfoSave
{
    /// <summary>
    /// 遥测数据分桶存储作业
    /// </summary>
    [DisallowConcurrentExecution]
    public class TelemeteringBucketSaveJob : IJob
    {
        /// <summary>
        /// 执行分桶存储作业
        /// </summary>
        public async Task Execute(IJobExecutionContext context)
        {
            try
            {
                var dataMap = context.MergedJobDataMap;

                var connection = (ConnectionConfig)dataMap["connection"];
                var interval = (FixedIntervalEnum)dataMap["interval"];
                var redisRepo = (RedisDataRepository)dataMap["redisRepo"];
                var modelRepo = (IMongoDbRepository<BsonDocument, Guid>)dataMap["modelRepo"];
                var statisticsRepo = (IMongoDbRepository<TelemeteringStatisticsResult, Guid>)dataMap["statisticsRepo"];
                var bucketRepo = (IMongoDbRepository<TelemeteringBucket, Guid>)dataMap["bucketRepo"];
                var statsBucketRepo = (IMongoDbRepository<TelemeteringStatisticsBucket, Guid>)dataMap["statsBucketRepo"];
                var cache = (RunningDataCache)dataMap["cache"];

                // 处理实时数据分桶存储
                await ProcessRealTimeBucket(connection, interval, redisRepo, bucketRepo, cache);

                // 处理统计数据分桶存储
                await ProcessStatisticsBucket(connection, interval, redisRepo, statisticsRepo, statsBucketRepo, cache);
            }
            catch (Exception ex)
            {
                Log4Helper.Error(typeof(TelemeteringBucketSaveJob), $"执行分桶存储作业异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理实时数据分桶存储
        /// </summary>
        private async Task ProcessRealTimeBucket(
            ConnectionConfig connection,
            FixedIntervalEnum interval,
            RedisDataRepository redisRepo,
            IMongoDbRepository<TelemeteringBucket, Guid> bucketRepo,
            RunningDataCache cache)
        {
            try
            {
                // 只有小时级别的间隔才处理实时数据分桶
                if (interval != FixedIntervalEnum.Hour1)
                    return;

                string redisKey = redisRepo.TelemeteringModelListRediskey + "_" + (DataSourceCategoryEnum)connection.DataSourceCategoryName;
                var ycDatas = await redisRepo.TelemeteringModelListRedis.HashSetGetAllAsync(redisKey);

                if (ycDatas == null || !ycDatas.Any())
                {
                    Log4Helper.Info(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 未找到实时数据");
                    return;
                }

                // 按小时分桶
                DateTime now = DateTime.Now;
                DateTime bucketStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);
                DateTime bucketEnd = bucketStart.AddHours(1).AddSeconds(-1);

                // 设置集合名称 - 按年月分表
                string category = ((DataSourceCategoryEnum)connection.DataSourceCategoryName).ToString();
                bucketRepo.CollectionName = $"{nameof(TelemeteringBucket)}_{category}_{now:yyyyMM}";

                foreach (var telemetry in ycDatas)
                {
                    if (telemetry.EquipmentInfoId == null || !cache.EquipmentInfoSimDic.TryGetValue(telemetry.EquipmentInfoId.Value, out var equipment))
                    {
                        Log4Helper.Warn(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 无效 EquipmentInfoId: {telemetry.EquipmentInfoId}");
                        continue;
                    }

                    if (telemetry.DataSourceCategory == null)
                    {
                        Log4Helper.Warn(typeof(TelemeteringBucketSaveJob), $"遥测点 {telemetry.Id} 的DataSourceCategory为空");
                        continue;
                    }

                    // 查找或创建当前小时的桶
                    var bucketFilter = Builders<TelemeteringBucket>.Filter.And(
                        Builders<TelemeteringBucket>.Filter.Eq(x => x.TelemeteringConfigurationId, telemetry.Id),
                        Builders<TelemeteringBucket>.Filter.Eq(x => x.StartTime, bucketStart),
                        Builders<TelemeteringBucket>.Filter.Eq(x => x.EndTime, bucketEnd)
                    );

                    var bucket =  bucketRepo.GetAllIncludeToFindFluent(bucketFilter).FirstOrDefault();

                    // 创建新数据点
                    var newPoint = new TelemeteringPoint
                    {
                        Timestamp = telemetry.ResultTime,
                        Value = telemetry.ResultValue,
                        SaveMethod = 2 // 变化保存
                    };

                    if (bucket == null)
                    {
                        // 创建新桶
                        bucket = new TelemeteringBucket
                        {
                            TelemeteringConfigurationId = telemetry.Id,
                            Name = telemetry.Name,
                            EquipmentInfoId = telemetry.EquipmentInfoId,
                            EquipmentInfoName = equipment.Name,
                            Unit = telemetry.Unit,
                            DataSourceCategory = (int?)telemetry.DataSourceCategory,
                            StartTime = bucketStart,
                            EndTime = bucketEnd,
                            Count = 1,
                            MinValue = telemetry.ResultValue,
                            MaxValue = telemetry.ResultValue,
                            AvgValue = telemetry.ResultValue,
                            SumValue = telemetry.ResultValue,
                            FirstValue = telemetry.ResultValue,
                            LastValue = telemetry.ResultValue,
                            StdDeviation = 0,
                            Measurements = new List<TelemeteringPoint> { newPoint }
                        };

                        await bucketRepo.InsertOneAsync(bucket);
                        Log4Helper.Debug(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 创建新桶: {telemetry.Id}, 时间: {bucketStart:yyyy-MM-dd HH:mm:ss}");
                    }
                    else
                    {
                        // 更新现有桶
                        var updateBuilder = Builders<TelemeteringBucket>.Update;
                        var updates = new List<UpdateDefinition<TelemeteringBucket>>();

                        // 添加新的测量点
                        updates.Add(updateBuilder.Push(x => x.Measurements, newPoint));
                        updates.Add(updateBuilder.Inc(x => x.Count, 1));
                        updates.Add(updateBuilder.Set(x => x.LastValue, telemetry.ResultValue));
                        
                        // 更新最小值
                        if (telemetry.ResultValue < bucket.MinValue)
                        {
                            updates.Add(updateBuilder.Set(x => x.MinValue, telemetry.ResultValue));
                        }
                        
                        // 更新最大值
                        if (telemetry.ResultValue > bucket.MaxValue)
                        {
                            updates.Add(updateBuilder.Set(x => x.MaxValue, telemetry.ResultValue));
                        }
                        
                        // 更新总和和平均值
                        float newSum = bucket.SumValue + telemetry.ResultValue;
                        float newAvg = newSum / (bucket.Count + 1);
                        updates.Add(updateBuilder.Set(x => x.SumValue, newSum));
                        updates.Add(updateBuilder.Set(x => x.AvgValue, newAvg));
                        
                        // 执行更新
                        var updateResult = await bucketRepo.FindOneAndUpdateAsync(bucketFilter, updateBuilder.Combine(updates));
                        Log4Helper.Debug(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 更新桶: {telemetry.Id}, 时间: {bucketStart:yyyy-MM-dd HH:mm:ss}, 结果: {updateResult.Count}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(typeof(TelemeteringBucketSaveJob), $"处理实时数据分桶存储异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理统计数据分桶存储
        /// </summary>
        private async Task ProcessStatisticsBucket(
            ConnectionConfig connection,
            FixedIntervalEnum interval,
            RedisDataRepository redisRepo,
            IMongoDbRepository<TelemeteringStatisticsResult, Guid> statisticsRepo,
            IMongoDbRepository<TelemeteringStatisticsBucket, Guid> statsBucketRepo,
            RunningDataCache cache)
        {
            try
            {
                // 获取统计数据
                string category = ((DataSourceCategoryEnum)connection.DataSourceCategoryName).ToString();
                statisticsRepo.CollectionName = $"TelemeteringStatisticsResult_{interval}_{DateTime.Now.Year}";

                // 查询最近的统计结果
                DateTime now = DateTime.Now;
                DateTime startTime = now.AddMinutes(-10); // 查询最近10分钟的统计结果

                var filter = Builders<TelemeteringStatisticsResult>.Filter.And(
                    Builders<TelemeteringStatisticsResult>.Filter.Gte(x => x.StatisticsDateTime, startTime),
                    Builders<TelemeteringStatisticsResult>.Filter.Eq(x => x.IntervalType, interval)
                );

                var statistics =  statisticsRepo.GetAllIncludeToFindFluent(filter).ToList();

                if (statistics == null || !statistics.Any())
                {
                    Log4Helper.Info(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 未找到统计数据, 间隔: {interval}");
                    return;
                }

                // 按遥测点分组
                var statsByTelemetry = statistics.GroupBy(s => s.TelemeteringConfigurationId);

                foreach (var group in statsByTelemetry)
                {
                    var telemeteringId = group.Key;
                    var statsList = group.ToList();
                    
                    if (statsList.Count == 0)
                        continue;

                    // 获取遥测点信息
                    string redisKey = redisRepo.TelemeteringModelListRediskey + "_" + category;
                    var telemetryList = await redisRepo.TelemeteringModelListRedis.HashSetGetAllAsync(redisKey);
                    var telemetry = telemetryList.FirstOrDefault(t => t.Id == telemeteringId);

                    if (telemetry == null)
                    {
                        Log4Helper.Warn(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 未找到遥测点: {telemeteringId}");
                        continue;
                    }

                    if (telemetry.EquipmentInfoId == null || !cache.EquipmentInfoSimDic.TryGetValue(telemetry.EquipmentInfoId.Value, out var equipment))
                    {
                        Log4Helper.Warn(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 无效 EquipmentInfoId: {telemetry.EquipmentInfoId}");
                        continue;
                    }

                    string equipmentName = equipment.Name;

                    // 计算桶的开始和结束时间（按月）
                    DateTime bucketStart = new DateTime(now.Year, now.Month, 1);
                    DateTime bucketEnd = bucketStart.AddMonths(1).AddSeconds(-1);

                    // 设置集合名称 - 按年份和间隔类型分表
                    statsBucketRepo.CollectionName = $"{nameof(TelemeteringStatisticsBucket)}_{interval}_{now.Year}";

                    // 查找或创建当前月的统计桶
                    var bucketFilter = Builders<TelemeteringStatisticsBucket>.Filter.And(
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.TelemeteringConfigurationId, telemeteringId),
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.Year, now.Year),
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.Month, now.Month),
                        Builders<TelemeteringStatisticsBucket>.Filter.Eq(x => x.IntervalType, interval)
                    );

                    var bucket = await statsBucketRepo.FirstOrDefaultAsync(bucketFilter);

                    // 转换统计结果为桶内的结果格式
                    var results = statsList.Select(s => new StatisticsResult
                    {
                        StatisticsType = s.StatisticsType,
                        StatisticsDateTime = s.StatisticsDateTime,
                        ResultValue = s.ResultValue,
                        DataCount = s.DataCount,
                        ResultTime = s.ResultTime
                    }).ToList();

                    if (bucket == null)
                    {
                        // 创建新的统计桶
                        bucket = new TelemeteringStatisticsBucket
                        {
                            TelemeteringConfigurationId = telemeteringId,
                            Name = telemetry.Name,
                            EquipmentInfoId = telemetry.EquipmentInfoId,
                            EquipmentInfoName = equipmentName,
                            Unit = telemetry.Unit,
                            DataSourceCategory = (int?)telemetry.DataSourceCategory,
                            IntervalType = interval,
                            StartTime = bucketStart,
                            EndTime = bucketEnd,
                            Count = results.Count,
                            Year = now.Year,
                            Month = now.Month,
                            Results = results
                        };

                        await statsBucketRepo.InsertAsync(bucket);
                        Log4Helper.Debug(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 创建新统计桶: {telemeteringId}, 年月: {now:yyyy-MM}, 间隔: {interval}");
                    }
                    else
                    {
                        // 更新现有统计桶
                        var updateBuilder = Builders<TelemeteringStatisticsBucket>.Update;
                        var updates = new List<UpdateDefinition<TelemeteringStatisticsBucket>>();

                        // 添加新的统计结果
                        foreach (var result in results)
                        {
                            updates.Add(updateBuilder.Push(x => x.Results, result));
                        }
                        
                        updates.Add(updateBuilder.Inc(x => x.Count, results.Count));
                        
                        // 执行更新
                        var updateResult = await statsBucketRepo.UpdateAsync(bucketFilter, updateBuilder.Combine(updates));
                        Log4Helper.Debug(typeof(TelemeteringBucketSaveJob), $"[{connection.Name}] 更新统计桶: {telemeteringId}, 年月: {now:yyyy-MM}, 间隔: {interval}, 结果: {updateResult.Count}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(typeof(TelemeteringBucketSaveJob), $"处理统计数据分桶存储异常: {ex.Message}", ex);
            }
        }
    }
} 