﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImStation
    {
        public ImStation()
        {
            IaMonitorAreas = new HashSet<IaMonitorArea>();
            IaStatAccCtlers = new HashSet<IaStatAccCtler>();
            IaStatAidVideoDevs = new HashSet<IaStatAidVideoDev>();
            ImGateWays = new HashSet<ImGateWay>();
            ImNoticeBoards = new HashSet<ImNoticeBoard>();
            ImProtectDevices = new HashSet<ImProtectDevice>();
            ImSheBeis = new HashSet<ImSheBei>();
            ImVariantBools = new HashSet<ImVariantBool>();
        }

        public string StatCode { get; set; } = null!;
        public string PrjId { get; set; } = null!;
        public string StatName { get; set; } = null!;
        public int SeqNo { get; set; }

        public virtual ImProject Prj { get; set; } = null!;
        public virtual ICollection<IaMonitorArea> IaMonitorAreas { get; set; }
        public virtual ICollection<IaStatAccCtler> IaStatAccCtlers { get; set; }
        public virtual ICollection<IaStatAidVideoDev> IaStatAidVideoDevs { get; set; }
        public virtual ICollection<ImGateWay> ImGateWays { get; set; }
        public virtual ICollection<ImNoticeBoard> ImNoticeBoards { get; set; }
        public virtual ICollection<ImProtectDevice> ImProtectDevices { get; set; }
        public virtual ICollection<ImSheBei> ImSheBeis { get; set; }
        public virtual ICollection<ImVariantBool> ImVariantBools { get; set; }
    }
}
