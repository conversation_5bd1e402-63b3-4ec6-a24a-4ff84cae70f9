﻿using LoginTest;
using MongoDB.Bson;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.IO;
using System.Text;
using System.Threading;
using Yunda.ISAS.RobotCommunicationServer.WebSocket;
using YunDa.ISAS.DataTransferObject.Iec104;

namespace ConsoleTest
{
    internal class Program
    {
        private static string url = "http://192.168.108.57:10086/";
        readonly static CancellationTokenSource  _cancellationTokenSource = new CancellationTokenSource();
        static void  Test(string text)
        {
            //JToken parsedJson = JToken.Parse(text);
            //var beautified = parsedJson.ToString(Formatting.Indented);
            //var minified = parsedJson.ToString(Formatting.None);
            Console.WriteLine(DateTime.Now);
            Console.WriteLine(text);
        }
        private static  void Main(string[] args)
        {

            SearchSqlFile.Run();

            Console.ReadLine();
            //var strMsg = "{\\data\\:{\\taskTotalProgress\\:100,\\failCount\\:0,\\transId\\:1690275385,\\errMsg\\:\\\\,\\viewPointId\\:1688697933,\\totalCount\\:1,\\robotId\\:1,\\deviceId\\:3,\\execId\\:62404,\\taskType\\:\\定期任务\\,\\progressStatus\\:3,\\leftTime\\:120,\\successCount\\:1,\\taskName\\:\\yunda-test2\\,\\beginTime\\:\\2023-07-25 16:55:21\\,\\endTime\\:\\\\,\\taskId\\:116,\\taskStatus\\:0,\\templatedId\\:23},\\dataType\\:\\taskStatus\\}";
            ////strMsg = strMsg.Replace("\\\\","\" \"");
            //strMsg = strMsg.Replace("\\", "\"");
            //JObject jsonObject = JObject.Parse(strMsg);

            //string dataTypeValue = (string)jsonObject["dataType"];
            //if (dataTypeValue == "taskStatus")
            //{
            //    var jtoken = jsonObject["data"].ToString();
            //    //JObject.Parse(jtoken);
            //    if (jtoken != null)
            //    {
            //        var data = JsonConvert.DeserializeObject<TaskStatusData>(jtoken);
            //        Console.WriteLine();
            //    }

            //}
            //var taskRecvBase = JsonConvert.DeserializeObject<T5TaskRecvBase<string>>(strMsg);

            //byte[] imageBytes = Convert.FromBase64String( TestData.pngstr);
            //using (FileStream fs = new FileStream("D:/1.png", FileMode.Create))
            //{
            //    fs.Write(imageBytes, 0, imageBytes.Length);
            //}
            //Console.WriteLine();
            //IOberverTest oberverTest = new IOberverTest();
            //  oberverTest.ConnectAsync(new Uri("ws://192.168.81.32:9090/DataMonitoring"), _cancellationTokenSource.Token).Wait();
            //var bytes= Encoding.UTF8.GetBytes("{\"GroupType\":9999,\r\n  \"MessageType\":1\r\n  }");
            //oberverTest.SendAsync(bytes, TransportMessageType.Text, false, _cancellationTokenSource.Token);
            //oberverTest.TextObservable.Subscribe(new Action<string>(Test));
            //var date = DateTime.Parse("12:00:00");
            //Console.WriteLine("Hello World!");
            //Console.ReadLine();

            //AuthenticateModel loginModel = new AuthenticateModel
            //{
            //    UserName = "admin",
            //    Password = "123qwe"
            //};
            //JObject rstJObject = HttpHelper.HttpPostRequest<JObject>(url + "api/TokenAuth/Authenticate", loginModel);
            //AuthenticateResultModel rstModel = JsonConvert.DeserializeObject<AuthenticateResultModel>(rstJObject["result"].ToString());
            //Console.WriteLine("AccessToken:" + rstModel.AccessToken);
            //PageSearchCondition<UserSearchConditionInput> searchCondition = new PageSearchCondition<UserSearchConditionInput>();
            //searchCondition.PageIndex = 1;
            //searchCondition.PageSize = 10;
            //searchCondition.SearchCondition = new UserSearchConditionInput();
            //searchCondition.SearchCondition.UserName = "";
            //searchCondition = new
            //{
            //    linkageId= "08d82f00-838b-479e-8eb4-28270c3e6e5f"
            //};
            //JObject rstJObject1 = HttpHelper.HttpPostRequest<JObject>(url + "api/services/isas/LinkageExecuteActivity/FindActivityByLinkageId?linkageId="+ "08d82f00-838b-479e-8eb4-28270c3e6e5f", null, null);
            //Console.WriteLine("Result:" + rstJObject1);

            //string json = JsonConvert.SerializeObject(loginModel, Formatting.Indented);
            //string x = ObjectToJsonStr(loginModel);
            //Console.ReadLine();
        }
        private static string ObjectToJsonStr(object obj)
        {
            if (obj == null) return "";
            var serializerSettings = new JsonSerializerSettings
            {
                // 设置为驼峰命名
                ContractResolver = new CamelCasePropertyNamesContractResolver(),

            };

            return JsonConvert.SerializeObject(obj, Formatting.Indented, serializerSettings);
        }
    }

    public class AuthenticateModel
    {
        public string UserName { get; set; }

        public string Password { get; set; }

        public bool RememberClient { get; set; }
    }

    public class AuthenticateResultModel
    {
        public string AccessToken { get; set; }

        public string EncryptedAccessToken { get; set; }

        public int ExpireInSeconds { get; set; }

        public Guid UserId { get; set; }
    }
}