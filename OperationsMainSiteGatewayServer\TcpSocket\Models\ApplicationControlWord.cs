﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Yunda.SOMS.OperationsMainSiteGatewayServer.TcpSocket.Models
{
    public class ApplicationControlWord
    {
        // 定义各标志位的位置
        private const int FIR_MASK = 0x80;  // 1000 0000
        private const int FIN_MASK = 0x40;  // 0100 0000
        private const int CON_MASK = 0x20;  // 0010 0000
        private const int SEQUENCE_MASK = 0x1F; // 0001 1111 (5 bits for SEQUENCE)

        // FIR: 第一个分段
        public bool FIR { get; set; }
        // FIN: 最后一个分段
        public bool FIN { get; set; }
        // CON: 需要确认
        public bool CON { get; set; }
        // SEQUENCE: 序号 (0-31)
        public int SEQUENCE { get; set; }

        // 构造函数
        public ApplicationControlWord(bool fir, bool fin, bool con, int sequence)
        {
            FIR = fir;
            FIN = fin;
            CON = con;
            SEQUENCE = sequence & SEQUENCE_MASK;  // 确保序号在 0-31 范围内
        }

        // 转换为字节（应用控制字）
        public byte ToByte()
        {
            byte controlByte = 0;

            if (FIR) controlByte |= (byte)FIR_MASK;  // 设置 FIR 位
            if (FIN) controlByte |= (byte)FIN_MASK;  // 设置 FIN 位
            if (CON) controlByte |= (byte)CON_MASK;  // 设置 CON 位

            controlByte |= (byte)(SEQUENCE & SEQUENCE_MASK);  // 设置 SEQUENCE 位

            return controlByte;
        }

        // 从字节解析出各个字段
        public static ApplicationControlWord FromByte(byte controlByte)
        {
            bool fir = (controlByte & FIR_MASK) != 0;
            bool fin = (controlByte & FIN_MASK) != 0;
            bool con = (controlByte & CON_MASK) != 0;
            int sequence = controlByte & SEQUENCE_MASK; // 提取 SEQUENCE 位

            return new ApplicationControlWord(fir, fin, con, sequence);
        }
    }

}
