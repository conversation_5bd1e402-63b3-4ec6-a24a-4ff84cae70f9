﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaVModule
    {
        public string ModuleId { get; set; } = null!;
        public string ModuleName { get; set; } = null!;
        public string DirId { get; set; } = null!;
        public string ModuleType { get; set; } = null!;
        public string FuncId { get; set; } = null!;
        public string? ImgName { get; set; }
        public int? SeqNo { get; set; }
        public string State { get; set; } = null!;
        public string DirName { get; set; } = null!;
        public int? DirSeqNo { get; set; }
        public string? FuncName { get; set; }
        public string? FuncType { get; set; }
    }
}
