﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImReportCfgDatum
    {
        public int Id { get; set; }
        public string RptCfgId { get; set; } = null!;
        public string DataId { get; set; } = null!;
        public string? Title { get; set; }
        public int? ShowIntl { get; set; }
        public string? Reserved { get; set; }

        public virtual ImDeviceDatum Data { get; set; } = null!;
        public virtual ImReportCfg RptCfg { get; set; } = null!;
    }
}
