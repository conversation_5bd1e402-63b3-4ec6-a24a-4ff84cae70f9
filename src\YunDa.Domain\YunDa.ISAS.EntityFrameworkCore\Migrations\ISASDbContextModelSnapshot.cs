﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using YunDa.ISAS.EntityFrameworkCore.EntityFrameworkCore;

namespace YunDa.ISAS.Migrations
{
    [DbContext(typeof(ISASDbContext))]
    partial class ISASDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.1.32")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("YunDa.ISAS.Entities.ClientConfiguration.ThreeDimension.CCThreeDimension", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ModelId")
                        .HasColumnType("int");

                    b.Property<string>("ModelName")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("TelecommandConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("ThreeDimensionDataCategory")
                        .HasColumnType("int");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("VideoDevId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("TelecommandConfigurationId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.HasIndex("TransformerSubstationId");

                    b.HasIndex("VideoDevId");

                    b.ToTable("cc_three_dimension");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.MultidimensionalCheck", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<Guid?>("PatternRecognitionConfigutrationId1")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("PatternRecognitionConfigutrationId2")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("PresetPointId1")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("PresetPointId2")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("PatternRecognitionConfigutrationId1");

                    b.HasIndex("PatternRecognitionConfigutrationId2");

                    b.HasIndex("PresetPointId1");

                    b.HasIndex("PresetPointId2");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_multidimensional_check");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.MultidimensionalCheckSchedule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("EndTime")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("IntervalMinute")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("StartTime")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("Week")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("dm_multidimensional_check_schedule");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.SelfCheckingConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("DataType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("JudgmentMode")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<float?>("LowerLimit")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("RepetitiveDisplacementTimes")
                        .HasColumnType("int");

                    b.Property<int>("RepetitiveSendTimes")
                        .HasColumnType("int");

                    b.Property<float>("SendTelemeteringValue")
                        .HasColumnType("float");

                    b.Property<int>("SendTelesignalisationValue")
                        .HasColumnType("int");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<int>("TimeOfJudgment")
                        .HasColumnType("int");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<float?>("UpperLimit")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_self_checking_configuration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TeleCommandPlanSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_telecommand_plan_setting");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TeleCommandPlanTime", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ByDays")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Freq")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("SendTelecommandTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("TeleCommandPlanSettingId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TeleCommandPlanSettingId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_telecommand_plan_time");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TeleCommandSettingItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TeleCommandPlanSettingId")
                        .HasColumnType("char(36)");

                    b.Property<int>("TeleCommandValue")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelecommandConfigurationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TeleCommandPlanSettingId");

                    b.HasIndex("TelecommandConfigurationId");

                    b.ToTable("dm_telecommand_setting_item");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelecommandConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("CPUSector")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("DataSourceCategory")
                        .HasColumnType("int");

                    b.Property<int>("DeviceAddress")
                        .HasColumnType("int");

                    b.Property<int>("DispatcherAddress")
                        .HasColumnType("int");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int>("InfoAddress")
                        .HasColumnType("int");

                    b.Property<int>("InfoCPUSector")
                        .HasColumnType("int");

                    b.Property<int>("InfoDeviceAddress")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSave")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendDispatcher")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVirtualDevice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("NoContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<Guid?>("RelatedTelesignalisationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("RemoteType")
                        .HasColumnType("int");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("UnsurenessContent")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("YesContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("RelatedTelesignalisationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_telecommand_configuration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelecommandTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSave")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendDispatcher")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("NoContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<Guid?>("RelatedTelesignalisationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("RemoteType")
                        .HasColumnType("int");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("UnsurenessContent")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("YesContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.HasIndex("EquipmentTypeId");

                    b.ToTable("dm_telecommand_template");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelemeteringAlarmTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DMAlarmCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<float>("MaxValue")
                        .HasColumnType("float");

                    b.Property<float>("MinValue")
                        .HasColumnType("float");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid>("TelemeteringTemplateId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("DMAlarmCategoryId");

                    b.HasIndex("TelemeteringTemplateId");

                    b.ToTable("dm_telemetering_alarm_template");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("CPUSector")
                        .HasColumnType("int");

                    b.Property<float>("Coefficient")
                        .HasColumnType("float");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("DataSourceCategory")
                        .HasColumnType("int");

                    b.Property<int>("DecimalDigits")
                        .HasColumnType("int");

                    b.Property<int>("DeviceAddress")
                        .HasColumnType("int");

                    b.Property<int>("DispatcherAddress")
                        .HasColumnType("int");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int>("InfoAddress")
                        .HasColumnType("int");

                    b.Property<int>("InfoCPUSector")
                        .HasColumnType("int");

                    b.Property<int>("InfoDeviceAddress")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEnvironmentTemp")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSave")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSelfCheckingValue")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendDispatcher")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVirtualDevice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<float?>("LowerLimit")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<Guid?>("SelfCheckingConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Unit")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<float?>("UpperLimit")
                        .HasColumnType("float");

                    b.Property<string>("ismsbaseYCId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("SelfCheckingConfigurationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_telemetering_Configuration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelemeteringTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<float>("Coefficient")
                        .HasColumnType("float");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("DecimalDigits")
                        .HasColumnType("int");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSave")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendDispatcher")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("Unit")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentTypeId");

                    b.ToTable("dm_telemetering_template");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("CPUSector")
                        .HasColumnType("int");

                    b.Property<int>("CommValue")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DMAlarmCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("DataSourceCategory")
                        .HasColumnType("int");

                    b.Property<int>("DeviceAddress")
                        .HasColumnType("int");

                    b.Property<int>("DispatcherAddress")
                        .HasColumnType("int");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int>("InfoAddress")
                        .HasColumnType("int");

                    b.Property<int>("InfoCPUSector")
                        .HasColumnType("int");

                    b.Property<int>("InfoDeviceAddress")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsCommStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSave")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSelfCheckingValue")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendDispatcher")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVirtualDevice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("NoContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<int>("RemoteType")
                        .HasColumnType("int");

                    b.Property<Guid?>("SelfCheckingConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("UnsurenessContent")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("YesContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("ismsbaseYXId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.HasIndex("DMAlarmCategoryId");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("SelfCheckingConfigurationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_telesignalisation_configuration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("CommValue")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DMAlarmCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsCommStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSave")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSendDispatcher")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("NoContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<int>("RemoteType")
                        .HasColumnType("int");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("UnsurenessContent")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("YesContent")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.HasIndex("DMAlarmCategoryId");

                    b.HasIndex("EquipmentTypeId");

                    b.ToTable("dm_telesignalisation_template");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.Foundation.NameDateText", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Content")
                        .HasColumnType("longtext CHARACTER SET utf8mb4")
                        .HasMaxLength(65535);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("fd_name_date_text");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentDataCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("DataSourceCategory")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Icon")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("Name");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_equipment_data_category");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentDataCategoryExactly", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentDataCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentDataCategoryId");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_equipment_data_category_exactly");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("BelongEquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("FactorySerialNumber")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("InstallationArea")
                        .HasColumnType("varchar(40) CHARACTER SET utf8mb4")
                        .HasMaxLength(40);

                    b.Property<DateTime?>("InstallationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRemoteControl")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("MaintenanceRecord")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("ManufacturerInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("PostionDescription")
                        .HasColumnType("varchar(40) CHARACTER SET utf8mb4")
                        .HasMaxLength(40);

                    b.Property<DateTime?>("ProductionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("RunNumber")
                        .HasColumnType("varchar(40) CHARACTER SET utf8mb4")
                        .HasMaxLength(40);

                    b.Property<int?>("SafetyPlanStateType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SafetyPlanTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("SafetyStateType")
                        .HasColumnType("int");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("VerificationDate")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("VerificationPerson")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("VerificationRecords")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.HasIndex("BelongEquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("ManufacturerInfoId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_equipment_info");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentLinkTeledata", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DataEquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentDataCategoryExactlyId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisable")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("TeleDataProperty")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("DataEquipmentInfoId");

                    b.HasIndex("EquipmentDataCategoryExactlyId");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.ToTable("gi_equipment_link_Teledata");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentLocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("gi_equipment_location");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int>("EquipmentTypeLevel")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("gi_equipment_type");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentTypeViewPoint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentTypeId");

                    b.ToTable("gi_equipment_type_view_point");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentViewPoint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeViewPointId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeViewPointId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_equipment_view_point");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.ManufacturerInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ManufacturerAddress")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("ManufacturerCode")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("ManufacturerName")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(29) CHARACTER SET utf8mb4")
                        .HasMaxLength(29);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.HasKey("Id");

                    b.ToTable("gi_manufacturer_info");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.MasterStation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("MasterStationAddress")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<string>("MasterStationIp")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<int?>("MasterStationLevel")
                        .HasColumnType("int");

                    b.Property<int>("MasterStationPort")
                        .HasColumnType("int");

                    b.Property<int?>("MasterStationType")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("gi_master_station");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.PowerSupplyLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LineName")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.HasKey("Id");

                    b.ToTable("gi_power_supply_line");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.SubMasterStationRelation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("MasterStationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("MasterStationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_submasterstation_relation");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CommMgrIP")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("DataMonitoringAddress")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<string>("ExternalCommAddress")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<string>("Iec104ServerUrl")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<string>("InspectionServiceBaseUrl")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double");

                    b.Property<string>("MasterStationAddress")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<int?>("MasterStationType")
                        .HasColumnType("int");

                    b.Property<Guid?>("PowerSupplyLineId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("RobotServerAddress")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<string>("RobotServiceBaseUrl")
                        .HasColumnType("varchar(250) CHARACTER SET utf8mb4")
                        .HasMaxLength(250);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("SubstationName")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.HasKey("Id");

                    b.HasIndex("PowerSupplyLineId");

                    b.ToTable("gi_transformer_substation");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotDeviceInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("AppearanceType")
                        .HasColumnType("varchar(10) CHARACTER SET utf8mb4")
                        .HasMaxLength(10);

                    b.Property<string>("BayId")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("BayName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("DeviceId")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("DeviceLevel")
                        .HasColumnType("int");

                    b.Property<string>("DeviceName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("DeviceType")
                        .HasColumnType("varchar(10) CHARACTER SET utf8mb4")
                        .HasMaxLength(10);

                    b.Property<string>("FeverType")
                        .HasColumnType("varchar(10) CHARACTER SET utf8mb4")
                        .HasMaxLength(10);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("MainDeviceId")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("MainDeviceName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("MeterType")
                        .HasColumnType("varchar(10) CHARACTER SET utf8mb4")
                        .HasMaxLength(10);

                    b.Property<string>("Phase")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("PresetPointId")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("PresetPointName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("RecognitionTypeList")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<Guid>("RobotInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RobotTaskId")
                        .HasColumnType("char(36)");

                    b.Property<string>("SaveTypeList")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int?>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("VoltageLevel")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.HasKey("Id");

                    b.HasIndex("RobotInfoId");

                    b.HasIndex("RobotTaskId");

                    b.ToTable("ms_robot_device_info");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("GeneralCameraId")
                        .HasColumnType("char(36)");

                    b.Property<string>("IP")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<Guid?>("InfraredCameraId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ManufacturerInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int?>("Port")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int?>("RobotType")
                        .HasColumnType("int");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Url")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.HasIndex("GeneralCameraId");

                    b.HasIndex("InfraredCameraId");

                    b.HasIndex("ManufacturerInfoId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("ms_robot_info");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotTask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CycleExecuteTime")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("CycleWeek")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int>("DeviceLevel")
                        .HasColumnType("int");

                    b.Property<string>("DeviceList")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime?>("FixedStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsIssue")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTemp")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MasterStationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<int>("RobotAction")
                        .HasColumnType("int");

                    b.Property<Guid>("RobotInfoId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("TaskId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("TempId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MasterStationId");

                    b.HasIndex("RobotInfoId");

                    b.ToTable("ms_robot_task");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotTaskItemLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RobotDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RobotTaskId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("RobotDeviceInfoId");

                    b.HasIndex("RobotTaskId");

                    b.ToTable("ms_robot_task_item_link");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.DMAlarmCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Color")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Ico")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.HasKey("Id");

                    b.ToTable("dm_alarm_category");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageCondition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("CameraAuthenticationId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("CompareType")
                        .HasColumnType("int");

                    b.Property<float?>("ComparisonValue")
                        .HasColumnType("float");

                    b.Property<int>("ConditionType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeterminationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("LinkageStrategyId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LogicalOperator")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("CameraAuthenticationId");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("LinkageStrategyId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.ToTable("dm_linkage_condition");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageExecuteActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("ActivityType")
                        .HasColumnType("int");

                    b.Property<Guid?>("CameraAuthenticationId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsCapturePicture")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRecordVideo")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("KeepTime")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("LinkageStrategyId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("PresetPointId")
                        .HasColumnType("char(36)");

                    b.Property<int>("RecordDuration")
                        .HasColumnType("int");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelecommandConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("TelecommandValue")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CameraAuthenticationId");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("LinkageStrategyId");

                    b.HasIndex("PresetPointId");

                    b.HasIndex("TelecommandConfigurationId");

                    b.ToTable("dm_linkage_execute_activity");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageStrategy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConditionIds")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DMAlarmCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<int>("ForceLinkageSeconds")
                        .HasColumnType("int");

                    b.Property<int>("ForceLinkageTimes")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPatternRecognize")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("RepeatLinkageInterval")
                        .HasColumnType("int");

                    b.Property<string>("Rule")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<int>("TimeOfWithRelationship")
                        .HasColumnType("int");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("DMAlarmCategoryId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("dm_linkage_strategy");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.TelemeteringAlarmStrategy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DMAlarmCategoryId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<float>("MaxValue")
                        .HasColumnType("float");

                    b.Property<float>("MinValue")
                        .HasColumnType("float");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("DMAlarmCategoryId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.ToTable("dm_telemetering_alarm_strategy");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("SysConfigurationType")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)")
                        .HasMaxLength(36);

                    b.HasKey("Id");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("sys_configuration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysFunction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Icon")
                        .HasColumnType("varchar(40) CHARACTER SET utf8mb4")
                        .HasMaxLength(40);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOperatorPage")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LoadUrl")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("SysFunctionId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("sys_function");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.HasKey("Id");

                    b.ToTable("sys_role");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysRoleFunction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEdit")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("SysFunctionId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("SysRoleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("SysFunctionId");

                    b.HasIndex("SysRoleId");

                    b.ToTable("sys_role_function");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysRoleUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("SysRoleId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("SysUserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("SysRoleId");

                    b.HasIndex("SysUserId");

                    b.ToTable("sys_role_user");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("ErrorTimes")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOnline")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastLoginErrorDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("OrganizationalUnit")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(29) CHARACTER SET utf8mb4")
                        .HasMaxLength(29);

                    b.Property<string>("RealName")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("UserPriority")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("sys_user");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.CameraAuthentication", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Illustration")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("Source")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int?>("StationLevel")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("vs_camera_authentication");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.InspectionCard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CardName")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int?>("CenterType")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsIssue")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTemporary")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MasterStationId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("OpenWiper")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("ResidenceTime")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("MasterStationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("vs_inspection_card");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.InspectionItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("InspectionCardId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsImageRecognition")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRecordVideo")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ItemName")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("PicturesInterval")
                        .HasColumnType("int");

                    b.Property<int>("PicturesNumber")
                        .HasColumnType("int");

                    b.Property<Guid?>("PresetPointId")
                        .HasColumnType("char(36)");

                    b.Property<int>("ProcessAction")
                        .HasColumnType("int");

                    b.Property<int>("ProcessDuration")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("VideoDevId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("InspectionCardId");

                    b.HasIndex("PresetPointId");

                    b.HasIndex("VideoDevId");

                    b.ToTable("vs_inspection_Item");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.InspectionPlanTask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ExecutionDate")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("ExecutionTime")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<int?>("ExecutionWeek")
                        .HasColumnType("int");

                    b.Property<Guid?>("InspectionCardId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("PlanTaskName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("InspectionCardId");

                    b.ToTable("vs_inspection_plan_task");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.LightingControl", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CloseTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("LightingOpenRule")
                        .HasColumnType("int");

                    b.Property<int>("LightingPreheatSeconds")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime>("OpenTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("PresetPointId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelecommandConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("PresetPointId");

                    b.HasIndex("TelecommandConfigurationId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("vs_lighting_control");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.MeasureTemperaturePoint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CoordinateJsonStr")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<float?>("Distance")
                        .HasColumnType("float");

                    b.Property<float?>("Emissivity")
                        .HasColumnType("float");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("MeasureTemperatureType")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<Guid?>("PresetPointId")
                        .HasColumnType("char(36)");

                    b.Property<float?>("ReflectedTemperature")
                        .HasColumnType("float");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("PresetPointId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.ToTable("vs_measures_temperature_point");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.PatternRecognitionConfigutration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Arg")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Box")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Decimal")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNewAlgorithnm")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ItemName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<Guid?>("PresetPointId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Rect")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("ResultsMapping")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Type")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("Unit")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("VideoDevId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("PresetPointId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.HasIndex("VideoDevId");

                    b.ToTable("vs_pattern_recognition_configutration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentViewPointId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsImageRecognition")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("VideoDevId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("EquipmentViewPointId");

                    b.HasIndex("VideoDevId");

                    b.ToTable("vs_preset_point");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int?>("ChannelNo")
                        .HasColumnType("int");

                    b.Property<int>("CodeStreamType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("CtrAuPos")
                        .HasColumnType("int");

                    b.Property<string>("DevCode")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("DevName")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int?>("DevNo")
                        .HasColumnType("int");

                    b.Property<string>("DevPassword")
                        .HasColumnType("varchar(30) CHARACTER SET utf8mb4")
                        .HasMaxLength(30);

                    b.Property<int>("DevType")
                        .HasColumnType("int");

                    b.Property<string>("DevUserName")
                        .HasColumnType("varchar(30) CHARACTER SET utf8mb4")
                        .HasMaxLength(30);

                    b.Property<string>("ExternalCommIP")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("IP")
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.Property<string>("InstallationArea")
                        .IsRequired()
                        .HasColumnType("varchar(40) CHARACTER SET utf8mb4")
                        .HasMaxLength(40);

                    b.Property<DateTime?>("InstallationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPTZ")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("LinkVideoDevId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ManufacturerInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("MasterStationId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("Port")
                        .HasColumnType("int");

                    b.Property<string>("PostionDescription")
                        .IsRequired()
                        .HasColumnType("varchar(40) CHARACTER SET utf8mb4")
                        .HasMaxLength(40);

                    b.Property<DateTime?>("ProductionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int?>("SafetyPlanStateType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SafetyPlanTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("SafetyStateType")
                        .HasColumnType("int");

                    b.Property<int?>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("VideoDevId")
                        .HasColumnType("char(36)");

                    b.Property<int>("VoiceType")
                        .HasColumnType("int");

                    b.Property<float?>("X")
                        .HasColumnType("float");

                    b.Property<float?>("Y")
                        .HasColumnType("float");

                    b.Property<float?>("Z")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("LinkVideoDevId");

                    b.HasIndex("ManufacturerInfoId");

                    b.HasIndex("MasterStationId");

                    b.HasIndex("TransformerSubstationId");

                    b.HasIndex("VideoDevId");

                    b.ToTable("vs_video_dev");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("ConfigType")
                        .HasColumnType("int");

                    b.Property<string>("ConfigValue")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<Guid?>("EnergyConsumptionDeviceId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelemeteringId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationId")
                        .HasColumnType("char(36)");

                    b.Property<int>("ValueType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EnergyConsumptionDeviceId");

                    b.HasIndex("TelemeteringId");

                    b.HasIndex("TelesignalisationId");

                    b.ToTable("dm_energy_consumption_config");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("DeviceType")
                        .HasColumnType("int");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOperating")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("LastOperationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("MonitoringConfig")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.ToTable("dm_energy_consumption_device");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.DataMonitoring.EnergyOperationCriteria", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("ComparisonType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("DeviceType")
                        .HasColumnType("int");

                    b.Property<Guid?>("EnergyDeviceId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("InfoAddress")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("ParameterName")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int>("ParameterType")
                        .HasColumnType("int");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<int>("SourceType")
                        .HasColumnType("int");

                    b.Property<Guid?>("TelemeteringId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationId")
                        .HasColumnType("char(36)");

                    b.Property<double>("Threshold")
                        .HasColumnType("double");

                    b.Property<Guid?>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EnergyDeviceId");

                    b.HasIndex("TelemeteringId");

                    b.HasIndex("TelesignalisationId");

                    b.ToTable("dm_energy_operation_criteria");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.BoardCardHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("BoardCardInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("BoardId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ContentJson")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ContentNewJson")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EventDescription")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("EventRecordType")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSend")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("ProtectionDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RecodeDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("gi_board_card_history");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.BoardCardInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("BoardId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("BoardType")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("BoardTypeId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("BootVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("CcdChecksum")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("CidChecksum")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FpgaVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("HardwareVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("IOCrc")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("IOVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Iec61850Version")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime?>("InstallationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("InterfaceChecksum")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("InterfaceDatabaseVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("InterfaceVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("MaintenanceRecord")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("ManufacturerInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("OsVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime?>("ProductionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ProgramVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ProgramVersionCrc")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ProtectionChecksum")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ProtectionDatabaseVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("ProtectionDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ProtectionVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("SystemVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("VerificationDate")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("VerificationPerson")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("VerificationRecords")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("YjCrc")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("YjVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerInfoId");

                    b.HasIndex("ProtectionDeviceInfoId");

                    b.ToTable("gi_board_card_info");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.EquipmentDataCategoryBase", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255) CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("gi_equipment_data_category_base");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.EquipmentIndicatorComment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255) CHARACTER SET utf8mb4");

                    b.Property<string>("Description")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("gi_equipment_indicator_comment");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.EquipmentIndicatorConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CalculationFormula")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<decimal>("DataPrecision")
                        .HasColumnType("decimal(65,30)");

                    b.Property<Guid?>("EquipmentTypeId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(65,30)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentTypeId");

                    b.HasIndex("Name");

                    b.ToTable("gi_equipment_indicator_config");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.IntervalEquipmentInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ProtectionDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("SubstationIntervalId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("ProtectionDeviceInfoId");

                    b.HasIndex("SubstationIntervalId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_interval_equipmentInfo");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceGateway", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("GatewayIP1")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("GatewayIP2")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int?>("GatewayPort1")
                        .HasColumnType("int");

                    b.Property<int?>("GatewayPort2")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("PhysicalAddress")
                        .HasColumnType("int");

                    b.Property<Guid>("ProtectionDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Protocol")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ProtectionDeviceInfoId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_protection_device_gateway");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ContentJson")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ContentNewJson")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EventDescription")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("EventRecordType")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSend")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("ProtectionDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("RecodeDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProtectionDeviceInfoId");

                    b.ToTable("gi_protection_device_history");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("BaselineBoardVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("BayName")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("CanSwitchDZZone")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int>("DeviceAddress")
                        .HasColumnType("int");

                    b.Property<int?>("DeviceState")
                        .HasColumnType("int");

                    b.Property<int?>("EndOfDKJL")
                        .HasColumnType("int");

                    b.Property<Guid>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("HardwareVersion")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ISMS_DeviceId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsIncludeDz")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid?>("ProtectionDeviceGatewayId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ProtectionDeviceTypeId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<string>("Specification")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int?>("StartOfDKJL")
                        .HasColumnType("int");

                    b.Property<bool>("SupportDKJL")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("SupportVersion")
                        .HasColumnType("int");

                    b.Property<int>("SupportsDualCurrent")
                        .HasColumnType("int");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("ProtectionDeviceGatewayId");

                    b.HasIndex("ProtectionDeviceTypeId");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_protection_device_info");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("AnalogParseMode")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("CanSwitchDZZone")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("DZReadOnly")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("DZZoneCount")
                        .HasColumnType("int");

                    b.Property<int?>("EndOfDKJL")
                        .HasColumnType("int");

                    b.Property<string>("EventParseMode")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Generation")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsCRCC")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ManufacturerInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Model")
                        .HasColumnType("varchar(50) CHARACTER SET utf8mb4")
                        .HasMaxLength(50);

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<int>("PUCtgyCode")
                        .HasColumnType("int");

                    b.Property<string>("Specification")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int?>("StartOfDKJL")
                        .HasColumnType("int");

                    b.Property<bool>("Support12YC")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportDKJL")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportDZ")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportEventReport")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportFaultReport")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportLoadRecording")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportRecordingFiles")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportSelfTestReport")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("SupportVersion")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerInfoId");

                    b.ToTable("gi_protection_device_type");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("CpuIndex")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("CtrlWordTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("EnumTypeId")
                        .HasColumnType("int");

                    b.Property<Guid>("EquipmentInfoId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ISMS_DeviceDZId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ISMS_DeviceId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsHidden")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<float?>("Max")
                        .HasColumnType("float");

                    b.Property<float?>("Min")
                        .HasColumnType("float");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid>("ProtectionDeviceInfoId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ProtectionSettingTypeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("RelatedCtId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("RelatedPtId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<double?>("SettingCoeff")
                        .HasColumnType("double");

                    b.Property<double?>("SettingCoeff1")
                        .HasColumnType("double");

                    b.Property<string>("SettingComment")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<int>("SettingIndex")
                        .HasColumnType("int");

                    b.Property<int?>("SettingPrecision")
                        .HasColumnType("int");

                    b.Property<int?>("SettingPrecision1")
                        .HasColumnType("int");

                    b.Property<string>("SettingRange")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("SettingUnit")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("SettingUnit1")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<double?>("UnitConversionCoeff")
                        .HasColumnType("double");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentInfoId");

                    b.HasIndex("ProtectionDeviceInfoId");

                    b.HasIndex("ProtectionSettingTypeId");

                    b.ToTable("gi_protection_setting");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionSettingType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.ToTable("gi_protection_setting_type");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("CircuitType")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("PictureBase64")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PicturePath")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500) CHARACTER SET utf8mb4")
                        .HasMaxLength(500);

                    b.Property<int>("SeqNo")
                        .HasColumnType("int");

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_secondary_circuit");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuitLogicExpression", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsCompleteExpression")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LogicalExpression")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(200) CHARACTER SET utf8mb4")
                        .HasMaxLength(200);

                    b.Property<string>("PicturePath")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid>("SecondaryCircuitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelemeteringConfigurationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TelesignalisationConfigurationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("SecondaryCircuitId");

                    b.HasIndex("TelemeteringConfigurationId");

                    b.HasIndex("TelesignalisationConfigurationId");

                    b.ToTable("gi_secondary_circuit_logic_expression");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuitProtectionDevice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("ProtectionDeviceId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<Guid>("SecondaryCircuitId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ProtectionDeviceId");

                    b.HasIndex("SecondaryCircuitId");

                    b.ToTable("gi_secondary_circuit_protection_device");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SubstationInterval", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("CreatorUserId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("CurrentCapacity")
                        .HasColumnType("int");

                    b.Property<Guid?>("DeleterUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("IntervalType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("LastModifierUserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<string>("ProtectionType")
                        .HasColumnType("varchar(100) CHARACTER SET utf8mb4")
                        .HasMaxLength(100);

                    b.Property<Guid>("TransformerSubstationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("VoltageLevel")
                        .IsRequired()
                        .HasColumnType("varchar(20) CHARACTER SET utf8mb4")
                        .HasMaxLength(20);

                    b.HasKey("Id");

                    b.HasIndex("TransformerSubstationId");

                    b.ToTable("gi_substation_interval");
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.System.AppUrlConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Value")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.ToTable("sys_app_url_configuration");
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.ClientConfiguration.ThreeDimension.CCThreeDimension", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelecommandConfiguration", "TelecommandConfiguration")
                        .WithMany()
                        .HasForeignKey("TelecommandConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "VideoInfo")
                        .WithMany()
                        .HasForeignKey("VideoDevId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.MultidimensionalCheck", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PatternRecognitionConfigutration", "PatternRecognitionConfigutration1")
                        .WithMany()
                        .HasForeignKey("PatternRecognitionConfigutrationId1")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PatternRecognitionConfigutration", "PatternRecognitionConfigutration2")
                        .WithMany()
                        .HasForeignKey("PatternRecognitionConfigutrationId2")
                        .HasConstraintName("FK_dm_multidimensional_check_vs_pattern_recognition_configutra~1")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint1")
                        .WithMany()
                        .HasForeignKey("PresetPointId1")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint2")
                        .WithMany()
                        .HasForeignKey("PresetPointId2")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.SelfCheckingConfiguration", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TeleCommandPlanSetting", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TeleCommandPlanTime", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TeleCommandPlanSetting", "TeleCommandPlanSetting")
                        .WithMany()
                        .HasForeignKey("TeleCommandPlanSettingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TeleCommandSettingItem", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TeleCommandPlanSetting", "TeleCommandPlanSetting")
                        .WithMany("TeleCommandSettingItems")
                        .HasForeignKey("TeleCommandPlanSettingId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelecommandConfiguration", "TelecommandConfiguration")
                        .WithMany()
                        .HasForeignKey("TelecommandConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelecommandConfiguration", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "RelatedTelesignalisation")
                        .WithMany()
                        .HasForeignKey("RelatedTelesignalisationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelecommandTemplate", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany("TelecommandTemplates")
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelemeteringAlarmTemplate", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.DMAlarmCategory", "DMAlarmCategory")
                        .WithMany()
                        .HasForeignKey("DMAlarmCategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringTemplate", "TelemeteringTemplate")
                        .WithMany("TelemeteringAlarmTemplates")
                        .HasForeignKey("TelemeteringTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.SelfCheckingConfiguration", "SelfCheckingConfiguration")
                        .WithMany()
                        .HasForeignKey("SelfCheckingConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelemeteringTemplate", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany("TelemeteringTemplates")
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.DMAlarmCategory", "DMAlarmCategory")
                        .WithMany()
                        .HasForeignKey("DMAlarmCategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.SelfCheckingConfiguration", "SelfCheckingConfiguration")
                        .WithMany()
                        .HasForeignKey("SelfCheckingConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationTemplate", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.DMAlarmCategory", "DMAlarmCategory")
                        .WithMany()
                        .HasForeignKey("DMAlarmCategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany("TelesignalisationTemplates")
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.Foundation.NameDateText", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentDataCategory", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.EquipmentDataCategoryBase", "EquipmentDataCategoryBase")
                        .WithMany()
                        .HasForeignKey("Name")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentDataCategoryExactly", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentDataCategory", "EquipmentDataCategory")
                        .WithMany()
                        .HasForeignKey("EquipmentDataCategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "BelongEquipmentInfo")
                        .WithMany()
                        .HasForeignKey("BelongEquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany("EquipmentInfos")
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.ManufacturerInfo", "ManufacturerInfo")
                        .WithMany()
                        .HasForeignKey("ManufacturerInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentLinkTeledata", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "DataEquipmentInfo")
                        .WithMany()
                        .HasForeignKey("DataEquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentDataCategoryExactly", "EquipmentDataCategoryExactly")
                        .WithMany()
                        .HasForeignKey("EquipmentDataCategoryExactlyId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentTypeViewPoint", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.EquipmentViewPoint", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentTypeViewPoint", "EquipmentTypeViewPoint")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeViewPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.SubMasterStationRelation", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "MasterStation")
                        .WithMany()
                        .HasForeignKey("MasterStationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.PowerSupplyLine", "PowerSupplyLine")
                        .WithMany("TransformerSubstations")
                        .HasForeignKey("PowerSupplyLineId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotDeviceInfo", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MobileSurveillance.RobotInfo", "RobotInfo")
                        .WithMany()
                        .HasForeignKey("RobotInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.MobileSurveillance.RobotTask", null)
                        .WithMany("RobotDeviceInfos")
                        .HasForeignKey("RobotTaskId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotInfo", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "GeneralCamera")
                        .WithMany()
                        .HasForeignKey("GeneralCameraId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "InfraredCamera")
                        .WithMany()
                        .HasForeignKey("InfraredCameraId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.ManufacturerInfo", "ManufacturerInfo")
                        .WithMany()
                        .HasForeignKey("ManufacturerInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotTask", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.MasterStation", "MasterStation")
                        .WithMany()
                        .HasForeignKey("MasterStationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.MobileSurveillance.RobotInfo", "RobotInfo")
                        .WithMany()
                        .HasForeignKey("RobotInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MobileSurveillance.RobotTaskItemLink", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MobileSurveillance.RobotDeviceInfo", "RobotDeviceInfo")
                        .WithMany()
                        .HasForeignKey("RobotDeviceInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.MobileSurveillance.RobotTask", "RobotTask")
                        .WithMany()
                        .HasForeignKey("RobotTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageCondition", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.CameraAuthentication", "CameraAuthentication")
                        .WithMany()
                        .HasForeignKey("CameraAuthenticationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageStrategy", "LinkageStrategy")
                        .WithMany("LinkageConditions")
                        .HasForeignKey("LinkageStrategyId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageExecuteActivity", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.CameraAuthentication", "CameraAuthentication")
                        .WithMany()
                        .HasForeignKey("CameraAuthenticationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageStrategy", "LinkageStrategy")
                        .WithMany("LinkageExecuteActivities")
                        .HasForeignKey("LinkageStrategyId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint")
                        .WithMany()
                        .HasForeignKey("PresetPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelecommandConfiguration", "TelecommandConfiguration")
                        .WithMany()
                        .HasForeignKey("TelecommandConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.LinkageStrategy", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.DMAlarmCategory", "DMAlarmCategory")
                        .WithMany()
                        .HasForeignKey("DMAlarmCategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.MySQL.DataMonitoring.TelemeteringAlarmStrategy", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.MySQL.DataMonitoring.DMAlarmCategory", "DMAlarmCategory")
                        .WithMany()
                        .HasForeignKey("DMAlarmCategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany("TelemeteringAlarmStrategys")
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysConfiguration", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysRoleFunction", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.System.SysFunction", "SysFunction")
                        .WithMany()
                        .HasForeignKey("SysFunctionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.System.SysRole", "SysRole")
                        .WithMany()
                        .HasForeignKey("SysRoleId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.System.SysRoleUser", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.System.SysRole", "SysRole")
                        .WithMany()
                        .HasForeignKey("SysRoleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.System.SysUser", "SysUser")
                        .WithMany()
                        .HasForeignKey("SysUserId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.InspectionCard", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.MasterStation", "MasterStation")
                        .WithMany()
                        .HasForeignKey("MasterStationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany("InspectionCards")
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.InspectionItem", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.InspectionCard", "InspectionCard")
                        .WithMany("InspectionItems")
                        .HasForeignKey("InspectionCardId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint")
                        .WithMany("InspectionItems")
                        .HasForeignKey("PresetPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "VideoDev")
                        .WithMany("InspectionItems")
                        .HasForeignKey("VideoDevId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.InspectionPlanTask", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.InspectionCard", "InspectionCard")
                        .WithMany("InspectionPlanTasks")
                        .HasForeignKey("InspectionCardId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.LightingControl", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint")
                        .WithMany()
                        .HasForeignKey("PresetPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelecommandConfiguration", "TelecommandConfiguration")
                        .WithMany()
                        .HasForeignKey("TelecommandConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.MeasureTemperaturePoint", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint")
                        .WithMany("MeasureTemperaturePoints")
                        .HasForeignKey("PresetPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.PatternRecognitionConfigutration", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", "PresetPoint")
                        .WithMany()
                        .HasForeignKey("PresetPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "Telemetering")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "Telesignalisation")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "VideoDev")
                        .WithMany()
                        .HasForeignKey("VideoDevId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.PresetPoint", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentViewPoint", "EquipmentViewPoint")
                        .WithMany()
                        .HasForeignKey("EquipmentViewPointId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "VideoDev")
                        .WithMany("PresetPoints")
                        .HasForeignKey("VideoDevId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "LinkVideoDev")
                        .WithMany()
                        .HasForeignKey("LinkVideoDevId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.ManufacturerInfo", "ManufacturerInfo")
                        .WithMany()
                        .HasForeignKey("ManufacturerInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.MasterStation", "MasterStation")
                        .WithMany()
                        .HasForeignKey("MasterStationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany("VideoDevs")
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.VideoSurveillance.VideoDev", "Parent")
                        .WithMany()
                        .HasForeignKey("VideoDevId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionConfig", b =>
                {
                    b.HasOne("YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice", "EnergyConsumptionDevice")
                        .WithMany()
                        .HasForeignKey("EnergyConsumptionDeviceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.DataMonitoring.EnergyOperationCriteria", b =>
                {
                    b.HasOne("YunDa.SOMS.Entities.DataMonitoring.EnergyConsumptionDevice", "EnergyDevice")
                        .WithMany()
                        .HasForeignKey("EnergyDeviceId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.BoardCardInfo", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.ManufacturerInfo", "ManufacturerInfo")
                        .WithMany()
                        .HasForeignKey("ManufacturerInfoId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", "ProtectionDeviceInfo")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceInfoId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.EquipmentIndicatorConfig", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentType", "EquipmentType")
                        .WithMany()
                        .HasForeignKey("EquipmentTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.EquipmentIndicatorComment", "EquipmentIndicatorComment")
                        .WithMany()
                        .HasForeignKey("Name")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.IntervalEquipmentInfo", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", "ProtectionDeviceInfo")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.SubstationInterval", "SubstationInterval")
                        .WithMany()
                        .HasForeignKey("SubstationIntervalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceGateway", b =>
                {
                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", "ProtectionDeviceInfo")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceHistory", b =>
                {
                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", "ProtectionDeviceInfo")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceInfoId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceGateway", "ProtectionDeviceGateway")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceGatewayId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceType", "ProtectionDeviceType")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceTypeId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceType", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.ManufacturerInfo", "ManufacturerInfo")
                        .WithMany()
                        .HasForeignKey("ManufacturerInfoId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.ProtectionSetting", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.EquipmentInfo", "EquipmentInfo")
                        .WithMany()
                        .HasForeignKey("EquipmentInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", "ProtectionDeviceInfo")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionSettingType", "ProtectionSettingType")
                        .WithMany()
                        .HasForeignKey("ProtectionSettingTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuit", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuitLogicExpression", b =>
                {
                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuit", "SecondaryCircuit")
                        .WithMany()
                        .HasForeignKey("SecondaryCircuitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelemeteringConfiguration", "TelemeteringConfiguration")
                        .WithMany()
                        .HasForeignKey("TelemeteringConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("YunDa.ISAS.Entities.DataMonitoring.TelesignalisationConfiguration", "TelesignalisationConfiguration")
                        .WithMany()
                        .HasForeignKey("TelesignalisationConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuitProtectionDevice", b =>
                {
                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.ProtectionDeviceInfo", "ProtectionDevice")
                        .WithMany()
                        .HasForeignKey("ProtectionDeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("YunDa.SOMS.Entities.GeneralInformation.SecondaryCircuit", "SecondaryCircuit")
                        .WithMany()
                        .HasForeignKey("SecondaryCircuitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("YunDa.SOMS.Entities.GeneralInformation.SubstationInterval", b =>
                {
                    b.HasOne("YunDa.ISAS.Entities.GeneralInformation.TransformerSubstation", "TransformerSubstation")
                        .WithMany()
                        .HasForeignKey("TransformerSubstationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
