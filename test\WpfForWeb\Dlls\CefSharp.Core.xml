<?xml version="1.0"?>
<doc>
    <assembly>
        "CefSharp.Core"
    </assembly>
    <members>
        <member name="M:CefSharp.Internals.StringUtils.CreateExceptionString(scoped_refptr&lt;CefV8Exception&gt;)">
            <summary>
Creates a detailed expection string from a provided Cef V8 exception.
</summary>
            <param name="exception">The exception which will be used as base for the message</param>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.AssignNativeFromClr(_cef_string_utf16_t*!System.Runtime.CompilerServices.IsImplicitlyDereferenced,System.String)">
            <summary>
Assigns the provided cef_string_t object from the given .NET string.
</summary>
            <param name="cefStr">The cef_string_t that should be updated.</param>
            <param name="str">The .NET string whose value should be used to update cefStr.</param>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToNative(System.Collections.Generic.IEnumerable`1{System.String})">
            <summary>
Converts a .NET List of strings to native (unmanaged) format.
</summary>
            <param name="str">The List of strings that should be converted.</param>
            <returns>An unmanaged representation of the provided List of strings, or an empty List if the input is a nullptr.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToNative(System.String)">
            <summary>
Converts a .NET string to native (unmanaged) format. Note that this method does not allocate a new copy of the
</summary>
            <param name="str">The string that should be converted.</param>
            <returns>An unmanaged representation of the provided string, or an empty string if the input string is a nullptr.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToClr(std.vector&lt;CefStringBase&lt;CefStringTraitsUTF16&gt;,std.allocator&lt;CefStringBase&lt;CefStringTraitsUTF16&gt;&gt;&gt;!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Converts an unmanaged vector of strings to a (managed) .NET List of strings.
</summary>
            <param name="cefStr">The vector of strings that should be converted.</param>
            <returns>A .NET List of strings.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToClr(CefStringBase&lt;CefStringTraitsUTF16&gt;!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Converts an unmanaged string to a (managed) .NET string.
</summary>
            <param name="cefStr">The string that should be converted.</param>
            <returns>A .NET string.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToClr(_cef_string_utf16_t!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Converts an unmanaged string to a (managed) .NET string.
</summary>
            <param name="cefStr">The string that should be converted.</param>
            <returns>A .NET string.</returns>
        </member>
        <member name="T:CefSharp.PopupFeatures">
            <summary>
Class representing popup window features.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.WindowlessFrameRate">
            <summary>
The maximum rate in frames per second (fps) that CefRenderHandler::OnPaint
will be called for a windowless browser. The actual fps may be lower if
the browser cannot generate frames at the requested rate. The minimum
value is 1 and the maximum value is 60 (default 30). This value can also be
changed dynamically via IBrowserHost.SetWindowlessFrameRate.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.AcceptLanguageList">
            <summary>
Comma delimited ordered list of language codes without any whitespace that
will be used in the "Accept-Language" HTTP header. May be overridden on a
per-browser basis using the CefBrowserSettings.AcceptLanguageList value.
If both values are empty then "en-US,en" will be used. Can be overridden
for individual RequestContext instances via the
RequestContextSettings.AcceptLanguageList value.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.BackgroundColor">
            <summary>
Opaque background color used for the browser before a document is loaded
and when no document color is specified. By default the background color
will be the same as CefSettings.BackgroundColor. Only the RGB compontents
of the specified value will be used. The alpha component must greater than
0 to enable use of the background color but will be otherwise ignored.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.WebGl">
            <summary>
Controls whether WebGL can be used. Note that WebGL requires hardware
support and may not work on all systems even when enabled. Also
configurable using the "disable-webgl" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.ApplicationCache">
            <summary>
Controls whether the application cache can be used. Also configurable using
the "disable-application-cache" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.Databases">
            <summary>
Controls whether databases can be used. Also configurable using the
"disable-databases" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.LocalStorage">
            <summary>
Controls whether local storage can be used. Also configurable using the
"disable-local-storage" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.TabToLinks">
            <summary>
Controls whether the tab key can advance focus to links. Also configurable
using the "disable-tab-to-links" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.TextAreaResize">
            <summary>
Controls whether text areas can be resized. Also configurable using the
"disable-text-area-resize" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.ImageShrinkStandaloneToFit">
            <summary>
Controls whether standalone images will be shrunk to fit the page. Also
configurable using the "image-shrink-standalone-to-fit" command-line
switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.ImageLoading">
            <summary>
Controls whether image URLs will be loaded from the network. A cached image
will still be rendered if requested. Also configurable using the
"disable-image-loading" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.WebSecurity">
            <summary>
Controls whether web security restrictions (same-origin policy) will be
enforced. Disabling this setting is not recommend as it will allow risky
security behavior such as cross-site scripting (XSS). Also configurable
using the "disable-web-security" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.FileAccessFromFileUrls">
            <summary>
Controls whether file URLs will have access to other file URLs. Also
configurable using the "allow-access-from-files" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.UniversalAccessFromFileUrls">
            <summary>
Controls whether file URLs will have access to all URLs. Also configurable
using the "allow-universal-access-from-files" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.Plugins">
            <summary>
Controls whether any plugins will be loaded. Also configurable using the
"disable-plugins" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.JavascriptDomPaste">
            <summary>
Controls whether DOM pasting is supported in the editor via
execCommand("paste"). The |javascript_access_clipboard| setting must also
be enabled. Also configurable using the "disable-javascript-dom-paste"
command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.JavascriptAccessClipboard">
            <summary>
Controls whether JavaScript can access the clipboard. Also configurable
using the "disable-javascript-access-clipboard" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.JavascriptCloseWindows">
            <summary>
Controls whether JavaScript can be used to close windows that were not
opened via JavaScript. JavaScript can still be used to close windows that
were opened via JavaScript. Also configurable using the
"disable-javascript-close-windows" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.Javascript">
            <summary>
Controls whether JavaScript can be executed.
(Disable javascript)
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.RemoteFonts">
            <summary>
Controls the loading of fonts from remote sources. Also configurable using
the "disable-remote-fonts" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.DefaultEncoding">
            <summary>
Default encoding for Web content. If empty "ISO-8859-1" will be used. Also
configurable using the "default-encoding" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.MinimumLogicalFontSize">
            <summary>
MinimumLogicalFontSize
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.MinimumFontSize">
            <summary>
MinimumFontSize
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.DefaultFixedFontSize">
            <summary>
DefaultFixedFontSize
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.DefaultFontSize">
            <summary>
DefaultFontSize
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.FantasyFontFamily">
            <summary>
FantasyFontFamily
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.CursiveFontFamily">
            <summary>
CursiveFontFamily
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.SansSerifFontFamily">
            <summary>
SansSerifFontFamily
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.SerifFontFamily">
            <summary>
SerifFontFamily
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.FixedFontFamily">
            <summary>
FixedFontFamily
</summary>
        </member>
        <member name="P:CefSharp.BrowserSettings.StandardFontFamily">
            <summary>
StandardFontFamily
</summary>
        </member>
        <member name="M:CefSharp.BrowserSettings.#ctor">
            <summary>
Default Constructor
</summary>
        </member>
        <member name="M:CefSharp.BrowserSettings.#ctor(CefStructBase&lt;CefBrowserSettingsTraits&gt;*)">
            <summary>
Internal Constructor
</summary>
        </member>
        <member name="T:CefSharp.BrowserSettings">
            <summary>
Browser initialization settings. Specify NULL or 0 to get the recommended
default values. The consequences of using custom values may not be well
tested. Many of these and other settings can also configured using command-
line switches.
</summary>
        </member>
        <member name="M:CefSharp.Cef.RegisterWidevineCdmAsync(System.String)">
            <summary>
Register the Widevine CDM plugin.

See <see cref="M:CefSharp.Cef.RegisterWidevineCdm(System.String,CefSharp.IRegisterCdmCallback)" /> for more details.
</summary>
            <param name="path"> is a directory that contains the Widevine CDM files</param>
            <returns>Returns a Task that can be awaited to receive the <see cref="T:CefSharp.CdmRegistration" /> response.</returns>
        </member>
        <member name="M:CefSharp.Cef.RegisterWidevineCdm(System.String,CefSharp.IRegisterCdmCallback)">
            <summary>
Register the Widevine CDM plugin.

The client application is responsible for downloading an appropriate
platform-specific CDM binary distribution from Google, extracting the
contents, and building the required directory structure on the local machine.
The <see cref="!:IBrowserHost.StartDownload" /> method class can be used
to implement this functionality in CefSharp. Contact Google via
https://www.widevine.com/contact.html for details on CDM download.


path is a directory that must contain the following files:
  1. manifest.json file from the CDM binary distribution (see below).
  2. widevinecdm file from the CDM binary distribution (e.g.
     widevinecdm.dll on Windows).
  3. widevidecdmadapter file from the CEF binary distribution (e.g.
     widevinecdmadapter.dll on Windows).

If any of these files are missing or if the manifest file has incorrect
contents the registration will fail and callback will receive an ErrorCode
value of <see cref="!:CdmRegistrationErrorCode.IncorrectContents" />.

The manifest.json file must contain the following keys:
  A. "os": Supported OS (e.g. "mac", "win" or "linux").
  B. "arch": Supported architecture (e.g. "ia32" or "x64").
  C. "x-cdm-module-versions": Module API version (e.g. "4").
  D. "x-cdm-interface-versions": Interface API version (e.g. "8").
  E. "x-cdm-host-versions": Host API version (e.g. "8").
  F. "version": CDM version (e.g. "1.4.8.903").
  G. "x-cdm-codecs": List of supported codecs (e.g. "vp8,vp9.0,avc1").

A through E are used to verify compatibility with the current Chromium
version. If the CDM is not compatible the registration will fail and
callback will receive an ErrorCode value of <see cref="!:CdmRegistrationErrorCode.Incompatible" />.

If registration is not supported at the time that Cef.RegisterWidevineCdm() is called then callback
will receive an ErrorCode value of <see cref="!:CdmRegistrationErrorCode.NotSupported" />.
</summary>
            <param name="path"> is a directory that contains the Widevine CDM files</param>
            <param name="callback">optional callback - <see cref="!:IRegisterCdmCallback.OnRegistrationCompletecallback" /> 
will be executed asynchronously once registration is complete</param>
        </member>
        <member name="M:CefSharp.Cef.SetCrashKeyValue(System.String,System.String)">
            <summary>
Sets or clears a specific key-value pair from the crash metadata.
</summary>
        </member>
        <member name="M:CefSharp.Cef.GetGlobalRequestContext">
            <summary>
Gets the Global Request Context. Make sure to Dispose of this object when finished.
</summary>
            <returns>Returns the global request context or null.</returns>
        </member>
        <member name="M:CefSharp.Cef.CurrentlyOnThread(CefSharp.CefThreadIds)">
            <summary>
Returns true if called on the specified CEF thread.
</summary>
            <returns>Returns true if called on the specified thread.</returns>
        </member>
        <member name="M:CefSharp.Cef.GetGeolocationAsync">
            <summary>
Request a one-time geolocation update.
This function bypasses any user permission checks so should only be
used by code that is allowed to access location information. 
</summary>
            <returns>Returns 'best available' location info or, if the location update failed, with error info.</returns>
        </member>
        <member name="M:CefSharp.Cef.GetGeolocation(CefSharp.IGetGeolocationCallback)">
            <summary>
Request a one-time geolocation update.
This function bypasses any user permission checks so should only be
used by code that is allowed to access location information. 
</summary>
            <returns>Returns 'best available' location info or, if the location update failed, with error info.</returns>
        </member>
        <member name="M:CefSharp.Cef.EnableHighDPISupport">
            <summary>
Call during process startup to enable High-DPI support on Windows 7 or newer.
Older versions of Windows should be left DPI-unaware because they do not
support DirectWrite and GDI fonts are kerned very badly.
</summary>
        </member>
        <member name="M:CefSharp.Cef.UnregisterInternalWebPlugin(System.String)">
            <summary>
Unregister an internal plugin. This may be undone the next time RefreshWebPlugins() is called. 
</summary>
            <param name="path">Path (directory + file).</param>
        </member>
        <member name="M:CefSharp.Cef.RefreshWebPlugins">
            <summary>
Cause the plugin list to refresh the next time it is accessed regardless of whether it has already been loaded.
</summary>
        </member>
        <member name="M:CefSharp.Cef.GetPlugins">
            <summary>
Async returns a list containing Plugin Information
(Wrapper around CefVisitWebPluginInfo)
</summary>
            <returns>Returns List of <see cref="!:Plugin" /> structs.</returns>
        </member>
        <member name="M:CefSharp.Cef.VisitWebPluginInfo(CefSharp.IWebPluginInfoVisitor)">
            <summary>
Visit web plugin information. Can be called on any thread in the browser process.
</summary>
        </member>
        <member name="M:CefSharp.Cef.ClearSchemeHandlerFactories">
            <summary>
Clear all registered scheme handler factories.
</summary>
            <returns>Returns false on error.</returns>
        </member>
        <member name="M:CefSharp.Cef.ShutdownWithoutChecks">
            <summary>
This method should only be used by advanced users, if your unsure then use Cef.Shutdown().
This function should be called on the main application thread to shut down
the CEF browser process before the application exits. This method simply obtains a lock
and calls the native CefShutdown method, only IsInitialized is checked. All ChromiumWebBrowser
instances MUST be Disposed of before calling this method. If calling this method results in a crash
or hangs then you're likely hanging on to some unmanaged resources or haven't closed all of your browser
instances
</summary>
        </member>
        <member name="M:CefSharp.Cef.Shutdown">
            <summary>
Shuts down CefSharp and the underlying CEF infrastructure. This method is safe to call multiple times; it will only
shut down CEF on the first call (all subsequent calls will be ignored).
This method should be called on the main application thread to shut down the CEF browser process before the application exits. 
If you are Using CefSharp.OffScreen then you must call this explicitly before your application exits or it will hang.
This method must be called on the same thread as Initialize. If you don't call Shutdown explicitly then CefSharp.Wpf and CefSharp.WinForms
versions will do their best to call Shutdown for you, if your application is having trouble closing then call thus explicitly.
</summary>
        </member>
        <member name="M:CefSharp.Cef.GetGlobalCookieManager">
            <summary>
Returns the global cookie manager.
</summary>
        </member>
        <member name="M:CefSharp.Cef.ClearCrossOriginWhitelist">
            <summary>Remove all entries from the cross-origin access whitelist.</summary>
            <remarks>
Remove all entries from the cross-origin access whitelist. Returns false if
the whitelist cannot be accessed.
</remarks>
        </member>
        <member name="M:CefSharp.Cef.RemoveCrossOriginWhitelistEntry(System.String,System.String,System.String,System.Boolean)">
            <summary>Remove entry from cross-origin whitelist</summary>
            <param name="sourceOrigin">The origin allowed to be accessed by the target protocol/domain.</param>
            <param name="targetProtocol">The target protocol allowed to access the source origin.</param>
            <param name="targetDomain">The optional target domain allowed to access the source origin.</param>
            <param name="allowTargetSubdomains">If set to true would allow a blah.example.com if the 
    <paramref name="targetDomain" /> was set to example.com
</param>
            <remarks>
Remove an entry from the cross-origin access whitelist. Returns false if
<paramref name="sourceOrigin" /> is invalid or the whitelist cannot be accessed.
</remarks>
        </member>
        <member name="M:CefSharp.Cef.AddCrossOriginWhitelistEntry(System.String,System.String,System.String,System.Boolean)">
            <summary>Add an entry to the cross-origin whitelist.</summary>
            <param name="sourceOrigin">The origin allowed to be accessed by the target protocol/domain.</param>
            <param name="targetProtocol">The target protocol allowed to access the source origin.</param>
            <param name="targetDomain">The optional target domain allowed to access the source origin.</param>
            <param name="allowTargetSubdomains">If set to true would allow a blah.example.com if the 
    <paramref name="targetDomain" /> was set to example.com
</param>
            <remarks>
The same-origin policy restricts how scripts hosted from different origins
(scheme + domain + port) can communicate. By default, scripts can only access
resources with the same origin. Scripts hosted on the HTTP and HTTPS schemes
(but no other schemes) can use the "Access-Control-Allow-Origin" header to
allow cross-origin requests. For example, https://source.example.com can make
XMLHttpRequest requests on http://target.example.com if the
http://target.example.com request returns an "Access-Control-Allow-Origin:
https://source.example.com" response header.
Scripts in separate frames or iframes and hosted from the same protocol and
domain suffix can execute cross-origin JavaScript if both pages set the
document.domain value to the same domain suffix. For example,
scheme://foo.example.com and scheme://bar.example.com can communicate using
JavaScript if both domains set document.domain="example.com".
This method is used to allow access to origins that would otherwise violate
the same-origin policy. Scripts hosted underneath the fully qualified
<paramref name="sourceOrigin" /> URL (like http://www.example.com) will be allowed access to
all resources hosted on the specified <paramref name="targetProtocol" /> and <paramref name="targetDomain" />.
If <paramref name="targetDomain" /> is non-empty and <paramref name="allowTargetSubdomains" /> if false only
exact domain matches will be allowed. If <paramref name="targetDomain" /> contains a top-
level domain component (like "example.com") and <paramref name="allowTargetSubdomains" /> is
true sub-domain matches will be allowed. If <paramref name="targetDomain" /> is empty and
<paramref name="allowTargetSubdomains" /> if true all domains and IP addresses will be
allowed.
This method cannot be used to bypass the restrictions on local or display
isolated schemes. See the comments on <see cref="T:CefSharp.CefCustomScheme" /> for more
information.

This function may be called on any thread. Returns false if <paramref name="sourceOrigin" />
is invalid or the whitelist cannot be accessed.
</remarks>
        </member>
        <member name="M:CefSharp.Cef.ExecuteProcess">
            <summary>
This function should be called from the application entry point function to execute a secondary process.
It can be used to run secondary processes from the browser client executable (default behavior) or
from a separate executable specified by the CefSettings.browser_subprocess_path value.
If called for the browser process (identified by no "type" command-line value) it will return immediately with a value of -1.
If called for a recognized secondary process it will block until the process should exit and then return the process exit code.
The |application| parameter may be empty. The |windows_sandbox_info| parameter is only used on Windows and may be NULL (see cef_sandbox_win.h for details). 
</summary>
        </member>
        <member name="M:CefSharp.Cef.DoMessageLoopWork">
            <summary>
Perform a single iteration of CEF message loop processing.This function is
provided for cases where the CEF message loop must be integrated into an
existing application message loop. Use of this function is not recommended
for most users; use CefSettings.MultiThreadedMessageLoop if possible (the deault).
When using this function care must be taken to balance performance
against excessive CPU usage. It is recommended to enable the
CefSettings.ExternalMessagePump option when using
this function so that IBrowserProcessHandler.OnScheduleMessagePumpWork()
callbacks can facilitate the scheduling process. This function should only be
called on the main application thread and only if Cef.Initialize() is called
with a CefSettings.MultiThreadedMessageLoop value of false. This function
will not block.
</summary>
        </member>
        <member name="M:CefSharp.Cef.QuitMessageLoop">
            <summary>
Quit the CEF message loop that was started by calling Cef.RunMessageLoop().
This function should only be called on the main application thread and only
if Cef.RunMessageLoop() was used.
</summary>
        </member>
        <member name="M:CefSharp.Cef.RunMessageLoop">
            <summary>
Run the CEF message loop. Use this function instead of an application-
provided message loop to get the best balance between performance and CPU
usage. This function should only be called on the main application thread and
only if Cef.Initialize() is called with a
CefSettings.MultiThreadedMessageLoop value of false. This function will
block until a quit message is received by the system.
</summary>
        </member>
        <member name="M:CefSharp.Cef.Initialize(CefSharp.CefSettings,System.Boolean,CefSharp.IBrowserProcessHandler)">
            <summary>
Initializes CefSharp with user-provided settings.
It's important to note that Initialize/Shutdown <strong>MUST</strong> be called on your main
applicaiton thread (Typically the UI thead). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <param name="cefSettings">CefSharp configuration settings.</param>
            <param name="performDependencyCheck">Check that all relevant dependencies avaliable, throws exception if any are missing</param>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="M:CefSharp.Cef.Initialize(CefSharp.CefSettings)">
            <summary>
Initializes CefSharp with user-provided settings.
It's important to note that Initialize and Shutdown <strong>MUST</strong> be called on your main
applicaiton thread (Typically the UI thead). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <param name="cefSettings">CefSharp configuration settings.</param>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="M:CefSharp.Cef.Initialize">
            <summary>
Initializes CefSharp with the default settings. 
This function can only be called once, subsiquent calls will result in an Exception.
It's important to note that Initialize and Shutdown <strong>MUST</strong> be called on your main
applicaiton thread (Typically the UI thead). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="P:CefSharp.Cef.CefCommitHash">
            <summary>
Gets a value that indicates the Git Hash for CEF version currently being used.
</summary>
            <value>The Git Commit Hash</value>
        </member>
        <member name="P:CefSharp.Cef.ChromiumVersion">
            <summary>Gets a value that indicates the Chromium version currently being used.</summary>
            <value>The Chromium version.</value>
        </member>
        <member name="P:CefSharp.Cef.CefVersion">
            <summary>Gets a value that indicates the CEF version currently being used.</summary>
            <value>The CEF Version</value>
        </member>
        <member name="P:CefSharp.Cef.CefSharpVersion">
            <summary>Gets a value that indicates the version of CefSharp currently being used.</summary>
            <value>The CefSharp version.</value>
        </member>
        <member name="P:CefSharp.Cef.IsInitialized">
            <summary>Gets a value that indicates whether CefSharp is initialized.</summary>
            <value>true if CefSharp is initialized; otherwise, false.</value>
        </member>
        <member name="M:CefSharp.RequestContext.ResolveHostCached(System.Uri,System.Collections.Generic.IList`1{System.String}@)">
            <summary>
Attempts to resolve origin to a list of associated IP addresses using
cached data. This method must be called on the CEF IO thread. Use
Cef.IOThreadTaskFactory to execute on that thread.
</summary>
            <param name="origin">host name to resolve</param>
            <param name="resolvedIpAddresses">list of resolved IP
addresses or empty list if no cached data is available.</param>
            <returns> Returns <see cref="!:CefErrorCode.None" /> on success</returns>
        </member>
        <member name="M:CefSharp.RequestContext.ResolveHostAsync(System.Uri)">
            <summary>
Attempts to resolve origin to a list of associated IP addresses.
</summary>
            <param name="origin">host name to resolve</param>
            <returns>A task that represents the Resoolve Host operation. The value of the TResult parameter contains ResolveCallbackResult.</returns>
        </member>
        <member name="M:CefSharp.RequestContext.CloseAllConnections(CefSharp.ICompletionCallback)">
            <summary>
Clears all active and idle connections that Chromium currently has.
This is only recommended if you have released all other CEF objects but
don't yet want to call Cef.Shutdown().
</summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.RequestContext.ClearCertificateExceptions(CefSharp.ICompletionCallback)">
            <summary>
Clears all certificate exceptions that were added as part of handling
<see cref="!:IRequestHandler.OnCertificateError" />. If you call this it is
recommended that you also call <see cref="!:IRequestContext.CloseAllConnections" /> or you risk not
being prompted again for server certificates if you reconnect quickly.
</summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.RequestContext.SetPreference(System.String,System.Object,System.String@)">
            <summary>
Set the value associated with preference name. If value is null the
preference will be restored to its default value. If setting the preference
fails then error will be populated with a detailed description of the
problem. This method must be called on the CEF UI thread.
Preferences set via the command-line usually cannot be modified.
</summary>
            <param name="name">preference key</param>
            <param name="value">preference value</param>
            <param name="error">out error</param>
            <returns>Returns true if the value is set successfully and false otherwise.</returns>
/// <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks></member>
        <member name="M:CefSharp.RequestContext.CanSetPreference(System.String)">
            <summary>
Returns true if the preference with the specified name can be modified
using SetPreference. As one example preferences set via the command-line
usually cannot be modified. This method must be called on the CEF UI thread.
</summary>
            <param name="name">preference key</param>
            <returns>Returns true if the preference with the specified name can be modified
using SetPreference</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.RequestContext.GetAllPreferences(System.Boolean)">
            <summary>
Returns all preferences as a dictionary. The returned
object contains a copy of the underlying preference values and
modifications to the returned object will not modify the underlying
preference values. This method must be called on the browser process UI
thread.
</summary>
            <param name="includeDefaults">If true then
preferences currently at their default value will be included.</param>
            <returns>Preferences (dictionary can have sub dictionaries)</returns>
        </member>
        <member name="M:CefSharp.RequestContext.GetPreference(System.String)">
            <summary>
Returns the value for the preference with the specified name. Returns
NULL if the preference does not exist. The returned object contains a copy
of the underlying preference value and modifications to the returned object
will not modify the underlying preference value. This method must be called
on the CEF UI thread.
</summary>
            <param name="name">preference name</param>
            <returns>Returns the value for the preference with the specified name</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.RequestContext.HasPreference(System.String)">
            <summary>
Returns true if a preference with the specified name exists. This method
must be called on the CEF UI thread.
</summary>
            <param name="name">name of preference</param>
            <returns>bool if the preference exists</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
Cef.OnContextInitialized and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.RequestContext.PurgePluginListCache(System.Boolean)">
            <summary>
Tells all renderer processes associated with this context to throw away
their plugin list cache. If reloadPages is true they will also reload
all pages with plugins. RequestContextHandler.OnBeforePluginLoad may
be called to rebuild the plugin list cache.
</summary>
            <param name="reloadPages">reload any pages with pluginst</param>
        </member>
        <member name="P:CefSharp.RequestContext.CachePath">
            <summary>
Returns the cache path for this object. If empty an "incognito mode"
in-memory cache is being used.
</summary>
        </member>
        <member name="M:CefSharp.RequestContext.ClearSchemeHandlerFactories">
            <summary>
Clear all registered scheme handler factories. 
</summary>
            <returns>Returns false on error.</returns>
        </member>
        <member name="M:CefSharp.RequestContext.RegisterSchemeHandlerFactory(System.String,System.String,CefSharp.ISchemeHandlerFactory)">
            <summary>
Register a scheme handler factory for the specified schemeName and optional domainName.
An empty domainName value for a standard scheme will cause the factory to match all domain
names. The domainName value will be ignored for non-standard schemes. If schemeName is
a built-in scheme and no handler is returned by factory then the built-in scheme handler
factory will be called. If schemeName is a custom scheme then you must also implement the
CefApp::OnRegisterCustomSchemes() method in all processes. This function may be called multiple
times to change or remove the factory that matches the specified schemeName and optional
domainName.
</summary>
            <param name="schemeName">Scheme Name</param>
            <param name="domainName">Optional domain name</param>
            <param name="factory">Scheme handler factory</param>
            <returns>Returns false if an error occurs.</returns>
        </member>
        <member name="P:CefSharp.RequestContext.IsGlobal">
            <summary>
Returns true if this object is the global context. The global context is
used by default when creating a browser or URL request with a NULL context
argument.
</summary>
        </member>
        <member name="M:CefSharp.RequestContext.GetDefaultCookieManager(CefSharp.ICompletionCallback)">
            <summary>
Returns the default cookie manager for this object. This will be the global
cookie manager if this object is the global request context. Otherwise,
this will be the default cookie manager used when this request context does
not receive a value via IRequestContextHandler.GetCookieManager(). 
</summary>
            <param name="callback">If callback is non-NULL it will be executed asnychronously on the CEF IO thread
after the manager's storage has been initialized.</param>
            <returns>Returns the default cookie manager for this object</returns>
        </member>
        <member name="M:CefSharp.RequestContext.IsSharingWith(CefSharp.IRequestContext)">
            <summary>
Returns true if this object is sharing the same storage as the specified context.
</summary>
            <param name="context">context to compare</param>
            <returns>Returns true if same storage</returns>
        </member>
        <member name="M:CefSharp.RequestContext.IsSame(CefSharp.IRequestContext)">
            <summary>
Returns true if this object is pointing to the same context object.
</summary>
            <param name="context">context to compare</param>
            <returns>Returns true if the same</returns>
        </member>
        <member name="M:CefSharp.RequestContext.CreateContext(CefSharp.IRequestContext,CefSharp.IRequestContextHandler)">
            <summary>
Creates a new context object that shares storage with other and uses an
optional handler.
</summary>
            <param name="other">shares storage with this RequestContext</param>
            <param name="requestContextHandler">optional requestContext handler</param>
            <returns>Returns a nre RequestContext</returns>
        </member>
        <member name="T:CefSharp.RequestContext">
            <summary>
A request context provides request handling for a set of related browser objects.
A request context is specified when creating a new browser object via the CefBrowserHost
static factory methods. Browser objects with different request contexts will never be
hosted in the same render process. Browser objects with the same request context may or
may not be hosted in the same render process depending on the process model.
Browser objects created indirectly via the JavaScript window.open function or targeted
links will share the same render process and the same request context as the source browser.
When running in single-process mode there is only a single render process (the main process)
and so all browsers created in single-process mode will share the same request context.
This will be the first request context passed into a CefBrowserHost static factory method
and all other request context objects will be ignored. 
</summary>
        </member>
        <member name="P:CefSharp.RequestContextSettings.IgnoreCertificateErrors">
            <summary>
Set to true to ignore errors related to invalid SSL certificates.
Enabling this setting can lead to potential security vulnerabilities like
"man in the middle" attacks. Applications that load content from the
internet should not enable this setting. Can be set globally using the
CefSettings.IgnoreCertificateErrors value. This value will be ignored if
CachePath matches the CefSettings.cache_path value.
</summary>
        </member>
        <member name="P:CefSharp.RequestContextSettings.AcceptLanguageList">
            <summary>
Comma delimited ordered list of language codes without any whitespace that
will be used in the "Accept-Language" HTTP header. Can be set globally
using the CefSettings.accept_language_list value or overridden on a per-
browser basis using the BrowserSettings.AcceptLanguageList value. If
all values are empty then "en-US,en" will be used. This value will be
ignored if CachePath matches the CefSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.RequestContextSettings.CachePath">
            <summary>
The location where cache data will be stored on disk. If empty then
browsers will be created in "incognito mode" where in-memory caches are
used for storage and no data is persisted to disk. HTML5 databases such as
localStorage will only persist across sessions if a cache path is
specified. To share the global browser cache and related configuration set
this value to match the CefSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.RequestContextSettings.PersistUserPreferences">
            <summary>
To persist user preferences as a JSON file in the cache path directory set
this value to true. Can be set globally using the
CefSettings.PersistUserPreferences value. This value will be ignored if
CachePath is empty or if it matches the CefSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.RequestContextSettings.PersistSessionCookies">
            <summary>
To persist session cookies (cookies without an expiry date or validity
interval) by default when using the global cookie manager set this value to
true. Session cookies are generally intended to be transient and most
Web browsers do not persist them. Can be set globally using the
CefSettings.PersistSessionCookies value. This value will be ignored if
CachePath is empty or if it matches the CefSettings.CachePath value.
</summary>
        </member>
        <member name="M:CefSharp.RequestContextSettings.#ctor">
            <summary>
Default constructor
</summary>
        </member>
        <member name="T:CefSharp.RequestContextSettings">
            <summary>
RequestContextSettings
</summary>
        </member>
        <member name="M:CefSharp.Internals.CefRegisterCdmCallbackAdapter.OnCdmRegistrationComplete(cef_cdm_registration_error_t,CefStringBase&lt;CefStringTraitsUTF16&gt;!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Method that will be called when CDM registration is complete. |result|
will be CEF_CDM_REGISTRATION_ERROR_NONE if registration completed
successfully. Otherwise, |result| and |error_message| will contain
additional information about why registration failed.
</summary>
        </member>
        <member name="M:CefSharp.CefSettings.SetOffScreenRenderingBestPerformanceArgs">
            <summary>
Set command line arguments for best OSR (Offscreen and WPF) Rendering performance
This will disable WebGL, look at the source to determine which flags best suite
your requirements.
</summary>
        </member>
        <member name="M:CefSharp.CefSettings.DisableGpuAcceleration">
            <summary>
Set command line argument to disable GPU Acceleration, this will disable WebGL.
</summary>
        </member>
        <member name="M:CefSharp.CefSettings.RegisterExtension(CefSharp.CefExtension)">
            <summary>
Registers an extension with the provided settings.
</summary>
            <param name="extension">The CefExtension that contains the extension code.</param>
        </member>
        <member name="M:CefSharp.CefSettings.RegisterScheme(CefSharp.CefCustomScheme)">
            <summary>
Registers a custom scheme using the provided settings.
</summary>
            <param name="cefCustomScheme">The CefCustomScheme which provides the details about the scheme.</param>
        </member>
        <member name="P:CefSharp.CefSettings.FocusedNodeChangedEnabled">
            <summary>
If true a message will be sent from the render subprocess to the
browser when a DOM node (or no node) gets focus. The default is
false.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.AcceptLanguageList">
            <summary>
Comma delimited ordered list of language codes without any whitespace that
will be used in the "Accept-Language" HTTP header. May be set globally
using the CefSettings.AcceptLanguageList value. If both values are
empty then "en-US,en" will be used.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.PersistUserPreferences">
            <summary>
To persist user preferences as a JSON file in the cache path directory set
this value to true. A CachePath value must also be specified
to enable this feature. Also configurable using the
"persist-user-preferences" command-line switch. Can be overridden for
individual RequestContext instances via the
RequestContextSettings.PersistUserPreferences value.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.PersistSessionCookies">
            <summary>
To persist session cookies (cookies without an expiry date or validity
interval) by default when using the global cookie manager set this value to
true. Session cookies are generally intended to be transient and most
Web browsers do not persist them. A CachePath value must also be
specified to enable this feature. Also configurable using the
"persist-session-cookies" command-line switch. Can be overridden for
individual RequestContext instances via the
RequestContextSettings.PersistSessionCookies value.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.WindowlessRenderingEnabled">
            <summary>
Set to true (1) to enable windowless (off-screen) rendering support. Do not
enable this value if the application does not use windowless rendering as
it may reduce rendering performance on some systems.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.UserAgent">
            <summary>
Value that will be returned as the User-Agent HTTP header. If empty the
default User-Agent string will be used. Also configurable using the
"user-agent" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.UncaughtExceptionStackSize">
            <summary>
The number of stack trace frames to capture for uncaught exceptions.
Specify a positive value to enable the CefRenderProcessHandler::
OnUncaughtException() callback. Specify 0 (default value) and
OnUncaughtException() will not be called. Also configurable using the
"uncaught-exception-stack-size" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.RemoteDebuggingPort">
            <summary>
Set to a value between 1024 and 65535 to enable remote debugging on the
specified port. For example, if 8080 is specified the remote debugging URL
will be http://localhost:8080. CEF can be remotely debugged from any CEF or
Chrome browser window. Also configurable using the "remote-debugging-port"
command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.ProductVersion">
            <summary>
Value that will be inserted as the product portion of the default
User-Agent string. If empty the Chromium product version will be used. If
|userAgent| is specified this value will be ignored. Also configurable
using the "product-version" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.PackLoadingDisabled">
            <summary>
Set to true to disable loading of pack files for resources and locales.
A resource bundle handler must be provided for the browser and render
processes via CefApp::GetResourceBundleHandler() if loading of pack files
is disabled. Also configurable using the "disable-pack-loading" command-
line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.JavascriptFlags">
            <summary>
Custom flags that will be used when initializing the V8 JavaScript engine.
The consequences of using custom flags may not be well tested. Also
configurable using the "js-flags" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.LogSeverity">
            <summary>
The log severity. Only messages of this severity level or higher will be
logged. Also configurable using the "log-severity" command-line switch with
a value of "verbose", "info", "warning", "error", "error-report" or
"disable".
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.LogFile">
            <summary>
The directory and file name to use for the debug log. If empty a default
log file name and location will be used. On Windows and Linux a "debug.log"
file will be written in the main executable directory.
Also configurable using the"log-file" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.ResourcesDirPath">
            <summary>
The fully qualified path for the resources directory. If this value is
empty the cef.pak and/or devtools_resources.pak files must be located in
the module directory. Also configurable using the "resources-dir-path" command-line
switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.LocalesDirPath">
            <summary>
The fully qualified path for the locales directory. If this value is empty
the locales directory must be located in the module directory.
Also configurable using the "locales-dir-path" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.Locale">
            <summary>
The locale string that will be passed to WebKit. If empty the default
locale of "en-US" will be used. Also configurable using the "lang"
command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.IgnoreCertificateErrors">
            <summary>
Set to true in order to completely ignore SSL certificate errors.
This is NOT recommended.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.UserDataPath">
            <summary>
The location where user data such as spell checking dictionary files will
be stored on disk. If empty then the default platform-specific user data
directory will be used ("~/.cef_user_data" directory on Linux,
"~/Library/Application Support/CEF/User Data" directory on Mac OS X,
"Local Settings\Application Data\CEF\User Data" directory under the user
profile directory on Windows).
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.CachePath">
            <summary>
The location where cache data will be stored on disk. If empty then
browsers will be created in "incognito mode" where in-memory caches are
used for storage and no data is persisted to disk. HTML5 databases such as
localStorage will only persist across sessions if a cache path is
specified. Can be overridden for individual CefRequestContext instances via
the RequestContextSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.BrowserSubprocessPath">
            <summary>
The path to a separate executable that will be launched for sub-processes.
By default the browser process executable is used. See the comments on
Cef.ExecuteProcess() for details. Also configurable using the
"browser-subprocess-path" command-line switch. Default is CefSharp.BrowserSubprocess.exe
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.MultiThreadedMessageLoop">
            <summary>
thread. If false than the CefDoMessageLoopWork() function must be
called from your application message loop. This option is only supported on
Windows. The default value is true
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.ExternalMessagePump">
            <summary>
Set to true to control browser process main (UI) thread message pump
scheduling via the IBrowserProcessHandler.OnScheduleMessagePumpWork
callback. This option is recommended for use in combination with the
Cef.DoMessageLoopWork() function in cases where the CEF message loop must be
integrated into an existing application message loop (see additional
comments and warnings on Cef.DoMessageLoopWork). Enabling this option is not
recommended for most users; leave this option disabled and use either
MultiThreadedMessageLoop (the default) if possible.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.CommandLineArgsDisabled">
            <summary>
Set to true to disable configuration of browser process features using
standard CEF and Chromium command-line arguments. Configuration can still
be specified using CEF data structures or by adding to CefCommandLineArgs
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.CefCommandLineArgs">
            <summary>
Add custom command line argumens to this collection, they will be
added in OnBeforeCommandLineProcessing.
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.Extensions">
            <summary>
Add CefExtensions to be registered
</summary>
        </member>
        <member name="P:CefSharp.CefSettings.CefCustomSchemes">
            <summary>
Add Customs schemes to this collection
</summary>
        </member>
        <member name="M:CefSharp.CefSettings.#ctor">
            <summary>
Default Constructor
</summary>
        </member>
        <member name="T:CefSharp.CefSettings">
            <summary>
Initialization settings. Many of these and other settings can also configured
using command-line switches.
</summary>
        </member>
        <member name="M:CefSharp.Internals.CefFrameWrapper.LoadRequest(CefSharp.IRequest)">

Load the request represented by the |request| object.

</member>
        <!-- Discarding badly formed XML document comment for member 'P:CefSharp.Cef.CrashReportingEnabled'. -->
        <!-- Discarding badly formed XML document comment for member 'P:CefSharp.Cef.CrashReportingEnabled'. -->
    </members>
</doc>