﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImFaultType2010
    {
        public ImFaultType2010()
        {
            ImFaultActType2010s = new HashSet<ImFaultActType2010>();
        }

        public string Id { get; set; } = null!;
        public string DeviceCtgy { get; set; } = null!;
        public int FaultCode { get; set; }
        public string FaultName { get; set; } = null!;

        public virtual ImDevCtgy DeviceCtgyNavigation { get; set; } = null!;
        public virtual ICollection<ImFaultActType2010> ImFaultActType2010s { get; set; }
    }
}
