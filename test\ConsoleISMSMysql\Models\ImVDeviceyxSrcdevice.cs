﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImVDeviceyxSrcdevice
    {
        public string Id { get; set; }
        public string Yxdataid { get; set; }
        public string Srcdevid { get; set; }
        public string Devnameofyx { get; set; }
        public string Dataname { get; set; }
        public string Srcdevname { get; set; }
    }
}
