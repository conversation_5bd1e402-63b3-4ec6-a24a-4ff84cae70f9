﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceYm
    {
        public string Id { get; set; } = null!;
        public string Ymname { get; set; } = null!;
        public string? Unit { get; set; }
        public double? Cof { get; set; }
        public double? MaxValue { get; set; }

        public virtual ImDeviceDatum IdNavigation { get; set; } = null!;
    }
}
