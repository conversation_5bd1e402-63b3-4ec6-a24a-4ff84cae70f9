﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class Subscription
    {
        public Subscription()
        {
            ActiveSubscriptions = new HashSet<ActiveSubscription>();
            Notifications = new HashSet<Notification>();
        }

        public Guid SubscriptionId { get; set; }
        public Guid OwnerId { get; set; }
        public Guid ReportOid { get; set; }
        public string Locale { get; set; } = null!;
        public int InactiveFlags { get; set; }
        public string? ExtensionSettings { get; set; }
        public Guid ModifiedById { get; set; }
        public DateTime ModifiedDate { get; set; }
        public string? Description { get; set; }
        public string? LastStatus { get; set; }
        public string EventType { get; set; } = null!;
        public string? MatchData { get; set; }
        public DateTime? LastRunTime { get; set; }
        public string? Parameters { get; set; }
        public string? DataSettings { get; set; }
        public string? DeliveryExtension { get; set; }
        public int Version { get; set; }
        public int ReportZone { get; set; }

        public virtual User ModifiedBy { get; set; } = null!;
        public virtual User Owner { get; set; } = null!;
        public virtual Catalog ReportO { get; set; } = null!;
        public virtual ICollection<ActiveSubscription> ActiveSubscriptions { get; set; }
        public virtual ICollection<Notification> Notifications { get; set; }
    }
}
