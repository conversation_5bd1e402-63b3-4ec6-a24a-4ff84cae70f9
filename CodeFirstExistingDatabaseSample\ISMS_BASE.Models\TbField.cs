﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbField
    {
        public string FieldId { get; set; } = null!;
        public string TableId { get; set; } = null!;
        public string FieldName { get; set; } = null!;
        public string Prcname { get; set; } = null!;
        public int SeqNo { get; set; }
        public string FieldType { get; set; } = null!;
        public int FieldSize { get; set; }
        public int Required { get; set; }
        public int IsPrimary { get; set; }
        public int IsCond { get; set; }
        public int IsRef { get; set; }
        public string? RefTable { get; set; }
        public string? RefTableAlias { get; set; }
        public string? RefCodeField { get; set; }
        public string? RefNameField { get; set; }
        public int DispWidth { get; set; }
        public string? CheckValue { get; set; }
        public string? DefaultValue { get; set; }
        public string? Hint { get; set; }
        public int CanStat { get; set; }
        public int? ReadOnly { get; set; }
        public int ShowOnAdd { get; set; }
        public int ShowOnEdit { get; set; }
        public int ShowOnBrw { get; set; }
        public int ShowOnQry { get; set; }
        public string? ColorMode { get; set; }
        public string? FormatStr { get; set; }
        public string? GroupName { get; set; }
        public int IsName { get; set; }

        public virtual TbTable Table { get; set; } = null!;
    }
}
