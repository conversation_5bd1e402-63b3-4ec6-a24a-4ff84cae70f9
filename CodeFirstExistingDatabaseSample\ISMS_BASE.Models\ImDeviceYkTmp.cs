﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceYkTmp
    {
        public string Id { get; set; } = null!;
        public string Ykname { get; set; } = null!;
        public string? RelatedYxId { get; set; }
        public string YkType { get; set; } = null!;
        public string? SwOnStr { get; set; }
        public string? SwOffStr { get; set; }
        public string? SwUncertStr { get; set; }
        public string? PreYxId { get; set; }
        public int? PreState4Yk { get; set; }
        public string LockMode { get; set; } = null!;
        public string IsResetCmd { get; set; } = null!;
        public string? PreYxIdOff { get; set; }
        public int? PreState4YkOff { get; set; }
        public string LockModeOff { get; set; } = null!;

        public virtual ImDeviceDataTmp IdNavigation { get; set; } = null!;
    }
}
