﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImAsdu140Fltcurrinfo
    {
        public string Companyname { get; set; }
        public int Currindcode { get; set; }
        public string Currname { get; set; }
        public string Unit { get; set; }
        public int Precise { get; set; }
        public double Coeff { get; set; }

        public virtual ImManufacturer CompanynameNavigation { get; set; }
    }
}
