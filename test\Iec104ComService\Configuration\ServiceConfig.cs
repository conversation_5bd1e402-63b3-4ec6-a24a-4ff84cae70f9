﻿using System;

namespace Configuration
{
    /// <summary>
    /// 服务配置
    /// </summary>
    public class ServiceConfig
    {
        /// <summary>
        /// 通信装置配置
        /// </summary>
        public virtual CommDeviceConfig CommDeviceConfig { get; set; } = new CommDeviceConfig();

        /// <summary>
        /// Web服务配置
        /// </summary>
        public virtual WebSeriviceConfig WebSeriviceConfig { get; set; } = new WebSeriviceConfig();

        /// <summary>
        /// 默认配置
        /// </summary>
        public virtual DefaultConfig DefaultConfig { get; set; } = new DefaultConfig();
    }

    /// <summary>
    /// 通信装置配置
    /// </summary>
    public class CommDeviceConfig
    {
        /// <summary>
        /// 装置IP
        /// </summary>
        public virtual string IP { get; set; }

        /// <summary>
        /// 装置端口号
        /// </summary>
        public virtual int Port { get; set; }

        /// <summary>
        /// 装置地址
        /// </summary>
        public virtual int Address { get; set; }

        /// <summary>
        /// 遥测起始地址
        /// </summary>
        public virtual int TelemeteringStartAddress { get; set; }

        /// <summary>
        /// 遥测数量
        /// </summary>
        public virtual int TelemeteringCount { get; set; }

        /// <summary>
        /// 遥信起始地址
        /// </summary>
        public virtual int TelesignalisationStartAddress { get; set; }

        /// <summary>
        /// 遥信数量
        /// </summary>
        public virtual int TelesignalisationCount { get; set; }

        /// <summary>
        /// 电度起始地址
        /// </summary>
        public virtual int ElectricalDegreeStartAddress { get; set; }

        /// <summary>
        /// 电度数量
        /// </summary>
        public virtual int ElectricalDegreeCount { get; set; }
    }

    /// <summary>
    /// Web服务配置
    /// </summary>
    public class WebSeriviceConfig
    {
        /// <summary>
        /// Web服务IP
        /// </summary>
        public virtual string IP { get; set; }

        /// <summary>
        /// Web服务端口
        /// </summary>
        public virtual int Port { get; set; }
    }

    /// <summary>
    /// 默认配置
    /// </summary>
    public class DefaultConfig
    {
        /// <summary>
        /// 变电所Id
        /// </summary>
        public virtual Guid? SubstationId { get; set; }

        /// <summary>
        /// 变电所名称
        /// </summary>
        public virtual string SubstationName { get; set; }

        /// <summary>
        /// WebSocketIP
        /// </summary>
        public virtual string WebSocketIP { get; set; }

        /// <summary>
        /// WebSocketPort
        /// </summary>
        public virtual int WebSocketPort { get; set; }
        /// <summary>
        /// WebSocketPath
        /// </summary>
        //public virtual string WebSocketPath { get; set; }
        /// <summary>
        /// 数据采集及保存间隔
        /// </summary>
        public virtual double DataHandleInterval { get; set; }
        /// <summary>
        /// 自启动
        /// </summary>
        public virtual bool IsAutoStartup { get; set; }
    }

}