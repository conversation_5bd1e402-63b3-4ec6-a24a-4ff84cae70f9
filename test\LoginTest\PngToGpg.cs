﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

namespace LoginTest
{

    public static class PngToGpg
    {
        public static void Run()
        {
            // 指定PNG文件路径和目标JPEG文件路径
            string pngFilePath = "test.png";
            string jpegFilePath = "output.jpg";

            // 转换PNG到JPEG
            ConvertPngToJpeg(pngFilePath, jpegFilePath);
            Console.WriteLine($"PNG图像已转换为JPEG并保存到 {jpegFilePath}");
        }

        static void ConvertPngToJpeg(string inputFilePath, string outputFilePath)
        {
            using (Image image = Image.FromFile(inputFilePath))
            {
                // 创建JPEG编码器的参数，设置图像质量
                EncoderParameters encoderParameters = new EncoderParameters(1);
                encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, 20); // 调整JPEG质量，0-100之间

                // 获取JPEG编码器
                ImageCodecInfo jpegCodec = GetEncoderInfo(ImageFormat.Jpeg);

                // 保存图像为JPEG格式
                image.Save(outputFilePath, jpegCodec, encoderParameters);
            }
        }

        static ImageCodecInfo GetEncoderInfo(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }
    }
}
