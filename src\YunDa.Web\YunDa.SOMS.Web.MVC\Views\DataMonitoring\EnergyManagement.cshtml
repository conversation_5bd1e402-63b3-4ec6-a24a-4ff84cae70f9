@{
    ViewData["Title"] = "能耗管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@section styles{
    <link type="text/css" href="~/css/plugins/chosen/chosen.min.css" rel="stylesheet">
    <link type="text/css" href="~/css/plugins/bootstrap-clockpicker/bootstrap-clockpicker.min.css" rel="stylesheet">
    <link type="text/css" href="~/css/plugins/bootstrap-datepicker/bootstrap-datepicker3.min.css" rel="stylesheet">
    <link type="text/css" href="~/css/plugins/webuploader/webuploader.css" rel="stylesheet">

    <style>
        .selectLable {
            float: left;
            position: relative;
            margin-top: 5px;
        }

        .searchDiv {
            margin-bottom: 5px;
            margin-top: 5px;
        }
    </style>
}

<div id="energyManagementHtml" class="animated fadeIn full-height" style="margin:0 0 0 -20px; padding-left: 0px; padding-right: 0px;">
    <div class="col-sm-2 border-right full-height" style="padding-right: 0px; padding-left: 0px;margin-right: 0px;">
        @await Html.PartialAsync("~/Views/GeneralInformation/_TranSubstationTree.cshtml")
    </div>
    <div class="col-sm-10 full-height" style="padding-right:0px;margin-right:0px;">
        <div id="cardChildrenDiv" class="" style="padding:0px;margin:0px;height:100%;">
            <div class="panel blank-panel" style="padding:5px 0px; margin: 0px; height:calc(100% - 10px);">
                <div class="panel-heading form-inline" style="padding: 0px; margin: 0px;">
                    <div class="panel-options">
                        <ul id="cardChildrenTab" class="nav nav-tabs">
                            <li class="active">
                                <a data-toggle="tab" href="#energyDevice"><i class="fa fa-server"></i>能耗设备</a>
                            </li>
                            <li class="">
                                <a data-toggle="tab" href="#energyCriteria"><i class="fa fa-list-ol"></i>投运判断标准</a>
                            </li>
                            <li class="">
                                <a data-toggle="tab" href="#energyConfig"><i class="fa fa-cogs"></i>能耗配置</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="panel-body" style="padding: 0px; margin: 0px;height:calc(100% - 56px); ">
                    <div class="tab-content">
                        <div id="energyDevice" class="tab-pane active">
                            <div class="form-inline" id="energyDeviceToolBar" role="group">
                                <div class="form-group">
                                    <label class="control-label">设备名称</label>
                                    <input type="text" class="form-control" placeholder="请输入设备名称" v-model="itemName" name="itemName">
                                </div>
                                <div class="form-group">
                                    <label class="control-label">设备类型</label>
                                    <select class="form-control" v-model="deviceType" v-on:change="handleDeviceTypeChange">
                                        <option value="-1">全部</option>
                                        <option value="1">主变</option>
                                        <option value="2">AT变</option>
                                        <option value="3">馈线</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-success" v-on:click="searchHit" style="display:none" authority-management="true">
                                    <i class="fa fa-search" aria-hidden="true"></i>&nbsp;查询
                                </button>
                                <button type="button" class="btn btn-primary" v-on:click="addHit" style="display:none" authority-management="true">
                                    <i class="fa fa-plus" aria-hidden="true"></i>&nbsp;添加
                                </button>
                                <button type="button" class="btn btn-danger" v-on:click="deleteHit" style="display:none" authority-management="true">
                                    <i class="fa fa-trash" aria-hidden="true"></i>&nbsp;删除
                                </button>
                            </div>
                            <table id="energyDeviceTable" data-height="100%" data-mobile-responsive="true" authority-management="true"></table>
                        </div>
                        <div id="energyCriteria" class="tab-pane">
                            <div class="form-inline" id="energyCriteriaToolBar" role="group">
                                <div class="form-group">
                                    <label class="control-label">标准名称</label>
                                    <input type="text" class="form-control" placeholder="请输入标准名称" v-model="itemName" name="itemName">
                                </div>
                                <div class="form-group">
                                    <label class="control-label">参数类型</label>
                                    <select class="form-control" v-model="parameterType" v-on:change="handleParameterTypeChange">
                                        <option value="-1">全部</option>
                                        <option value="1">电流</option>
                                        <option value="2">电压</option>
                                        <option value="3">有功功率</option>
                                        <option value="4">无功功率</option>
                                        <option value="5">温度</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary" v-on:click="searchHit" style="display:none" authority-management="true">
                                    <i class="fa fa-search" aria-hidden="true"></i>&nbsp;查询
                                </button>
                                <button type="button" class="btn btn-primary" v-on:click="addHit" style="display:none" authority-management="true">
                                    <i class="fa fa-plus" aria-hidden="true"></i>&nbsp;添加
                                </button>
                                <button type="button" class="btn btn-danger" v-on:click="deleteHit" style="display:none" authority-management="true">
                                    <i class="fa fa-trash" aria-hidden="true"></i>&nbsp;删除
                                </button>
                            </div>
                            <table id="energyCriteriaTable" data-height="100%" data-mobile-responsive="true" authority-management="true"></table>
                        </div>
                        <div id="energyConfig" class="tab-pane">
                            <div class="form-inline" id="energyConfigToolBar" role="group">
                                <div class="form-group">
                                    <label class="control-label">配置描述</label>
                                    <input type="text" class="form-control" placeholder="请输入配置描述" v-model="name" name="name">
                                </div>
                                <div class="form-group">
                                    <label class="control-label">配置类型</label>
                                    <select class="form-control" v-model="configType" v-on:change="handleConfigTypeChange">
                                        <option value="-1">全部</option>
                                        <option value="1">阈值配置</option>
                                        <option value="2">计算参数</option>
                                        <option value="3">显示配置</option>
                                        <option value="4">告警配置</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary" v-on:click="searchHit" style="display:none" authority-management="true">
                                    <i class="fa fa-search" aria-hidden="true"></i>&nbsp;查询
                                </button>
                                <button type="button" class="btn btn-primary" v-on:click="addHit" style="display:none" authority-management="true">
                                    <i class="fa fa-plus" aria-hidden="true"></i>&nbsp;添加
                                </button>
                                <button type="button" class="btn btn-danger" v-on:click="deleteHit" style="display:none" authority-management="true">
                                    <i class="fa fa-trash" aria-hidden="true"></i>&nbsp;删除
                                </button>
                            </div>
                            <table id="energyConfigTable" data-height="100%" data-mobile-responsive="true" authority-management="true"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("~/Views/DataMonitoring/EnergyManagement/EnergyDeviceModal.cshtml")
@await Html.PartialAsync("~/Views/DataMonitoring/EnergyManagement/EnergyCriteriaModal.cshtml")
@await Html.PartialAsync("~/Views/DataMonitoring/EnergyManagement/EnergyConfigModal.cshtml")

@section scripts{
    <!-- jsTree -->
    <script src="~/js/plugins/jsTree/jstree.min.js"></script>
    <script src="~/view-resources/Views/GeneralInformation/_TranSubstationTree.js" type="text/javascript"></script>

    <!-- Chosen -->
    <script src="~/js/plugins/chosen/chosen.jquery.min.js" type="text/javascript"></script>
    <script src="~/js/plugins/chosen/vue.chosen.js" type="text/javascript"></script>
    <!-- 日期时间选择器 -->
    <script src="~/js/plugins/bootstrap-datepicker/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/js/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js" type="text/javascript"></script>
    <script src="~/js/plugins/bootstrap-datepicker/vue.datepicker.js" type="text/javascript"></script>
    <script src="~/js/plugins/bootstrap-clockpicker/bootstrap-clockpicker.min.js" type="text/javascript"></script>
    <script src="~/js/plugins/bootstrap-clockpicker/vue.clockpicker.js" type="text/javascript"></script>
    <!-- 主体js -->

    <script src="~/view-resources/Views/DataMonitoring/EnergyManagement/EnergyDeviceList.js"></script>
    <script src="~/view-resources/Views/DataMonitoring/EnergyManagement/EnergyCriteriaList.js"></script>
    <script src="~/view-resources/Views/DataMonitoring/EnergyManagement/EnergyConfigList.js"></script>
    <script src="~/view-resources/Views/DataMonitoring/EnergyManagement/EnergyManagement.js"></script>

}
