﻿using System;
using YunDa.ISAS.Entities.DataMonitoring;

namespace Yunda.ISAS.DataMonitoringServer.WPF.ViewModel
{
    public class Recoder
    {
        /// <summary>
        /// 日志种类
        /// </summary>
        public string MsgType { get; set; }
        /// <summary>
        /// 日志内容
        /// </summary>
        public string Msg { get; set; }
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime DateTime { get; set; }
    }
    public class InfoAddrLiveData
    {

        public string TypeName { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; }

        public string Name { get; set; }
        /// <summary>
        /// 调度地址
        /// </summary>
        public int InfoAddr { get; set; }
        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime DateTime { get; set; }
    }
}
