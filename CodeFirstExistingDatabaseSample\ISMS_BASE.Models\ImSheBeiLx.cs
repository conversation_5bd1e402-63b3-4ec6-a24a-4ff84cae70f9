﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImSheBeiLx
    {
        public ImSheBeiLx()
        {
            ImSheBeis = new HashSet<ImSheBei>();
        }

        public int Sblxbm { get; set; }
        public string Sblxmc { get; set; } = null!;
        public int? Sblxxh { get; set; }

        public virtual ICollection<ImSheBei> ImSheBeis { get; set; }
    }
}
