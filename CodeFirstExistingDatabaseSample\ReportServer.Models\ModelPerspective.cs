﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ModelPerspective
    {
        public Guid Id { get; set; }
        public Guid ModelId { get; set; }
        public string PerspectiveId { get; set; } = null!;
        public string? PerspectiveName { get; set; }
        public string? PerspectiveDescription { get; set; }

        public virtual Catalog Model { get; set; } = null!;
    }
}
