﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceYxTmp
    {
        public string Id { get; set; } = null!;
        public string Yxname { get; set; } = null!;
        public string? YxType { get; set; }
        public string? SwOnStr { get; set; }
        public string? SwOffStr { get; set; }
        public string? SwUncertStr { get; set; }
        public string AlertLevel { get; set; } = null!;
        public string NormalState { get; set; } = null!;

        public virtual ImAlertLevel AlertLevelNavigation { get; set; } = null!;
        public virtual ImDeviceDataTmp IdNavigation { get; set; } = null!;
    }
}
