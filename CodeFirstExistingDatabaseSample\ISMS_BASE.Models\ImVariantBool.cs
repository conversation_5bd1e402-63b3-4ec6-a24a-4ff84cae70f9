﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVariantBool
    {
        public string VaId { get; set; } = null!;
        public string Vaname { get; set; } = null!;
        public string StatCode { get; set; } = null!;
        public string? TrueStr { get; set; }
        public string? FalseStr { get; set; }
        public string? Expr { get; set; }
        public string? ExprDesc { get; set; }
        public string? ExprDataIds { get; set; }
        public string State { get; set; } = null!;
        public string AlertType { get; set; } = null!;
        public string? AlertMsg { get; set; }
        public string IsFibre { get; set; } = null!;
        public string? Memo { get; set; }

        public virtual ImStation StatCodeNavigation { get; set; } = null!;
    }
}
