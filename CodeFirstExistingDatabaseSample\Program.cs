﻿using CodeFirstExistingDatabaseSample.ISMS_BASE;

namespace CodeFirstExistingDatabaseSample
{
    class Program
    {
        static void Main(string[] args)
        {
            //using (var db = new ISMS_BASEContext())
            //{
            //    // Create and save a new Blog
            //    //var name = Console.ReadLine();

            //    //var blog = new Blog { Name = name };
            //    //db.Blogs.Add(blog);
            //    //db.SaveChanges();

            //    // Display all Blogs from the database
            //    var query = from b in db.ImDeviceData
            //                orderby b.DeviceId
            //                select b;

            //    Console.WriteLine("All blogs in the database:");
            //    foreach (var item in query)
            //    {
            //        Console.WriteLine($"数据名称:{item.DataName}");
            //    }
            //    var query1 = from b in db.ImProtectDevices
            //                 orderby b.DeviceAddr
            //                 select b;
            //    foreach (var item in query1)
            //    {
            //        Console.WriteLine($"保护装置名称:{item.DeviceName}");
            //    }
            //    Console.WriteLine("Press any key to exit...");
            //    Console.ReadKey();
            //}
        }
    }

}