﻿using Abp.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Text;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Session;
using YunDa.SOMS.Entities.DataMonitoring;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    public class EnergyOperationCriteriaAppService : ISASAppServiceBase
    {
        private readonly IRepository<EnergyOperationCriteria, Guid> _criteriaRepository;
        public EnergyOperationCriteriaAppService(ISessionAppService sessionAppService) : base(sessionAppService)
        {
        }
    }
}
