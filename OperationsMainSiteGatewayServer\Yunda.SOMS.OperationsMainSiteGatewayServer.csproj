﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  
    <Nullable>enable</Nullable>
    <AssemblyName>运维调度通信网关</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

	<ItemGroup>
		<!-- ABP Framework (compatible with .NET Core 3.1) -->

		<!-- DotNetty (compatible with .NET Core 3.1) -->
		<PackageReference Include="DotNetty.Codecs" Version="0.7.6" />
		<PackageReference Include="DotNetty.Transport" Version="0.7.6" />

		<!-- Microsoft.Extensions.Configuration.Json (updated to match dependencies) -->
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />

		<!-- Serilog (compatible with .NET Core 3.1) -->
		<PackageReference Include="Serilog" Version="2.10.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="2.2.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="3.1.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="4.1.0" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\src\YunDa.Application\YunDa.ISAS.Application.Core\YunDa.SOMS.Application.Core.csproj" />
    <ProjectReference Include="..\src\YunDa.Application\YunDa.ISAS.DataTransferObject\YunDa.SOMS.DataTransferObject.csproj" />
    <ProjectReference Include="..\src\YunDa.Domain\YunDa.ISAS.MongoDB\YunDa.SOMS.MongoDB.csproj" />
    <ProjectReference Include="..\src\YunDa.Domain\YunDa.ISAS.Redis.Entities\YunDa.SOMS.Redis.Entities.csproj" />
    <ProjectReference Include="..\src\YunDa.Domain\YunDa.ISAS.Redis\YunDa.SOMS.Redis.csproj" />
    <ProjectReference Include="..\src\YunDa.Web\YunDa.ISAS.Web.Core\YunDa.SOMS.Web.Core.csproj" />
  </ItemGroup>

</Project>
