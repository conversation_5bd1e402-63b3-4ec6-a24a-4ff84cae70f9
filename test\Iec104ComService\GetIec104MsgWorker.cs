﻿using Client104Side;
using Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using NetMQ;
using NetMQ.Sockets;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;


namespace Iec104ComService
{
    class GetIec104MsgWorker : BackgroundService
    {
        private readonly PublisherSocket _publisherSocket;
        private readonly IConfigurationRoot _configurationRoot;

        public GetIec104MsgWorker(PublisherSocket publisherSocket, IConfigurationRoot configurationRoot)
        {
            _publisherSocket = publisherSocket;
            _configurationRoot = configurationRoot;
        }
        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                CommDeviceConfig commDeviceConfig = new CommDeviceConfig();
                commDeviceConfig.Address = int.Parse(_configurationRoot.GetSection("CommDeviceConfig").GetSection("Address").Value);
                commDeviceConfig.ElectricalDegreeCount = 0;
                commDeviceConfig.ElectricalDegreeStartAddress = 0;
                commDeviceConfig.IP = _configurationRoot.GetSection("CommDeviceConfig").GetSection("IP").Value;
                commDeviceConfig.Port = int.Parse(_configurationRoot.GetSection("CommDeviceConfig").GetSection("Port").Value);
                commDeviceConfig.TelemeteringCount = int.Parse(_configurationRoot.GetSection("CommDeviceConfig").GetSection("TelemeteringCount").Value);
                commDeviceConfig.TelemeteringStartAddress = int.Parse(_configurationRoot.GetSection("CommDeviceConfig").GetSection("TelemeteringStartAddress").Value);
                commDeviceConfig.TelesignalisationCount = int.Parse(_configurationRoot.GetSection("CommDeviceConfig").GetSection("TelesignalisationCount").Value);
                commDeviceConfig.TelesignalisationStartAddress = int.Parse(_configurationRoot.GetSection("CommDeviceConfig").GetSection("TelesignalisationStartAddress").Value);
                ICE104CommunicationCallback.Start(commDeviceConfig);
                while (!ICE104CommunicationCallback.Started)
                {
                    Log.Information("连接远动管理机...");
                    await Task.Delay(30000);
                }
                Log.Information("远动管理机完成");
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message);
            }
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {

            while (!stoppingToken.IsCancellationRequested)
            {
                if (ICE104CommunicationCallback.Started)
                {
                    ExcuteYCInfo();
                    ExcuteYXInfo();
                }
                await Task.Delay(100);
            }
        }
        private void ExcuteYCInfo()
        {
            int flag = 0;
            IntPtr ycIntPtr = ICE104EndPointController.StructToIntPtr(new YC_TYPE());
            int count = 0;
            while (flag == 0 || flag == -1)
            {
                //0：变化数据   -1：未变化数据  -2：取尽
                flag = ICE104EndPointController.Iec104ClnGetYCBuf(ycIntPtr);
                if (flag == 0)
                {
                    var info = ICE104EndPointController.IntPtrToStruct<YC_TYPE>(ycIntPtr);
                    var message = JsonConvert.SerializeObject(info);
                    Log.Information(message);
                    _publisherSocket.SendMoreFrame("ISAS_104_Shift_Data").SendFrame(message);
                }
                count++;
            }
            Marshal.FreeHGlobal(ycIntPtr);
        }
        private void ExcuteYXInfo()
        {
            IntPtr resYX = ICE104EndPointController.StructToIntPtr(new RECORDYXBURST());
            int flag = 0;
            int count = 0;
            while (flag == 0 || flag == -1)
            {
                //0：变化数据   -1：未变化数据  -2：取尽
                flag = ICE104EndPointController.Iec104ClnReadYXBurstRecord(resYX);
                if (flag == 0)
                {
                    var info = ICE104EndPointController.IntPtrToStruct<RECORDYXBURST>(resYX);
                    var message = JsonConvert.SerializeObject(info);
                    Log.Information(message);
                    _publisherSocket.SendMoreFrame("ISAS_104_Shift_Data").SendFrame(message);
                }
                count++;
            }
            Marshal.FreeHGlobal(resYX);
        }
    }
}
