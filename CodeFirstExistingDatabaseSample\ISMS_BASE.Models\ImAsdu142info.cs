﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAsdu142info
    {
        public string CompanyName { get; set; } = null!;
        public string ValueType { get; set; } = null!;
        public int ValueIndex { get; set; }
        public string ValueName { get; set; } = null!;
        public string? Unit { get; set; }
        public int Precise { get; set; }
        public double Coeff { get; set; }
        public string ParseMode { get; set; } = null!;
        public string? ParseFormat { get; set; }

        public virtual ImManufacturer CompanyNameNavigation { get; set; } = null!;
    }
}
