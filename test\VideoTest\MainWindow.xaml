﻿<Window x:Class="VideoTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:WinFormHost="clr-namespace:System.Windows.Forms.Integration;assembly=WindowsFormsIntegration"
        xmlns:WinFormControls="clr-namespace:System.Windows.Forms;assembly=System.Windows.Forms"
        mc:Ignorable="d"
        Title="MainWindow" Height="850" Width="1500" Background="LightGray" FontSize="14">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
            <ColumnDefinition Width="400"/>
            <ColumnDefinition Width="300"/>
        </Grid.ColumnDefinitions>
        <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <!--<WinFormHost:WindowsFormsHost x:Name="WinBoxHost" Grid.Row="0">
                    <WinFormControls:PictureBox x:Name="WinBox" />
                </WinFormHost:WindowsFormsHost>-->
                <Image x:Name="WinBox">
                    
                </Image>
                <WinFormHost:WindowsFormsHost x:Name="WinBoxHost1" Grid.Row="1">
                    <WinFormControls:PictureBox x:Name="WinBox1" />
                </WinFormHost:WindowsFormsHost>
            </Grid>
        </Border>
        <Border Grid.Row="0" Grid.Column="1" BorderBrush="Gray" BorderThickness="1">
            <Grid Grid.Row="0" Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />

                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />
                    <RowDefinition Height="40" />

                    <RowDefinition />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="60" />
                    <ColumnDefinition />
                    <ColumnDefinition Width="60" />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <Border>
                    <TextBlock Text="Ip" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Column="1">
                    <TextBox x:Name="textBoxIP" Text="192.168.81." VerticalContentAlignment="Center" Margin="0,2" />
                </Border>
                <Border Grid.Column="2">
                    <TextBlock Text="Port" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Column="3">
                    <TextBox x:Name="textBoxPort" Text="8000" VerticalContentAlignment="Center" Margin="0,2" />
                </Border>
                <Border Grid.Row="1">
                    <TextBlock Text="用户名" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Row="1" Grid.Column="1">
                    <TextBox x:Name="textBoxUserName" Text="admin" VerticalContentAlignment="Center" Margin="0,2" />
                </Border>
                <Border Grid.Row="1" Grid.Column="2">
                    <TextBlock Text="密码" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Row="1" Grid.Column="3">
                    <TextBox x:Name="textBoxPassword" Text="yunda123" VerticalContentAlignment="Center" Margin="0,2" />
                </Border>
                <Border Grid.Row="2" Grid.Column="0">
                    <TextBlock Text="品牌" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>

                <Border Grid.Row="2" Grid.Column="1">
                    <ComboBox x:Name="cbBrand" SelectedIndex="1" SelectedValuePath="Content" Margin="0,2" VerticalContentAlignment="Center">
                        <ComboBoxItem Content="DHua"/>
                        <ComboBoxItem Content="Hik"/>
                        <ComboBoxItem Content="DALI"/>
                    </ComboBox>
                </Border>
                <Border Grid.Row="2" Grid.Column="2">
                    <TextBlock Text="通道号" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Row="2" Grid.Column="3">
                    <!--<ComboBox x:Name="cbPlayChannel" SelectedIndex="0" SelectedValuePath="Content" Margin="0,2" VerticalContentAlignment="Center">
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="2"/>
                        <ComboBoxItem Content="3"/>
                        <ComboBoxItem Content="7"/>
                        <ComboBoxItem Content="8"/>
                        <ComboBoxItem Content="33"/>
                        <ComboBoxItem Content="34"/>
                    </ComboBox>-->
                    <TextBox Text=""  x:Name="cbPlayChannel"></TextBox>
                </Border>
                <Border Grid.Row="3">
                    <TextBlock Text="预置点" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Row="3" Grid.Column="1">
                    <!--<ComboBox x:Name="cbPreset" SelectedIndex="0" SelectedValuePath="Content" Margin="0,2" VerticalContentAlignment="Center" SelectionChanged="cbPreset_SelectionChanged">
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="2"/>
                        <ComboBoxItem Content="3"/>
                        <ComboBoxItem Content="4"/>
                        <ComboBoxItem Content="5"/>
                        <ComboBoxItem Content="6"/>
                        <ComboBoxItem Content="7"/>
                        <ComboBoxItem Content="8"/>
                        <ComboBoxItem Content="9"/>
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="11"/>
                        <ComboBoxItem Content="12"/>
                        <ComboBoxItem Content="13"/>
                        <ComboBoxItem Content="14"/>
                        <ComboBoxItem Content="15"/>
                        <ComboBoxItem Content="16"/>
                    </ComboBox>-->
                    <TextBox Text="" x:Name="cbPreset"></TextBox>
                </Border>
                <Border Grid.Row="3" Grid.Column="2">
                    <TextBlock Text="测温点" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,5,0" />
                </Border>
                <Border Grid.Row="3" Grid.Column="3">
                    <!--<ComboBox x:Name="cbMeasureTemperature" SelectedIndex="0" SelectedValuePath="Content" Margin="0,2" VerticalContentAlignment="Center">
                        <ComboBoxItem Content="0"/>
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="2"/>
                        <ComboBoxItem Content="3"/>
                        <ComboBoxItem Content="4"/>
                        <ComboBoxItem Content="5"/>
                        <ComboBoxItem Content="6"/>
                        <ComboBoxItem Content="7"/>
                        <ComboBoxItem Content="8"/>
                        <ComboBoxItem Content="9"/>
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="11"/>
                        <ComboBoxItem Content="12"/>
                        <ComboBoxItem Content="13"/>
                        <ComboBoxItem Content="14"/>
                        <ComboBoxItem Content="15"/>
                        <ComboBoxItem Content="16"/>
                    </ComboBox>-->
                    <TextBox Text="" x:Name="cbMeasureTemperature"></TextBox>
                </Border>
                <Border Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="4">
                    <WrapPanel VerticalAlignment="Center" HorizontalAlignment="Right">
                        <Button Content="登录"  Width="80"  Height="30" VerticalAlignment="Center" Margin="2" Click="Login_Click" />
                        <Button Content="播放"  Width="80" Height="30" VerticalAlignment="Center" Margin="2" Click="VideoPlay_Click" />
                        <Button Content="测温"  Width="80" Height="30" VerticalAlignment="Center" Margin="2" Click="MeasureTemperature_Click" IsEnabled="True"/>
                        <Button Content="截图"  Width="80" Height="30" VerticalAlignment="Center" Margin="2" Click="Capture_Click" />
                    </WrapPanel>
                </Border>
                <Border Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="4">
                    <WrapPanel VerticalAlignment="Center" HorizontalAlignment="Right">
                        <Button Content="上"  Width="80"  Height="30" VerticalAlignment="Center" Margin="2" PreviewMouseLeftButtonDown="Up_MouseLeftButtonDown" PreviewMouseLeftButtonUp="Up_MouseLeftButtonUp"/>
                        <Button Content="下"  Width="80" Height="30" VerticalAlignment="Center" Margin="2" PreviewMouseLeftButtonDown="Down_MouseLeftButtonDown" PreviewMouseLeftButtonUp="Down_MouseLeftButtonUp"/>
                        <Button Content="左"  Width="80" Height="30" VerticalAlignment="Center" Margin="2" PreviewMouseLeftButtonDown="Left_MouseLeftButtonDown" PreviewMouseLeftButtonUp="Left_MouseLeftButtonUp"/>
                        <Button Content="右"  Width="80" Height="30" VerticalAlignment="Center" Margin="2" PreviewMouseLeftButtonDown="Right_MouseLeftButtonDown" PreviewMouseLeftButtonUp="Right_MouseLeftButtonUp"/>
                    </WrapPanel>
                </Border>
                <Button Content="GoPST"  Height="30" VerticalAlignment="Center" Margin="4,0,75,0" Click="TurnTopreset_Click" Grid.Column="1" Grid.Row="6"/>
                <Button Content="播放2" Visibility="Collapsed" Height="30" VerticalAlignment="Top" Margin="30,1,89,0" Click="StartRealPlayM4_Click" Grid.Column="2" Grid.Row="7" Grid.ColumnSpan="2"/>

                <Button Content="SetPST"  Height="30" VerticalAlignment="Center"  Click="Setpreset_Click" Grid.Column="1" Grid.Row="6" Margin="70,0,19,0"/>
                <Button Content="CloseOSD"  Height="30" VerticalAlignment="Center"  Click="CloseOSD_Click" Grid.Column="3" Grid.Row="6" Margin="4,0,76,0" />

                <Button Content="ShowOSD"  Height="30" VerticalAlignment="Center"  Click="ShowOSD_Click" Grid.Column="3" Grid.Row="6" Margin="75,0,5,0" />

                <TextBlock Text="startTime" VerticalAlignment="Center" HorizontalAlignment="Left" Grid.Row="8" />
                <TextBox x:Name="startTime" Text="00:00"  Grid.Column="1" Grid.Row="8"></TextBox>
                <TextBlock Text="endTime" VerticalAlignment="Center" HorizontalAlignment="Left" Grid.Column="2" Grid.Row="8" />
                <TextBox x:Name="endTime" Text="00:10"   Grid.Column="3" Grid.Row="8"></TextBox>
                <Button Content="下载录像"  Height="30" VerticalAlignment="Top" Margin="4,6,55,0" Click="DownloadPlayback_Click" Grid.Column="1" Grid.Row="9"/>
                <Button Content="播放录像" Margin="4,6,55,0" Click="Playbackt_Click" Grid.Column="3" Grid.Row="9" Height="30" VerticalAlignment="Top"/>
                <TextBox x:Name="channels" Text="35,36"  Grid.Column="1" Grid.Row="6" Margin="0,36,0,4" Grid.RowSpan="2"/>
                <TextBlock Text="多通道号" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="7" />
                <Frame x:Name="frame" Grid.Row="10"></Frame>
            </Grid>
        </Border>

        <Border  Grid.Column="2" BorderBrush="Gray" BorderThickness="1">
            <DockPanel LastChildFill="True">
                <Border DockPanel.Dock="Top" Height="45" BorderBrush="Gray" BorderThickness="1">
                    <TextBlock x:Name="LogText" Foreground="Red" FontSize="14" VerticalAlignment="Center" Margin="10,0" TextWrapping="Wrap"/>
                </Border>
                <Border BorderBrush="Gray" BorderThickness="1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <StackPanel>
                            <TextBlock x:Name="RstText" Foreground="Black" FontSize="14" VerticalAlignment="Top" Margin="10,0" TextWrapping="Wrap" />
                            <StackPanel Background="#20335D" Height="500">
                                <Button Content="#FF0000" Background="#FF0000" Height="30" Margin="0,0,0,10"/>
                                <Button Content="#00FF00" Background="#00FF00" Height="30" Margin="0,0,0,10"/>
                                <Button Content="#9B59B6" Background="#9B59B6" Height="30" Margin="0,0,0,10"/>

                                <Button Content="#D35400" Background="#D35400" Height="30" Margin="0,0,0,10"/>
                                <Button Content="#FFD700" Background="#FFD700" Height="30" Margin="0,0,0,10"/>

                                <Button Content="#3498DB" Background="#3498DB" Height="30" Margin="0,0,0,10"/>

                                <Button Content="#F39C12" Background="#F39C12" Height="30" Margin="0,0,0,10"/>

                                <Button Content="#47AE60" Background="#47AE60" Height="30" Margin="0,0,0,10"/>
                             
                                <Button Content="#E67E22" Background="#E67E22" Height="30" Margin="0,0,0,10"/>
                                <Button Content="#1ABC9C" Background="#1ABC9C" Height="30" Margin="0,0,0,10"/>

                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Border>
            </DockPanel>
        </Border>
        
    </Grid>
</Window>
