﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImVProtectdevice
    {
        public string Deviceid { get; set; }
        public int Deviceaddr { get; set; }
        public string Devicename { get; set; }
        public string Statcode { get; set; }
        public string Gatewayid { get; set; }
        public string Bayname { get; set; }
        public int? Devicestate { get; set; }
        public int Puctgycode { get; set; }
        public int Canswdzzone { get; set; }
        public int Support12yc { get; set; }
        public int Supportversion { get; set; }
        public int Supportdkjl { get; set; }
        public int? Startofdkjl { get; set; }
        public int? Endofdkjl { get; set; }
        public string Devicetype { get; set; }
        public string Wavepath { get; set; }
        public string Statname { get; set; }
        public string Gatewayname { get; set; }
        public string Gatewayip1 { get; set; }
        public int? Gatewayport1 { get; set; }
        public string Gatewayip2 { get; set; }
        public int? Gatewayport2 { get; set; }
        public int Phyaddr { get; set; }
        public string Manufacturer { get; set; }
        public string Devctgy { get; set; }
        public string Generation { get; set; }
        public int Dzzonecount { get; set; }
        public string Model { get; set; }
        public string Analogparsemode { get; set; }
        public string Eventparsemode { get; set; }
        public int Supportguzhangbg { get; set; }
        public int Supportshijianbg { get; set; }
        public int Supportzijianbg { get; set; }
        public int Supportlubowj { get; set; }
        public int Supportdz { get; set; }
        public int Dzreadonly { get; set; }
        public int Supportfhlubo { get; set; }
    }
}
