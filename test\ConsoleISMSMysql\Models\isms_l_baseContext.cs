﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class isms_l_baseContext : DbContext
    {
        public isms_l_baseContext()
        {
        }

        public isms_l_baseContext(DbContextOptions<isms_l_baseContext> options)
            : base(options)
        {
        }

        public virtual DbSet<Im3posswitch> Im3posswitch { get; set; }
        public virtual DbSet<ImAlertlevel> ImAlertlevel { get; set; }
        public virtual DbSet<ImAlertstate> ImAlertstate { get; set; }
        public virtual DbSet<ImAlerttype> ImAlerttype { get; set; }
        public virtual DbSet<ImAnalogdata2010> ImAnalogdata2010 { get; set; }
        public virtual DbSet<ImAnalogdata20102> ImAnalogdata20102 { get; set; }
        public virtual DbSet<ImAsdu140Fltacttype> ImAsdu140Fltacttype { get; set; }
        public virtual DbSet<ImAsdu140Fltcurrinfo> ImAsdu140Fltcurrinfo { get; set; }
        public virtual DbSet<ImAsdu142info> ImAsdu142info { get; set; }
        public virtual DbSet<ImAutoreport> ImAutoreport { get; set; }
        public virtual DbSet<ImAutoreportitem> ImAutoreportitem { get; set; }
        public virtual DbSet<ImBreakernoenum> ImBreakernoenum { get; set; }
        public virtual DbSet<ImCtrlworddef> ImCtrlworddef { get; set; }
        public virtual DbSet<ImCurve> ImCurve { get; set; }
        public virtual DbSet<ImCurveType> ImCurveType { get; set; }
        public virtual DbSet<ImCurveitem> ImCurveitem { get; set; }
        public virtual DbSet<ImDataviewer> ImDataviewer { get; set; }
        public virtual DbSet<ImDataviewerData> ImDataviewerData { get; set; }
        public virtual DbSet<ImDevctgy> ImDevctgy { get; set; }
        public virtual DbSet<ImDevicedata> ImDevicedata { get; set; }
        public virtual DbSet<ImDevicedataTmp> ImDevicedataTmp { get; set; }
        public virtual DbSet<ImDevicedz> ImDevicedz { get; set; }
        public virtual DbSet<ImDevicedzTmp> ImDevicedzTmp { get; set; }
        public virtual DbSet<ImDevicedzenum> ImDevicedzenum { get; set; }
        public virtual DbSet<ImDevicedzenum2> ImDevicedzenum2 { get; set; }
        public virtual DbSet<ImDevicedzenumpu> ImDevicedzenumpu { get; set; }
        public virtual DbSet<ImDeviceva> ImDeviceva { get; set; }
        public virtual DbSet<ImDeviceyc> ImDeviceyc { get; set; }
        public virtual DbSet<ImDeviceycTmp> ImDeviceycTmp { get; set; }
        public virtual DbSet<ImDeviceyk> ImDeviceyk { get; set; }
        public virtual DbSet<ImDeviceykTmp> ImDeviceykTmp { get; set; }
        public virtual DbSet<ImDeviceym> ImDeviceym { get; set; }
        public virtual DbSet<ImDeviceymTmp> ImDeviceymTmp { get; set; }
        public virtual DbSet<ImDeviceyx> ImDeviceyx { get; set; }
        public virtual DbSet<ImDeviceyxSrcdevice> ImDeviceyxSrcdevice { get; set; }
        public virtual DbSet<ImDeviceyxTmp> ImDeviceyxTmp { get; set; }
        public virtual DbSet<ImDiagram> ImDiagram { get; set; }
        public virtual DbSet<ImDzcheckrule> ImDzcheckrule { get; set; }
        public virtual DbSet<ImDztype> ImDztype { get; set; }
        public virtual DbSet<ImEventflag> ImEventflag { get; set; }
        public virtual DbSet<ImEventparam> ImEventparam { get; set; }
        public virtual DbSet<ImEventtype> ImEventtype { get; set; }
        public virtual DbSet<ImEventtype2010> ImEventtype2010 { get; set; }
        public virtual DbSet<ImEventtype2010Deviceyktmp> ImEventtype2010Deviceyktmp { get; set; }
        public virtual DbSet<ImFaultacttype> ImFaultacttype { get; set; }
        public virtual DbSet<ImFaultacttype2010> ImFaultacttype2010 { get; set; }
        public virtual DbSet<ImFaultacttype20102> ImFaultacttype20102 { get; set; }
        public virtual DbSet<ImFaultacttypePd> ImFaultacttypePd { get; set; }
        public virtual DbSet<ImFaultparam> ImFaultparam { get; set; }
        public virtual DbSet<ImFaultparamPd> ImFaultparamPd { get; set; }
        public virtual DbSet<ImFaultreportitem> ImFaultreportitem { get; set; }
        public virtual DbSet<ImFaulttype> ImFaulttype { get; set; }
        public virtual DbSet<ImFaulttype2010> ImFaulttype2010 { get; set; }
        public virtual DbSet<ImFaulttypePd> ImFaulttypePd { get; set; }
        public virtual DbSet<ImGateway> ImGateway { get; set; }
        public virtual DbSet<ImGlyph> ImGlyph { get; set; }
        public virtual DbSet<ImGlyphdefprop> ImGlyphdefprop { get; set; }
        public virtual DbSet<ImGlyphdevdata> ImGlyphdevdata { get; set; }
        public virtual DbSet<ImGlyphhots> ImGlyphhots { get; set; }
        public virtual DbSet<ImGrid> ImGrid { get; set; }
        public virtual DbSet<ImLabel> ImLabel { get; set; }
        public virtual DbSet<ImLinkage> ImLinkage { get; set; }
        public virtual DbSet<ImManufacturer> ImManufacturer { get; set; }
        public virtual DbSet<ImNoticeboard> ImNoticeboard { get; set; }
        public virtual DbSet<ImProgcontrol> ImProgcontrol { get; set; }
        public virtual DbSet<ImProgcontrolitem> ImProgcontrolitem { get; set; }
        public virtual DbSet<ImProject> ImProject { get; set; }
        public virtual DbSet<ImProtectdevice> ImProtectdevice { get; set; }
        public virtual DbSet<ImProtectdeviceTmp> ImProtectdeviceTmp { get; set; }
        public virtual DbSet<ImPuCtgy> ImPuCtgy { get; set; }
        public virtual DbSet<ImPuUpdatelog> ImPuUpdatelog { get; set; }
        public virtual DbSet<ImPuWavechl> ImPuWavechl { get; set; }
        public virtual DbSet<ImPuctgyFaulttype> ImPuctgyFaulttype { get; set; }
        public virtual DbSet<ImPuctgyFltrptitem> ImPuctgyFltrptitem { get; set; }
        public virtual DbSet<ImReportType> ImReportType { get; set; }
        public virtual DbSet<ImReportcfg> ImReportcfg { get; set; }
        public virtual DbSet<ImReportcfgdata> ImReportcfgdata { get; set; }
        public virtual DbSet<ImShebei> ImShebei { get; set; }
        public virtual DbSet<ImShebeiDevicedata> ImShebeiDevicedata { get; set; }
        public virtual DbSet<ImShebeiProtdevice> ImShebeiProtdevice { get; set; }
        public virtual DbSet<ImShebeilx> ImShebeilx { get; set; }
        public virtual DbSet<ImShebeizt> ImShebeizt { get; set; }
        public virtual DbSet<ImStation> ImStation { get; set; }
        public virtual DbSet<ImSwitchstate> ImSwitchstate { get; set; }
        public virtual DbSet<ImTimesrc> ImTimesrc { get; set; }
        public virtual DbSet<ImVDevdatalinkage> ImVDevdatalinkage { get; set; }
        public virtual DbSet<ImVDevicedata> ImVDevicedata { get; set; }
        public virtual DbSet<ImVDevicedz> ImVDevicedz { get; set; }
        public virtual DbSet<ImVDeviceyc> ImVDeviceyc { get; set; }
        public virtual DbSet<ImVDeviceyk> ImVDeviceyk { get; set; }
        public virtual DbSet<ImVDeviceym> ImVDeviceym { get; set; }
        public virtual DbSet<ImVDeviceyx> ImVDeviceyx { get; set; }
        public virtual DbSet<ImVDeviceyxSrcdevice> ImVDeviceyxSrcdevice { get; set; }
        public virtual DbSet<ImVProtectdevice> ImVProtectdevice { get; set; }
        public virtual DbSet<ImVProtectdeviceTmp> ImVProtectdeviceTmp { get; set; }
        public virtual DbSet<ImVariantBool> ImVariantBool { get; set; }
        public virtual DbSet<ImVariantboolstate> ImVariantboolstate { get; set; }
        public virtual DbSet<ImVersion> ImVersion { get; set; }
        public virtual DbSet<ImVersiontypeenum2010> ImVersiontypeenum2010 { get; set; }
        public virtual DbSet<ImWatchdog> ImWatchdog { get; set; }
        public virtual DbSet<ImYkType> ImYkType { get; set; }
        public virtual DbSet<ImYxType> ImYxType { get; set; }
        public virtual DbSet<TbBumen> TbBumen { get; set; }
        public virtual DbSet<TbBumenlx> TbBumenlx { get; set; }
        public virtual DbSet<TbDatamodihis> TbDatamodihis { get; set; }
        public virtual DbSet<TbField> TbField { get; set; }
        public virtual DbSet<TbImage> TbImage { get; set; }
        public virtual DbSet<TbQuanxian> TbQuanxian { get; set; }
        public virtual DbSet<TbSession> TbSession { get; set; }
        public virtual DbSet<TbTable> TbTable { get; set; }
        public virtual DbSet<TbTreenode> TbTreenode { get; set; }
        public virtual DbSet<TbVirtualtable> TbVirtualtable { get; set; }
        public virtual DbSet<TbXitongmokuai> TbXitongmokuai { get; set; }
        public virtual DbSet<TbXitongrizhi> TbXitongrizhi { get; set; }
        public virtual DbSet<TbYonghu> TbYonghu { get; set; }
        public virtual DbSet<TbYonghuguanxi> TbYonghuguanxi { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. See http://go.microsoft.com/fwlink/?LinkId=723263 for guidance on storing connection strings.
                optionsBuilder.UseMySql("server=192.168.81.229;port=3306;uid=root;pwd=******;database=isms_l_base;sslmode=none;maxpoolsize=200;connectiontimeout=60;pooling=true", x => x.ServerVersion("8.0.15-mysql"));
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Im3posswitch>(entity =>
            {
                entity.ToTable("im_3posswitch");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gndykdataid)
                    .HasColumnName("gndykdataid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gndykdataname)
                    .HasColumnName("gndykdataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gndyxdataid)
                    .HasColumnName("gndyxdataid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gndyxdataname)
                    .HasColumnName("gndyxdataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.Im3posswitch)
                    .HasForeignKey<Im3posswitch>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_3posswitch_glyph");
            });

            modelBuilder.Entity<ImAlertlevel>(entity =>
            {
                entity.HasKey(e => e.Alertlevelcode)
                    .HasName("PRIMARY");

                entity.ToTable("im_alertlevel");

                entity.Property(e => e.Alertlevelcode)
                    .HasColumnName("alertlevelcode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alertlevelname)
                    .IsRequired()
                    .HasColumnName("alertlevelname")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Color)
                    .HasColumnName("color")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImAlertstate>(entity =>
            {
                entity.HasKey(e => e.Statecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_alertstate");

                entity.Property(e => e.Statecode)
                    .HasColumnName("statecode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statename)
                    .IsRequired()
                    .HasColumnName("statename")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImAlerttype>(entity =>
            {
                entity.HasKey(e => e.Alerttype)
                    .HasName("PRIMARY");

                entity.ToTable("im_alerttype");

                entity.HasIndex(e => e.Alertlevel)
                    .HasName("fk_alttype_level");

                entity.Property(e => e.Alerttype)
                    .HasColumnName("alerttype")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alertlevel)
                    .IsRequired()
                    .HasColumnName("alertlevel")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alerttypename)
                    .HasColumnName("alerttypename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.AlertlevelNavigation)
                    .WithMany(p => p.ImAlerttype)
                    .HasForeignKey(d => d.Alertlevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_alttype_level");
            });

            modelBuilder.Entity<ImAnalogdata2010>(entity =>
            {
                entity.HasKey(e => new { e.Devicectgy, e.Datacode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_analogdata_2010");

                entity.Property(e => e.Devicectgy)
                    .HasColumnName("devicectgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datacode)
                    .HasColumnName("datacode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Cof1).HasColumnName("cof1");

                entity.Property(e => e.Cof2).HasColumnName("cof2");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Enumstr)
                    .HasColumnName("enumstr")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Precise1)
                    .HasColumnName("precise1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Precise2)
                    .HasColumnName("precise2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Sym1)
                    .HasColumnName("sym1")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Sym2)
                    .HasColumnName("sym2")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.DevicectgyNavigation)
                    .WithMany(p => p.ImAnalogdata2010)
                    .HasForeignKey(d => d.Devicectgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_analogdata2010_devctgy");
            });

            modelBuilder.Entity<ImAnalogdata20102>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.Datacode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_analogdata_2010_2");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Datacode)
                    .HasColumnName("datacode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Cof1).HasColumnName("cof1");

                entity.Property(e => e.Cof2).HasColumnName("cof2");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Enumstr)
                    .HasColumnName("enumstr")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Precise1)
                    .HasColumnName("precise1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Precise2)
                    .HasColumnName("precise2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Sym1)
                    .HasColumnName("sym1")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Sym2)
                    .HasColumnName("sym2")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImAnalogdata20102)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_analogdata2_puctgy");
            });

            modelBuilder.Entity<ImAsdu140Fltacttype>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.Actcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_asdu140_fltacttype");

                entity.HasIndex(e => e.Companyname)
                    .HasName("fk_asdu140fltacttype_manu");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actcode)
                    .HasColumnName("actcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actname)
                    .IsRequired()
                    .HasColumnName("actname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Companyname)
                    .IsRequired()
                    .HasColumnName("companyname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.CompanynameNavigation)
                    .WithMany(p => p.ImAsdu140Fltacttype)
                    .HasForeignKey(d => d.Companyname)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_asdu140fltacttype_manu");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImAsdu140Fltacttype)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_asdu140fltacttype");
            });

            modelBuilder.Entity<ImAsdu140Fltcurrinfo>(entity =>
            {
                entity.HasKey(e => new { e.Companyname, e.Currindcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_asdu140_fltcurrinfo");

                entity.Property(e => e.Companyname)
                    .HasColumnName("companyname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Currindcode)
                    .HasColumnName("currindcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Coeff).HasColumnName("coeff");

                entity.Property(e => e.Currname)
                    .IsRequired()
                    .HasColumnName("currname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Precise)
                    .HasColumnName("precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.CompanynameNavigation)
                    .WithMany(p => p.ImAsdu140Fltcurrinfo)
                    .HasForeignKey(d => d.Companyname)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_asdu140fltcurr_manu");
            });

            modelBuilder.Entity<ImAsdu142info>(entity =>
            {
                entity.HasKey(e => new { e.Companyname, e.Valuetype, e.Valueindex })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0, 0 });

                entity.ToTable("im_asdu142info");

                entity.Property(e => e.Companyname)
                    .HasColumnName("companyname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Valuetype)
                    .HasColumnName("valuetype")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Valueindex)
                    .HasColumnName("valueindex")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Coeff).HasColumnName("coeff");

                entity.Property(e => e.Parseformat)
                    .HasColumnName("parseformat")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Parsemode)
                    .IsRequired()
                    .HasColumnName("parsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Precise)
                    .HasColumnName("precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Valuename)
                    .IsRequired()
                    .HasColumnName("valuename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.CompanynameNavigation)
                    .WithMany(p => p.ImAsdu142info)
                    .HasForeignKey(d => d.Companyname)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_asdu142info_manu");
            });

            modelBuilder.Entity<ImAutoreport>(entity =>
            {
                entity.HasKey(e => e.Rptid)
                    .HasName("PRIMARY");

                entity.ToTable("im_autoreport");

                entity.Property(e => e.Rptid)
                    .HasColumnName("rptid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Createtime)
                    .HasColumnName("createtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Rptname)
                    .IsRequired()
                    .HasColumnName("rptname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rpttype)
                    .IsRequired()
                    .HasColumnName("rpttype")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Savepath)
                    .HasColumnName("savepath")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.State)
                    .IsRequired()
                    .HasColumnName("state_")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Tempfileobjid)
                    .HasColumnName("tempfileobjid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImAutoreportitem>(entity =>
            {
                entity.HasKey(e => e.Itemid)
                    .HasName("PRIMARY");

                entity.ToTable("im_autoreportitem");

                entity.HasIndex(e => e.Rptid)
                    .HasName("fk_autoreportitem_report");

                entity.Property(e => e.Itemid)
                    .HasColumnName("itemid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Colno)
                    .HasColumnName("colno")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataid)
                    .HasColumnName("dataid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dataname)
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Itemtype)
                    .IsRequired()
                    .HasColumnName("itemtype")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rowno)
                    .HasColumnName("rowno")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Rptid)
                    .IsRequired()
                    .HasColumnName("rptid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Showunit)
                    .IsRequired()
                    .HasColumnName("showunit")
                    .HasColumnType("varchar(5)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Time1)
                    .HasColumnName("time1")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Time2)
                    .HasColumnName("time2")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Timecell)
                    .HasColumnName("timecell")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Rpt)
                    .WithMany(p => p.ImAutoreportitem)
                    .HasForeignKey(d => d.Rptid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_autoreportitem_report");
            });

            modelBuilder.Entity<ImBreakernoenum>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.Enumindex })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_breakernoenum");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Enumindex)
                    .HasColumnName("enumindex")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Enumname)
                    .IsRequired()
                    .HasColumnName("enumname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImBreakernoenum)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_breakernoenum");
            });

            modelBuilder.Entity<ImCtrlworddef>(entity =>
            {
                entity.HasKey(e => e.Typeid)
                    .HasName("PRIMARY");

                entity.ToTable("im_ctrlworddef");

                entity.HasIndex(e => e.Manufacturer)
                    .HasName("fk_ctrlworddef_manu");

                entity.Property(e => e.Typeid)
                    .HasColumnName("typeid")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Bit10Meanings)
                    .HasColumnName("bit10_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit11Meanings)
                    .HasColumnName("bit11_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit12Meanings)
                    .HasColumnName("bit12_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit13Meanings)
                    .HasColumnName("bit13_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit14Meanings)
                    .HasColumnName("bit14_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit15Meanings)
                    .HasColumnName("bit15_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit16Meanings)
                    .HasColumnName("bit16_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit17Meanings)
                    .HasColumnName("bit17_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit18Meanings)
                    .HasColumnName("bit18_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit19Meanings)
                    .HasColumnName("bit19_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit1Meanings)
                    .HasColumnName("bit1_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit20Meanings)
                    .HasColumnName("bit20_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit21Meanings)
                    .HasColumnName("bit21_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit22Meanings)
                    .HasColumnName("bit22_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit23Meanings)
                    .HasColumnName("bit23_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit24Meanings)
                    .HasColumnName("bit24_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit25Meanings)
                    .HasColumnName("bit25_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit26Meanings)
                    .HasColumnName("bit26_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit27Meanings)
                    .HasColumnName("bit27_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit28Meanings)
                    .HasColumnName("bit28_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit29Meanings)
                    .HasColumnName("bit29_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit2Meanings)
                    .HasColumnName("bit2_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit30Meanings)
                    .HasColumnName("bit30_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit31Meanings)
                    .HasColumnName("bit31_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit32Meanings)
                    .HasColumnName("bit32_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit3Meanings)
                    .HasColumnName("bit3_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit4Meanings)
                    .HasColumnName("bit4_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit5Meanings)
                    .HasColumnName("bit5_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit6Meanings)
                    .HasColumnName("bit6_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit7Meanings)
                    .HasColumnName("bit7_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit8Meanings)
                    .HasColumnName("bit8_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bit9Meanings)
                    .HasColumnName("bit9_meanings")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Manufacturer)
                    .IsRequired()
                    .HasColumnName("manufacturer")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.TypeComment)
                    .HasColumnName("type_comment")
                    .HasColumnType("varchar(180)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.ManufacturerNavigation)
                    .WithMany(p => p.ImCtrlworddef)
                    .HasForeignKey(d => d.Manufacturer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_ctrlworddef_manu");
            });

            modelBuilder.Entity<ImCurve>(entity =>
            {
                entity.ToTable("im_curve");

                entity.HasIndex(e => e.Curvetype)
                    .HasName("fk_curve_type");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Cretime)
                    .HasColumnName("cretime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Curvename)
                    .IsRequired()
                    .HasColumnName("curvename")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Curvetype)
                    .IsRequired()
                    .HasColumnName("curvetype")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Minutes)
                    .HasColumnName("minutes")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Sampling)
                    .HasColumnName("sampling")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Ymax).HasColumnName("ymax");

                entity.Property(e => e.Ymin).HasColumnName("ymin");

                entity.HasOne(d => d.CurvetypeNavigation)
                    .WithMany(p => p.ImCurve)
                    .HasForeignKey(d => d.Curvetype)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_curve_type");
            });

            modelBuilder.Entity<ImCurveType>(entity =>
            {
                entity.HasKey(e => e.Cvetypecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_curve_type");

                entity.Property(e => e.Cvetypecode)
                    .HasColumnName("cvetypecode")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Cvetypename)
                    .IsRequired()
                    .HasColumnName("cvetypename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImCurveitem>(entity =>
            {
                entity.HasKey(e => new { e.Curveid, e.Dataid })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_curveitem");

                entity.HasIndex(e => e.Dataid)
                    .HasName("fk_curveitem_devicedata");

                entity.Property(e => e.Curveid)
                    .HasColumnName("curveid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dataid)
                    .HasColumnName("dataid")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Linecolor)
                    .HasColumnName("linecolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Linewidth)
                    .HasColumnName("linewidth")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.Curve)
                    .WithMany(p => p.ImCurveitem)
                    .HasForeignKey(d => d.Curveid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_curveitem_curve");

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImCurveitem)
                    .HasForeignKey(d => d.Dataid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_curveitem_devicedata");
            });

            modelBuilder.Entity<ImDataviewer>(entity =>
            {
                entity.ToTable("im_dataviewer");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Colcnt)
                    .HasColumnName("colcnt")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Namebold)
                    .HasColumnName("namebold")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Namecharset)
                    .HasColumnName("namecharset")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Namecolor)
                    .HasColumnName("namecolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Namefontname)
                    .HasColumnName("namefontname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Nameitalic)
                    .HasColumnName("nameitalic")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Namesize)
                    .HasColumnName("namesize")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Namestrikeout)
                    .HasColumnName("namestrikeout")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Nameunderline)
                    .HasColumnName("nameunderline")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Valuebold)
                    .HasColumnName("valuebold")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Valuecharset)
                    .HasColumnName("valuecharset")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Valuecolor)
                    .HasColumnName("valuecolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Valuecolwidth)
                    .HasColumnName("valuecolwidth")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Valuefontname)
                    .HasColumnName("valuefontname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Valueitalic)
                    .HasColumnName("valueitalic")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Valuesize)
                    .HasColumnName("valuesize")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Valuestrikeout)
                    .HasColumnName("valuestrikeout")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Valueunderline)
                    .HasColumnName("valueunderline")
                    .HasColumnType("smallint(6)");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDataviewer)
                    .HasForeignKey<ImDataviewer>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_dataviewer_glyph");
            });

            modelBuilder.Entity<ImDataviewerData>(entity =>
            {
                entity.HasKey(e => new { e.Dataviewerid, e.Dataid })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_dataviewer_data");

                entity.Property(e => e.Dataviewerid)
                    .HasColumnName("dataviewerid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dataid)
                    .HasColumnName("dataid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.Dataviewer)
                    .WithMany(p => p.ImDataviewerData)
                    .HasForeignKey(d => d.Dataviewerid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_dvd_dv");
            });

            modelBuilder.Entity<ImDevctgy>(entity =>
            {
                entity.HasKey(e => e.Devctgycode)
                    .HasName("PRIMARY");

                entity.ToTable("im_devctgy");

                entity.Property(e => e.Devctgycode)
                    .HasColumnName("devctgycode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devctgyname)
                    .IsRequired()
                    .HasColumnName("devctgyname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImDevicedata>(entity =>
            {
                entity.ToTable("im_devicedata");

                entity.HasIndex(e => e.Deviceid)
                    .HasName("fk_devicedata_protdevice");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Beizhu)
                    .HasColumnName("beizhu")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .IsRequired()
                    .HasColumnName("domain")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Srctempdataid)
                    .HasColumnName("srctempdataid")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDevicedata)
                    .HasForeignKey(d => d.Deviceid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_devicedata_protdevice");
            });

            modelBuilder.Entity<ImDevicedataTmp>(entity =>
            {
                entity.ToTable("im_devicedata_tmp");

                entity.HasIndex(e => e.Deviceid)
                    .HasName("deviceid");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Beizhu)
                    .HasColumnName("beizhu")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.CfgfileBz)
                    .HasColumnName("cfgfile_bz")
                    .HasColumnType("int(11)");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .IsRequired()
                    .HasColumnName("domain")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Secaddr101)
                    .HasColumnName("secaddr101")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Toscada)
                    .HasColumnName("toscada")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDevicedataTmp)
                    .HasForeignKey(d => d.Deviceid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_devicedata_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImDevicedz>(entity =>
            {
                entity.ToTable("im_devicedz");

                entity.HasIndex(e => e.Deviceid)
                    .HasName("fk_devicedz_protdevice");

                entity.HasIndex(e => e.DzType)
                    .HasName("fk_devicedz_dztype");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.CtrlwordTypeid)
                    .HasColumnName("ctrlword_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzCoeff).HasColumnName("dz_coeff");

                entity.Property(e => e.DzCoeff1).HasColumnName("dz_coeff_1");

                entity.Property(e => e.DzComment)
                    .HasColumnName("dz_comment")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzIndex)
                    .HasColumnName("dz_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzMax).HasColumnName("dz_max");

                entity.Property(e => e.DzMin).HasColumnName("dz_min");

                entity.Property(e => e.DzName)
                    .HasColumnName("dz_name")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzPrecise)
                    .HasColumnName("dz_precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzPrecise1)
                    .HasColumnName("dz_precise_1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzRange)
                    .HasColumnName("dz_range")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzType)
                    .HasColumnName("dz_type")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzUnit)
                    .HasColumnName("dz_unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzUnit1)
                    .HasColumnName("dz_unit_1")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzUnitcvtCoeff).HasColumnName("dz_unitcvt_coeff");

                entity.Property(e => e.EnumTypeid)
                    .HasColumnName("enum_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Hidden)
                    .IsRequired()
                    .HasColumnName("hidden")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Readonly)
                    .HasColumnName("readonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.RelatectId)
                    .HasColumnName("relatect_id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelateptId)
                    .HasColumnName("relatept_id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDevicedz)
                    .HasForeignKey(d => d.Deviceid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_devicedz_protdevice");

                entity.HasOne(d => d.DzTypeNavigation)
                    .WithMany(p => p.ImDevicedz)
                    .HasForeignKey(d => d.DzType)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_devicedz_dztype");
            });

            modelBuilder.Entity<ImDevicedzTmp>(entity =>
            {
                entity.ToTable("im_devicedz_tmp");

                entity.HasIndex(e => e.Deviceid)
                    .HasName("deviceid");

                entity.HasIndex(e => e.DzType)
                    .HasName("dz_type");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.CtrlwordTypeid)
                    .HasColumnName("ctrlword_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzCoeff).HasColumnName("dz_coeff");

                entity.Property(e => e.DzCoeff1).HasColumnName("dz_coeff_1");

                entity.Property(e => e.DzComment)
                    .HasColumnName("dz_comment")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzIndex)
                    .HasColumnName("dz_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzMax).HasColumnName("dz_max");

                entity.Property(e => e.DzMin).HasColumnName("dz_min");

                entity.Property(e => e.DzName)
                    .HasColumnName("dz_name")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzPrecise)
                    .HasColumnName("dz_precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzPrecise1)
                    .HasColumnName("dz_precise_1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzRange)
                    .HasColumnName("dz_range")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzType)
                    .HasColumnName("dz_type")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzUnit)
                    .HasColumnName("dz_unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzUnit1)
                    .HasColumnName("dz_unit_1")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzUnitcvtCoeff).HasColumnName("dz_unitcvt_coeff");

                entity.Property(e => e.EnumTypeid)
                    .HasColumnName("enum_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Hidden)
                    .IsRequired()
                    .HasColumnName("hidden")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Readonly)
                    .HasColumnName("readonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.RelatectId)
                    .HasColumnName("relatect_id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelateptId)
                    .HasColumnName("relatept_id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImDevicedzTmp)
                    .HasForeignKey(d => d.Deviceid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_devicedz_tmp_ibfk_2");

                entity.HasOne(d => d.DzTypeNavigation)
                    .WithMany(p => p.ImDevicedzTmp)
                    .HasForeignKey(d => d.DzType)
                    .HasConstraintName("im_devicedz_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImDevicedzenum>(entity =>
            {
                entity.HasKey(e => new { e.EnumTypeid, e.EnumIndex })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_devicedzenum");

                entity.HasIndex(e => e.Manufacturer)
                    .HasName("fk_devicedzenum_manu");

                entity.Property(e => e.EnumTypeid)
                    .HasColumnName("enum_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumIndex)
                    .HasColumnName("enum_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumComment)
                    .HasColumnName("enum_comment")
                    .HasColumnType("varchar(200)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Manufacturer)
                    .IsRequired()
                    .HasColumnName("manufacturer")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.ManufacturerNavigation)
                    .WithMany(p => p.ImDevicedzenum)
                    .HasForeignKey(d => d.Manufacturer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_devicedzenum_manu");
            });

            modelBuilder.Entity<ImDevicedzenum2>(entity =>
            {
                entity.HasKey(e => new { e.EnumTypeid, e.EnumByte, e.EnumIndex })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0, 0 });

                entity.ToTable("im_devicedzenum2");

                entity.Property(e => e.EnumTypeid)
                    .HasColumnName("enum_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumByte)
                    .HasColumnName("enum_byte")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.EnumIndex)
                    .HasColumnName("enum_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumComment)
                    .HasColumnName("enum_comment")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImDevicedzenumpu>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.EnumTypeid, e.EnumIndex })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0, 0 });

                entity.ToTable("im_devicedzenumpu");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumTypeid)
                    .HasColumnName("enum_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumIndex)
                    .HasColumnName("enum_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.EnumComment)
                    .HasColumnName("enum_comment")
                    .HasColumnType("varchar(200)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImDevicedzenumpu)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_devicedzenumpu_puctgy");
            });

            modelBuilder.Entity<ImDeviceva>(entity =>
            {
                entity.ToTable("im_deviceva");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Expr)
                    .HasColumnName("expr")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Vaname)
                    .IsRequired()
                    .HasColumnName("vaname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceva)
                    .HasForeignKey<ImDeviceva>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_deviceva_devicedata");
            });

            modelBuilder.Entity<ImDeviceyc>(entity =>
            {
                entity.ToTable("im_deviceyc");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Cof).HasColumnName("cof");

                entity.Property(e => e.Cof2).HasColumnName("cof2");

                entity.Property(e => e.Downlimit).HasColumnName("downlimit");

                entity.Property(e => e.Intl4save).HasColumnName("intl4save");

                entity.Property(e => e.Precise)
                    .HasColumnName("precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Precise2)
                    .HasColumnName("precise2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Savemode)
                    .IsRequired()
                    .HasColumnName("savemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit2)
                    .HasColumnName("unit2")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Uplimit).HasColumnName("uplimit");

                entity.Property(e => e.Uplimit4stat).HasColumnName("uplimit4stat");

                entity.Property(e => e.Ycname)
                    .IsRequired()
                    .HasColumnName("ycname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceyc)
                    .HasForeignKey<ImDeviceyc>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_deviceyc_devicedata");
            });

            modelBuilder.Entity<ImDeviceycTmp>(entity =>
            {
                entity.ToTable("im_deviceyc_tmp");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Cof).HasColumnName("cof");

                entity.Property(e => e.Cof2).HasColumnName("cof2");

                entity.Property(e => e.Downlimit).HasColumnName("downlimit");

                entity.Property(e => e.Intl4save).HasColumnName("intl4save");

                entity.Property(e => e.Precise)
                    .HasColumnName("precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Precise2)
                    .HasColumnName("precise2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Savemode)
                    .IsRequired()
                    .HasColumnName("savemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit2)
                    .HasColumnName("unit2")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Uplimit).HasColumnName("uplimit");

                entity.Property(e => e.Uplimit4stat).HasColumnName("uplimit4stat");

                entity.Property(e => e.Ycname)
                    .IsRequired()
                    .HasColumnName("ycname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceycTmp)
                    .HasForeignKey<ImDeviceycTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_deviceyc_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImDeviceyk>(entity =>
            {
                entity.ToTable("im_deviceyk");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Isresetcmd)
                    .IsRequired()
                    .HasColumnName("isresetcmd")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lockmode)
                    .IsRequired()
                    .HasColumnName("lockmode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.LockmodeOff)
                    .IsRequired()
                    .HasColumnName("lockmode_off")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Prestate4yk)
                    .HasColumnName("prestate4yk")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Prestate4ykOff)
                    .HasColumnName("prestate4yk_off")
                    .HasColumnType("int(11)");

                entity.Property(e => e.PreyxId)
                    .HasColumnName("preyx_id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.PreyxIdOff)
                    .HasColumnName("preyx_id_off")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelatedyxId)
                    .HasColumnName("relatedyx_id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swoffstr)
                    .HasColumnName("swoffstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swonstr)
                    .HasColumnName("swonstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swuncertstr)
                    .HasColumnName("swuncertstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.YkType)
                    .IsRequired()
                    .HasColumnName("yk_type")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ykname)
                    .IsRequired()
                    .HasColumnName("ykname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceyk)
                    .HasForeignKey<ImDeviceyk>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_deviceyk_devicedata");
            });

            modelBuilder.Entity<ImDeviceykTmp>(entity =>
            {
                entity.ToTable("im_deviceyk_tmp");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Isresetcmd)
                    .IsRequired()
                    .HasColumnName("isresetcmd")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lockmode)
                    .IsRequired()
                    .HasColumnName("lockmode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.LockmodeOff)
                    .IsRequired()
                    .HasColumnName("lockmode_off")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Prestate4yk)
                    .HasColumnName("prestate4yk")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Prestate4ykOff)
                    .HasColumnName("prestate4yk_off")
                    .HasColumnType("int(11)");

                entity.Property(e => e.PreyxId)
                    .HasColumnName("preyx_id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.PreyxIdOff)
                    .HasColumnName("preyx_id_off")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelatedyxId)
                    .HasColumnName("relatedyx_id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swoffstr)
                    .HasColumnName("swoffstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swonstr)
                    .HasColumnName("swonstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swuncertstr)
                    .HasColumnName("swuncertstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.YkType)
                    .IsRequired()
                    .HasColumnName("yk_type")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ykname)
                    .IsRequired()
                    .HasColumnName("ykname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceykTmp)
                    .HasForeignKey<ImDeviceykTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_deviceyk_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImDeviceym>(entity =>
            {
                entity.ToTable("im_deviceym");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Cof).HasColumnName("cof");

                entity.Property(e => e.Maxvalue).HasColumnName("maxvalue");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ymname)
                    .IsRequired()
                    .HasColumnName("ymname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceym)
                    .HasForeignKey<ImDeviceym>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_deviceym_devicedata");
            });

            modelBuilder.Entity<ImDeviceymTmp>(entity =>
            {
                entity.ToTable("im_deviceym_tmp");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Cof).HasColumnName("cof");

                entity.Property(e => e.Maxvalue).HasColumnName("maxvalue");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ymname)
                    .IsRequired()
                    .HasColumnName("ymname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceymTmp)
                    .HasForeignKey<ImDeviceymTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_deviceym_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImDeviceyx>(entity =>
            {
                entity.ToTable("im_deviceyx");

                entity.HasIndex(e => e.Alertlevel)
                    .HasName("fk_deviceyx_level");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alertlevel)
                    .IsRequired()
                    .HasColumnName("alertlevel")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Normalstate)
                    .IsRequired()
                    .HasColumnName("normalstate")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swoffstr)
                    .HasColumnName("swoffstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swonstr)
                    .HasColumnName("swonstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swuncertstr)
                    .HasColumnName("swuncertstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.YxType)
                    .HasColumnName("yx_type")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yxname)
                    .IsRequired()
                    .HasColumnName("yxname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.AlertlevelNavigation)
                    .WithMany(p => p.ImDeviceyx)
                    .HasForeignKey(d => d.Alertlevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_deviceyx_level");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceyx)
                    .HasForeignKey<ImDeviceyx>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_deviceyx_devicedata");
            });

            modelBuilder.Entity<ImDeviceyxSrcdevice>(entity =>
            {
                entity.ToTable("im_deviceyx_srcdevice");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcdevid)
                    .IsRequired()
                    .HasColumnName("srcdevid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yxdataid)
                    .IsRequired()
                    .HasColumnName("yxdataid")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImDeviceyxTmp>(entity =>
            {
                entity.ToTable("im_deviceyx_tmp");

                entity.HasIndex(e => e.Alertlevel)
                    .HasName("alertlevel");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alertlevel)
                    .IsRequired()
                    .HasColumnName("alertlevel")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Normalstate)
                    .IsRequired()
                    .HasColumnName("normalstate")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swoffstr)
                    .HasColumnName("swoffstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swonstr)
                    .HasColumnName("swonstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swuncertstr)
                    .HasColumnName("swuncertstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.YxType)
                    .HasColumnName("yx_type")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yxname)
                    .IsRequired()
                    .HasColumnName("yxname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.AlertlevelNavigation)
                    .WithMany(p => p.ImDeviceyxTmp)
                    .HasForeignKey(d => d.Alertlevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_deviceyx_tmp_ibfk_2");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDeviceyxTmp)
                    .HasForeignKey<ImDeviceyxTmp>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_deviceyx_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImDiagram>(entity =>
            {
                entity.ToTable("im_diagram");

                entity.HasIndex(e => e.Prjid)
                    .HasName("fk_imdiagram_project");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Createtime)
                    .HasColumnName("createtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Creator)
                    .HasColumnName("creator")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gridsize)
                    .HasColumnName("gridsize")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Height)
                    .HasColumnName("height")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Prjid)
                    .IsRequired()
                    .HasColumnName("prjid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Pwroffcolor)
                    .HasColumnName("pwroffcolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Pwroncolor)
                    .HasColumnName("pwroncolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Scale).HasColumnName("scale");

                entity.Property(e => e.Showgrid)
                    .HasColumnName("showgrid")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Switchoffcolor)
                    .HasColumnName("switchoffcolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Switchoncolor)
                    .HasColumnName("switchoncolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Switchunknowncolor)
                    .HasColumnName("switchunknowncolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Width)
                    .HasColumnName("width")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImDiagram)
                    .HasForeignKey<ImDiagram>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_imdiagram_glyph");

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImDiagram)
                    .HasForeignKey(d => d.Prjid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_imdiagram_project");
            });

            modelBuilder.Entity<ImDzcheckrule>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.Dzname })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_dzcheckrule");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dzname)
                    .HasColumnName("dzname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Destdzname)
                    .HasColumnName("destdzname")
                    .HasColumnType("varchar(1000)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param)
                    .HasColumnName("param")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ruledesc)
                    .HasColumnName("ruledesc")
                    .HasColumnType("varchar(1000)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rulename)
                    .IsRequired()
                    .HasColumnName("rulename")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImDztype>(entity =>
            {
                entity.HasKey(e => e.Dztypeid)
                    .HasName("PRIMARY");

                entity.ToTable("im_dztype");

                entity.Property(e => e.Dztypeid)
                    .HasColumnName("dztypeid")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Dztype)
                    .IsRequired()
                    .HasColumnName("dztype")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImEventflag>(entity =>
            {
                entity.HasKey(e => new { e.Devicectgy, e.Evtflagcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_eventflag");

                entity.Property(e => e.Devicectgy)
                    .HasColumnName("devicectgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Evtflagcode)
                    .HasColumnName("evtflagcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Param1mean)
                    .IsRequired()
                    .HasColumnName("param1mean")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param1sym)
                    .HasColumnName("param1sym")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param1valuetype)
                    .HasColumnName("param1valuetype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param2mean)
                    .IsRequired()
                    .HasColumnName("param2mean")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param2sym)
                    .HasColumnName("param2sym")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param2valuetype)
                    .HasColumnName("param2valuetype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param3mean)
                    .HasColumnName("param3mean")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param3sym)
                    .HasColumnName("param3sym")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Param3valuetype)
                    .HasColumnName("param3valuetype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.DevicectgyNavigation)
                    .WithMany(p => p.ImEventflag)
                    .HasForeignKey(d => d.Devicectgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_evengflag_devctgy");
            });

            modelBuilder.Entity<ImEventparam>(entity =>
            {
                entity.HasKey(e => new { e.Devicectgy, e.Paramvalue })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_eventparam");

                entity.Property(e => e.Devicectgy)
                    .HasColumnName("devicectgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Paramvalue)
                    .HasColumnName("paramvalue")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Paramdesc)
                    .IsRequired()
                    .HasColumnName("paramdesc")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.DevicectgyNavigation)
                    .WithMany(p => p.ImEventparam)
                    .HasForeignKey(d => d.Devicectgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_eventparam_devctgy");
            });

            modelBuilder.Entity<ImEventtype>(entity =>
            {
                entity.HasKey(e => new { e.Evtcode, e.Puctgycode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_eventtype");

                entity.HasIndex(e => e.Alertlevel)
                    .HasName("fk_eventtype_level");

                entity.HasIndex(e => e.Puctgycode)
                    .HasName("fk_eventtype_puctgy");

                entity.Property(e => e.Evtcode)
                    .HasColumnName("evtcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Alertlevel)
                    .IsRequired()
                    .HasColumnName("alertlevel")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Evtname)
                    .IsRequired()
                    .HasColumnName("evtname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Isyicisb)
                    .IsRequired()
                    .HasColumnName("isyicisb")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.AlertlevelNavigation)
                    .WithMany(p => p.ImEventtype)
                    .HasForeignKey(d => d.Alertlevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_eventtype_level");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImEventtype)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_eventtype_puctgy");
            });

            modelBuilder.Entity<ImEventtype2010>(entity =>
            {
                entity.HasKey(e => new { e.Devicectgy, e.Evtcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_eventtype_2010");

                entity.HasIndex(e => e.Alertlevel)
                    .HasName("fk_eventtype2010_level");

                entity.Property(e => e.Devicectgy)
                    .HasColumnName("devicectgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Evtcode)
                    .HasColumnName("evtcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Alertlevel)
                    .IsRequired()
                    .HasColumnName("alertlevel")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Evtname)
                    .IsRequired()
                    .HasColumnName("evtname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.AlertlevelNavigation)
                    .WithMany(p => p.ImEventtype2010)
                    .HasForeignKey(d => d.Alertlevel)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_eventtype2010_level");

                entity.HasOne(d => d.DevicectgyNavigation)
                    .WithMany(p => p.ImEventtype2010)
                    .HasForeignKey(d => d.Devicectgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_eventtype2010_devctgy");
            });

            modelBuilder.Entity<ImEventtype2010Deviceyktmp>(entity =>
            {
                entity.HasKey(e => e.Recid)
                    .HasName("PRIMARY");

                entity.ToTable("im_eventtype2010_deviceyktmp");

                entity.Property(e => e.Recid)
                    .HasColumnName("recid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicectgy)
                    .IsRequired()
                    .HasColumnName("devicectgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Evtcode)
                    .HasColumnName("evtcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.YktmpId)
                    .IsRequired()
                    .HasColumnName("yktmp_id")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImFaultacttype>(entity =>
            {
                entity.HasKey(e => new { e.Actcode, e.Faultcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_faultacttype");

                entity.HasIndex(e => e.Faultcode)
                    .HasName("fk_faultacttype_faulttype");

                entity.Property(e => e.Actcode)
                    .HasColumnName("actcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Faultcode)
                    .HasColumnName("faultcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actname)
                    .IsRequired()
                    .HasColumnName("actname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.FaultcodeNavigation)
                    .WithMany(p => p.ImFaultacttype)
                    .HasForeignKey(d => d.Faultcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_faultacttype_faulttype");
            });

            modelBuilder.Entity<ImFaultacttype2010>(entity =>
            {
                entity.HasKey(e => new { e.Flttypeid, e.Actcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_faultacttype_2010");

                entity.Property(e => e.Flttypeid)
                    .HasColumnName("flttypeid")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Actcode)
                    .HasColumnName("actcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actname)
                    .IsRequired()
                    .HasColumnName("actname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Flttype)
                    .WithMany(p => p.ImFaultacttype2010)
                    .HasForeignKey(d => d.Flttypeid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_faultacttype_faulttype2010");
            });

            modelBuilder.Entity<ImFaultacttype20102>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.Actcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_faultacttype_2010_2");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actcode)
                    .HasColumnName("actcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actname)
                    .IsRequired()
                    .HasColumnName("actname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImFaultacttype20102)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_faultacttype2010_2_puctgy");
            });

            modelBuilder.Entity<ImFaultacttypePd>(entity =>
            {
                entity.HasKey(e => new { e.Actcode, e.Faultcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_faultacttype_pd");

                entity.HasIndex(e => e.Faultcode)
                    .HasName("fk_faultacttypepd_faulttypepd");

                entity.Property(e => e.Actcode)
                    .HasColumnName("actcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Faultcode)
                    .HasColumnName("faultcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Actname)
                    .IsRequired()
                    .HasColumnName("actname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.FaultcodeNavigation)
                    .WithMany(p => p.ImFaultacttypePd)
                    .HasForeignKey(d => d.Faultcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_faultacttypepd_faulttypepd");
            });

            modelBuilder.Entity<ImFaultparam>(entity =>
            {
                entity.HasKey(e => e.Paramcode)
                    .HasName("PRIMARY");

                entity.ToTable("im_faultparam");

                entity.Property(e => e.Paramcode)
                    .HasColumnName("paramcode")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Paramcof1).HasColumnName("paramcof1");

                entity.Property(e => e.Paramcof2).HasColumnName("paramcof2");

                entity.Property(e => e.Paramname)
                    .IsRequired()
                    .HasColumnName("paramname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Paramsym1)
                    .HasColumnName("paramsym1")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Paramsym2)
                    .HasColumnName("paramsym2")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImFaultparamPd>(entity =>
            {
                entity.HasKey(e => new { e.Paramcode, e.Faultcode })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_faultparam_pd");

                entity.HasIndex(e => e.Faultcode)
                    .HasName("fk_faultparampd_faulttypepd");

                entity.Property(e => e.Paramcode)
                    .HasColumnName("paramcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Faultcode)
                    .HasColumnName("faultcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Paramcof1).HasColumnName("paramcof1");

                entity.Property(e => e.Paramcof2).HasColumnName("paramcof2");

                entity.Property(e => e.Paramname)
                    .IsRequired()
                    .HasColumnName("paramname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Paramsym1)
                    .HasColumnName("paramsym1")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Paramsym2)
                    .HasColumnName("paramsym2")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.FaultcodeNavigation)
                    .WithMany(p => p.ImFaultparamPd)
                    .HasForeignKey(d => d.Faultcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_faultparampd_faulttypepd");
            });

            modelBuilder.Entity<ImFaultreportitem>(entity =>
            {
                entity.HasKey(e => e.Itemname)
                    .HasName("PRIMARY");

                entity.ToTable("im_faultreportitem");

                entity.Property(e => e.Itemname)
                    .HasColumnName("itemname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImFaulttype>(entity =>
            {
                entity.HasKey(e => e.Faultcode)
                    .HasName("PRIMARY");

                entity.ToTable("im_faulttype");

                entity.Property(e => e.Faultcode)
                    .HasColumnName("faultcode")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Faultname)
                    .IsRequired()
                    .HasColumnName("faultname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImFaulttype2010>(entity =>
            {
                entity.ToTable("im_faulttype_2010");

                entity.HasIndex(e => e.Devicectgy)
                    .HasName("fk_faulttype_2010_devctgy");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicectgy)
                    .IsRequired()
                    .HasColumnName("devicectgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Faultcode)
                    .HasColumnName("faultcode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Faultname)
                    .IsRequired()
                    .HasColumnName("faultname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.DevicectgyNavigation)
                    .WithMany(p => p.ImFaulttype2010)
                    .HasForeignKey(d => d.Devicectgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_faulttype_2010_devctgy");
            });

            modelBuilder.Entity<ImFaulttypePd>(entity =>
            {
                entity.HasKey(e => e.Faultcode)
                    .HasName("PRIMARY");

                entity.ToTable("im_faulttype_pd");

                entity.Property(e => e.Faultcode)
                    .HasColumnName("faultcode")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Faultname)
                    .IsRequired()
                    .HasColumnName("faultname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImGateway>(entity =>
            {
                entity.HasKey(e => e.Gatewayid)
                    .HasName("PRIMARY");

                entity.ToTable("im_gateway");

                entity.HasIndex(e => e.Statcode)
                    .HasName("fk_gateway_station");

                entity.Property(e => e.Gatewayid)
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayip1)
                    .HasColumnName("gatewayip1")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayip2)
                    .HasColumnName("gatewayip2")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayname)
                    .IsRequired()
                    .HasColumnName("gatewayname")
                    .HasColumnType("varchar(200)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayport1)
                    .HasColumnName("gatewayport1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Gatewayport2)
                    .HasColumnName("gatewayport2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Phyaddr)
                    .HasColumnName("phyaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Protocol)
                    .HasColumnName("protocol")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.StatcodeNavigation)
                    .WithMany(p => p.ImGateway)
                    .HasForeignKey(d => d.Statcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_gateway_station");
            });

            modelBuilder.Entity<ImGlyph>(entity =>
            {
                entity.ToTable("im_glyph");

                entity.HasIndex(e => e.Diagid)
                    .HasName("ix_imglyph_diagid");

                entity.HasIndex(e => e.Parentid)
                    .HasName("ix_imglyph_parentid");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bkcolor)
                    .HasColumnName("bkcolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Bottom).HasColumnName("bottom");

                entity.Property(e => e.Diagid)
                    .IsRequired()
                    .HasColumnName("diagid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Direction)
                    .HasColumnName("direction")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Glyphtype)
                    .HasColumnName("glyphtype")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Left).HasColumnName("left");

                entity.Property(e => e.Linecolor)
                    .HasColumnName("linecolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Linestyle)
                    .HasColumnName("linestyle")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Linewidth)
                    .HasColumnName("linewidth")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Linkdiagid)
                    .HasColumnName("linkdiagid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Locked)
                    .HasColumnName("locked")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Name)
                    .HasColumnName("name")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Parentid)
                    .HasColumnName("parentid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Pwroffcolor)
                    .HasColumnName("pwroffcolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Pwroncolor)
                    .HasColumnName("pwroncolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Pwronthreshold).HasColumnName("pwronthreshold");

                entity.Property(e => e.Reserved1)
                    .HasColumnName("reserved1")
                    .HasColumnType("longtext")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Reserved2)
                    .HasColumnName("reserved2")
                    .HasColumnType("longtext")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Right).HasColumnName("right");

                entity.Property(e => e.State)
                    .HasColumnName("state")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Switchoffcolor)
                    .HasColumnName("switchoffcolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Switchoncolor)
                    .HasColumnName("switchoncolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Switchunknowncolor)
                    .HasColumnName("switchunknowncolor")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Top).HasColumnName("top");

                entity.Property(e => e.Transparent)
                    .HasColumnName("transparent")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.ZOrder)
                    .HasColumnName("z_order")
                    .HasColumnType("smallint(6)");
            });

            modelBuilder.Entity<ImGlyphdefprop>(entity =>
            {
                entity.HasKey(e => e.Glyphtype)
                    .HasName("PRIMARY");

                entity.ToTable("im_glyphdefprop");

                entity.Property(e => e.Glyphtype)
                    .HasColumnName("glyphtype")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Glyphname)
                    .IsRequired()
                    .HasColumnName("glyphname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Height).HasColumnName("height");

                entity.Property(e => e.Width).HasColumnName("width");
            });

            modelBuilder.Entity<ImGlyphdevdata>(entity =>
            {
                entity.HasKey(e => e.Glyphid)
                    .HasName("PRIMARY");

                entity.ToTable("im_glyphdevdata");

                entity.Property(e => e.Glyphid)
                    .HasColumnName("glyphid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Controldataid)
                    .HasColumnName("controldataid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Controldataname)
                    .HasColumnName("controldataname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Measuredataid)
                    .HasColumnName("measuredataid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Measuredataname)
                    .HasColumnName("measuredataname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Glyph)
                    .WithOne(p => p.ImGlyphdevdata)
                    .HasForeignKey<ImGlyphdevdata>(d => d.Glyphid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_glyphdevdata_glyph");
            });

            modelBuilder.Entity<ImGlyphhots>(entity =>
            {
                entity.HasKey(e => new { e.Glyphid, e.Hotindex })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_glyphhots");

                entity.Property(e => e.Glyphid)
                    .HasColumnName("glyphid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Hotindex)
                    .HasColumnName("hotindex")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.Connectedglyph)
                    .HasColumnName("connectedglyph")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Hottype)
                    .HasColumnName("hottype")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.X).HasColumnName("x");

                entity.Property(e => e.Y).HasColumnName("y");

                entity.HasOne(d => d.Glyph)
                    .WithMany(p => p.ImGlyphhots)
                    .HasForeignKey(d => d.Glyphid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_imglyphhots_glyph");
            });

            modelBuilder.Entity<ImGrid>(entity =>
            {
                entity.ToTable("im_grid");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Colcnt)
                    .HasColumnName("colcnt")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Rowcnt)
                    .HasColumnName("rowcnt")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImGrid)
                    .HasForeignKey<ImGrid>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_imgrid_glyph");
            });

            modelBuilder.Entity<ImLabel>(entity =>
            {
                entity.ToTable("im_label");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Caption)
                    .HasColumnName("caption")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.FBold)
                    .HasColumnName("f_bold")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.FCharset)
                    .HasColumnName("f_charset")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.FColor)
                    .HasColumnName("f_color")
                    .HasColumnType("int(11)");

                entity.Property(e => e.FItalic)
                    .HasColumnName("f_italic")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.FName)
                    .HasColumnName("f_name")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.FSize)
                    .HasColumnName("f_size")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.FStrikeout)
                    .HasColumnName("f_strikeout")
                    .HasColumnType("smallint(6)");

                entity.Property(e => e.FUnderline)
                    .HasColumnName("f_underline")
                    .HasColumnType("smallint(6)");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.ImLabel)
                    .HasForeignKey<ImLabel>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_imlabel_glyph");
            });

            modelBuilder.Entity<ImLinkage>(entity =>
            {
                entity.HasKey(e => e.Lnkid)
                    .HasName("PRIMARY");

                entity.ToTable("im_linkage");

                entity.Property(e => e.Lnkid)
                    .HasColumnName("lnkid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Action)
                    .HasColumnName("action")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Actparam)
                    .HasColumnName("actparam")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lnktype)
                    .IsRequired()
                    .HasColumnName("lnktype")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Resource)
                    .IsRequired()
                    .HasColumnName("resource")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcobjid)
                    .IsRequired()
                    .HasColumnName("srcobjid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcobjtype)
                    .IsRequired()
                    .HasColumnName("srcobjtype")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.State)
                    .IsRequired()
                    .HasColumnName("state")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImManufacturer>(entity =>
            {
                entity.HasKey(e => e.Manucode)
                    .HasName("PRIMARY");

                entity.ToTable("im_manufacturer");

                entity.Property(e => e.Manucode)
                    .HasColumnName("manucode")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Manuname)
                    .IsRequired()
                    .HasColumnName("manuname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImNoticeboard>(entity =>
            {
                entity.HasKey(e => e.NbId)
                    .HasName("PRIMARY");

                entity.ToTable("im_noticeboard");

                entity.HasIndex(e => e.Statcode)
                    .HasName("fk_nb_station");

                entity.HasIndex(e => e.YkId)
                    .HasName("fk_nb_deviceyk");

                entity.Property(e => e.NbId)
                    .HasColumnName("nb_id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Description)
                    .HasColumnName("description")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Executetime)
                    .HasColumnName("executetime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Operusername)
                    .IsRequired()
                    .HasColumnName("operusername")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Operwholename)
                    .HasColumnName("operwholename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.YkId)
                    .IsRequired()
                    .HasColumnName("yk_id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.StatcodeNavigation)
                    .WithMany(p => p.ImNoticeboard)
                    .HasForeignKey(d => d.Statcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_nb_station");

                entity.HasOne(d => d.Yk)
                    .WithMany(p => p.ImNoticeboard)
                    .HasForeignKey(d => d.YkId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_nb_deviceyk");
            });

            modelBuilder.Entity<ImProgcontrol>(entity =>
            {
                entity.ToTable("im_progcontrol");

                entity.HasIndex(e => e.Prjid)
                    .HasName("fk_progcontrol_project");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Createtime)
                    .HasColumnName("createtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Prjid)
                    .IsRequired()
                    .HasColumnName("prjid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Progctlname)
                    .IsRequired()
                    .HasColumnName("progctlname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImProgcontrol)
                    .HasForeignKey(d => d.Prjid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_progcontrol_project");
            });

            modelBuilder.Entity<ImProgcontrolitem>(entity =>
            {
                entity.ToTable("im_progcontrolitem");

                entity.HasIndex(e => e.Progctlid)
                    .HasName("fk_pcitem_progcontrol");

                entity.HasIndex(e => e.Ykdataid)
                    .HasName("fk_pcitem_deviceyk");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Delaytime)
                    .HasColumnName("delaytime")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Progctlid)
                    .IsRequired()
                    .HasColumnName("progctlid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Waityxtimeout)
                    .HasColumnName("waityxtimeout")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Ykdataid)
                    .IsRequired()
                    .HasColumnName("ykdataid")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ykstate)
                    .HasColumnName("ykstate")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.Progctl)
                    .WithMany(p => p.ImProgcontrolitem)
                    .HasForeignKey(d => d.Progctlid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_pcitem_progcontrol");

                entity.HasOne(d => d.Ykdata)
                    .WithMany(p => p.ImProgcontrolitem)
                    .HasForeignKey(d => d.Ykdataid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_pcitem_deviceyk");
            });

            modelBuilder.Entity<ImProject>(entity =>
            {
                entity.ToTable("im_project");

                entity.HasIndex(e => e.Name)
                    .HasName("ix_project_name")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Createtime)
                    .HasColumnName("createtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Creator)
                    .HasColumnName("creator")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasColumnName("name")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Protcommmgrip)
                    .IsRequired()
                    .HasColumnName("protcommmgrip")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImProtectdevice>(entity =>
            {
                entity.HasKey(e => e.Deviceid)
                    .HasName("PRIMARY");

                entity.ToTable("im_protectdevice");

                entity.HasIndex(e => e.Gatewayid)
                    .HasName("fk_protdevice_gateway");

                entity.HasIndex(e => e.Puctgycode)
                    .HasName("fk_protdevice_puctgy");

                entity.HasIndex(e => e.Statcode)
                    .HasName("fk_protdevice_station");

                entity.Property(e => e.Deviceid)
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bayname)
                    .HasColumnName("bayname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Canswdzzone)
                    .HasColumnName("canswdzzone")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicestate)
                    .HasColumnName("devicestate")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Devicetype)
                    .HasColumnName("devicetype")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Endofdkjl)
                    .HasColumnName("endofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Startofdkjl)
                    .HasColumnName("startofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Support12yc)
                    .HasColumnName("support1_2yc")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdkjl)
                    .HasColumnName("supportdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportversion)
                    .HasColumnName("supportversion")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Wavepath)
                    .HasColumnName("wavepath")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Gateway)
                    .WithMany(p => p.ImProtectdevice)
                    .HasForeignKey(d => d.Gatewayid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_protdevice_gateway");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImProtectdevice)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_protdevice_puctgy");

                entity.HasOne(d => d.StatcodeNavigation)
                    .WithMany(p => p.ImProtectdevice)
                    .HasForeignKey(d => d.Statcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_protdevice_station");
            });

            modelBuilder.Entity<ImProtectdeviceTmp>(entity =>
            {
                entity.ToTable("im_protectdevice_tmp");

                entity.HasIndex(e => e.Devicename)
                    .HasName("uk_protdevice_tmp_name")
                    .IsUnique();

                entity.HasIndex(e => e.Puctgycode)
                    .HasName("uk_protdevice_tmp")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithOne(p => p.ImProtectdeviceTmp)
                    .HasForeignKey<ImProtectdeviceTmp>(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("im_protectdevice_tmp_ibfk_1");
            });

            modelBuilder.Entity<ImPuCtgy>(entity =>
            {
                entity.HasKey(e => e.Puctgycode)
                    .HasName("PRIMARY");

                entity.ToTable("im_pu_ctgy");

                entity.HasIndex(e => e.Devctgy)
                    .HasName("fk_puctgy_devctgy");

                entity.HasIndex(e => e.Manufacturer)
                    .HasName("fk_puctgy_manu");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Analogparsemode)
                    .IsRequired()
                    .HasColumnName("analogparsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Canswdzzone)
                    .HasColumnName("canswdzzone")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Devctgy)
                    .IsRequired()
                    .HasColumnName("devctgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dzreadonly)
                    .HasColumnName("dzreadonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dzzonecount)
                    .HasColumnName("dzzonecount")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Endofdkjl)
                    .HasColumnName("endofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Eventparsemode)
                    .IsRequired()
                    .HasColumnName("eventparsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Generation)
                    .IsRequired()
                    .HasColumnName("generation")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Iscrcc)
                    .IsRequired()
                    .HasColumnName("iscrcc")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Manufacturer)
                    .IsRequired()
                    .HasColumnName("manufacturer")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Model)
                    .HasColumnName("model")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Puctgyname)
                    .IsRequired()
                    .HasColumnName("puctgyname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Startofdkjl)
                    .HasColumnName("startofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Support12yc)
                    .HasColumnName("support1_2yc")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdkjl)
                    .HasColumnName("supportdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdz)
                    .HasColumnName("supportdz")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportfhlubo)
                    .HasColumnName("supportfhlubo")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportguzhangbg)
                    .HasColumnName("supportguzhangbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportlubowj)
                    .HasColumnName("supportlubowj")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportshijianbg)
                    .HasColumnName("supportshijianbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportversion)
                    .HasColumnName("supportversion")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportzijianbg)
                    .HasColumnName("supportzijianbg")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.DevctgyNavigation)
                    .WithMany(p => p.ImPuCtgy)
                    .HasForeignKey(d => d.Devctgy)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_puctgy_devctgy");

                entity.HasOne(d => d.ManufacturerNavigation)
                    .WithMany(p => p.ImPuCtgy)
                    .HasForeignKey(d => d.Manufacturer)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_puctgy_manu");
            });

            modelBuilder.Entity<ImPuUpdatelog>(entity =>
            {
                entity.ToTable("im_pu_updatelog");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Createtime)
                    .HasColumnName("createtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Editor)
                    .IsRequired()
                    .HasColumnName("editor")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Upddate)
                    .IsRequired()
                    .HasColumnName("upddate")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Upddesc)
                    .IsRequired()
                    .HasColumnName("upddesc")
                    .HasColumnType("longtext")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImPuWavechl>(entity =>
            {
                entity.ToTable("im_pu_wavechl");

                entity.HasIndex(e => e.Puctgycode)
                    .HasName("fk_puwavechl_puctgy");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Chlname)
                    .IsRequired()
                    .HasColumnName("chlname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Chlnum)
                    .HasColumnName("chlnum")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Chltype)
                    .IsRequired()
                    .HasColumnName("chltype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Chlunit1)
                    .HasColumnName("chlunit1")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Chlunit2)
                    .HasColumnName("chlunit2")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImPuWavechl)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_puwavechl_puctgy");
            });

            modelBuilder.Entity<ImPuctgyFaulttype>(entity =>
            {
                entity.ToTable("im_puctgy_faulttype");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Faulttypeid)
                    .HasColumnName("faulttypeid")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImPuctgyFltrptitem>(entity =>
            {
                entity.HasKey(e => new { e.Puctgycode, e.Itemname })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("im_puctgy_fltrptitem");

                entity.HasIndex(e => e.Itemname)
                    .HasName("fk_puctgy_fltrptitem2");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Itemname)
                    .HasColumnName("itemname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Showlabel)
                    .HasColumnName("showlabel")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.ItemnameNavigation)
                    .WithMany(p => p.ImPuctgyFltrptitem)
                    .HasForeignKey(d => d.Itemname)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_puctgy_fltrptitem2");

                entity.HasOne(d => d.PuctgycodeNavigation)
                    .WithMany(p => p.ImPuctgyFltrptitem)
                    .HasForeignKey(d => d.Puctgycode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_puctgy_fltrptitem1");
            });

            modelBuilder.Entity<ImReportType>(entity =>
            {
                entity.HasKey(e => e.Rpttypecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_report_type");

                entity.Property(e => e.Rpttypecode)
                    .HasColumnName("rpttypecode")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rpttypename)
                    .IsRequired()
                    .HasColumnName("rpttypename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImReportcfg>(entity =>
            {
                entity.ToTable("im_reportcfg");

                entity.HasIndex(e => e.Rpttype)
                    .HasName("fk_reportcfg_rpttype");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Createtime)
                    .HasColumnName("createtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Expstartcol)
                    .HasColumnName("expstartcol")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Expstartrow)
                    .HasColumnName("expstartrow")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Rptname)
                    .IsRequired()
                    .HasColumnName("rptname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rpttype)
                    .IsRequired()
                    .HasColumnName("rpttype")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Templatename)
                    .HasColumnName("templatename")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.RpttypeNavigation)
                    .WithMany(p => p.ImReportcfg)
                    .HasForeignKey(d => d.Rpttype)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_reportcfg_rpttype");
            });

            modelBuilder.Entity<ImReportcfgdata>(entity =>
            {
                entity.ToTable("im_reportcfgdata");

                entity.HasIndex(e => e.Dataid)
                    .HasName("fk_reportcfgdata_devicedata");

                entity.HasIndex(e => e.Rptcfgid)
                    .HasName("fk_reportcfgdata_reportcfg");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataid)
                    .IsRequired()
                    .HasColumnName("dataid")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Reserved)
                    .HasColumnName("reserved")
                    .HasColumnType("longtext")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rptcfgid)
                    .IsRequired()
                    .HasColumnName("rptcfgid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Showintl)
                    .HasColumnName("showintl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Title)
                    .HasColumnName("title")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImReportcfgdata)
                    .HasForeignKey(d => d.Dataid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_reportcfgdata_devicedata");

                entity.HasOne(d => d.Rptcfg)
                    .WithMany(p => p.ImReportcfgdata)
                    .HasForeignKey(d => d.Rptcfgid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_reportcfgdata_reportcfg");
            });

            modelBuilder.Entity<ImShebei>(entity =>
            {
                entity.HasKey(e => e.Shebeiid)
                    .HasName("PRIMARY");

                entity.ToTable("im_shebei");

                entity.HasIndex(e => e.Sblxbm)
                    .HasName("fk_biandiansb_sblx");

                entity.HasIndex(e => e.Statcode)
                    .HasName("fk_biandiansb_station");

                entity.Property(e => e.Shebeiid)
                    .HasColumnName("shebeiid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Anzhuangrq)
                    .HasColumnName("anzhuangrq")
                    .HasColumnType("datetime");

                entity.Property(e => e.Beizhu)
                    .HasColumnName("beizhu")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Chuchangrq)
                    .HasColumnName("chuchangrq")
                    .HasColumnType("datetime");

                entity.Property(e => e.Daxiuzhouqi)
                    .HasColumnName("daxiuzhouqi")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Guzibh)
                    .HasColumnName("guzibh")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Guzijiazhi).HasColumnName("guzijiazhi");

                entity.Property(e => e.Sblxbm)
                    .HasColumnName("sblxbm")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Shebeibh)
                    .HasColumnName("shebeibh")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Shebeixh)
                    .HasColumnName("shebeixh")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Touyunrq)
                    .HasColumnName("touyunrq")
                    .HasColumnType("datetime");

                entity.Property(e => e.Xiaoxiuzhouqi)
                    .HasColumnName("xiaoxiuzhouqi")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yunxingbh)
                    .IsRequired()
                    .HasColumnName("yunxingbh")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Zhizaochang)
                    .HasColumnName("zhizaochang")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Zhongxiuzhouqi)
                    .HasColumnName("zhongxiuzhouqi")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.SblxbmNavigation)
                    .WithMany(p => p.ImShebei)
                    .HasForeignKey(d => d.Sblxbm)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_biandiansb_sblx");

                entity.HasOne(d => d.StatcodeNavigation)
                    .WithMany(p => p.ImShebei)
                    .HasForeignKey(d => d.Statcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_biandiansb_station");
            });

            modelBuilder.Entity<ImShebeiDevicedata>(entity =>
            {
                entity.HasKey(e => e.Recid)
                    .HasName("PRIMARY");

                entity.ToTable("im_shebei_devicedata");

                entity.HasIndex(e => e.Dataid)
                    .HasName("fk_sbdevdata_devdata");

                entity.HasIndex(e => e.Shebeiid)
                    .HasName("fk_sbdevdata_shebei");

                entity.Property(e => e.Recid)
                    .HasColumnName("recid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dataid)
                    .IsRequired()
                    .HasColumnName("dataid")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Shebeiid)
                    .IsRequired()
                    .HasColumnName("shebeiid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Data)
                    .WithMany(p => p.ImShebeiDevicedata)
                    .HasForeignKey(d => d.Dataid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_sbdevdata_devdata");

                entity.HasOne(d => d.Shebei)
                    .WithMany(p => p.ImShebeiDevicedata)
                    .HasForeignKey(d => d.Shebeiid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_sbdevdata_shebei");
            });

            modelBuilder.Entity<ImShebeiProtdevice>(entity =>
            {
                entity.HasKey(e => e.Recid)
                    .HasName("PRIMARY");

                entity.ToTable("im_shebei_protdevice");

                entity.HasIndex(e => e.Deviceid)
                    .HasName("fk_sbdevice_device");

                entity.HasIndex(e => e.Shebeiid)
                    .HasName("fk_sbdevice_shebei");

                entity.Property(e => e.Recid)
                    .HasColumnName("recid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Shebeiid)
                    .IsRequired()
                    .HasColumnName("shebeiid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Device)
                    .WithMany(p => p.ImShebeiProtdevice)
                    .HasForeignKey(d => d.Deviceid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_sbdevice_device");

                entity.HasOne(d => d.Shebei)
                    .WithMany(p => p.ImShebeiProtdevice)
                    .HasForeignKey(d => d.Shebeiid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_sbdevice_shebei");
            });

            modelBuilder.Entity<ImShebeilx>(entity =>
            {
                entity.HasKey(e => e.Sblxbm)
                    .HasName("PRIMARY");

                entity.ToTable("im_shebeilx");

                entity.Property(e => e.Sblxbm)
                    .HasColumnName("sblxbm")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Sblxmc)
                    .IsRequired()
                    .HasColumnName("sblxmc")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Sblxxh)
                    .HasColumnName("sblxxh")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImShebeizt>(entity =>
            {
                entity.HasKey(e => e.Sbztbm)
                    .HasName("PRIMARY");

                entity.ToTable("im_shebeizt");

                entity.Property(e => e.Sbztbm)
                    .HasColumnName("sbztbm")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Sbztmc)
                    .IsRequired()
                    .HasColumnName("sbztmc")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Sbztxh)
                    .HasColumnName("sbztxh")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImStation>(entity =>
            {
                entity.HasKey(e => e.Statcode)
                    .HasName("PRIMARY");

                entity.ToTable("im_station");

                entity.HasIndex(e => e.Prjid)
                    .HasName("fk_station_project");

                entity.Property(e => e.Statcode)
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Prjid)
                    .IsRequired()
                    .HasColumnName("prjid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Statname)
                    .IsRequired()
                    .HasColumnName("statname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Prj)
                    .WithMany(p => p.ImStation)
                    .HasForeignKey(d => d.Prjid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_station_project");
            });

            modelBuilder.Entity<ImSwitchstate>(entity =>
            {
                entity.HasKey(e => e.Swstatecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_switchstate");

                entity.Property(e => e.Swstatecode)
                    .HasColumnName("swstatecode")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Swstatestr)
                    .IsRequired()
                    .HasColumnName("swstatestr")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImTimesrc>(entity =>
            {
                entity.HasKey(e => e.Timesrccode)
                    .HasName("PRIMARY");

                entity.ToTable("im_timesrc");

                entity.Property(e => e.Timesrccode)
                    .HasColumnName("timesrccode")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Timesrcname)
                    .IsRequired()
                    .HasColumnName("timesrcname")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVDevdatalinkage>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_devdatalinkage");

                entity.Property(e => e.Action)
                    .HasColumnName("action")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Actparam)
                    .HasColumnName("actparam")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Beizhu)
                    .HasColumnName("beizhu")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .IsRequired()
                    .HasColumnName("domain")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Lnkid)
                    .IsRequired()
                    .HasColumnName("lnkid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lnktype)
                    .IsRequired()
                    .HasColumnName("lnktype")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Resource)
                    .IsRequired()
                    .HasColumnName("resource")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcobjid)
                    .IsRequired()
                    .HasColumnName("srcobjid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcobjtype)
                    .IsRequired()
                    .HasColumnName("srcobjtype")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srctempdataid)
                    .HasColumnName("srctempdataid")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.State)
                    .IsRequired()
                    .HasColumnName("state")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statname)
                    .IsRequired()
                    .HasColumnName("statname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImVDevicedata>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_devicedata");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Beizhu)
                    .HasColumnName("beizhu")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .IsRequired()
                    .HasColumnName("domain")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Srctempdataid)
                    .HasColumnName("srctempdataid")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImVDevicedz>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_devicedz");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.CtrlwordTypeid)
                    .HasColumnName("ctrlword_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzCoeff).HasColumnName("dz_coeff");

                entity.Property(e => e.DzCoeff1).HasColumnName("dz_coeff_1");

                entity.Property(e => e.DzComment)
                    .HasColumnName("dz_comment")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzIndex)
                    .HasColumnName("dz_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzMax).HasColumnName("dz_max");

                entity.Property(e => e.DzMin).HasColumnName("dz_min");

                entity.Property(e => e.DzName)
                    .HasColumnName("dz_name")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzPrecise)
                    .HasColumnName("dz_precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzPrecise1)
                    .HasColumnName("dz_precise_1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzRange)
                    .HasColumnName("dz_range")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzType)
                    .HasColumnName("dz_type")
                    .HasColumnType("int(11)");

                entity.Property(e => e.DzUnit)
                    .HasColumnName("dz_unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzUnit1)
                    .HasColumnName("dz_unit_1")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.DzUnitcvtCoeff).HasColumnName("dz_unitcvt_coeff");

                entity.Property(e => e.Dztypename)
                    .IsRequired()
                    .HasColumnName("dztypename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.EnumTypeid)
                    .HasColumnName("enum_typeid")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Hidden)
                    .IsRequired()
                    .HasColumnName("hidden")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Readonly)
                    .HasColumnName("readonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.RelatectId)
                    .HasColumnName("relatect_id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelateptId)
                    .HasColumnName("relatept_id")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVDeviceyc>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_deviceyc");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Cof).HasColumnName("cof");

                entity.Property(e => e.Cof2).HasColumnName("cof2");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Downlimit).HasColumnName("downlimit");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Intl4save).HasColumnName("intl4save");

                entity.Property(e => e.Precise)
                    .HasColumnName("precise")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Precise2)
                    .HasColumnName("precise2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Savemode)
                    .IsRequired()
                    .HasColumnName("savemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit2)
                    .HasColumnName("unit2")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Uplimit).HasColumnName("uplimit");

                entity.Property(e => e.Uplimit4stat).HasColumnName("uplimit4stat");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Ycname)
                    .IsRequired()
                    .HasColumnName("ycname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVDeviceyk>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_deviceyk");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Isresetcmd)
                    .IsRequired()
                    .HasColumnName("isresetcmd")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lockmode)
                    .IsRequired()
                    .HasColumnName("lockmode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.LockmodeOff)
                    .IsRequired()
                    .HasColumnName("lockmode_off")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Prestate4yk)
                    .HasColumnName("prestate4yk")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Prestate4ykOff)
                    .HasColumnName("prestate4yk_off")
                    .HasColumnType("int(11)");

                entity.Property(e => e.PreyxId)
                    .HasColumnName("preyx_id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.PreyxIdOff)
                    .HasColumnName("preyx_id_off")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelatedyxId)
                    .HasColumnName("relatedyx_id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.RelatedyxName)
                    .HasColumnName("relatedyx_name")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swoffstr)
                    .HasColumnName("swoffstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swonstr)
                    .HasColumnName("swonstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swuncertstr)
                    .HasColumnName("swuncertstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");

                entity.Property(e => e.YkType)
                    .IsRequired()
                    .HasColumnName("yk_type")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ykname)
                    .IsRequired()
                    .HasColumnName("ykname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVDeviceym>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_deviceym");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Cof).HasColumnName("cof");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Maxvalue).HasColumnName("maxvalue");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unit)
                    .HasColumnName("unit")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Ymname)
                    .IsRequired()
                    .HasColumnName("ymname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVDeviceyx>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_deviceyx");

                entity.Property(e => e.Alertlevel)
                    .IsRequired()
                    .HasColumnName("alertlevel")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Autosave)
                    .HasColumnName("autosave")
                    .HasColumnType("int(11)");

                entity.Property(e => e.CpuIndex)
                    .HasColumnName("cpu_index")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Datatype)
                    .IsRequired()
                    .HasColumnName("datatype")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.InfoAddr)
                    .HasColumnName("info_addr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Normalstate)
                    .IsRequired()
                    .HasColumnName("normalstate")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swoffstr)
                    .HasColumnName("swoffstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swonstr)
                    .HasColumnName("swonstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Swuncertstr)
                    .HasColumnName("swuncertstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Visible)
                    .HasColumnName("visible")
                    .HasColumnType("int(11)");

                entity.Property(e => e.YxType)
                    .HasColumnName("yx_type")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yxname)
                    .IsRequired()
                    .HasColumnName("yxname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVDeviceyxSrcdevice>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_deviceyx_srcdevice");

                entity.Property(e => e.Dataname)
                    .IsRequired()
                    .HasColumnName("dataname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devnameofyx)
                    .IsRequired()
                    .HasColumnName("devnameofyx")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcdevid)
                    .IsRequired()
                    .HasColumnName("srcdevid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Srcdevname)
                    .IsRequired()
                    .HasColumnName("srcdevname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yxdataid)
                    .IsRequired()
                    .HasColumnName("yxdataid")
                    .HasColumnType("varchar(12)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVProtectdevice>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_protectdevice");

                entity.Property(e => e.Analogparsemode)
                    .IsRequired()
                    .HasColumnName("analogparsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bayname)
                    .HasColumnName("bayname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Canswdzzone)
                    .HasColumnName("canswdzzone")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Devctgy)
                    .IsRequired()
                    .HasColumnName("devctgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Deviceaddr)
                    .HasColumnName("deviceaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Deviceid)
                    .IsRequired()
                    .HasColumnName("deviceid")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicestate)
                    .HasColumnName("devicestate")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Devicetype)
                    .HasColumnName("devicetype")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dzreadonly)
                    .HasColumnName("dzreadonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dzzonecount)
                    .HasColumnName("dzzonecount")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Endofdkjl)
                    .HasColumnName("endofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Eventparsemode)
                    .IsRequired()
                    .HasColumnName("eventparsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayid)
                    .IsRequired()
                    .HasColumnName("gatewayid")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayip1)
                    .HasColumnName("gatewayip1")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayip2)
                    .HasColumnName("gatewayip2")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayname)
                    .IsRequired()
                    .HasColumnName("gatewayname")
                    .HasColumnType("varchar(200)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gatewayport1)
                    .HasColumnName("gatewayport1")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Gatewayport2)
                    .HasColumnName("gatewayport2")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Generation)
                    .IsRequired()
                    .HasColumnName("generation")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Manufacturer)
                    .IsRequired()
                    .HasColumnName("manufacturer")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Model)
                    .HasColumnName("model")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Phyaddr)
                    .HasColumnName("phyaddr")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Startofdkjl)
                    .HasColumnName("startofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statname)
                    .IsRequired()
                    .HasColumnName("statname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Support12yc)
                    .HasColumnName("support1_2yc")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdkjl)
                    .HasColumnName("supportdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdz)
                    .HasColumnName("supportdz")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportfhlubo)
                    .HasColumnName("supportfhlubo")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportguzhangbg)
                    .HasColumnName("supportguzhangbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportlubowj)
                    .HasColumnName("supportlubowj")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportshijianbg)
                    .HasColumnName("supportshijianbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportversion)
                    .HasColumnName("supportversion")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportzijianbg)
                    .HasColumnName("supportzijianbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Wavepath)
                    .HasColumnName("wavepath")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVProtectdeviceTmp>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("im_v_protectdevice_tmp");

                entity.Property(e => e.Analogparsemode)
                    .IsRequired()
                    .HasColumnName("analogparsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Canswdzzone)
                    .HasColumnName("canswdzzone")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Devctgy)
                    .IsRequired()
                    .HasColumnName("devctgy")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Devicename)
                    .IsRequired()
                    .HasColumnName("devicename")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dzreadonly)
                    .HasColumnName("dzreadonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Dzzonecount)
                    .HasColumnName("dzzonecount")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Endofdkjl)
                    .HasColumnName("endofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Eventparsemode)
                    .IsRequired()
                    .HasColumnName("eventparsemode")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Generation)
                    .IsRequired()
                    .HasColumnName("generation")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Id)
                    .IsRequired()
                    .HasColumnName("id")
                    .HasColumnType("varchar(3)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Iscrcc)
                    .IsRequired()
                    .HasColumnName("iscrcc")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Manufacturer)
                    .IsRequired()
                    .HasColumnName("manufacturer")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Model)
                    .HasColumnName("model")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Puctgycode)
                    .HasColumnName("puctgycode")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Puctgyname)
                    .IsRequired()
                    .HasColumnName("puctgyname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Startofdkjl)
                    .HasColumnName("startofdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Support12yc)
                    .HasColumnName("support1_2yc")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdkjl)
                    .HasColumnName("supportdkjl")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportdz)
                    .HasColumnName("supportdz")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportfhlubo)
                    .HasColumnName("supportfhlubo")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportguzhangbg)
                    .HasColumnName("supportguzhangbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportlubowj)
                    .HasColumnName("supportlubowj")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportshijianbg)
                    .HasColumnName("supportshijianbg")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportversion)
                    .HasColumnName("supportversion")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Supportzijianbg)
                    .HasColumnName("supportzijianbg")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImVariantBool>(entity =>
            {
                entity.HasKey(e => e.VaId)
                    .HasName("PRIMARY");

                entity.ToTable("im_variant_bool");

                entity.HasIndex(e => e.Statcode)
                    .HasName("fk_vabool_station");

                entity.Property(e => e.VaId)
                    .HasColumnName("va_id")
                    .HasColumnType("varchar(7)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alertmsg)
                    .HasColumnName("alertmsg")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Alerttype)
                    .IsRequired()
                    .HasColumnName("alerttype")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Expr)
                    .HasColumnName("expr")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Exprdataids)
                    .HasColumnName("exprdataids")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Exprdesc)
                    .HasColumnName("exprdesc")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Falsestr)
                    .HasColumnName("falsestr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Isfibre)
                    .IsRequired()
                    .HasColumnName("isfibre")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Memo)
                    .HasColumnName("memo")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Statcode)
                    .IsRequired()
                    .HasColumnName("statcode")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.State)
                    .IsRequired()
                    .HasColumnName("state")
                    .HasColumnType("varchar(6)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Truestr)
                    .HasColumnName("truestr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Vaname)
                    .IsRequired()
                    .HasColumnName("vaname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.StatcodeNavigation)
                    .WithMany(p => p.ImVariantBool)
                    .HasForeignKey(d => d.Statcode)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_vabool_station");
            });

            modelBuilder.Entity<ImVariantboolstate>(entity =>
            {
                entity.HasKey(e => e.Varboolstatecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_variantboolstate");

                entity.Property(e => e.Varboolstatecode)
                    .HasColumnName("varboolstatecode")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Varboolstatestr)
                    .IsRequired()
                    .HasColumnName("varboolstatestr")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVersion>(entity =>
            {
                entity.HasKey(e => e.Lastmodified)
                    .HasName("PRIMARY");

                entity.ToTable("im_version");

                entity.Property(e => e.Lastmodified)
                    .HasColumnName("lastmodified")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Desc)
                    .HasColumnName("desc")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImVersiontypeenum2010>(entity =>
            {
                entity.HasKey(e => e.Enumindex)
                    .HasName("PRIMARY");

                entity.ToTable("im_versiontypeenum_2010");

                entity.Property(e => e.Enumindex)
                    .HasColumnName("enumindex")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Enumname)
                    .IsRequired()
                    .HasColumnName("enumname")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImWatchdog>(entity =>
            {
                entity.HasKey(e => e.Exename)
                    .HasName("PRIMARY");

                entity.ToTable("im_watchdog");

                entity.Property(e => e.Exename)
                    .HasColumnName("exename")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8")
                    .HasCollation("utf8_general_ci");

                entity.Property(e => e.Lasttime)
                    .HasColumnName("lasttime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Tickcount)
                    .HasColumnName("tickcount")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<ImYkType>(entity =>
            {
                entity.HasKey(e => e.Typecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_yk_type");

                entity.Property(e => e.Typecode)
                    .HasColumnName("typecode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Typename)
                    .IsRequired()
                    .HasColumnName("typename")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<ImYxType>(entity =>
            {
                entity.HasKey(e => e.Typecode)
                    .HasName("PRIMARY");

                entity.ToTable("im_yx_type");

                entity.Property(e => e.Typecode)
                    .HasColumnName("typecode")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Typename)
                    .IsRequired()
                    .HasColumnName("typename")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbBumen>(entity =>
            {
                entity.ToTable("tb_bumen");

                entity.HasIndex(e => e.Bmlxbm)
                    .HasName("fk_bm_bmlx");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bmlxbm)
                    .IsRequired()
                    .HasColumnName("bmlxbm")
                    .HasColumnType("varchar(2)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bumenbm)
                    .HasColumnName("bumenbm")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bumenmc)
                    .IsRequired()
                    .HasColumnName("bumenmc")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.BmlxbmNavigation)
                    .WithMany(p => p.TbBumen)
                    .HasForeignKey(d => d.Bmlxbm)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_bm_bmlx");

                entity.HasOne(d => d.IdNavigation)
                    .WithOne(p => p.TbBumen)
                    .HasForeignKey<TbBumen>(d => d.Id)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_bm_treenode");
            });

            modelBuilder.Entity<TbBumenlx>(entity =>
            {
                entity.HasKey(e => e.Bmlxbm)
                    .HasName("PRIMARY");

                entity.ToTable("tb_bumenlx");

                entity.Property(e => e.Bmlxbm)
                    .HasColumnName("bmlxbm")
                    .HasColumnType("varchar(2)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bmlxmc)
                    .IsRequired()
                    .HasColumnName("bmlxmc")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbDatamodihis>(entity =>
            {
                entity.ToTable("tb_datamodihis");

                entity.HasIndex(e => e.Dstobjid)
                    .HasName("ind_datamodihis");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Content)
                    .HasColumnName("content")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dstobjid)
                    .HasColumnName("dstobjid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dstobjname)
                    .HasColumnName("dstobjname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dsttable)
                    .HasColumnName("dsttable")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Editor)
                    .IsRequired()
                    .HasColumnName("editor")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Edittime)
                    .HasColumnName("edittime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Type)
                    .IsRequired()
                    .HasColumnName("type")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbField>(entity =>
            {
                entity.HasKey(e => e.Fieldid)
                    .HasName("PRIMARY");

                entity.ToTable("tb_field");

                entity.HasIndex(e => e.Tableid)
                    .HasName("fk_field_table");

                entity.Property(e => e.Fieldid)
                    .HasColumnName("fieldid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Canstat)
                    .HasColumnName("canstat")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Checkvalue)
                    .HasColumnName("checkvalue")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Colormode)
                    .HasColumnName("colormode")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Defaultvalue)
                    .HasColumnName("defaultvalue")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dispwidth)
                    .HasColumnName("dispwidth")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Fieldname)
                    .IsRequired()
                    .HasColumnName("fieldname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Fieldsize)
                    .HasColumnName("fieldsize")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Fieldtype)
                    .IsRequired()
                    .HasColumnName("fieldtype")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Formatstr)
                    .HasColumnName("formatstr")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Groupname)
                    .HasColumnName("groupname")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Hint)
                    .HasColumnName("hint")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Iscond)
                    .HasColumnName("iscond")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Isname)
                    .HasColumnName("isname")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Isprimary)
                    .HasColumnName("isprimary")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Isref)
                    .HasColumnName("isref")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Prcname)
                    .IsRequired()
                    .HasColumnName("prcname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Readonly)
                    .HasColumnName("readonly")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Refcodefield)
                    .HasColumnName("refcodefield")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Refnamefield)
                    .HasColumnName("refnamefield")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Reftable)
                    .HasColumnName("reftable")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Reftablealias)
                    .HasColumnName("reftablealias")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Required)
                    .HasColumnName("required")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Showonadd)
                    .HasColumnName("showonadd")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Showonbrw)
                    .HasColumnName("showonbrw")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Showonedit)
                    .HasColumnName("showonedit")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Showonqry)
                    .HasColumnName("showonqry")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Tableid)
                    .IsRequired()
                    .HasColumnName("tableid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Table)
                    .WithMany(p => p.TbField)
                    .HasForeignKey(d => d.Tableid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_field_table");
            });

            modelBuilder.Entity<TbImage>(entity =>
            {
                entity.HasKey(e => e.Objid)
                    .HasName("PRIMARY");

                entity.ToTable("tb_image");

                entity.Property(e => e.Objid)
                    .HasColumnName("objid")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Data).HasColumnName("data");

                entity.Property(e => e.Format)
                    .IsRequired()
                    .HasColumnName("format_")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Length)
                    .HasColumnName("length")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<TbQuanxian>(entity =>
            {
                entity.HasKey(e => new { e.Yonghuming, e.Mokuaibianhao })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("tb_quanxian");

                entity.Property(e => e.Yonghuming)
                    .HasColumnName("yonghuming")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Mokuaibianhao)
                    .HasColumnName("mokuaibianhao")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Quanxian)
                    .HasColumnName("quanxian")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.YonghumingNavigation)
                    .WithMany(p => p.TbQuanxian)
                    .HasForeignKey(d => d.Yonghuming)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_quanxian_yonghu");
            });

            modelBuilder.Entity<TbSession>(entity =>
            {
                entity.HasKey(e => e.Sessionid)
                    .HasName("PRIMARY");

                entity.ToTable("tb_session");

                entity.Property(e => e.Sessionid)
                    .HasColumnName("sessionid")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Clientip)
                    .HasColumnName("clientip")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lasttime)
                    .HasColumnName("lasttime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Softinfo)
                    .HasColumnName("softinfo")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Username)
                    .HasColumnName("username")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbTable>(entity =>
            {
                entity.HasKey(e => e.Tableid)
                    .HasName("PRIMARY");

                entity.ToTable("tb_table");

                entity.Property(e => e.Tableid)
                    .HasColumnName("tableid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ctgy)
                    .IsRequired()
                    .HasColumnName("ctgy")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dbconnname)
                    .HasColumnName("dbconnname")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Isview)
                    .IsRequired()
                    .HasColumnName("isview")
                    .HasColumnType("varchar(4)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Orderby)
                    .HasColumnName("orderby")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Prcname)
                    .IsRequired()
                    .HasColumnName("prcname")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Tablename)
                    .IsRequired()
                    .HasColumnName("tablename")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Tblname4view)
                    .HasColumnName("tblname4view")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbTreenode>(entity =>
            {
                entity.HasKey(e => e.Nodeid)
                    .HasName("PRIMARY");

                entity.ToTable("tb_treenode");

                entity.Property(e => e.Nodeid)
                    .HasColumnName("nodeid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ctgy)
                    .IsRequired()
                    .HasColumnName("ctgy")
                    .HasColumnType("varchar(30)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Desc)
                    .HasColumnName("desc_")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Nodename)
                    .IsRequired()
                    .HasColumnName("nodename")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Parentid)
                    .HasColumnName("parentid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<TbVirtualtable>(entity =>
            {
                entity.HasKey(e => e.Tablename)
                    .HasName("PRIMARY");

                entity.ToTable("tb_virtualtable");

                entity.HasIndex(e => e.Basetableid)
                    .HasName("fk_virtualtable_table1");

                entity.HasIndex(e => e.Derivetableid)
                    .HasName("fk_virtualtable_table2");

                entity.Property(e => e.Tablename)
                    .HasColumnName("tablename")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Basetableid)
                    .IsRequired()
                    .HasColumnName("basetableid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ctgyfldinbasetable)
                    .HasColumnName("ctgyfldinbasetable")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Ctgyinbasetable)
                    .HasColumnName("ctgyinbasetable")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Derivetableid)
                    .IsRequired()
                    .HasColumnName("derivetableid")
                    .HasColumnType("varchar(40)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Orderby)
                    .HasColumnName("orderby")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Seqno)
                    .HasColumnName("seqno")
                    .HasColumnType("int(11)");

                entity.HasOne(d => d.Basetable)
                    .WithMany(p => p.TbVirtualtableBasetable)
                    .HasForeignKey(d => d.Basetableid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_virtualtable_table1");

                entity.HasOne(d => d.Derivetable)
                    .WithMany(p => p.TbVirtualtableDerivetable)
                    .HasForeignKey(d => d.Derivetableid)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_virtualtable_table2");
            });

            modelBuilder.Entity<TbXitongmokuai>(entity =>
            {
                entity.HasKey(e => e.Mokuaibianhao)
                    .HasName("PRIMARY");

                entity.ToTable("tb_xitongmokuai");

                entity.Property(e => e.Mokuaibianhao)
                    .HasColumnName("mokuaibianhao")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Fumokuai)
                    .HasColumnName("fumokuai")
                    .HasColumnType("varchar(8)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Mokuaicengci)
                    .HasColumnName("mokuaicengci")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Mokuaileixing)
                    .IsRequired()
                    .HasColumnName("mokuaileixing")
                    .HasColumnType("varchar(1)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Mokuailujing)
                    .HasColumnName("mokuailujing")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Mokuaimc)
                    .HasColumnName("mokuaimc")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Mokuaimiaoshu)
                    .HasColumnName("mokuaimiaoshu")
                    .HasColumnType("varchar(100)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Mokuaixuhao)
                    .HasColumnName("mokuaixuhao")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Zidongyunxing)
                    .HasColumnName("zidongyunxing")
                    .HasColumnType("int(11)");
            });

            modelBuilder.Entity<TbXitongrizhi>(entity =>
            {
                entity.ToTable("tb_xitongrizhi");

                entity.Property(e => e.Id)
                    .HasColumnName("id")
                    .HasColumnType("int(11)")
                    .ValueGeneratedNever();

                entity.Property(e => e.Fashengriqi)
                    .IsRequired()
                    .HasColumnName("fashengriqi")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Fashengshijian)
                    .IsRequired()
                    .HasColumnName("fashengshijian")
                    .HasColumnType("varchar(5)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Miaoshu)
                    .IsRequired()
                    .HasColumnName("miaoshu")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yonghu)
                    .IsRequired()
                    .HasColumnName("yonghu")
                    .HasColumnType("varchar(20)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbYonghu>(entity =>
            {
                entity.HasKey(e => e.Yonghuming)
                    .HasName("PRIMARY");

                entity.ToTable("tb_yonghu");

                entity.Property(e => e.Yonghuming)
                    .HasColumnName("yonghuming")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Bumenid)
                    .HasColumnName("bumenid")
                    .HasColumnType("varchar(38)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Chuangjiansj)
                    .HasColumnName("chuangjiansj")
                    .HasColumnType("datetime");

                entity.Property(e => e.Errcount)
                    .HasColumnName("errcount")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Kouling)
                    .HasColumnName("kouling")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Lastchangpasstime)
                    .HasColumnName("lastchangpasstime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Lasterrtime)
                    .HasColumnName("lasterrtime")
                    .HasColumnType("datetime");

                entity.Property(e => e.Leixing)
                    .IsRequired()
                    .HasColumnName("leixing")
                    .HasColumnType("char(1)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Quanming)
                    .HasColumnName("quanming")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yonghumiaoshu)
                    .HasColumnName("yonghumiaoshu")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Zhuangtai)
                    .HasColumnName("zhuangtai")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Zhuye)
                    .HasColumnName("zhuye")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<TbYonghuguanxi>(entity =>
            {
                entity.HasKey(e => new { e.Yonghuzu, e.Yonghu })
                    .HasName("PRIMARY")
                    .HasAnnotation("MySql:IndexPrefixLength", new[] { 0, 0 });

                entity.ToTable("tb_yonghuguanxi");

                entity.HasIndex(e => e.Yonghu)
                    .HasName("fk_yonghuguanxi2_yonghu");

                entity.Property(e => e.Yonghuzu)
                    .HasColumnName("yonghuzu")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Yonghu)
                    .HasColumnName("yonghu")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.YonghuNavigation)
                    .WithMany(p => p.TbYonghuguanxiYonghuNavigation)
                    .HasForeignKey(d => d.Yonghu)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_yonghuguanxi2_yonghu");

                entity.HasOne(d => d.YonghuzuNavigation)
                    .WithMany(p => p.TbYonghuguanxiYonghuzuNavigation)
                    .HasForeignKey(d => d.Yonghuzu)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("fk_yonghuguanxi1_yonghu");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
