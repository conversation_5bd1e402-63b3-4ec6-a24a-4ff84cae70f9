﻿<Window x:Class="Yunda.ISAS.DataMonitoringServer.WPF.View.DataWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converter="clr-namespace:Yunda.ISAS.DataMonitoringServer.WPF.Converters"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:local="Yunda.ISAS.DataMonitoringServer.WPF.ViewModel"
        xmlns:controls="clr-namespace:Yunda.SOMS.DataMonitoringServer.WPF.UserControlers"
        TextElement.Foreground="{StaticResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontFamily="微软雅黑"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        WindowStartupLocation="CenterScreen"
        mc:Ignorable="d"
        x:Name="MainWindow" 
        Title="数据服务" Height="650" Width="1100" MaxHeight="800">
    <Window.Resources>
    </Window.Resources>
    <Grid Background="{StaticResource WheatColor}">
        <DockPanel LastChildFill="True">
            <materialDesign:Card DockPanel.Dock="Top" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <controls:ConnectionGridControl Grid.Row="0" Margin="5"/>
                </Grid>
            </materialDesign:Card>
            <materialDesign:Card DockPanel.Dock="Top" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="1" Orientation="Vertical" HorizontalAlignment="Left">
                        <WrapPanel Margin="0,0,0,5">
                            <TextBlock VerticalAlignment="Center" Text="变电站选择:"/>
                            <ComboBox Height="40" Width="240" Margin="15,2,5,5"
                          ItemsSource="{Binding TransformerSubstations}"
                          DisplayMemberPath="Text"
                          SelectedItem="{Binding TransformerSubstation}"
                          Foreground="Black"
                          Style="{StaticResource MaterialDesignComboBox}"
                          materialDesign:HintAssist.IsFloating="True"/>
                            <WrapPanel Margin="0,15,0,10" Width="110">
                                <TextBlock VerticalAlignment="Center" Text="自动启动：" />
                                <ToggleButton IsChecked="{Binding SettingModel.IsAutoStartup, Converter={StaticResource BoolPositiveConverter}}"
                                  IsEnabled="{Binding IsRunning, Converter={StaticResource InverseBoolValueConverter}}"
                                  Style="{StaticResource MaterialDesignSwitchToggleButton}"/>
                            </WrapPanel>
                            <Button Command="{Binding StartCommand}" Content="{Binding StatBtnContent}" Background="Red" Margin="5"
                        Foreground="#fff" Width="90" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Command="{Binding UpdateAllMenuCmd}" Content="总召唤" Background="BlueViolet" Margin="5"
                        Foreground="#f3f3f3" Width="80" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"
                        IsEnabled="{Binding IsRunning, Converter={StaticResource BoolPositiveConverter}}"/>
                            <Button Command="{Binding CleanCommand}" Content="清屏" Background="Wheat" Margin="5"
                        Foreground="#000" Width="70" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Content="更多..." x:Name="MoreButton" Click="ToggleMoreOptions"
                        Background="LightGray" Margin="5" Width="80" Height="30"
                        Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                        </WrapPanel>

                        <!-- 第二行开始，默认折叠 -->
                        <WrapPanel x:Name="SecondRowPanel" Visibility="Collapsed">
                            <Button Command="{Binding AddConnectionCommand}" Content="添加连接" Background="LightBlue" Margin="5"
Foreground="#000" Width="100" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Command="{Binding RemoveConnectionCommand}" Content="删除连接" Background="LightBlue" Margin="5"
Foreground="#000" Width="100" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Command="{Binding ToggleRealTimeDataCommand}" Content="实时数据" Background="LightBlue" Margin="5"
                        Foreground="#000" Width="100" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Command="{Binding ExportLiveDataCommand}" Content="导出数据" Background="LightBlue" Margin="5"
                        Foreground="#000" Width="100" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Command="{Binding ClearRedisCommand}" Content="清除缓存" Background="Red" Margin="5"
                        Foreground="#fff" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Command="{Binding SaveCommand}" Content="保存配置" Background="#1ab394" Margin="5"
                        Foreground="#000" Width="100" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"
                        IsEnabled="{Binding IsRunning, Converter={StaticResource InverseBoolValueConverter}}"/>
                            <Button Click="restart_Click" Content="重启服务" Background="OrangeRed" Margin="5"
                        Foreground="#fff" Width="100" Height="30" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Click="SendRunstateFtpFile" Content="下发配置到装置" Background="Red" Margin="5"
                        Foreground="#fff" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                            <Button Click="exitBtn_click" Content="退出" Background="LightBlue" Margin="5"
                        Foreground="#000" Style="{StaticResource MaterialDesignRaisedLightButton}"/>
                        </WrapPanel>
                    </StackPanel>

                </Grid>
            </materialDesign:Card>
            <DockPanel Background="{StaticResource WheatColor}"  LastChildFill="True" DockPanel.Dock="Top"  >
                <ProgressBar DockPanel.Dock="Top"  Height="4" Padding="0" Margin="10,5" Background="LightGreen" BorderBrush="AliceBlue" Foreground="Green" IsIndeterminate="{Binding Path=IsRunning}" />
                <ScrollViewer VerticalScrollBarVisibility="Visible" MouseWheel="ScrollViewer_MouseWheel">
                    <ListView x:Name="recordersListview" ItemsSource="{Binding Path=Recorders}" FontSize="12">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Width="180" DisplayMemberBinding="{Binding Path=DateTime, StringFormat={}{0:MM-dd HH:mm:ss fff}}" Header="时间" />
                                <GridViewColumn Width="120" DisplayMemberBinding="{Binding Path=MsgType}" Header="类别" />
                                <GridViewColumn Width="500" DisplayMemberBinding="{Binding Path=Msg}" Header="信息" />
                            </GridView>
                        </ListView.View>

                        <ListView.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="过滤报文" FontFamily="微软雅黑" FontWeight="Regular" Name="regMenu" Command="{Binding Path=RegMenuCmd}"></MenuItem>
                                <MenuItem Header="暂停报文" FontFamily="微软雅黑" FontWeight="Regular" Name="stopMenu" Command="{Binding Path=StopMenuCmd}"></MenuItem>
                                <MenuItem Header="开始报文" FontFamily="微软雅黑" FontWeight="Regular" Name="startMenu" Command="{Binding Path=StartMenuCmd}" ></MenuItem>
                               
                                <MenuItem Header="更新三遥数据" FontFamily="微软雅黑" FontWeight="Regular"  Command="{Binding Path=UpdateAllMenuCmd}">
                                </MenuItem>
                                <MenuItem Header="更新指定数据" FontFamily="微软雅黑" FontWeight="Regular"  Command="{Binding Path=UpdateOneMenuCmd}" >
                                </MenuItem>
                                <MenuItem Header="更新联动数据" FontFamily="微软雅黑" FontWeight="Regular"  Command="{Binding Path=UpdateLinkageDataCommand}" >
                                </MenuItem>
                                <MenuItem Header="清屏" FontFamily="微软雅黑" FontWeight="Regular"  Command="{Binding Path=CleanCommand}" >
                                </MenuItem>
                                <MenuItem Header="测试界面" FontFamily="微软雅黑" FontWeight="Regular"  Command="{Binding Path=TestCommand}" >
                                </MenuItem>
                            </ContextMenu>

                        </ListView.ContextMenu>
                    </ListView>
                </ScrollViewer>
            </DockPanel>

            <TabControl DockPanel.Dock="Top" Visibility="Collapsed">
                <TabItem Header="实时报文">
                </TabItem>
                <TabItem Header="实时监视" IsSelected="False" Visibility="Collapsed">
                    <ScrollViewer VerticalScrollBarVisibility="Visible" x:Name="liveDatasScrollViewer" MouseWheel="ScrollViewer_MouseWheel">
                        <ListView ItemsSource="{Binding Path=InfoAddrLiveDatas}" FontSize="12" d:ItemsSource="{d:SampleData ItemCount=5}" >
                            <ListView.View>
                                <GridView>
                                    <GridViewColumn Width="180" DisplayMemberBinding="{Binding Path=DateTime, StringFormat={}{0:MM-dd HH:mm:ss fff}}" Header="更新时间" />
                                    <GridViewColumn Width="180" DisplayMemberBinding="{Binding Path=EquipmentName}" Header="设备名称" />
                                    <GridViewColumn Width="180" DisplayMemberBinding="{Binding Path=Name}" Header="名称" />
                                    <GridViewColumn Width="180" DisplayMemberBinding="{Binding Path=TypeName}" Header="类型" />
                                    <GridViewColumn Width="150" DisplayMemberBinding="{Binding Path=InfoAddr}" Header="调度地址" />
                                    <GridViewColumn Width="150" DisplayMemberBinding="{Binding Path=Value}" Header="数值" />
                                </GridView>
                            </ListView.View>
                        </ListView>
                    </ScrollViewer>
                </TabItem>
            </TabControl>

        </DockPanel>
    </Grid>
</Window>
