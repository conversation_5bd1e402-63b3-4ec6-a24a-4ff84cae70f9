﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbAccessController
    {
        public string CtlerId { get; set; } = null!;
        public string CtlerName { get; set; } = null!;
        public string CtlerType { get; set; } = null!;
        public int DoorNum { get; set; }
        public string? DoorNames { get; set; }
        public string? NetAddr { get; set; }
        public int? Port { get; set; }
        public string? UserName { get; set; }
        public string? Passowrd { get; set; }
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public string UseState { get; set; } = null!;
        public string? Comment { get; set; }
        public int? SerialNo { get; set; }

        public virtual IaStatAccCtler? IaStatAccCtler { get; set; }
    }
}
