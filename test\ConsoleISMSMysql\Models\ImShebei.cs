﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImShebei
    {
        public ImShebei()
        {
            ImShebeiDevicedata = new HashSet<ImShebeiDevicedata>();
            ImShebeiProtdevice = new HashSet<ImShebeiProtdevice>();
        }

        public string Shebeiid { get; set; }
        public int Sblxbm { get; set; }
        public string Statcode { get; set; }
        public string Yunxingbh { get; set; }
        public string Shebeibh { get; set; }
        public string Shebeixh { get; set; }
        public string Zhizaochang { get; set; }
        public DateTime? Chuchangrq { get; set; }
        public DateTime? Anzhuangrq { get; set; }
        public DateTime? Touyunrq { get; set; }
        public double? Guz<PERSON>az<PERSON> { get; set; }
        public string Guzibh { get; set; }
        public string <PERSON><PERSON><PERSON> { get; set; }
        public string <PERSON><PERSON><PERSON> { get; set; }
        public string <PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string Beizhu { get; set; }

        public virtual ImShebeilx SblxbmNavigation { get; set; }
        public virtual ImStation StatcodeNavigation { get; set; }
        public virtual ICollection<ImShebeiDevicedata> ImShebeiDevicedata { get; set; }
        public virtual ICollection<ImShebeiProtdevice> ImShebeiProtdevice { get; set; }
    }
}
