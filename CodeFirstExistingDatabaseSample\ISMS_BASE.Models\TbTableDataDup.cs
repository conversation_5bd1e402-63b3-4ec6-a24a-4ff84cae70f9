﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbTableDataDup
    {
        public string DataDupId { get; set; } = null!;
        public string TableMapId { get; set; } = null!;
        public string? SrcWhereClause { get; set; }
        public string DataDupCtgy { get; set; } = null!;

        public virtual TbTableMap TableMap { get; set; } = null!;
    }
}
