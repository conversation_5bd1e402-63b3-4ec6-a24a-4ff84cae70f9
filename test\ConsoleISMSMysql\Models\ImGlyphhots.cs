﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImGlyphhots
    {
        public string Glyphid { get; set; }
        public short Hotindex { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public short Hottype { get; set; }
        public string Connectedglyph { get; set; }

        public virtual ImGlyph Glyph { get; set; }
    }
}
