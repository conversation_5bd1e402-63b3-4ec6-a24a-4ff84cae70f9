﻿using Abp.Application.Services;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using YunDa.ISAS.DataTransferObject;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    /// <summary>
    /// 能耗概览服务接口
    /// </summary>
    public interface IEnergyConsumptionOverviewAppService: IApplicationService
    {
        /// <summary>
        /// 获取功率数据
        /// </summary>
        /// <param name="RealTimePowerType">实时功率类型</param>
        /// <returns>功率数据</returns>
        Task<RequestResult<RealTimePower>> GetPowerStatistics(RealTimePowerTypeEnum RealTimePowerType);
        
        /// <summary>
        /// 获取实时功率因数数据
        /// </summary>
        /// <returns>功率因数</returns>
        Task<RequestResult<float>> GetRealTimePowerFactor();
        
        /// <summary>
        /// 获取馈线能耗分布
        /// </summary>
        /// <param name="interval">时间间隔类型</param>
        /// <returns>馈线能耗分布数据</returns>
        Task<RequestResult<FeederEnergyDistribution>> GetFeederEnergyDistribution(RealTimePowerTypeEnum interval = RealTimePowerTypeEnum.Daily);
        
        /// <summary>
        /// 获取馈线累计能耗
        /// </summary>
        /// <param name="interval">时间间隔类型</param>
        /// <returns>馈线累计能耗数据</returns>
        Task<RequestResult<FeederCumulativeEnergy>> GetFeederCumulativeEnergyConsumption(RealTimePowerTypeEnum interval);
    }
}
