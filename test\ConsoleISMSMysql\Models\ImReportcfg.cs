﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImReportcfg
    {
        public ImReportcfg()
        {
            ImReportcfgdata = new HashSet<ImReportcfgdata>();
        }

        public string Id { get; set; }
        public string Rptname { get; set; }
        public string Rpttype { get; set; }
        public DateTime Createtime { get; set; }
        public string Templatename { get; set; }
        public int? Expstartrow { get; set; }
        public int? Expstartcol { get; set; }

        public virtual ImReportType RpttypeNavigation { get; set; }
        public virtual ICollection<ImReportcfgdata> ImReportcfgdata { get; set; }
    }
}
