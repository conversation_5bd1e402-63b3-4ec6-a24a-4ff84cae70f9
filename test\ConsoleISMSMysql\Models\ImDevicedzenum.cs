﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDevicedzenum
    {
        public int EnumTypeid { get; set; }
        public int EnumIndex { get; set; }
        public string EnumComment { get; set; }
        public string Manufacturer { get; set; }

        public virtual ImManufacturer ManufacturerNavigation { get; set; }
    }
}
