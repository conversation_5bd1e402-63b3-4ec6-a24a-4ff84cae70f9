<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <RootNamespace>OneClickPublishWeb</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="cfg\config.json" />
    <None Remove="cfg\isas.pfx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="cfg\config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Web.Administration" Version="11.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="cfg\isas.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
