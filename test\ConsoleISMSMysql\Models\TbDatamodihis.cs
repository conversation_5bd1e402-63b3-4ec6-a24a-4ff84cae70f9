﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbDatamodihis
    {
        public int Id { get; set; }
        public string Type { get; set; }
        public string Dsttable { get; set; }
        public string Dstobjname { get; set; }
        public string Dstobjid { get; set; }
        public string Editor { get; set; }
        public DateTime Edittime { get; set; }
        public string Content { get; set; }
    }
}
