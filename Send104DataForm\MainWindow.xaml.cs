﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Documents;
using ToolLibrary;
using Yunda.ISAS.MongoDB.Entities.MeasuresTemperature;

namespace Send104DataForm
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;
        }
        private bool isTesting = false;
        public int DevAddr { get; set; } = 142;
        public int CpuSector { get; set; } = 1;
        public int StartInfoAddr { get; set; } = 1783;
        public int InfoCount { get; set; } = 300;
        public int Interval { get; set; } = 1000;
        public string Url { get; set; } = "192.168.81.32";

        //int _devAddr = 142;
        //int cpuSector = 1;
        //int startInfoAddr = 1783;
        //int _infoCount = 1000;
        //string _requestSendDataUri = "http://192.168.81.32:32346/Iec104Data/SendData";

        double testValueMax = 40.0;
        double testValueMin = -40.0;

        //private void StartTest()
        //{
        //    Random random = new Random();

        //    // 生成-40到40之间的随机浮点数
           
        //    for (int i = startInfoAddr; i < startInfoAddr+ _infoCount; i++)
        //    {
        //        IecServerData input = new IecServerData()
        //        {
        //            devCpu = (byte)cpuSector ,
        //            dataType = 1,
        //            dateTime = DateTime.Now,
        //            devAddr = (byte)_devAddr,
        //            inf = i,
        //            ycValue = (float)( random.NextDouble() * (80) - 40)
        //        };
                
        //        HttpHelper.HttpPostRequest
        //           (_requestSendDataUri, input);
        //        Task.Delay(100).Wait();
        //    }
            
        //}
        private async void StartTest_Click(object sender, RoutedEventArgs e)
        {
            if (!isTesting)
            {
                isTesting = true;
                Paragraph paragraph = new Paragraph();
                paragraph.Inlines.Add(new Run($"测试开始\n"));
                InfoTextBlock.Document.Blocks.Add(paragraph);
                await Task.Run(() => StartTest());
                paragraph.Inlines.Add(new Run($"测试结束\n"));
                InfoTextBlock.Document.Blocks.Add(paragraph);
                isTesting = false;
            }
            else
            {
                MessageBox.Show("测试正在进行中，请等待测试结束后再开始新的测试。");
            }
        }
        private void StopTest_Click(object sender, RoutedEventArgs e)
        {
            isTesting = false;
            Paragraph paragraph = new Paragraph();
            paragraph.Inlines.Add(new Run($"测试被停止\n"));
            InfoTextBlock.Document.Blocks.Add(paragraph);
        }

        private void StartTest()
        {
            Random random = new Random();
            while (true)
            {
                Dispatcher.Invoke(() => InfoTextBlock.Document.Blocks.Clear());
                for (int i = StartInfoAddr; i < StartInfoAddr + InfoCount && isTesting; i++)
                {
                    IecServerData input = new IecServerData()
                    {
                        devName = "",
                        devCpu = (byte)CpuSector,
                        dataType = 1,
                        dateTime = DateTime.Now,
                        devAddr = (byte)DevAddr,
                        inf = i,
                        ycValue = (float)(random.NextDouble() * (80) - 40)
                    };

                    // 模拟HTTP请求
                    HttpHelper.HttpPostRequest($"http://{Url}:32346/Iec104Data/SendData", input);

                    // 打印信息
                    Dispatcher.Invoke(() =>
                    {
                        Paragraph paragraph = new Paragraph();
                        paragraph.Inlines.Add(new Run($"发送数据：地址：{i}，值{input.ycValue}\n"));
                        InfoTextBlock.Document.Blocks.Add(paragraph);
                        InfoTextBlock.ScrollToEnd(); // 将滚动条滚动到最底部
                    });

                }
                Task.Delay(Interval).Wait();
            }

        }
    }

    public struct IecServerData
    {
        /// <summary>
        /// 数据类型 1遥测 2遥信
        /// </summary>
        public int dataType { get; set; }
        /// <summary>
        /// 装置地址
        /// </summary>
        public byte devAddr { get; set; }
        /// <summary>
        /// cpu扇区号
        /// </summary>
        public byte devCpu { get; set; }
        /// <summary>
        /// 信息体地址
        /// </summary>
        public int inf { get; set; }
        /// <summary>
        /// 遥测值
        /// </summary>
        public float ycValue { get; set; }
        /// <summary>
        /// 遥信值
        /// </summary>
        public int yxValue { get; set; }
        /// <summary>
        /// 时间
        /// </summary>
        public DateTime dateTime { get; set; }
        /// <summary>
        /// 装置名称
        /// </summary>
        public string devName { get; set; }
        /// <summary>
        /// 测温结果，可不填
        /// </summary>
        public MeasureTemperatureResult measureTemperatureResult { get; set; }
    }
}
