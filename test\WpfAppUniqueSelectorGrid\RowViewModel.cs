﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace UniqueSelectorGrid
{
    public class RowViewModel : INotifyPropertyChanged
    {
        private readonly MainViewModel _parent;
        private string _dataSourceCategoryName;
        private string _deviceName;
        private bool _isEnabled;
        private string _dataSourceType;
        private DateTime _timestamp;

        public RowViewModel(MainViewModel parent)
        {
            _parent = parent;
            _timestamp = DateTime.Now;
            _isEnabled = true;
        }

        public string DataSourceCategoryName
        {
            get => _dataSourceCategoryName;
            set
            {
                if (_dataSourceCategoryName != value)
                {
                    _dataSourceCategoryName = value;
                    OnPropertyChanged(nameof(DataSourceCategoryName));
                    foreach (var row in _parent.Rows)
                        row.OnPropertyChanged(nameof(FilteredCategoryList));
                }
            }
        }

        public string DeviceName
        {
            get => _deviceName;
            set
            {
                if (_deviceName != value)
                {
                    _deviceName = value;
                    OnPropertyChanged(nameof(DeviceName));
                }
            }
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    OnPropertyChanged(nameof(IsEnabled));
                }
            }
        }

        public string DataSourceType
        {
            get => _dataSourceType;
            set
            {
                if (_dataSourceType != value)
                {
                    _dataSourceType = value;
                    OnPropertyChanged(nameof(DataSourceType));
                }
            }
        }

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged(nameof(Timestamp));
                }
            }
        }

        public IEnumerable<string> FilteredCategoryList =>
            _parent.AllCategories.Except(
                _parent.Rows
                       .Where(r => r != this)
                       .Select(r => r.DataSourceCategoryName)
                       .Where(x => !string.IsNullOrWhiteSpace(x))
            );

        public event PropertyChangedEventHandler PropertyChanged;
        public void OnPropertyChanged(string propName) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propName));
    }
}
