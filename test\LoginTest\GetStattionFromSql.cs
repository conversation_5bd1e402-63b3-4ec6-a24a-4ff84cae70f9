﻿using System;
using System.IO;
using System.Text.RegularExpressions;

namespace LoginTest
{
    
    public static class GetStattionFromSql
    {
        public static void Run()
        {
            string backupFilePath = "D:\\桌面\\isas_sys_db\\isas_backup_20230919202525.sql"; // 备份文件路径
            string tableName = "gi_transformer_substation";

            try
            {
                using (StreamReader reader = new StreamReader(backupFilePath))
                {
                    string line;
                    bool isInTableSection = false;

                    while ((line = reader.ReadLine()) != null)
                    {
                        // 检查是否进入表的定义部分
                        if (line.Contains($"CREATE TABLE `{tableName}`"))
                        {
                            isInTableSection = true;
                            continue;
                        }

                        // 在表定义部分内，查找INSERT INTO语句
                        if (isInTableSection && line.Trim().StartsWith("INSERT INTO `gi_transformer_substation`"))
                        {
                            // 提取INSERT INTO语句的值部分
                            string valuesPart = line.Substring(line.IndexOf("(") + 1).TrimEnd(';');

                            // 分割值部分并提取字段的值
                            string[] values = valuesPart.Split(',');

                            string id = values[0].Trim('\'');
                            string creatorUserId = values[1].Trim('\'');
                            string creationTime = values[2].Trim('\'');
                            string lastModificationTime = values[3].Trim('\'');
                            string lastModifierUserId = values[4].Trim('\'');
                            int seqNo = int.Parse(values[5].Trim());
                            string substationName = values[6].Trim('\'');

                            
                            if (substationName.Contains("所"))
                            {
                                Console.WriteLine($"SubstationName: {substationName}");

                            }
                            // 可以继续提取其他字段的值
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }
            Console.WriteLine("啥都没找到");
        }
    }
    public static class GetLastVersion
    {
        public static void Run()
        {
            string backupFilePath = "D:\\桌面\\isas_sys_db\\isas_backup_20230919202525.sql"; // 备份文件路径

            string tableName = "__efmigrationshistory";

            try
            {
                string lastInsertValue = "";

                using (StreamReader reader = new StreamReader(backupFilePath))
                {
                    string line;

                    while ((line = reader.ReadLine()) != null)
                    {
                        // 查找INSERT INTO语句
                        if (line.Trim().StartsWith("INSERT INTO `__efmigrationshistory") && line.Contains($"`{tableName}`"))
                        {
                            // 提取INSERT INTO语句的值部分
                            string valuesPart = line.Substring(line.IndexOf("(") + 1).TrimEnd(';');

                            // 使用正则表达式提取最后一个值
                            MatchCollection matches = Regex.Matches(valuesPart, @"'([^']+)'");
                            if (matches.Count > 0)
                            {
                                lastInsertValue = matches[matches.Count - 2].Groups[1].Value;
                            }
                        }
                    }
                }

                // 输出最后一个插入值
                Console.WriteLine("最后一个插入值:");
                Console.WriteLine(lastInsertValue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }
        }
        static string ParseLastInsertValue(string insertStatement)
        {
            // 在INSERT INTO语句中查找最后一个值
            string[] values = insertStatement.Split(',');
            string lastValue = values[values.Length - 1];

            // 去除首尾括号和单引号
            lastValue = lastValue.Trim('(', ')', '\'');

            return lastValue;
        }
    }
}


