﻿using Microsoft.Win32;
using MQTTnet;
using MQTTnet.Server;
using MqttServerConsoleApp;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MqttWpfApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            //var str = Convert.FromBase64String("A2cnAQRoVw==");
            byte[] bytes = new byte[] { 0x03, 0x67, 0x27, 0x01, 0x04, 0x68, 0x57 };
            IntPtr intPtr = ToolLibrary.IntPtrSwitch.ByteToIntPtr(bytes);
            string resStr = "";
            var res = TransferDllResolving.sumTest2(intPtr, bytes.Length,1,ref resStr);
        }
        private async void Button_Click(object sender, RoutedEventArgs e)
        {
            var mqttFactory = new MqttFactory();
            // The port for the default endpoint is 1883.
            // The default endpoint is NOT encrypted!
            // Use the builder classes where possible.
            var mqttServerOptions = new MqttServerOptionsBuilder().WithDefaultEndpoint().Build();
            var mqttServer = mqttFactory.CreateMqttServer(mqttServerOptions);
                await mqttServer.StartAsync();
                mqttServer.SubscribeAsync("ug67", "uplink");
                mqttServer.SubscribeAsync("ug67", "downlink/$deveui");
                mqttServer.InterceptingPublishAsync += MqttServer_InterceptingPublishAsync;
            this.start.IsEnabled = false;
                // Stop and dispose the MQTT server if it is no longer needed!
                //await mqttServer.StopAsync();
        }
        private  Task MqttServer_InterceptingPublishAsync(InterceptingPublishEventArgs arg)
        {
            var str = Encoding.UTF8.GetString(arg.ApplicationMessage.Payload);
            Dispatcher.Invoke(() => { 
                this.list.Items.Add(str);
                var json = JObject.Parse(str);
                var datastr =  json["data"].ToString();
                var bytes = Convert.FromBase64String(datastr);
                
            });
            return Task.CompletedTask;
        }
        private void store_Click(object sender, RoutedEventArgs e)
        {
            SaveFileDialog sfd = new SaveFileDialog();
            //设置这个对话框的起始保存路径
            sfd.InitialDirectory = @"D:\";
            //设置保存的文件的类型，注意过滤器的语法
            sfd.Filter = "JSON文件|*.json|文件文件|*.txt";
            //调用ShowDialog()方法显示该对话框，该方法的返回值代表用户是否点击了确定按钮
            if (sfd.ShowDialog() == true)
            {
                var filename = sfd.FileName;

                using (StreamWriter sw = new StreamWriter(filename))
                {
                    foreach (var item in this.list.Items)
                    {
                        sw.WriteLine(item.ToString());
                    }
                }

                MessageBox.Show("保存成功");
            }
            else
            {
                MessageBox.Show("取消保存");

            }
        }
    }
}
