﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunDa.ISAS.Entities.MobileSurveillance;

namespace LoginTest
{
    public static class FindIndexTest
    {
        public static void Run()
        {
            string str = "304,306,344,346,354,358,418,420";
            var seqNo = (str.Split(',')?.ToList()?.FindIndex(t => t == "344") + 1).ToString();
            Console.WriteLine(seqNo);
        }
    }
}
