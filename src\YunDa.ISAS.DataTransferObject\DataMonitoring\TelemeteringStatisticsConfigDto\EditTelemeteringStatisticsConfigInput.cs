using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using System;
using System.ComponentModel.DataAnnotations;
using YunDa.ISAS.Entities.CommonDto;
using YunDa.ISAS.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsConfigDto
{
    /// <summary>
    /// 遥测统计配置输入DTO
    /// </summary>
    [AutoMapTo(typeof(TelemeteringStatisticsConfig))]
    public class EditTelemeteringStatisticsConfigInput : ISASAuditedEntityDto
    {
        /// <summary>
        /// 关联的遥测配置ID
        /// </summary>
        [Required]
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        [Required]
        public virtual int StatisticsType { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        [Required]
        public virtual int IntervalType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public virtual bool IsActive { get; set; } = true;

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(200)]
        public virtual string Description { get; set; }
    }
} 