﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDevCtgy
    {
        public ImDevCtgy()
        {
            ImAnalogData2010s = new HashSet<ImAnalogData2010>();
            ImEventFlags = new HashSet<ImEventFlag>();
            ImEventParams = new HashSet<ImEventParam>();
            ImEventType2010s = new HashSet<ImEventType2010>();
            ImFaultType2010s = new HashSet<ImFaultType2010>();
            ImPuCtgies = new HashSet<ImPuCtgy>();
        }

        public string DevCtgyCode { get; set; } = null!;
        public string DevCtgyName { get; set; } = null!;

        public virtual ICollection<ImAnalogData2010> ImAnalogData2010s { get; set; }
        public virtual ICollection<ImEventFlag> ImEventFlags { get; set; }
        public virtual ICollection<ImEventParam> ImEventParams { get; set; }
        public virtual ICollection<ImEventType2010> ImEventType2010s { get; set; }
        public virtual ICollection<ImFaultType2010> ImFaultType2010s { get; set; }
        public virtual ICollection<ImPuCtgy> ImPuCtgies { get; set; }
    }
}
