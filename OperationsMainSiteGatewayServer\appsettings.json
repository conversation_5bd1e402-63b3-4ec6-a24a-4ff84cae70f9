{"ConnectionStrings": {"MysqlSetting": "server=127.0.0.1;port=3306;uid=root;pwd=******; Database=soms_sys_db;SslMode=none;Pooling=False;", "ISMS_BASESqlServerSetting": "Server=***************;User ID=**;Password=**;Database=ISMS_BASE;Trusted_Connection=False;TrustServerCertificate=True", "ISMS_YcSqlServerSetting": "Server=***************;User ID=**;Password=**;Database=ISMS_Yc;Trusted_Connection=False;TrustServerCertificate=True", "ISMS_DataSqlServerSetting": "Server=***************;User ID=**;Password=**;Database=ISMS_Data;Trusted_Connection=False;TrustServerCertificate=True", "ISMS_ReportServerSqlServerSetting": "Server=***************;User ID=**;Password=**;Database=ReportServer;Trusted_Connection=False;TrustServerCertificate=True", "ISMS_ReportServerTempDBSqlServerSetting": "Server=***************;User ID=**;Password=**;Database=ReportServerTempDB;Trusted_Connection=False;TrustServerCertificate=True", "MongoDBSetting": {"Host": "127.0.0.1", "Port": "37017", "DatabaseName": "i**s_mongodb", "IsAuth": "false", "UserName": "i**sAdmin", "PassWord": "******"}, "RedisSetting": {"Host": "127.0.0.1", "Port": "36379", "Auth": "yunda123", "Name": "", "ClusterType": null, "DefaultDatabaseIndex": "0"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Mes**ge:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "../Logs/OperationsMainSiteGatewayServerLog-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Mes**ge:lj}{NewLine}{Exception}"}}, {"Name": "Seq", "Args": {"serverUrl": "http://localhost:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "MyApplication"}}, "addr": 10}