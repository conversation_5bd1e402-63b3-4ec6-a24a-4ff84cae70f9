﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAlertLevel
    {
        public ImAlertLevel()
        {
            ImAlertTypes = new HashSet<ImAlertType>();
            ImDeviceYxTmps = new HashSet<ImDeviceYxTmp>();
            ImDeviceYxes = new HashSet<ImDeviceYx>();
            ImEventType2010s = new HashSet<ImEventType2010>();
            ImEventTypes = new HashSet<ImEventType>();
        }

        public string AlertLevelCode { get; set; } = null!;
        public string AlertLevelName { get; set; } = null!;
        public int Color { get; set; }

        public virtual ICollection<ImAlertType> ImAlertTypes { get; set; }
        public virtual ICollection<ImDeviceYxTmp> ImDeviceYxTmps { get; set; }
        public virtual ICollection<ImDeviceYx> ImDeviceYxes { get; set; }
        public virtual ICollection<ImEventType2010> ImEventType2010s { get; set; }
        public virtual ICollection<ImEventType> ImEventTypes { get; set; }
    }
}
