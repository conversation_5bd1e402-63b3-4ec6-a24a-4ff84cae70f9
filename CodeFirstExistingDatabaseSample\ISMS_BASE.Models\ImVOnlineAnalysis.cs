﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVOnlineAnalysis
    {
        public string Id { get; set; } = null!;
        public string Method { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public string? DataIdofC2h2 { get; set; }
        public string? DataIdofC2h4 { get; set; }
        public string? DataIdofC2h6 { get; set; }
        public string? DataIdofCh4 { get; set; }
        public string? DataIdofH2 { get; set; }
        public string UseState { get; set; } = null!;
        public string IsAlert { get; set; } = null!;
        public string StatName { get; set; } = null!;
        public string DeviceName { get; set; } = null!;
        public string? C2h2Ycname { get; set; }
        public string? C2h4Ycname { get; set; }
        public string? C2h6Ycname { get; set; }
        public string? Ch4Ycname { get; set; }
        public string? H2Ycname { get; set; }
    }
}
