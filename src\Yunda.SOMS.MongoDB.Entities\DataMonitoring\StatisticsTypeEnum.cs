using System;
using System.ComponentModel;

namespace Yunda.SOMS.MongoDB.Entities.DataMonitoring
{
    /// <summary>
    /// 统计类型枚举
    /// </summary>
    public enum StatisticsTypeEnum
    {
        /// <summary>
        /// 实时值
        /// </summary>
        [Description("实时值")]
        RealTime = 0,

        /// <summary>
        /// 最大值
        /// </summary>
        [Description("最大值")]
        Maximum = 1,

        /// <summary>
        /// 最小值
        /// </summary>
        [Description("最小值")]
        Minimum = 2,

        /// <summary>
        /// 平均值
        /// </summary>
        [Description("平均值")]
        Average = 3,

        /// <summary>
        /// 差值（与上一时间点的差）
        /// </summary>
        [Description("差值")]
        Difference = 4,

        /// <summary>
        /// 累计值
        /// </summary>
        [Description("累计值")]
        Accumulated = 5,

        /// <summary>
        /// 标准差
        /// </summary>
        [Description("标准差")]
        StandardDeviation = 6,

        /// <summary>
        /// 中位数
        /// </summary>
        [Description("中位数")]
        Median = 7
    }
} 