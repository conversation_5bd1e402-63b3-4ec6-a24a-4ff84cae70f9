﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace YunDa.ISAS.Migrations
{
    public partial class update_table_v103 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "dm_energy_consumption_config",
                columns: table => new
                {
                    Id = table.Column<Guid>(nullable: false, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    CreatorUserId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    CreationTime = table.Column<DateTime>(nullable: false),
                    LastModificationTime = table.Column<DateTime>(nullable: true),
                    LastModifierUserId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    SeqNo = table.Column<int>(nullable: false),
                    TelesignalisationId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    TelemeteringId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    ConfigValue = table.Column<string>(maxLength: 500, nullable: true),
                    Description = table.Column<string>(maxLength: 200, nullable: true),
                    ConfigType = table.Column<int>(nullable: false),
                    ValueType = table.Column<int>(nullable: false),
                    IsActive = table.Column<bool>(nullable: false),
                    Remark = table.Column<string>(maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_energy_consumption_config", x => x.Id);
                    table.ForeignKey(
                        name: "FK_dm_energy_consumption_config_dm_telemetering_Configuration_T~",
                        column: x => x.TelemeteringId,
                        principalTable: "dm_telemetering_Configuration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_dm_energy_consumption_config_dm_telesignalisation_configurat~",
                        column: x => x.TelesignalisationId,
                        principalTable: "dm_telesignalisation_configuration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dm_energy_consumption_device",
                columns: table => new
                {
                    Id = table.Column<Guid>(nullable: false, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    CreatorUserId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    CreationTime = table.Column<DateTime>(nullable: false),
                    LastModificationTime = table.Column<DateTime>(nullable: true),
                    LastModifierUserId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    SeqNo = table.Column<int>(nullable: false),
                    DeviceType = table.Column<int>(nullable: false),
                    Status = table.Column<int>(nullable: false),
                    IsOperating = table.Column<bool>(nullable: false),
                    LastOperationTime = table.Column<DateTime>(nullable: true),
                    EquipmentInfoId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    MonitoringConfig = table.Column<string>(nullable: true),
                    IsActive = table.Column<bool>(nullable: false),
                    Remark = table.Column<string>(maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_energy_consumption_device", x => x.Id);
                    table.ForeignKey(
                        name: "FK_dm_energy_consumption_device_gi_equipment_info_EquipmentInfo~",
                        column: x => x.EquipmentInfoId,
                        principalTable: "gi_equipment_info",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "dm_energy_operation_criteria",
                columns: table => new
                {
                    Id = table.Column<Guid>(nullable: false, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    CreatorUserId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    CreationTime = table.Column<DateTime>(nullable: false),
                    LastModificationTime = table.Column<DateTime>(nullable: true),
                    LastModifierUserId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    SeqNo = table.Column<int>(nullable: false),
                    Name = table.Column<string>(maxLength: 100, nullable: false),
                    DeviceType = table.Column<int>(nullable: false),
                    ParameterType = table.Column<int>(nullable: false),
                    TelesignalisationId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    TelemeteringId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    ParameterName = table.Column<string>(maxLength: 100, nullable: true),
                    Threshold = table.Column<double>(nullable: false),
                    ComparisonType = table.Column<int>(nullable: false),
                    Priority = table.Column<int>(nullable: false),
                    SourceType = table.Column<int>(nullable: false),
                    EquipmentInfoId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    EnergyDeviceId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36) ,
                    TransformerSubstationId = table.Column<Guid>(nullable: true, type: "char(36) CHARACTER SET utf8mb4", maxLength: 36),
                    InfoAddress = table.Column<string>(nullable: true),
                    IsActive = table.Column<bool>(nullable: false),
                    Remark = table.Column<string>(maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_energy_operation_criteria", x => x.Id);
                    table.ForeignKey(
                        name: "FK_dm_energy_operation_criteria_dm_energy_consumption_device_En~",
                        column: x => x.EnergyDeviceId,
                        principalTable: "dm_energy_consumption_device",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_dm_energy_operation_criteria_dm_telemetering_Configuration_T~",
                        column: x => x.TelemeteringId,
                        principalTable: "dm_telemetering_Configuration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_dm_energy_operation_criteria_dm_telesignalisation_configurat~",
                        column: x => x.TelesignalisationId,
                        principalTable: "dm_telesignalisation_configuration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_consumption_config_TelemeteringId",
                table: "dm_energy_consumption_config",
                column: "TelemeteringId");

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_consumption_config_TelesignalisationId",
                table: "dm_energy_consumption_config",
                column: "TelesignalisationId");

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_consumption_device_EquipmentInfoId",
                table: "dm_energy_consumption_device",
                column: "EquipmentInfoId");

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_operation_criteria_EnergyDeviceId",
                table: "dm_energy_operation_criteria",
                column: "EnergyDeviceId");

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_operation_criteria_TelemeteringId",
                table: "dm_energy_operation_criteria",
                column: "TelemeteringId");

            migrationBuilder.CreateIndex(
                name: "IX_dm_energy_operation_criteria_TelesignalisationId",
                table: "dm_energy_operation_criteria",
                column: "TelesignalisationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "dm_energy_consumption_config");

            migrationBuilder.DropTable(
                name: "dm_energy_operation_criteria");

            migrationBuilder.DropTable(
                name: "dm_energy_consumption_device");
        }
    }
}
