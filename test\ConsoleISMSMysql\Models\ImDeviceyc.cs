﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDeviceyc
    {
        public string Id { get; set; }
        public string Ycname { get; set; }
        public string Unit { get; set; }
        public double? Cof { get; set; }
        public int? Precise { get; set; }
        public double? Uplimit { get; set; }
        public double? Downlimit { get; set; }
        public double? Intl4save { get; set; }
        public string Unit2 { get; set; }
        public double? Cof2 { get; set; }
        public int? Precise2 { get; set; }
        public string Savemode { get; set; }
        public double? Uplimit4stat { get; set; }

        public virtual ImDevicedata IdNavigation { get; set; }
    }
}
