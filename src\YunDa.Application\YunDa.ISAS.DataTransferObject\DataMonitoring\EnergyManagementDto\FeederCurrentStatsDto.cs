﻿using System;
using System.Collections.Generic;
using System.Text;

namespace YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto
{
    /// <summary>
    /// 馈线电流数据统计
    /// </summary>
    public class FeederCurrentStatsDto
    {
        /// <summary>
        /// 时间标签
        /// </summary>
        public List<string> TimeLabels { get; set; } = new List<string>();

        /// <summary>
        /// 馈线电流数据序列
        /// </summary>
        public List<FeederCurrentSeries> FeederSeries { get; set; } = new List<FeederCurrentSeries>();

        /// <summary>
        /// 查询间隔类型
        /// </summary>
        public RealTimePowerTypeEnum IntervalType { get; set; }
    }

    /// <summary>
    /// 馈线电流数据序列
    /// </summary>
    public class FeederCurrentSeries
    {
        /// <summary>
        /// 馈线名称
        /// </summary>
        public string FeederName { get; set; }

        /// <summary>
        /// 电流值序列（单位：A）
        /// </summary>
        public List<float> Values { get; set; } = new List<float>();

        /// <summary>
        /// 当前电流值（最新值）
        /// </summary>
        public float CurrentValue => Values.Count > 0 ? Values[Values.Count - 1] : 0;

        /// <summary>
        /// 最大电流值
        /// </summary>
        public float MaxValue
        {
            get
            {
                if (Values.Count == 0) return 0;
                float max = float.MinValue;
                foreach (var value in Values)
                {
                    if (value > max) max = value;
                }
                return max;
            }
        }

        /// <summary>
        /// 最小电流值
        /// </summary>
        public float MinValue
        {
            get
            {
                if (Values.Count == 0) return 0;
                float min = float.MaxValue;
                foreach (var value in Values)
                {
                    if (value > 0 && value < min) min = value;
                }
                return min == float.MaxValue ? 0 : min;
            }
        }

        /// <summary>
        /// 平均电流值
        /// </summary>
        public float AvgValue
        {
            get
            {
                if (Values.Count == 0) return 0;
                float sum = 0;
                int count = 0;
                foreach (var value in Values)
                {
                    if (value > 0)
                    {
                        sum += value;
                        count++;
                    }
                }
                return count > 0 ? sum / count : 0;
            }
        }
    }
}
