<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>
	<ItemGroup>
		<!-- EF Core SQLite 提供程序 -->
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="3.1.32" />

		<!-- EF Core 设计时工具包 -->
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="3.1.32" />

		<!-- 可选：EF Core 工具包 -->
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.32" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="iodb1.sql3">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>
