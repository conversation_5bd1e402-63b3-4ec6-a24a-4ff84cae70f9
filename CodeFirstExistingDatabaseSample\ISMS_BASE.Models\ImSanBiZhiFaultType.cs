﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImSanBiZhiFaultType
    {
        public ImSanBiZhiFaultType()
        {
            ImSanBiZhiFaultDefines = new HashSet<ImSanBiZhiFaultDefine>();
        }

        public int FltTypeCode { get; set; }
        public string FltTypeName { get; set; } = null!;
        public string? FltTypeExam { get; set; }

        public virtual ICollection<ImSanBiZhiFaultDefine> ImSanBiZhiFaultDefines { get; set; }
    }
}
