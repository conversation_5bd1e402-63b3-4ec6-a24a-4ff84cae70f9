﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImVDeviceyx
    {
        public string Id { get; set; }
        public string Yxname { get; set; }
        public string YxType { get; set; }
        public string Swonstr { get; set; }
        public string Swoffstr { get; set; }
        public string Swuncertstr { get; set; }
        public string Alertlevel { get; set; }
        public string Normalstate { get; set; }
        public int Deviceaddr { get; set; }
        public string Devicename { get; set; }
        public string Statcode { get; set; }
        public string Gatewayid { get; set; }
        public string Deviceid { get; set; }
        public string Datatype { get; set; }
        public string Dataname { get; set; }
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int Autosave { get; set; }
        public int Visible { get; set; }
    }
}
