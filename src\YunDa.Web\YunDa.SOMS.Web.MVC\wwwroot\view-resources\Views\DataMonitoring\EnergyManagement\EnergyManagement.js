$(document).ready(function () {
    energyManagement.init();

    substationTree.isShowEquipment = false;
    substationTree.initTree(subTreeChanged);
    $(".refresh-btn").click(function () {
        substationTree.initTree(subTreeChanged);
        substationTree.refreshTree();
    })
})
var energyManagement = {
    init: function () {
        var height = $(".full-height").height() - 60;
        
        energyDeviceList.initListFunc(height);
        energyCriteriaList.initListFunc(height);
        energyConfigList.initListFunc(height);
        // 初始化权限
        //this.authoritySetFunc();
        
        $('#cardChildrenTab a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            var tabName = $(e.target).attr('href').substr(1);
            
            if (tabName === 'energyDevice') {
                energyDeviceList.refreshTable();
            } else if (tabName === 'energyCriteria') {
                energyCriteriaList.refreshTable();
            } else if (tabName === 'energyConfig') {
                energyConfigList.refreshTable();
            }
        });

        energyDeviceList.initTable();
        energyCriteriaList.initTable();
        energyConfigList.initTable();
        
        $('#importDataBtn').click(function() {
            var activeTabId = $('.tab-pane.active').attr('id');
            var fileType = "";
            
            if (activeTabId === 'energyDevice') {
                fileType = "EnergyDevice";
            } else if (activeTabId === 'energyCriteria') {
                fileType = "EnergyCriteria";
            } else if (activeTabId === 'energyConfig') {
                fileType = "EnergyConfig";
            }
            
            $('#uploadFileTypeInput').val(fileType);
        });
        
        $('#exportDataBtn').click(function() {
            var activeTabId = $('.tab-pane.active').attr('id');
            var fileType = "";
            
            if (activeTabId === 'energyDevice') {
                fileType = "EnergyDevice";
            } else if (activeTabId === 'energyCriteria') {
                fileType = "EnergyCriteria";
            } else if (activeTabId === 'energyConfig') {
                fileType = "EnergyConfig";
            }
            
            $('#downloadFileTypeInput').val(fileType);
        });
    },
    
    // 添加权限设置函数
    authoritySetFunc: function() {
        // 处理权限管理标签
        isas.authorityManagement({
            el: "[authority-management='true']",
            success: function() {
                // 显示有权限的按钮
                $("#energyDeviceToolBar button[authority-management='true']").show();
                $("#energyCriteriaToolBar button[authority-management='true']").show();
                $("#energyConfigToolBar button[authority-management='true']").show();
            }
        });
    }
};
var treeNode = { type: null };
function subTreeChanged(node) {
    treeNode = node;
}