﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_Data.Models
{
    public partial class ImYcstat202407
    {
        public string DataId { get; set; } = null!;
        public DateTime HourTime { get; set; }
        public int StatIntl { get; set; }
        public double HourValue { get; set; }
        public double MaxValue { get; set; }
        public DateTime MaxOccTime { get; set; }
        public double MinValue { get; set; }
        public DateTime MinOccTime { get; set; }
        public double AvgValue { get; set; }
        public int SeqNo { get; set; }
        public string State { get; set; } = null!;
        public string? BeiZhu { get; set; }
    }
}
