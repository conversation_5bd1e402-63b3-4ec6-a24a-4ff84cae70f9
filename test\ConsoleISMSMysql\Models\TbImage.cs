﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbImage
    {
        public string Objid { get; set; }
        public int Length { get; set; }
        public string Format { get; set; }
        public byte[] Data { get; set; }
    }
}
