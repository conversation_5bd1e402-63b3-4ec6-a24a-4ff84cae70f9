﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NetMQ.Sockets;
using Serilog;

namespace Iec104ComService
{
    public class StartUp
    {
        public static string Basepath = "D:/Project/test/Iec104ComService/bin/Debug/netcoreapp3.1";
        public StartUp()
        {

        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            Log.Logger = new LoggerConfiguration()
          .Enrich.FromLogContext()
          .WriteTo.Console()
          .WriteTo.File(Basepath + "/log.log",
          restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Verbose,
          outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u3}] Message:{Message:lj}{NewLine}{Exception}",
          rollingInterval: RollingInterval.Day
          , retainedFileCountLimit: 30, encoding: System.Text.Encoding.Unicode)
          .CreateLogger();
            return Host.CreateDefaultBuilder(args)
          .ConfigureServices((hostContext, services) =>
          {
              services.AddHostedService<SendIec104MsgWorker>()
              .AddHostedService<GetIec104MsgWorker>()
              .AddSingleton(new ConfigurationBuilder().AddJsonFile(Basepath + "/appsetting.json").Build())
              .AddSingleton(new PublisherSocket("tcp://*:54300"))
              .AddSingleton(new SubscriberSocket("tcp://*:54301"));
          }).UseWindowsService();
        }

    }
}
