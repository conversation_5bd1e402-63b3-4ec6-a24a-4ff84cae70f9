﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceDz
    {
        public string Id { get; set; } = null!;
        public string DeviceId { get; set; } = null!;
        public int CpuIndex { get; set; }
        public int DzIndex { get; set; }
        public string? DzName { get; set; }
        public string? DzComment { get; set; }
        public string? DzRange { get; set; }
        public double? DzMin { get; set; }
        public double? DzMax { get; set; }
        public string? DzUnit { get; set; }
        public double? DzCoeff { get; set; }
        public int? DzPrecise { get; set; }
        public string? DzUnit1 { get; set; }
        public double? DzCoeff1 { get; set; }
        public int? DzPrecise1 { get; set; }
        public int? CtrlWordTypeId { get; set; }
        public int DzType { get; set; }
        public int? EnumTypeId { get; set; }
        public double? DzUnitCvtCoeff { get; set; }
        public string? RelatePtId { get; set; }
        public string? RelateCtId { get; set; }
        public int ReadOnly { get; set; }
        public string Hidden { get; set; } = null!;

        public virtual ImProtectDevice Device { get; set; } = null!;
        public virtual ImDztype DzTypeNavigation { get; set; } = null!;
    }
}
