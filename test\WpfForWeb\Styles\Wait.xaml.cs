﻿using System.Windows;
using System.Windows.Controls;

namespace WpfForWeb
{
    /// <summary>
    /// Wait.xaml 的交互逻辑
    /// </summary>
    public partial class Wait : UserControl
    {
        public static DependencyProperty IsTextInfoStr;
        public string TextInfoStr
        {
            get { return (string)GetValue(IsTextInfoStr); }
            set { SetValue(IsTextInfoStr, value); }
        }

        static Wait()
        {
            PropertyMetadata pm = new PropertyMetadata("请稍等，正在取得数据......", new PropertyChangedCallback(SetTextInfo));
            IsTextInfoStr = DependencyProperty.Register("IsTextInfoStr", typeof(string), typeof(Wait), pm);
        }

        private static void SetTextInfo(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            Wait w = (Wait)d;

        }
        public Wait()
        {
            this.InitializeComponent();
        }
    }
}