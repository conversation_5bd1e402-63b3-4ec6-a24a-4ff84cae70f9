﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDzcheckrule
    {
        public int Puctgycode { get; set; }
        public string Dzname { get; set; }
        public string Rulename { get; set; }
        public string Destdzname { get; set; }
        public string Param { get; set; }
        public string Ruledesc { get; set; }
    }
}
