﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class ExtendedDataSource
    {
        public Guid Dsid { get; set; }
        public Guid? ItemId { get; set; }
        public Guid? SubscriptionId { get; set; }
        public string? Name { get; set; }
        public string? Extension { get; set; }
        public Guid? Link { get; set; }
        public int? CredentialRetrieval { get; set; }
        public string? Prompt { get; set; }
        public byte[]? ConnectionString { get; set; }
        public byte[]? OriginalConnectionString { get; set; }
        public bool? OriginalConnectStringExpressionBased { get; set; }
        public byte[]? UserName { get; set; }
        public byte[]? Password { get; set; }
        public int? Flags { get; set; }
        public int Version { get; set; }
    }
}
