﻿<Window x:Class="FileUploadWpf.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FileUploadWpf"
        mc:Ignorable="d"
        Title="MainWindow" Height="350" Width="525" Background="LightGray" BorderBrush="Gray" BorderThickness="2" Loaded="Form1_Load">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="60" />
            <RowDefinition Height="60" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
        <Border Grid.ColumnSpan="2" BorderBrush="Gray" BorderThickness="0,0,0,2">
            <ListView x:Name="lb_log" Background="Transparent" BorderThickness="0">
            </ListView>
        </Border>
        <Border Grid.Row="1">
            <TextBlock x:Name="tb_filePath" Text="" Margin="5" FontSize="16" />
        </Border>
        <Border Grid.Row="1" Grid.Column="1">
            <TextBlock x:Name="tb_uploadData" Text="" Margin="5" FontSize="16" />
        </Border>
        <Border BorderBrush="Gray" BorderThickness="0,2,0,0" Grid.ColumnSpan="2" Grid.Row="2">
            <WrapPanel VerticalAlignment="Center" HorizontalAlignment="Right">
                <Button Width="80" Height="40" Content="上传" FontSize="18" Margin="20,0" Click="button1_Click" />
            </WrapPanel>
        </Border>
    </Grid>
</Window>