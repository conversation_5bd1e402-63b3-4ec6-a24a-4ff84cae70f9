﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImFaulttypePd
    {
        public ImFaulttypePd()
        {
            ImFaultacttypePd = new HashSet<ImFaultacttypePd>();
            ImFaultparamPd = new HashSet<ImFaultparamPd>();
        }

        public int Faultcode { get; set; }
        public string Faultname { get; set; }

        public virtual ICollection<ImFaultacttypePd> ImFaultacttypePd { get; set; }
        public virtual ICollection<ImFaultparamPd> ImFaultparamPd { get; set; }
    }
}
