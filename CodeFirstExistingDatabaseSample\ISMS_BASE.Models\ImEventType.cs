﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImEventType
    {
        public int EvtCode { get; set; }
        public int PuctgyCode { get; set; }
        public string EvtName { get; set; } = null!;
        public string AlertLevel { get; set; } = null!;
        public string IsYiCiSb { get; set; } = null!;

        public virtual ImAlertLevel AlertLevelNavigation { get; set; } = null!;
        public virtual ImPuCtgy PuctgyCodeNavigation { get; set; } = null!;
    }
}
