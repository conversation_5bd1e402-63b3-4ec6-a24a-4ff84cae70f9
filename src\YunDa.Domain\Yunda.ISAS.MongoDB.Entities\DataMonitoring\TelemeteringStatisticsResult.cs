﻿using Abp.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using Yunda.ISAS.MongoDB.Entities.Helper;

namespace Yunda.SOMS.MongoDB.Entities.DataMonitoring
{
    /// <summary>
    /// 遥测数据统计结果
    /// </summary>
    public class TelemeteringStatisticsResult : Entity<Guid>
    {
        /// <summary>
        /// 关联的遥测ID
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public virtual string EquipmentInfoName { get; set; }

        /// <summary>
        /// 遥测名称
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// 结果值
        /// </summary>
        public virtual float ResultValue { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public virtual string Unit { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual StatisticsTypeEnum StatisticsType { get; set; }

        /// <summary>
        /// 统计时间间隔
        /// </summary>
        [MongoDBDescendingIndex]
        public virtual Yunda.SOMS.MongoDB.Entities.DataMonitoring.FixedIntervalEnum IntervalType { get; set; }

        /// <summary>
        /// 统计时间
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        [MongoDBDescendingIndex]
        public virtual DateTime StatisticsDateTime { get; set; }

        /// <summary>
        /// 原始数据数量
        /// </summary>
        public virtual int DataCount { get; set; }
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime ResultTime { get; set; }
    }

    /// <summary>
    /// 统计类型枚举
    /// </summary>
    public enum StatisticsTypeEnum
    {
        /// <summary>
        /// 实时值
        /// </summary>
        [Description("实时值")]
        RealTime = 0,

        /// <summary>
        /// 最大值
        /// </summary>
        [Description("最大值")]
        Maximum = 1,

        /// <summary>
        /// 最小值
        /// </summary>
        [Description("最小值")]
        Minimum = 2,

        /// <summary>
        /// 平均值
        /// </summary>
        [Description("平均值")]
        Average = 3,

        /// <summary>
        /// 差值（与上一时间点的差）
        /// </summary>
        [Description("差值")]
        Difference = 4,

        /// <summary>
        /// 累计值
        /// </summary>
        [Description("累计值")]
        Accumulated = 5,

        /// <summary>
        /// 标准差
        /// </summary>
        [Description("标准差")]
        StandardDeviation = 6,

        /// <summary>
        /// 中位数
        /// </summary>
        [Description("中位数")]
        Median = 7
    }
}
