﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImCurveType
    {
        public ImCurveType()
        {
            ImCurve = new HashSet<ImCurve>();
        }

        public string Cvetypecode { get; set; }
        public string Cvetypename { get; set; }

        public virtual ICollection<ImCurve> ImCurve { get; set; }
    }
}
