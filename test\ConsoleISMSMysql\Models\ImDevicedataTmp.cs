﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDevicedataTmp
    {
        public string Id { get; set; }
        public string Deviceid { get; set; }
        public string Datatype { get; set; }
        public string Dataname { get; set; }
        public int CpuIndex { get; set; }
        public int InfoAddr { get; set; }
        public int Autosave { get; set; }
        public int Visible { get; set; }
        public int? Toscada { get; set; }
        public int? Secaddr101 { get; set; }
        public int? CfgfileBz { get; set; }
        public string Beizhu { get; set; }
        public string Domain { get; set; }

        public virtual ImProtectdeviceTmp Device { get; set; }
        public virtual ImDeviceycTmp ImDeviceycTmp { get; set; }
        public virtual ImDeviceykTmp ImDeviceykTmp { get; set; }
        public virtual ImDeviceymTmp ImDeviceymTmp { get; set; }
        public virtual ImDeviceyxTmp ImDeviceyxTmp { get; set; }
    }
}
