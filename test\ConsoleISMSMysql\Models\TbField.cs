﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbField
    {
        public string Fieldid { get; set; }
        public string Tableid { get; set; }
        public string Fieldname { get; set; }
        public string Prcname { get; set; }
        public int Seqno { get; set; }
        public string Fieldtype { get; set; }
        public int Fieldsize { get; set; }
        public int Required { get; set; }
        public int Isprimary { get; set; }
        public int Iscond { get; set; }
        public int Isref { get; set; }
        public string Reftable { get; set; }
        public string Reftablealias { get; set; }
        public string Refcodefield { get; set; }
        public string Refnamefield { get; set; }
        public int Dispwidth { get; set; }
        public string Checkvalue { get; set; }
        public string Defaultvalue { get; set; }
        public string Hint { get; set; }
        public int Canstat { get; set; }
        public int? Readonly { get; set; }
        public int Showonadd { get; set; }
        public int Showonedit { get; set; }
        public int Showonbrw { get; set; }
        public int Showonqry { get; set; }
        public string Colormode { get; set; }
        public string Formatstr { get; set; }
        public string Groupname { get; set; }
        public int Isname { get; set; }

        public virtual TbTable Table { get; set; }
    }
}
