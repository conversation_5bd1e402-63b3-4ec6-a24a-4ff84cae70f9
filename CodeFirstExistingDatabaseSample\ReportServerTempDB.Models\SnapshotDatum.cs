﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServerTempDB.Models
{
    public partial class SnapshotDatum
    {
        public Guid SnapshotDataId { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? ParamsHash { get; set; }
        public string? QueryParams { get; set; }
        public string? EffectiveParams { get; set; }
        public string? Description { get; set; }
        public bool? DependsOnUser { get; set; }
        public int PermanentRefcount { get; set; }
        public int TransientRefcount { get; set; }
        public DateTime ExpirationDate { get; set; }
        public int? PageCount { get; set; }
        public bool? HasDocMap { get; set; }
        public string Machine { get; set; } = null!;
        public short? PaginationMode { get; set; }
        public int? ProcessingFlags { get; set; }
        public bool? IsCached { get; set; }
    }
}
