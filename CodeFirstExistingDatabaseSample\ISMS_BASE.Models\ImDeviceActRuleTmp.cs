﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImDeviceActRuleTmp
    {
        public string RuleId { get; set; } = null!;
        public string DataId { get; set; } = null!;
        public int SeqNo { get; set; }
        public string LogicExpr { get; set; } = null!;
        public string LogicValue { get; set; } = null!;
        public string ActType { get; set; } = null!;
        public string? ActParam { get; set; }
        public string State { get; set; } = null!;

        public virtual ImDeviceDataTmp Data { get; set; } = null!;
    }
}
