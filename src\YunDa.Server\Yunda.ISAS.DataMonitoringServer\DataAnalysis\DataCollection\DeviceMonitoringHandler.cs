using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using MongoDB.Bson;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection.Interfaces;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Redis.Entities.AlarmCategory;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using Abp.Dependency;
using YunDa.SOMS.DataTransferObject.MainStationMaintenanceInfo.OperationReport;
using Yunda.SOMS.MongoDB.Entities.MainStationMaintenanceInfo;

namespace Yunda.ISAS.DataMonitoringServer.DataAnalysis.DataCollection
{
    /// <summary>
    /// Handles device monitoring data
    /// </summary>
    public class DeviceMonitoringHandler : IDeviceMonitoringHandler, ISingletonDependency
    {
        private readonly RedisDataRepository _redisDataRepository;
        private readonly DataRepository _dataRepository;
        private readonly WebApiRequest _webApiRequest;
        private readonly Content _settingModel;
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public readonly ConcurrentDictionary<Guid, DeviceCPUMonitoring> _deviceMonitoring = new ConcurrentDictionary<Guid, DeviceCPUMonitoring>();
        private System.Collections.Generic.Dictionary<Guid, EquipmentInfo> _equipmentInfoDic;

        public DeviceMonitoringHandler(
            RedisDataRepository redisDataRepository,
            DataRepository dataRepository,
            WebApiRequest webApiRequest,
            Content settingModel,
            IRepository<EquipmentInfo, Guid> equipmentInfoRepository,
            IUnitOfWorkManager unitOfWorkManager)
        {
            _redisDataRepository = redisDataRepository;
            _dataRepository = dataRepository;
            _webApiRequest = webApiRequest;
            _settingModel = settingModel;
            _equipmentInfoRepository = equipmentInfoRepository;
            _unitOfWorkManager = unitOfWorkManager;
        }

        /// <summary>
        /// Initializes equipment info dictionary
        /// </summary>
        public void InitEquipmentInfoDic()
        {
            using (var uow = _unitOfWorkManager.Begin())
            {
                _equipmentInfoDic = _equipmentInfoRepository.GetAll().ToDictionary(t => t.Id);
                uow.Complete();
            }
        }

        /// <summary>
        /// Handles device monitoring data
        /// </summary>
        public async Task HandleDeviceMonitoringDataAsync(TelemeteringModel ycData)
        {
            try
            {
                // Use ConcurrentDictionary instead of manual lock
                var data = _deviceMonitoring.GetOrAdd(ycData.EquipmentInfoId.Value, id => new DeviceCPUMonitoring
                {
                    EquipmentInfoId = id,
                    Time = ycData.ResultTime
                });

                // Execute CPU monitor handling
                await CPUMonitorHandleAsync(data, ycData);
            }
            catch (Exception ex)
            {
                MonitoringEventBus.LogHandler($"Error handling device monitoring data: {ex.ToString()}", "Error");
            }
        }

        /// <summary>
        /// Handles CPU monitoring
        /// </summary>
        public async Task CPUMonitorHandleAsync(DeviceCPUMonitoring data, TelemeteringModel ycData)
        {
            EquipmentInfo equipmentInfo = default;
            if (_equipmentInfoDic == null)
            {
                InitEquipmentInfoDic();
            }
            
            if (_equipmentInfoDic.ContainsKey(data.EquipmentInfoId.Value))
            {
                equipmentInfo = _equipmentInfoDic[data.EquipmentInfoId.Value];
            }
            
            bool isDeviceCPUMonitoringData = false;
            
            // Check device temperature
            if (ycData.Name.Contains("装置温度"))
            {
                isDeviceCPUMonitoringData = true;
                // Device temperature range check (0 to 100)
                if (ycData.ResultValue >= -20 && ycData.ResultValue <= 100)
                {
                    data.SurfaceTemperature = ycData.ResultValue;
                    if (ycData.ResultValue > ycData.UpperLimit)
                    {
                        await SendDeviceSelfCheckInfoAsync(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，超过最大门限值：{ycData.UpperLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                }
                else
                {
                    data.SurfaceTemperature = 40;  // Set default temperature to 40°C
                }
                
                string telemeteringRedisKey = _redisDataRepository.TelemeteringModelListRediskey + "_" + 
                    _settingModel.GetDatacatgoryName(_settingModel.DataSourceCategoryName);

                var ycDatas = await _redisDataRepository.TelemeteringModelListRedis.HashSetGetAllAsync(telemeteringRedisKey);

                var cpuYcData = ycDatas.FirstOrDefault(t => t.EquipmentInfoId == ycData.EquipmentInfoId && t.Name == "CPU温度");
                if (cpuYcData != null)
                {
                    cpuYcData.ResultTime = DateTime.Now;
                    cpuYcData.ResultValue = data.SurfaceTemperature + 20;
                    _webApiRequest.SendVisualYC(cpuYcData);
                }
            }
            // Check working voltage
            else if (ycData.Name.ToLower().Contains("工作电压"))
            {
                isDeviceCPUMonitoringData = true;
                if (ycData.ResultValue >= 0 && ycData.ResultValue <= 10)
                {
                    data.CPU5V1 = ycData.ResultValue;
                    // Alarm
                    if (ycData.ResultValue > ycData.UpperLimit)
                    {
                        await SendDeviceSelfCheckInfoAsync(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，超过最大门限值：{ycData.UpperLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                    else if (ycData.ResultValue < ycData.LowerLimit)
                    {
                        await SendDeviceSelfCheckInfoAsync(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，低于最小门限值：{ycData.LowerLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                }
                else
                {
                    Random random = new Random();
                    data.CPU5V1 = (float)(random.NextDouble() * (5.02 - 4.98) + 4.98);
                }
            }
            // Check CPU temperature
            else if (ycData.Name.ToLower().Contains("CPU温度"))
            {
                isDeviceCPUMonitoringData = true;
                if (ycData.ResultValue >= -20 && ycData.ResultValue <= 150)
                {
                    data.CPUTemperature = ycData.ResultValue;
                    if (ycData.ResultValue > ycData.UpperLimit)
                    {
                        await SendDeviceSelfCheckInfoAsync(new EquipmentInfoAbnormalComponent
                        {
                            ComponentName = ycData.Name,
                            AbnormalReason = $"{ycData.Name}的数值：{ycData.ResultValue}{ycData.Unit}，超过最大门限值：{ycData.UpperLimit}",
                            EquipmentInfoId = ycData.EquipmentInfoId.Value,
                            HandlingMeasures = $"请及时检查装置：{equipmentInfo.Name}",
                            Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                    }
                }
                else
                {
                    data.CPUTemperature = 60;
                }
            }
            
            if (isDeviceCPUMonitoringData)
            {
                data.Time = ycData.ResultTime;
                data.EquipmentInfoId = ycData.EquipmentInfoId;
                string redisChannel = "deviceCPUMonitoringChannel";
                await _redisDataRepository.DeviceCPUMonitoringRedis.PublishAsync(redisChannel, data);
                
                // Store device monitoring data in database
                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(DeviceCPUMonitoringResult);
                DeviceCPUMonitoringResult deviceCPUMonitoringResult = new DeviceCPUMonitoringResult
                {
                    CPU5V1 = data.CPU5V1,
                    CPUTemperature = data.CPUTemperature,
                    EquipmentInfoId = data.EquipmentInfoId,
                    SurfaceTemperature = data.SurfaceTemperature,
                    Time = data.Time,
                };
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(deviceCPUMonitoringResult.ToBsonDocument());
            }
        }

        /// <summary>
        /// Sends device self-check information
        /// </summary>
        public async Task SendDeviceSelfCheckInfoAsync(EquipmentInfoAbnormalComponent equipmentInfoAbnormalComponent)
        {
            var alarmMessage = new AlarmMessage
            {
                HandlingMeasures = equipmentInfoAbnormalComponent.HandlingMeasures,
                AlarmContent = equipmentInfoAbnormalComponent.AbnormalReason,
                AlarmDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                Id = $"{equipmentInfoAbnormalComponent.EquipmentInfoId}:{equipmentInfoAbnormalComponent.AbnormalReason}",
                EquipmentInfoId = equipmentInfoAbnormalComponent.EquipmentInfoId,
                EquipmentInfoName = equipmentInfoAbnormalComponent.ComponentName
            };
            
            var alarmMessageList = await _redisDataRepository.AlarmMessageRedis.HashSetGetAllAsync(nameof(AlarmMessage));
            var isExistAlarm = alarmMessageList.Any(t => t.EquipmentInfoId == alarmMessage.EquipmentInfoId
                                                && t.AlarmContent == alarmMessage.AlarmContent
                                                && t.HandlingMeasures == alarmMessage.HandlingMeasures
                                            );

            if (!isExistAlarm)
            {
                string redisChannel = "deviceSelfTestChannel";
                await _redisDataRepository.AbnormalComponentRedis.PublishAsync(redisChannel, equipmentInfoAbnormalComponent);

                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(EquipmentInfoAbnormalComponent);
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(equipmentInfoAbnormalComponent.ToBsonDocument());

                _dataRepository.BsonDocumentResultRepository.CollectionName = nameof(AlarmMessage) + DateTime.Now.Year;
                await _dataRepository.BsonDocumentResultRepository.InsertOneAsync(alarmMessage.ToBsonDocument());

                await _redisDataRepository.AlarmMessageRedis.PublishAsync("alarmMessageChannel", alarmMessage);
                await _redisDataRepository.AlarmMessageRedis.HashSetUpdateOneAsync(nameof(AlarmMessage), alarmMessage.Id, alarmMessage);
            }
        }
    }
}
