using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto
{
    /// <summary>
    /// 馈线累计能耗数据
    /// </summary>
    public class FeederCumulativeEnergy
    {
        /// <summary>
        /// 时间标签列表
        /// </summary>
        public List<string> TimeLabels { get; set; } = new List<string>();


        /// <summary>
        /// 馈线数据系列列表
        /// </summary>
        public List<FeederEnergySeries> FeederSeries { get; set; } = new List<FeederEnergySeries>();
        public List<float> AllFeederSeriesActiveValues { get; set; } = new List<float>();
        public List<float> AllFeederSeriesReactiveValues { get; set; } = new List<float>();

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public RealTimePowerTypeEnum IntervalType { get; set; }
    }

    /// <summary>
    /// 单个馈线的能耗数据系列
    /// </summary>
    public class FeederEnergySeries
    {

        /// <summary>
        /// 馈线名称
        /// </summary>
        public string FeederName { get; set; }

        /// <summary>
        /// 有功能耗值列表，与TimeLabels一一对应
        /// </summary>
        public List<float> ActiveValues { get; set; } = new List<float>();

        public float ActiveValuesCount { get => ActiveValues.Sum(); }
        /// <summary>
        /// 无功能耗值列表，与TimeLabels一一对应
        /// </summary>
        public List<float> ReactiveValues { get; set; } = new List<float>();
        public float ReactiveValuesCount { get => ReactiveValues.Sum(); }

    }
} 