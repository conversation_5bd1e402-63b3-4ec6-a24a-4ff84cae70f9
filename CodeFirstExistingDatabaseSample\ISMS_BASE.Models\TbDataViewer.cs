﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbDataViewer
    {
        public TbDataViewer()
        {
            TbDataViewerBusiObjs = new HashSet<TbDataViewerBusiObj>();
        }

        public string Id { get; set; } = null!;
        public int ColCnt { get; set; }
        public int ValueColWidth { get; set; }
        public short? NameCharSet { get; set; }
        public int? NameColor { get; set; }
        public string? NameFontName { get; set; }
        public short? NameSize { get; set; }
        public short? NameBold { get; set; }
        public short? NameItalic { get; set; }
        public short? NameStrikeout { get; set; }
        public short? NameUnderLine { get; set; }
        public short? ValueCharSet { get; set; }
        public int? ValueColor { get; set; }
        public string? ValueFontName { get; set; }
        public short? ValueSize { get; set; }
        public short? ValueBold { get; set; }
        public short? ValueItalic { get; set; }
        public short? ValueStrikeout { get; set; }
        public short? ValueUnderLine { get; set; }

        public virtual TbGlyph IdNavigation { get; set; } = null!;
        public virtual ICollection<TbDataViewerBusiObj> TbDataViewerBusiObjs { get; set; }
    }
}
