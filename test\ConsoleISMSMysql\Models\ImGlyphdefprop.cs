﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImGlyphdefprop
    {
        public int Glyphtype { get; set; }
        public string Glyphname { get; set; }
        public double? Width { get; set; }
        public double? Height { get; set; }
    }
}
