﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAutoReportItem
    {
        public string ItemId { get; set; } = null!;
        public string RptId { get; set; } = null!;
        public int RowNo { get; set; }
        public int ColNo { get; set; }
        public string ItemType { get; set; } = null!;
        public string DataType { get; set; } = null!;
        public string? DataId { get; set; }
        public string? DataName { get; set; }
        public string? Time1 { get; set; }
        public string? Time2 { get; set; }
        public string? TimeCell { get; set; }
        public string? Unit { get; set; }
        public string ShowUnit { get; set; } = null!;

        public virtual ImAutoReport Rpt { get; set; } = null!;
    }
}
