﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServer.Models
{
    public partial class Schedule
    {
        public Guid ScheduleId { get; set; }
        public string Name { get; set; } = null!;
        public DateTime StartDate { get; set; }
        public int Flags { get; set; }
        public DateTime? NextRunTime { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? EndDate { get; set; }
        public int? RecurrenceType { get; set; }
        public int? MinutesInterval { get; set; }
        public int? DaysInterval { get; set; }
        public int? WeeksInterval { get; set; }
        public int? DaysOfWeek { get; set; }
        public int? DaysOfMonth { get; set; }
        public int? Month { get; set; }
        public int? MonthlyWeek { get; set; }
        public int? State { get; set; }
        public string? LastRunStatus { get; set; }
        public int? ScheduledRunTimeout { get; set; }
        public Guid CreatedById { get; set; }
        public string EventType { get; set; } = null!;
        public string? EventData { get; set; }
        public int Type { get; set; }
        public DateTime? ConsistancyCheck { get; set; }
        public string? Path { get; set; }

        public virtual User CreatedBy { get; set; } = null!;
    }
}
