﻿using CodeFirstExistingDatabaseSample.ISMS_BASE;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;

//using Pomelo.EntityFrameworkCore.MySql.Infrastructure;
//using System;

namespace CodeFirstExistingDatabaseSample
{
    public static class DbContextOptionsConfigurer
    {
        public static void Configure(DbContextOptionsBuilder<ISMS_BASEContext> dbContextOptions, string connectionString)
        {
           /* dbContextOptions.UseSqlServer("Server=192.168.110.161;User ID=**;Password=**;Database=ISMS_BASE;Trusted_Connection=False;")*/;
            dbContextOptions.UseSqlServer(connectionString, builder => { builder.CommandTimeout(30); });
        }
    }
}