﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDztype
    {
        public ImDztype()
        {
            ImDevicedz = new HashSet<ImDevicedz>();
            ImDevicedzTmp = new HashSet<ImDevicedzTmp>();
        }

        public int Dztypeid { get; set; }
        public string Dztype { get; set; }

        public virtual ICollection<ImDevicedz> ImDevicedz { get; set; }
        public virtual ICollection<ImDevicedzTmp> ImDevicedzTmp { get; set; }
    }
}
