﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImVariantBool
    {
        public string VaId { get; set; }
        public string Vaname { get; set; }
        public string Statcode { get; set; }
        public string Truestr { get; set; }
        public string Falsestr { get; set; }
        public string Expr { get; set; }
        public string Exprdesc { get; set; }
        public string Exprdataids { get; set; }
        public string State { get; set; }
        public string Alerttype { get; set; }
        public string Alertmsg { get; set; }
        public string Isfibre { get; set; }
        public string Memo { get; set; }

        public virtual ImStation StatcodeNavigation { get; set; }
    }
}
