﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImLabel
    {
        public string Id { get; set; }
        public short? FCharset { get; set; }
        public int? FColor { get; set; }
        public string FName { get; set; }
        public short? FSize { get; set; }
        public short? FBold { get; set; }
        public short? FItalic { get; set; }
        public short? FStrikeout { get; set; }
        public short? FUnderline { get; set; }
        public string Caption { get; set; }

        public virtual ImGlyph IdNavigation { get; set; }
    }
}
