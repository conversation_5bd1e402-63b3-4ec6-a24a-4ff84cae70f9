﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImEventflag
    {
        public string Devicectgy { get; set; }
        public int Evtflagcode { get; set; }
        public string Param1mean { get; set; }
        public string Param1sym { get; set; }
        public string Param1valuetype { get; set; }
        public string Param2mean { get; set; }
        public string Param2sym { get; set; }
        public string Param2valuetype { get; set; }
        public string Param3mean { get; set; }
        public string Param3sym { get; set; }
        public string Param3valuetype { get; set; }

        public virtual ImDevctgy DevicectgyNavigation { get; set; }
    }
}
