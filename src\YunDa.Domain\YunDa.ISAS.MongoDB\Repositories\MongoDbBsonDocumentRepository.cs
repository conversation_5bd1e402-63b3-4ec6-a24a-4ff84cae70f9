using Abp.Domain.Entities;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using YunDa.ISAS.MongoDB.Configuration;
using YunDa.ISAS.MongoDB.Factory;

namespace YunDa.ISAS.MongoDB.Repositories
{
    public class MongoDbBsonDocumentRepository<TEntity, TPrimaryKey> : IMongoDbRepository<TEntity, TPrimaryKey> where TEntity : class
    {
        private string _collectionName = "";

        public string CollectionName
        {
            get
            {
                return string.IsNullOrWhiteSpace(_collectionName) ? typeof(TEntity).Name : _collectionName;
            }
            set
            {
                _collectionName = value;
            }
        }

        public virtual IMongoDatabase Database
        {
            get
            {
                return _databaseFactory.InstanceMongoDatabase();
            }
        }

        public virtual IMongoCollection<TEntity> Collection
        {
            get
            {
                return _databaseFactory.InstanceMongoDatabase().GetCollection<TEntity>(CollectionName);
            }
        }

        private readonly IMongoClientFactory _databaseFactory;

        public MongoDbBsonDocumentRepository(IMongoDBConfiguration mongoDbConfiguration, IMongoClientFactory databaseFactory)
        {
            _databaseFactory = databaseFactory;
        }

        public IQueryable<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            if (filter != null)
                return Collection.AsQueryable().Where(filter);
            else
                return Collection.AsQueryable();
        }

        public IEnumerable<TEntity> GetAllInclude(FilterDefinition<TEntity> filter, string[] field = null, SortDefinition<TEntity> sort = null)
        {
            try
            {
                var rst = Collection.Find(filter);
                if (sort != null)
                    rst = rst.Sort(sort);
                if (field != null && field.Length > 0)
                {
                    //制定查询字段
                    var fieldList = new List<ProjectionDefinition<TEntity>>();
                    for (int i = 0; i < field.Length; i++)
                    {
                        fieldList.Add(Builders<TEntity>.Projection.Include(field[i].ToString()));
                    }
                    var projection = Builders<TEntity>.Projection.Combine(fieldList);
                    fieldList?.Clear();
                    rst = rst.Project<TEntity>(projection);
                }
                IAsyncCursor<TEntity> cur = rst.ToCursor();
                IEnumerable<TEntity> rstList = cur.ToEnumerable<TEntity>();
                return rstList;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public IFindFluent<TEntity, TEntity> GetAllIncludeToFindFluent(FilterDefinition<TEntity> filter, string[] field = null, SortDefinition<TEntity> sort = null)
        {
            try
            {
                var rst = Collection.Find(filter);
                if (sort != null)
                    rst = rst.Sort(sort);
                if (field != null && field.Length > 0)
                {
                    //制定查询字段
                    var fieldList = new List<ProjectionDefinition<TEntity>>();
                    for (int i = 0; i < field.Length; i++)
                    {
                        fieldList.Add(Builders<TEntity>.Projection.Include(field[i].ToString()));
                    }
                    var projection = Builders<TEntity>.Projection.Combine(fieldList);
                    fieldList?.Clear();
                    rst = rst.Project<TEntity>(projection);
                }
                return rst;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public TEntity GetOne(TPrimaryKey id)

        {
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", id);
            var entity = Collection.Find(filter).FirstOrDefault();
            if (entity == null)
            {
                throw new EntityNotFoundException("There is no such an entity with given primary key. Entity type: " + typeof(TEntity).FullName + ", primary key: " + id);
            }

            return entity;
        }

        public Task<TEntity> GetOneAsync(TPrimaryKey id)
        {
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", id);
            var entity = Collection.Find(filter).FirstOrDefaultAsync();
            if (entity == null)
            {
                throw new EntityNotFoundException("There is no such an entity with given primary key. Entity type: " + typeof(TEntity).FullName + ", primary key: " + id);
            }

            return entity;
        }

        public TEntity FirstOrDefault(Expression<Func<TEntity, bool>> filter = null)
        {
            if (filter != null)
                return Collection.Find(filter).FirstOrDefault();
            else
                return Collection.AsQueryable().FirstOrDefault();
        }

        public Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> filter = null)
        {
            if (filter != null)
                return Collection.Find(filter).FirstOrDefaultAsync();
            else
                return Collection.AsQueryable().FirstOrDefaultAsync();
        }

        public void InsertOne(TEntity entity)
        {
            if (entity == null) return;
            Collection.InsertOne(entity);
        }

        public Task InsertOneAsync(TEntity entity)
        {
            if (entity == null) return null;
            return Collection.InsertOneAsync(entity);
        }

        public void InsertMany(IEnumerable<TEntity> entitys)
        {
            if (entitys == null || entitys.Count() == 0) return;
            Collection.InsertMany(entitys);
        }

        public Task InsertManyAsync(IEnumerable<TEntity> entitys)
        {
            if (entitys == null || entitys.Count() == 0) return null;
            return Collection.InsertManyAsync(entitys);
        }

        public UpdateResult UpdateOne(TEntity entity)
        {
            if (entity == null) return null;
            var fieldList = new List<UpdateDefinition<TEntity>>();
            foreach (var property in entity.ToBsonDocument())
            {
                if (property.Name != "Id")//更新集中不能有实体键_id
                {
                    fieldList.Add(Builders<TEntity>.Update.Set(property.Name, property.Value));
                }
            }
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", entity.ToBsonDocument()["Id"]);
            return UpdateOne(filter, fieldList);
        }
        public UpdateResult UpdateOne(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList)
        {
            if (filter == null || fieldList == null) return null;
            return Collection.UpdateOne(filter, Builders<TEntity>.Update.Combine(fieldList), new UpdateOptions() { IsUpsert = true });
        }
        public Task<UpdateResult> UpdateOneAsync(TEntity entity)
        {
            if (entity == null) return null;
            var fieldList = new List<UpdateDefinition<TEntity>>();
            foreach (var property in entity.ToBsonDocument())
            {
                if (property.Name != "Id")//更新集中不能有实体键_id
                {
                    fieldList.Add(Builders<TEntity>.Update.Set(property.Name, property.Value));
                }
            }
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", entity.ToBsonDocument()["Id"]);
            return UpdateOneAsync(filter, fieldList);
        }
        public Task<UpdateResult> UpdateOneAsync(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList)
        {
            if (filter == null || fieldList == null) return null;
            return Collection.UpdateOneAsync(filter, Builders<TEntity>.Update.Combine(fieldList), new UpdateOptions() { IsUpsert = true });
        }
        public UpdateResult UpdateMany(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList)
        {
            if (filter == null || fieldList == null) return null;
            return Collection.UpdateMany(filter, Builders<TEntity>.Update.Combine(fieldList), new UpdateOptions() { IsUpsert = true });
        }
        public Task<UpdateResult> UpdateManyAsync(FilterDefinition<TEntity> filter, List<UpdateDefinition<TEntity>> fieldList)
        {
            if (filter == null || fieldList == null) return null;
            return Collection.UpdateManyAsync(filter, Builders<TEntity>.Update.Combine(fieldList), new UpdateOptions() { IsUpsert = true });
        }
        public ReplaceOneResult ReplaceOne(TEntity entity)
        {
            if (entity == null) return null;
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", entity.ToBsonDocument()["Id"]);
            return Collection.ReplaceOne(filter, entity);
        }

        public Task<ReplaceOneResult> ReplaceOneAsync(TEntity entity)
        {
            if (entity == null) return null;
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", entity.ToBsonDocument()["Id"]);
            return Collection.ReplaceOneAsync(filter, entity);
        }
        public DeleteResult DeleteOne(TPrimaryKey id)
        {
            if (id == null) return null;
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", id);
            return Collection.DeleteOne(filter);
        }
        public DeleteResult DeleteMany(List<TPrimaryKey> ids)
        {
            if (ids == null) return null;
            //Collection.BulkWrite()
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.AnyIn("_id", ids);
            return Collection.DeleteMany(filter);
        }
        public Task<DeleteResult> DeleteOneAsync(TPrimaryKey id)
        {
            if (id == null) return null;
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.Eq("_id", id);
            return Collection.DeleteOneAsync(filter);
        }

        public DeleteResult DeleteMany(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.DeleteMany(filter);
        }

        public Task<DeleteResult> DeleteManyAsync(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.DeleteManyAsync(filter);
        }

        public long Count(Expression<Func<TEntity, bool>> filter = null)
        {
            if (filter != null)
                return Collection.CountDocuments(filter);
            else
                return Collection.CountDocuments(FilterDefinition<TEntity>.Empty);
        }

        public Task<long> CountAsync(Expression<Func<TEntity, bool>> filter = null)
        {
            if (filter != null)
                return Collection.CountDocumentsAsync(filter);
            else
                return Collection.CountDocumentsAsync(FilterDefinition<TEntity>.Empty);
        }

        public bool Exists(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.Find(filter).Any();
        }

        public async Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> filter)
        {
            return await Collection.Find(filter).AnyAsync();
        }

        public IFindFluent<TEntity, TEntity> GetPaged(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null)
        {
            var query = Collection.Find(filter);
            if (sort != null)
                query = query.Sort(sort);
            
            return query.Skip((pageIndex - 1) * pageSize).Limit(pageSize);
        }

        public async Task<IAsyncCursor<TEntity>> GetPagedAsync(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null)
        {
            var query = Collection.Find(filter);
            if (sort != null)
                query = query.Sort(sort);
            
            return await query.Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToCursorAsync();
        }

        public string CreateIndex(IndexKeysDefinition<TEntity> keys, CreateIndexOptions options = null)
        {
            var model = new CreateIndexModel<TEntity>(keys, options);
            return Collection.Indexes.CreateOne(model);
        }

        public Task<string> CreateIndexAsync(IndexKeysDefinition<TEntity> keys, CreateIndexOptions options = null)
        {
            var model = new CreateIndexModel<TEntity>(keys, options);
            return Collection.Indexes.CreateOneAsync(model);
        }

        public List<BsonDocument> GetIndexes()
        {
            return Collection.Indexes.List().ToList();
        }

        public async Task<List<BsonDocument>> GetIndexesAsync()
        {
            return await Collection.Indexes.List().ToListAsync();
        }

        public void DropIndex(string indexName)
        {
            Collection.Indexes.DropOne(indexName);
        }

        public Task DropIndexAsync(string indexName)
        {
            return Collection.Indexes.DropOneAsync(indexName);
        }

        public IAsyncCursor<TResult> Aggregate<TResult>(PipelineDefinition<TEntity, TResult> pipeline, AggregateOptions options = null)
        {
            return Collection.Aggregate(pipeline, options);
        }

        public Task<IAsyncCursor<TResult>> AggregateAsync<TResult>(PipelineDefinition<TEntity, TResult> pipeline, AggregateOptions options = null)
        {
            return Collection.AggregateAsync(pipeline, options);
        }

        public BulkWriteResult<TEntity> BulkWrite(IEnumerable<WriteModel<TEntity>> requests, BulkWriteOptions options = null)
        {
            return Collection.BulkWrite(requests, options);
        }

        public Task<BulkWriteResult<TEntity>> BulkWriteAsync(IEnumerable<WriteModel<TEntity>> requests, BulkWriteOptions options = null)
        {
            return Collection.BulkWriteAsync(requests, options);
        }

        public TEntity FindOneAndUpdate(FilterDefinition<TEntity> filter, UpdateDefinition<TEntity> update, FindOneAndUpdateOptions<TEntity> options = null)
        {
            return Collection.FindOneAndUpdate(filter, update, options);
        }

        public Task<TEntity> FindOneAndUpdateAsync(FilterDefinition<TEntity> filter, UpdateDefinition<TEntity> update, FindOneAndUpdateOptions<TEntity> options = null)
        {
            return Collection.FindOneAndUpdateAsync(filter, update, options);
        }

        public TEntity FindOneAndDelete(FilterDefinition<TEntity> filter, FindOneAndDeleteOptions<TEntity> options = null)
        {
            return Collection.FindOneAndDelete(filter, options);
        }

        public Task<TEntity> FindOneAndDeleteAsync(FilterDefinition<TEntity> filter, FindOneAndDeleteOptions<TEntity> options = null)
        {
            return Collection.FindOneAndDeleteAsync(filter, options);
        }

        public TEntity FindOneAndReplace(FilterDefinition<TEntity> filter, TEntity replacement, FindOneAndReplaceOptions<TEntity> options = null)
        {
            return Collection.FindOneAndReplace(filter, replacement, options);
        }

        public Task<TEntity> FindOneAndReplaceAsync(FilterDefinition<TEntity> filter, TEntity replacement, FindOneAndReplaceOptions<TEntity> options = null)
        {
            return Collection.FindOneAndReplaceAsync(filter, replacement, options);
        }

        public List<TEntity> GetAllList()
        {
            return Collection.Find(FilterDefinition<TEntity>.Empty).ToList();
        }

        public Task<List<TEntity>> GetAllListAsync()
        {
            return Collection.Find(FilterDefinition<TEntity>.Empty).ToListAsync();
        }

        public List<TEntity> GetAllList(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.Find(filter).ToList();
        }

        public Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.Find(filter).ToListAsync();
        }

        public List<TEntity> GetAllList(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).ToList();
        }

        public Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).ToListAsync();
        }

        public TEntity FirstOrDefault(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).FirstOrDefault();
        }

        public Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).FirstOrDefaultAsync();
        }

        public TEntity Single(Expression<Func<TEntity, bool>> filter)
        {
            var entity = Collection.Find(filter).SingleOrDefault();
            if (entity == null)
            {
                throw new InvalidOperationException($"No document found for filter: {filter}");
            }
            return entity;
        }

        public async Task<TEntity> SingleAsync(Expression<Func<TEntity, bool>> filter)
        {
            var entity = await Collection.Find(filter).SingleOrDefaultAsync();
            if (entity == null)
            {
                throw new InvalidOperationException($"No document found for filter: {filter}");
            }
            return entity;
        }

        public TEntity SingleOrDefault(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.Find(filter).SingleOrDefault();
        }

        public Task<TEntity> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> filter)
        {
            return Collection.Find(filter).SingleOrDefaultAsync();
        }

        public List<TEntity> GetByIds(IEnumerable<TPrimaryKey> ids)
        {
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.In("_id", ids);
            return Collection.Find(filter).ToList();
        }

        public Task<List<TEntity>> GetByIdsAsync(IEnumerable<TPrimaryKey> ids)
        {
            FilterDefinition<TEntity> filter = Builders<TEntity>.Filter.In("_id", ids);
            return Collection.Find(filter).ToListAsync();
        }

        public (List<TEntity> Items, long TotalCount) GetPagedWithCount(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null)
        {
            var query = Collection.Find(filter);
            if (sort != null)
                query = query.Sort(sort);

            long totalCount = query.CountDocuments();
            var items = query.Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToList();

            return (items, totalCount);
        }

        public async Task<(List<TEntity> Items, long TotalCount)> GetPagedWithCountAsync(FilterDefinition<TEntity> filter, int pageIndex, int pageSize, SortDefinition<TEntity> sort = null)
        {
            var query = Collection.Find(filter);
            if (sort != null)
                query = query.Sort(sort);

            long totalCount = await query.CountDocumentsAsync();
            var items = await query.Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToListAsync();

            return (items, totalCount);
        }

        public List<TField> GetFieldValues<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field)
        {
            return Collection.Find(filter).Project(Builders<TEntity>.Projection.Expression(field)).ToList();
        }

        public Task<List<TField>> GetFieldValuesAsync<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field)
        {
            return Collection.Find(filter).Project(Builders<TEntity>.Projection.Expression(field)).ToListAsync();
        }

        public List<TField> GetDistinctValues<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field)
        {
            var fieldName = GetPropertyName(field);
            var fieldDefinition = new StringFieldDefinition<TEntity, TField>(fieldName);
            return Collection.Distinct<TField>(fieldDefinition, filter).ToList();
        }

        public Task<List<TField>> GetDistinctValuesAsync<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field)
        {
            var fieldName = GetPropertyName(field);
            var fieldDefinition = new StringFieldDefinition<TEntity, TField>(fieldName);
            return Collection.Distinct<TField>(fieldDefinition, filter).ToListAsync();
        }

        public UpdateResult UpdateField<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field, TField value)
        {
            var fieldName = GetPropertyName(field);
            var update = Builders<TEntity>.Update.Set(fieldName, value);
            return Collection.UpdateMany(filter, update);
        }

        public Task<UpdateResult> UpdateFieldAsync<TField>(Expression<Func<TEntity, bool>> filter, Expression<Func<TEntity, TField>> field, TField value)
        {
            var fieldName = GetPropertyName(field);
            var update = Builders<TEntity>.Update.Set(fieldName, value);
            return Collection.UpdateManyAsync(filter, update);
        }

        public UpdateResult Increment(Expression<Func<TEntity, bool>> filter, string field, long value = 1)
        {
            var update = Builders<TEntity>.Update.Inc(field, value);
            return Collection.UpdateMany(filter, update);
        }

        public Task<UpdateResult> IncrementAsync(Expression<Func<TEntity, bool>> filter, string field, long value = 1)
        {
            var update = Builders<TEntity>.Update.Inc(field, value);
            return Collection.UpdateManyAsync(filter, update);
        }

        public List<TEntity> TextSearch(string text)
        {
            var filter = Builders<TEntity>.Filter.Text(text);
            return Collection.Find(filter).ToList();
        }

        public Task<List<TEntity>> TextSearchAsync(string text)
        {
            var filter = Builders<TEntity>.Filter.Text(text);
            return Collection.Find(filter).ToListAsync();
        }

        public List<TEntity> TextSearch(string text, Expression<Func<TEntity, bool>> filter)
        {
            var textFilter = Builders<TEntity>.Filter.Text(text);
            var combinedFilter = Builders<TEntity>.Filter.And(textFilter, filter);
            return Collection.Find(combinedFilter).ToList();
        }

        public Task<List<TEntity>> TextSearchAsync(string text, Expression<Func<TEntity, bool>> filter)
        {
            var textFilter = Builders<TEntity>.Filter.Text(text);
            var combinedFilter = Builders<TEntity>.Filter.And(textFilter, filter);
            return Collection.Find(combinedFilter).ToListAsync();
        }

        public TEntity GetOrCreate(Expression<Func<TEntity, bool>> filter, TEntity entity)
        {
            var existingEntity = Collection.Find(filter).FirstOrDefault();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            Collection.InsertOne(entity);
            return entity;
        }

        public async Task<TEntity> GetOrCreateAsync(Expression<Func<TEntity, bool>> filter, TEntity entity)
        {
            var existingEntity = await Collection.Find(filter).FirstOrDefaultAsync();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            await Collection.InsertOneAsync(entity);
            return entity;
        }

        public TEntity GetOrCreate(Expression<Func<TEntity, bool>> filter, Func<TEntity> createEntityFactory)
        {
            var existingEntity = Collection.Find(filter).FirstOrDefault();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            var entity = createEntityFactory();
            Collection.InsertOne(entity);
            return entity;
        }

        public async Task<TEntity> GetOrCreateAsync(Expression<Func<TEntity, bool>> filter, Func<TEntity> createEntityFactory)
        {
            var existingEntity = await Collection.Find(filter).FirstOrDefaultAsync();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            var entity = createEntityFactory();
            await Collection.InsertOneAsync(entity);
            return entity;
        }

        public UpdateResult UpdateMany(Expression<Func<TEntity, bool>> filter, Dictionary<string, object> updates)
        {
            var updateDefinitionList = new List<UpdateDefinition<TEntity>>();
            foreach (var update in updates)
            {
                updateDefinitionList.Add(Builders<TEntity>.Update.Set(update.Key, update.Value));
            }

            var combinedUpdate = Builders<TEntity>.Update.Combine(updateDefinitionList);
            return Collection.UpdateMany(filter, combinedUpdate);
        }

        public Task<UpdateResult> UpdateManyAsync(Expression<Func<TEntity, bool>> filter, Dictionary<string, object> updates)
        {
            var updateDefinitionList = new List<UpdateDefinition<TEntity>>();
            foreach (var update in updates)
            {
                updateDefinitionList.Add(Builders<TEntity>.Update.Set(update.Key, update.Value));
            }

            var combinedUpdate = Builders<TEntity>.Update.Combine(updateDefinitionList);
            return Collection.UpdateManyAsync(filter, combinedUpdate);
        }

        private string GetPropertyName<TField>(Expression<Func<TEntity, TField>> expression)
        {
            if (expression.Body is MemberExpression memberExpression)
            {
                return memberExpression.Member.Name;
            }
            else if (expression.Body is UnaryExpression unaryExpression && unaryExpression.Operand is MemberExpression memberExp)
            {
                return memberExp.Member.Name;
            }
            
            throw new ArgumentException("表达式必须是属性访问表达式", nameof(expression));
        }

        #region FilterDefinition方法实现

        public List<TEntity> GetAllList(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).ToList();
        }

        public Task<List<TEntity>> GetAllListAsync(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).ToListAsync();
        }

        public List<TEntity> GetAllList(FilterDefinition<TEntity> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).ToList();
        }

        public Task<List<TEntity>> GetAllListAsync(FilterDefinition<TEntity> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).ToListAsync();
        }

        public TEntity FirstOrDefault(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).FirstOrDefault();
        }

        public Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).FirstOrDefaultAsync();
        }

        public TEntity FirstOrDefault(FilterDefinition<TEntity> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).FirstOrDefault();
        }

        public Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).FirstOrDefaultAsync();
        }

        public TEntity Single(FilterDefinition<TEntity> filter)
        {
            var entity = Collection.Find(filter).SingleOrDefault();
            if (entity == null)
            {
                throw new InvalidOperationException($"No document found for filter");
            }
            return entity;
        }

        public async Task<TEntity> SingleAsync(FilterDefinition<TEntity> filter)
        {
            var entity = await Collection.Find(filter).SingleOrDefaultAsync();
            if (entity == null)
            {
                throw new InvalidOperationException($"No document found for filter");
            }
            return entity;
        }

        public TEntity SingleOrDefault(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).SingleOrDefault();
        }

        public Task<TEntity> SingleOrDefaultAsync(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).SingleOrDefaultAsync();
        }

        public long Count(FilterDefinition<TEntity> filter)
        {
            return Collection.CountDocuments(filter);
        }

        public Task<long> CountAsync(FilterDefinition<TEntity> filter)
        {
            return Collection.CountDocumentsAsync(filter);
        }

        public bool Exists(FilterDefinition<TEntity> filter)
        {
            return Collection.Find(filter).Any();
        }

        public async Task<bool> ExistsAsync(FilterDefinition<TEntity> filter)
        {
            return await Collection.Find(filter).AnyAsync();
        }

        public List<TField> GetFieldValues<TField>(FilterDefinition<TEntity> filter, string field)
        {
            var projection = Builders<TEntity>.Projection.Include(field).Exclude("_id");
            return Collection.Find(filter).Project<BsonDocument>(projection)
                .ToList()
                .Select(doc => BsonTypeMapper.MapToDotNetValue(doc.GetValue(field)))
                .OfType<TField>()
                .ToList();
        }

        public async Task<List<TField>> GetFieldValuesAsync<TField>(FilterDefinition<TEntity> filter, string field)
        {
            var projection = Builders<TEntity>.Projection.Include(field).Exclude("_id");
            var documents = await Collection.Find(filter).Project<BsonDocument>(projection).ToListAsync();
            return documents
                .Select(doc => BsonTypeMapper.MapToDotNetValue(doc.GetValue(field)))
                .OfType<TField>()
                .ToList();
        }

        public List<TField> GetDistinctValues<TField>(FilterDefinition<TEntity> filter, string field)
        {
            var fieldDefinition = new StringFieldDefinition<TEntity, TField>(field);
            return Collection.Distinct<TField>(fieldDefinition, filter).ToList();
        }

        public Task<List<TField>> GetDistinctValuesAsync<TField>(FilterDefinition<TEntity> filter, string field)
        {
            var fieldDefinition = new StringFieldDefinition<TEntity, TField>(field);
            return Collection.Distinct<TField>(fieldDefinition, filter).ToListAsync();
        }

        public UpdateResult UpdateField(FilterDefinition<TEntity> filter, string field, object value)
        {
            var update = Builders<TEntity>.Update.Set(field, value);
            return Collection.UpdateMany(filter, update);
        }

        public Task<UpdateResult> UpdateFieldAsync(FilterDefinition<TEntity> filter, string field, object value)
        {
            var update = Builders<TEntity>.Update.Set(field, value);
            return Collection.UpdateManyAsync(filter, update);
        }

        public UpdateResult Increment(FilterDefinition<TEntity> filter, string field, long value = 1)
        {
            var update = Builders<TEntity>.Update.Inc(field, value);
            return Collection.UpdateMany(filter, update);
        }

        public Task<UpdateResult> IncrementAsync(FilterDefinition<TEntity> filter, string field, long value = 1)
        {
            var update = Builders<TEntity>.Update.Inc(field, value);
            return Collection.UpdateManyAsync(filter, update);
        }

        public TEntity GetOrCreate(FilterDefinition<TEntity> filter, TEntity entity)
        {
            var existingEntity = Collection.Find(filter).FirstOrDefault();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            Collection.InsertOne(entity);
            return entity;
        }

        public async Task<TEntity> GetOrCreateAsync(FilterDefinition<TEntity> filter, TEntity entity)
        {
            var existingEntity = await Collection.Find(filter).FirstOrDefaultAsync();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            await Collection.InsertOneAsync(entity);
            return entity;
        }

        public TEntity GetOrCreate(FilterDefinition<TEntity> filter, Func<TEntity> createEntityFactory)
        {
            var existingEntity = Collection.Find(filter).FirstOrDefault();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            var entity = createEntityFactory();
            Collection.InsertOne(entity);
            return entity;
        }

        public async Task<TEntity> GetOrCreateAsync(FilterDefinition<TEntity> filter, Func<TEntity> createEntityFactory)
        {
            var existingEntity = await Collection.Find(filter).FirstOrDefaultAsync();
            if (existingEntity != null)
            {
                return existingEntity;
            }

            var entity = createEntityFactory();
            await Collection.InsertOneAsync(entity);
            return entity;
        }

        public DeleteResult DeleteMany(FilterDefinition<TEntity> filter)
        {
            return Collection.DeleteMany(filter);
        }

        public Task<DeleteResult> DeleteManyAsync(FilterDefinition<TEntity> filter)
        {
            return Collection.DeleteManyAsync(filter);
        }

        #endregion

        #region 更多FilterDefinition参数的查询方法实现

        public List<TEntity> GetAllListSorted(FilterDefinition<TEntity> filter, string field, bool isAscending = true)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).ToList();
        }

        public Task<List<TEntity>> GetAllListSortedAsync(FilterDefinition<TEntity> filter, string field, bool isAscending = true)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).ToListAsync();
        }

        public List<TEntity> GetAllListSorted(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields)
        {
            var sortDefinitions = new List<SortDefinition<TEntity>>();
            foreach (var field in sortFields)
            {
                sortDefinitions.Add(field.Value ? 
                    Builders<TEntity>.Sort.Ascending(field.Key) : 
                    Builders<TEntity>.Sort.Descending(field.Key));
            }
            
            var sort = Builders<TEntity>.Sort.Combine(sortDefinitions);
            return Collection.Find(filter).Sort(sort).ToList();
        }

        public Task<List<TEntity>> GetAllListSortedAsync(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields)
        {
            var sortDefinitions = new List<SortDefinition<TEntity>>();
            foreach (var field in sortFields)
            {
                sortDefinitions.Add(field.Value ? 
                    Builders<TEntity>.Sort.Ascending(field.Key) : 
                    Builders<TEntity>.Sort.Descending(field.Key));
            }
            
            var sort = Builders<TEntity>.Sort.Combine(sortDefinitions);
            return Collection.Find(filter).Sort(sort).ToListAsync();
        }

        public List<TEntity> GetAllListLimited(FilterDefinition<TEntity> filter, int limit)
        {
            return Collection.Find(filter).Limit(limit).ToList();
        }

        public Task<List<TEntity>> GetAllListLimitedAsync(FilterDefinition<TEntity> filter, int limit)
        {
            return Collection.Find(filter).Limit(limit).ToListAsync();
        }

        public List<TEntity> GetAllListLimited(FilterDefinition<TEntity> filter, string field, bool isAscending, int limit)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Limit(limit).ToList();
        }

        public Task<List<TEntity>> GetAllListLimitedAsync(FilterDefinition<TEntity> filter, string field, bool isAscending, int limit)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Limit(limit).ToListAsync();
        }

        public List<TEntity> GetAllListRange(FilterDefinition<TEntity> filter, int skip, int limit)
        {
            return Collection.Find(filter).Skip(skip).Limit(limit).ToList();
        }

        public Task<List<TEntity>> GetAllListRangeAsync(FilterDefinition<TEntity> filter, int skip, int limit)
        {
            return Collection.Find(filter).Skip(skip).Limit(limit).ToListAsync();
        }

        public List<TEntity> GetAllListRange(FilterDefinition<TEntity> filter, string field, bool isAscending, int skip, int limit)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Skip(skip).Limit(limit).ToList();
        }

        public Task<List<TEntity>> GetAllListRangeAsync(FilterDefinition<TEntity> filter, string field, bool isAscending, int skip, int limit)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Skip(skip).Limit(limit).ToListAsync();
        }

        public List<BsonDocument> GetAllListProjection(FilterDefinition<TEntity> filter, string[] fields)
        {
            var projection = CreateProjection(fields);
            return Collection.Find(filter).Project<BsonDocument>(projection).ToList();
        }

        public Task<List<BsonDocument>> GetAllListProjectionAsync(FilterDefinition<TEntity> filter, string[] fields)
        {
            var projection = CreateProjection(fields);
            return Collection.Find(filter).Project<BsonDocument>(projection).ToListAsync();
        }

        public List<BsonDocument> GetAllListProjection(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            return Collection.Find(filter).Sort(sort).Project<BsonDocument>(projection).ToList();
        }

        public Task<List<BsonDocument>> GetAllListProjectionAsync(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            return Collection.Find(filter).Sort(sort).Project<BsonDocument>(projection).ToListAsync();
        }

        public List<BsonDocument> GetAllListProjectionPaged(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            return Collection.Find(filter)
                .Sort(sort)
                .Skip((pageIndex - 1) * pageSize)
                .Limit(pageSize)
                .Project<BsonDocument>(projection)
                .ToList();
        }

        public Task<List<BsonDocument>> GetAllListProjectionPagedAsync(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            return Collection.Find(filter)
                .Sort(sort)
                .Skip((pageIndex - 1) * pageSize)
                .Limit(pageSize)
                .Project<BsonDocument>(projection)
                .ToListAsync();
        }

        public (List<TEntity> Items, long TotalCount) GetPagedWithCount(FilterDefinition<TEntity> filter, string sortField, bool isAscending, int pageIndex, int pageSize)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            var query = Collection.Find(filter);
            long totalCount = query.CountDocuments();
            var items = query.Sort(sort).Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToList();
            
            return (items, totalCount);
        }

        public async Task<(List<TEntity> Items, long TotalCount)> GetPagedWithCountAsync(FilterDefinition<TEntity> filter, string sortField, bool isAscending, int pageIndex, int pageSize)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            var query = Collection.Find(filter);
            long totalCount = await query.CountDocumentsAsync();
            var items = await query.Sort(sort).Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToListAsync();
            
            return (items, totalCount);
        }

        public (List<TEntity> Items, long TotalCount) GetPagedWithCount(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields, int pageIndex, int pageSize)
        {
            var sortDefinitions = new List<SortDefinition<TEntity>>();
            foreach (var field in sortFields)
            {
                sortDefinitions.Add(field.Value ? 
                    Builders<TEntity>.Sort.Ascending(field.Key) : 
                    Builders<TEntity>.Sort.Descending(field.Key));
            }
            
            var sort = Builders<TEntity>.Sort.Combine(sortDefinitions);
            var query = Collection.Find(filter);
            long totalCount = query.CountDocuments();
            var items = query.Sort(sort).Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToList();
            
            return (items, totalCount);
        }

        public async Task<(List<TEntity> Items, long TotalCount)> GetPagedWithCountAsync(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields, int pageIndex, int pageSize)
        {
            var sortDefinitions = new List<SortDefinition<TEntity>>();
            foreach (var field in sortFields)
            {
                sortDefinitions.Add(field.Value ? 
                    Builders<TEntity>.Sort.Ascending(field.Key) : 
                    Builders<TEntity>.Sort.Descending(field.Key));
            }
            
            var sort = Builders<TEntity>.Sort.Combine(sortDefinitions);
            var query = Collection.Find(filter);
            long totalCount = await query.CountDocumentsAsync();
            var items = await query.Sort(sort).Skip((pageIndex - 1) * pageSize).Limit(pageSize).ToListAsync();
            
            return (items, totalCount);
        }

        public (List<BsonDocument> Items, long TotalCount) GetPagedProjectionWithCount(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            var query = Collection.Find(filter);
            long totalCount = query.CountDocuments();
            var items = query.Sort(sort)
                .Skip((pageIndex - 1) * pageSize)
                .Limit(pageSize)
                .Project<BsonDocument>(projection)
                .ToList();
            
            return (items, totalCount);
        }

        public async Task<(List<BsonDocument> Items, long TotalCount)> GetPagedProjectionWithCountAsync(FilterDefinition<TEntity> filter, string[] fields, string sortField, bool isAscending, int pageIndex, int pageSize)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(sortField) : 
                Builders<TEntity>.Sort.Descending(sortField);
            
            var query = Collection.Find(filter);
            long totalCount = await query.CountDocumentsAsync();
            var items = await query.Sort(sort)
                .Skip((pageIndex - 1) * pageSize)
                .Limit(pageSize)
                .Project<BsonDocument>(projection)
                .ToListAsync();
            
            return (items, totalCount);
        }

        public UpdateResult Upsert(FilterDefinition<TEntity> filter, Dictionary<string, object> updates)
        {
            var updateDefinitionList = new List<UpdateDefinition<TEntity>>();
            foreach (var update in updates)
            {
                updateDefinitionList.Add(Builders<TEntity>.Update.Set(update.Key, update.Value));
            }

            var combinedUpdate = Builders<TEntity>.Update.Combine(updateDefinitionList);
            return Collection.UpdateOne(filter, combinedUpdate, new UpdateOptions { IsUpsert = true });
        }

        public Task<UpdateResult> UpsertAsync(FilterDefinition<TEntity> filter, Dictionary<string, object> updates)
        {
            var updateDefinitionList = new List<UpdateDefinition<TEntity>>();
            foreach (var update in updates)
            {
                updateDefinitionList.Add(Builders<TEntity>.Update.Set(update.Key, update.Value));
            }

            var combinedUpdate = Builders<TEntity>.Update.Combine(updateDefinitionList);
            return Collection.UpdateOneAsync(filter, combinedUpdate, new UpdateOptions { IsUpsert = true });
        }

        private ProjectionDefinition<TEntity> CreateProjection(string[] fields)
        {
            var fieldList = new List<ProjectionDefinition<TEntity>>();
            foreach (var field in fields)
            {
                fieldList.Add(Builders<TEntity>.Projection.Include(field));
            }
            
            if (fieldList.Count > 0)
            {
                fieldList.Add(Builders<TEntity>.Projection.Exclude("_id")); // 默认排除_id字段，除非明确包含
                return Builders<TEntity>.Projection.Combine(fieldList);
            }
            
            return Builders<TEntity>.Projection.Exclude("_id");
        }

        #endregion

        #region 原生MongoDB操作方法实现

        public IMongoCollection<TEntity> GetCollection()
        {
            return Collection;
        }

        public TResult RunCommand<TResult>(BsonDocument command, ReadPreference readPreference = null)
        {
            return Database.RunCommand<TResult>(command, readPreference);
        }

        public Task<TResult> RunCommandAsync<TResult>(BsonDocument command, ReadPreference readPreference = null)
        {
            return Database.RunCommandAsync<TResult>(command, readPreference);
        }

        public List<BsonDocument> Aggregate(BsonDocument[] pipeline)
        {
            return Collection.Aggregate<BsonDocument>(pipeline).ToList();
        }

        public Task<List<BsonDocument>> AggregateAsync(BsonDocument[] pipeline)
        {
            return Collection.Aggregate<BsonDocument>(pipeline).ToListAsync();
        }

        public List<TResult> Aggregate<TResult>(BsonDocument[] pipeline)
        {
            return Collection.Aggregate<TResult>(pipeline).ToList();
        }

        public Task<List<TResult>> AggregateAsync<TResult>(BsonDocument[] pipeline)
        {
            return Collection.Aggregate<TResult>(pipeline).ToListAsync();
        }

        public List<BsonDocument> Find(BsonDocument filter)
        {
            return Collection.Find(filter).As<BsonDocument>().ToList();
        }

        public Task<List<BsonDocument>> FindAsync(BsonDocument filter)
        {
            return Collection.Find(filter).As<BsonDocument>().ToListAsync();
        }

        public List<TResult> Find<TResult>(BsonDocument filter)
        {
            return Collection.Find(filter).As<TResult>().ToList();
        }

        public Task<List<TResult>> FindAsync<TResult>(BsonDocument filter)
        {
            return Collection.Find(filter).As<TResult>().ToListAsync();
        }

        public List<TResult> Find<TResult>(BsonDocument filter, BsonDocument projection)
        {
            return Collection.Find(filter).Project<TResult>(projection).ToList();
        }

        public Task<List<TResult>> FindAsync<TResult>(BsonDocument filter, BsonDocument projection)
        {
            return Collection.Find(filter).Project<TResult>(projection).ToListAsync();
        }

        public List<TResult> Find<TResult>(BsonDocument filter, BsonDocument sort, int skip, int limit)
        {
            return Collection.Find(filter).Sort(sort).Skip(skip).Limit(limit).As<TResult>().ToList();
        }

        public Task<List<TResult>> FindAsync<TResult>(BsonDocument filter, BsonDocument sort, int skip, int limit)
        {
            return Collection.Find(filter).Sort(sort).Skip(skip).Limit(limit).As<TResult>().ToListAsync();
        }

        public List<TResult> Find<TResult>(BsonDocument filter, BsonDocument projection, BsonDocument sort, int skip, int limit)
        {
            return Collection.Find(filter).Project<TResult>(projection).Sort(sort).Skip(skip).Limit(limit).ToList();
        }

        public Task<List<TResult>> FindAsync<TResult>(BsonDocument filter, BsonDocument projection, BsonDocument sort, int skip, int limit)
        {
            return Collection.Find(filter).Project<TResult>(projection).Sort(sort).Skip(skip).Limit(limit).ToListAsync();
        }

        public UpdateResult UpdateOne(BsonDocument filter, BsonDocument update, bool isUpsert = false)
        {
            return Collection.UpdateOne(filter, update, new UpdateOptions { IsUpsert = isUpsert });
        }

        public Task<UpdateResult> UpdateOneAsync(BsonDocument filter, BsonDocument update, bool isUpsert = false)
        {
            return Collection.UpdateOneAsync(filter, update, new UpdateOptions { IsUpsert = isUpsert });
        }

        public UpdateResult UpdateMany(BsonDocument filter, BsonDocument update, bool isUpsert = false)
        {
            return Collection.UpdateMany(filter, update, new UpdateOptions { IsUpsert = isUpsert });
        }

        public Task<UpdateResult> UpdateManyAsync(BsonDocument filter, BsonDocument update, bool isUpsert = false)
        {
            return Collection.UpdateManyAsync(filter, update, new UpdateOptions { IsUpsert = isUpsert });
        }

        public DeleteResult DeleteOne(BsonDocument filter)
        {
            return Collection.DeleteOne(filter);
        }

        public Task<DeleteResult> DeleteOneAsync(BsonDocument filter)
        {
            return Collection.DeleteOneAsync(filter);
        }

        public DeleteResult DeleteMany(BsonDocument filter)
        {
            return Collection.DeleteMany(filter);
        }

        public Task<DeleteResult> DeleteManyAsync(BsonDocument filter)
        {
            return Collection.DeleteManyAsync(filter);
        }

        public BsonDocument FindOneAndUpdate(BsonDocument filter, BsonDocument update, FindOneAndUpdateOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.FindOneAndUpdate<BsonDocument>(filter, update, options);
        }

        public Task<BsonDocument> FindOneAndUpdateAsync(BsonDocument filter, BsonDocument update, FindOneAndUpdateOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.FindOneAndUpdateAsync<BsonDocument>(filter, update, options);
        }

        public BsonDocument FindOneAndDelete(BsonDocument filter, FindOneAndDeleteOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.FindOneAndDelete<BsonDocument>(filter, options);
        }

        public Task<BsonDocument> FindOneAndDeleteAsync(BsonDocument filter, FindOneAndDeleteOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.FindOneAndDeleteAsync<BsonDocument>(filter, options);
        }

        public BsonDocument FindOneAndReplace(BsonDocument filter, BsonDocument replacement, FindOneAndReplaceOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.FindOneAndReplace<BsonDocument>(filter, replacement, options);
        }

        public Task<BsonDocument> FindOneAndReplaceAsync(BsonDocument filter, BsonDocument replacement, FindOneAndReplaceOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.FindOneAndReplaceAsync<BsonDocument>(filter, replacement, options);
        }

        public string CreateIndex(BsonDocument keys, CreateIndexOptions options = null)
        {
            var model = new CreateIndexModel<TEntity>(keys, options);
            return Collection.Indexes.CreateOne(model);
        }

        public Task<string> CreateIndexAsync(BsonDocument keys, CreateIndexOptions options = null)
        {
            var model = new CreateIndexModel<TEntity>(keys, options);
            return Collection.Indexes.CreateOneAsync(model);
        }

        public string CreateCompoundIndex(List<BsonElement> keys, CreateIndexOptions options = null)
        {
            var keysDocument = new BsonDocument(keys);
            var model = new CreateIndexModel<TEntity>(keysDocument, options);
            return Collection.Indexes.CreateOne(model);
        }

        public Task<string> CreateCompoundIndexAsync(List<BsonElement> keys, CreateIndexOptions options = null)
        {
            var keysDocument = new BsonDocument(keys);
            var model = new CreateIndexModel<TEntity>(keysDocument, options);
            return Collection.Indexes.CreateOneAsync(model);
        }

        public string CreateTextIndex(string[] fields, CreateIndexOptions options = null)
        {
            var keysDocument = new BsonDocument();
            foreach (var field in fields)
            {
                keysDocument.Add(field, "text");
            }
            var model = new CreateIndexModel<TEntity>(keysDocument, options);
            return Collection.Indexes.CreateOne(model);
        }

        public Task<string> CreateTextIndexAsync(string[] fields, CreateIndexOptions options = null)
        {
            var keysDocument = new BsonDocument();
            foreach (var field in fields)
            {
                keysDocument.Add(field, "text");
            }
            var model = new CreateIndexModel<TEntity>(keysDocument, options);
            return Collection.Indexes.CreateOneAsync(model);
        }

        public string CreateGeoIndex(string field, CreateIndexOptions options = null)
        {
            var keysDocument = new BsonDocument(field, "2dsphere");
            var model = new CreateIndexModel<TEntity>(keysDocument, options);
            return Collection.Indexes.CreateOne(model);
        }

        public Task<string> CreateGeoIndexAsync(string field, CreateIndexOptions options = null)
        {
            var keysDocument = new BsonDocument(field, "2dsphere");
            var model = new CreateIndexModel<TEntity>(keysDocument, options);
            return Collection.Indexes.CreateOneAsync(model);
        }

        public IAsyncCursor<BsonDocument> MapReduce(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.MapReduce(map, reduce, options);
        }

        public Task<IAsyncCursor<BsonDocument>> MapReduceAsync(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, BsonDocument> options = null)
        {
            return Collection.MapReduceAsync(map, reduce, options);
        }

        public IAsyncCursor<TResult> MapReduce<TResult>(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, TResult> options = null)
        {
            return Collection.MapReduce(map, reduce, options);
        }

        public Task<IAsyncCursor<TResult>> MapReduceAsync<TResult>(BsonJavaScript map, BsonJavaScript reduce, MapReduceOptions<TEntity, TResult> options = null)
        {
            return Collection.MapReduceAsync(map, reduce, options);
        }

        public BsonDocument GetStats()
        {
            var command = new BsonDocument { { "dbStats", 1 } };
            return Database.RunCommand<BsonDocument>(command);
        }

        public Task<BsonDocument> GetStatsAsync()
        {
            var command = new BsonDocument { { "dbStats", 1 } };
            return Database.RunCommandAsync<BsonDocument>(command);
        }

        public BsonDocument GetCollectionStats()
        {
            var command = new BsonDocument { { "collStats", CollectionName } };
            return Database.RunCommand<BsonDocument>(command);
        }

        public Task<BsonDocument> GetCollectionStatsAsync()
        {
            var command = new BsonDocument { { "collStats", CollectionName } };
            return Database.RunCommandAsync<BsonDocument>(command);
        }

        #endregion

        public async Task<bool> InsertAsync(TEntity entity)
        {
            if (entity == null) return false;
            try
            {
                await Collection.InsertOneAsync(entity);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<TEntity> UpdateAsync(FilterDefinition<TEntity> filter, UpdateDefinition<TEntity> update)
        {
            var options = new FindOneAndUpdateOptions<TEntity>
            {
                ReturnDocument = ReturnDocument.After
            };
            
            return await Collection.FindOneAndUpdateAsync(filter, update, options);
        }

        public TEntity FirstOrDefault(FilterDefinition<TEntity> filter, string field, bool isAscending = true)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).FirstOrDefault();
        }

        public Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter, string field, bool isAscending = true)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).FirstOrDefaultAsync();
        }

        public TEntity FirstOrDefault(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields)
        {
            var sortDefinitions = new List<SortDefinition<TEntity>>();
            foreach (var field in sortFields)
            {
                sortDefinitions.Add(field.Value ? 
                    Builders<TEntity>.Sort.Ascending(field.Key) : 
                    Builders<TEntity>.Sort.Descending(field.Key));
            }
            
            var sort = Builders<TEntity>.Sort.Combine(sortDefinitions);
            return Collection.Find(filter).Sort(sort).FirstOrDefault();
        }

        public Task<TEntity> FirstOrDefaultAsync(FilterDefinition<TEntity> filter, Dictionary<string, bool> sortFields)
        {
            var sortDefinitions = new List<SortDefinition<TEntity>>();
            foreach (var field in sortFields)
            {
                sortDefinitions.Add(field.Value ? 
                    Builders<TEntity>.Sort.Ascending(field.Key) : 
                    Builders<TEntity>.Sort.Descending(field.Key));
            }
            
            var sort = Builders<TEntity>.Sort.Combine(sortDefinitions);
            return Collection.Find(filter).Sort(sort).FirstOrDefaultAsync();
        }

        public TProjection FirstOrDefaultAs<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection)
        {
            return Collection.Find(filter).Project(projection).FirstOrDefault();
        }

        public Task<TProjection> FirstOrDefaultAsAsync<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection)
        {
            return Collection.Find(filter).Project(projection).FirstOrDefaultAsync();
        }

        public TProjection FirstOrDefaultAs<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).Project(projection).FirstOrDefault();
        }

        public Task<TProjection> FirstOrDefaultAsAsync<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, SortDefinition<TEntity> sort)
        {
            return Collection.Find(filter).Sort(sort).Project(projection).FirstOrDefaultAsync();
        }

        public TProjection FirstOrDefaultAs<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, string field, bool isAscending = true)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Project(projection).FirstOrDefault();
        }

        public Task<TProjection> FirstOrDefaultAsAsync<TProjection>(FilterDefinition<TEntity> filter, ProjectionDefinition<TEntity, TProjection> projection, string field, bool isAscending = true)
        {
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Project(projection).FirstOrDefaultAsync();
        }

        public BsonDocument FirstOrDefaultFields(FilterDefinition<TEntity> filter, string[] fields)
        {
            var projection = CreateProjection(fields);
            return Collection.Find(filter).Project<BsonDocument>(projection).FirstOrDefault();
        }

        public Task<BsonDocument> FirstOrDefaultFieldsAsync(FilterDefinition<TEntity> filter, string[] fields)
        {
            var projection = CreateProjection(fields);
            return Collection.Find(filter).Project<BsonDocument>(projection).FirstOrDefaultAsync();
        }

        public BsonDocument FirstOrDefaultFields(FilterDefinition<TEntity> filter, string[] fields, SortDefinition<TEntity> sort)
        {
            var projection = CreateProjection(fields);
            return Collection.Find(filter).Sort(sort).Project<BsonDocument>(projection).FirstOrDefault();
        }

        public Task<BsonDocument> FirstOrDefaultFieldsAsync(FilterDefinition<TEntity> filter, string[] fields, SortDefinition<TEntity> sort)
        {
            var projection = CreateProjection(fields);
            return Collection.Find(filter).Sort(sort).Project<BsonDocument>(projection).FirstOrDefaultAsync();
        }

        public BsonDocument FirstOrDefaultFields(FilterDefinition<TEntity> filter, string[] fields, string field, bool isAscending = true)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Project<BsonDocument>(projection).FirstOrDefault();
        }

        public Task<BsonDocument> FirstOrDefaultFieldsAsync(FilterDefinition<TEntity> filter, string[] fields, string field, bool isAscending = true)
        {
            var projection = CreateProjection(fields);
            var sort = isAscending ? 
                Builders<TEntity>.Sort.Ascending(field) : 
                Builders<TEntity>.Sort.Descending(field);
            
            return Collection.Find(filter).Sort(sort).Project<BsonDocument>(projection).FirstOrDefaultAsync();
        }

        public ReplaceOneResult Upsert(FilterDefinition<TEntity> filter, TEntity entity)
        {
            return Collection.ReplaceOne(filter, entity, new ReplaceOptions { IsUpsert = true });
        }

        public Task<ReplaceOneResult> UpsertAsync(FilterDefinition<TEntity> filter, TEntity entity)
        {
            return Collection.ReplaceOneAsync(filter, entity, new ReplaceOptions { IsUpsert = true });
        }
    }
}