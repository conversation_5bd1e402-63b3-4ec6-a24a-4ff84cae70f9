﻿using Abp.Dependency;
using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using VideoSurveillanceAdapter;
using Yunda.ISAS.DataMonitoringServer.DataAnalysis.Helper;
using Yunda.ISAS.DataMonitoringServer.DataCenter;
using Yunda.ISAS.DataMonitoringServer.WPF.ViewModel;
using Yunda.SOMS.DataMonitoringServer.WPF.View;
using YunDa.ISAS.Core;
using MessageBox = System.Windows.MessageBox;

namespace Yunda.ISAS.DataMonitoringServer.WPF.View
{

    /// <summary>
    /// Window.xaml 的交互逻辑
    /// </summary>
    public partial class DataWindow : Window, ISingletonDependency
    {
        private readonly CameraDataCenter _cameraDataCenter;
        private readonly MainViewModel _vm;
        private readonly ConfigurationHepler _configurationHepler;
        private readonly ConcurrentQueue<Paragraph> paragraphs = new ConcurrentQueue<Paragraph>();
        private readonly DispatcherTimer updateTimer;
        private readonly FtpFileSenderWindow _ftpFileSenderWindow;

        // 系统托盘图标
        private NotifyIcon _notifyIcon;

        public DataWindow(CameraDataCenter cameraDataCenter,
            MainViewModel vm,
            FtpFileSenderWindow ftpFileSenderWindow,
            ConfigurationHepler configurationHepler)
        {
            _vm = vm;
            this.DataContext = vm;
            _configurationHepler = configurationHepler;
            _cameraDataCenter = cameraDataCenter;
            _ftpFileSenderWindow = ftpFileSenderWindow;
            ShowInTaskbar = true;
            InitializeComponent();
            this.Loaded += this.DataWindow_Loaded;
            this.Closed += DataWindow_Closed;
            this.Closing += DataWindow_Closing;
            this.StateChanged += DataWindow_StateChanged;

            // 初始化系统托盘图标
            InitializeNotifyIcon();
        }

        /// <summary>
        /// 初始化系统托盘图标
        /// </summary>
        private void InitializeNotifyIcon()
        {
            _notifyIcon = new NotifyIcon();

            // 设置托盘图标
            // 尝试从程序资源加载图标，如果失败则使用系统默认图标
            try
            {
                string exePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                _notifyIcon.Icon = System.Drawing.Icon.ExtractAssociatedIcon(exePath);
            }
            catch
            {
                _notifyIcon.Icon = System.Drawing.SystemIcons.Application;
            }

            _notifyIcon.Visible = true;
            _notifyIcon.Text = "数据监控服务";

            // 创建右键菜单
            var contextMenu = new ContextMenuStrip();

            var openItem = new ToolStripMenuItem("打开主窗口");
            openItem.Click += (s, e) => ShowMainWindow();
            contextMenu.Items.Add(openItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            var exitItem = new ToolStripMenuItem("退出程序");
            exitItem.Click += (s, e) => ExitApplication();
            contextMenu.Items.Add(exitItem);

            _notifyIcon.ContextMenuStrip = contextMenu;

            // 双击托盘图标打开主窗口
            _notifyIcon.DoubleClick += (s, e) => ShowMainWindow();
        }

        /// <summary>
        /// 显示主窗口
        /// </summary>
        private void ShowMainWindow()
        {
            this.Show();
            this.WindowState = WindowState.Normal;
            this.Activate();
            this.Focus();
        }

        /// <summary>
        /// 退出应用程序
        /// </summary>
        private void ExitApplication()
        {
            this.Close();
        }

        /// <summary>
        /// 窗口状态改变事件处理
        /// </summary>
        private void DataWindow_StateChanged(object sender, EventArgs e)
        {
            // 当窗口最小化时，隐藏窗口到系统托盘
            if (this.WindowState == WindowState.Minimized)
            {
                this.Hide();

                // 可选：显示气泡提示
                _notifyIcon.ShowBalloonTip(2000, "数据监控服务", "应用程序已最小化到系统托盘", ToolTipIcon.Info);
            }
        }

        private void DataWindow_Closed(object sender, System.EventArgs e)
        {
            // 确保清理托盘图标
            if (_notifyIcon != null)
            {
                _notifyIcon.Visible = false;
                _notifyIcon.Dispose();
            }

            foreach (var videoPlayer in _cameraDataCenter.CameraCollection)
            {
                videoPlayer.LogoutCamera();//在窗口关闭的时候登出所有摄像头
            }
            try
            {
                //_vm._monitoringDataService.DataServiceStop();
                VideoPlayer.DisposeSDK(CameraBrand.Hik);
            }
            catch { }
            System.Diagnostics.Process.GetCurrentProcess().Kill();
        }

        private void DataWindow_Loaded(object sender, RoutedEventArgs e)
        {
            var time = System.IO.File.GetLastWriteTime(this.GetType().Assembly.Location);
            this.Title = $"{this.Title}[{AppVersionHelper.Version}][{time.ToString("yyyyMMdd")}]";

            // 设置托盘图标提示文本为应用标题
            if (_notifyIcon != null)
            {
                _notifyIcon.Text = this.Title.Length > 63 ? this.Title.Substring(0, 63) : this.Title;
            }
        }

        private void DataWindow_Closing(object sender, CancelEventArgs e)
        {
            // 修改窗口关闭行为：询问是否最小化到托盘
            if (e.Cancel == false) // 如果不是由其他地方取消关闭的
            {
                e.Cancel = true; // 取消关闭操作

                // 询问用户是否要退出程序或最小化到托盘
                var result = MessageBox.Show(
                    "是否要退出程序？\n\n选择\"是\"退出程序\n选择\"否\"最小化到系统托盘\n选择\"取消\"取消此操作",
                    "退出确认",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 真正退出前需要确认密码
                    ConfirmAgain();
                }
                else if (result == MessageBoxResult.No)
                {
                    // 最小化到托盘
                    //this.WindowState = WindowState.Minimized;
                    this.Hide();
                }
                // 如果是取消，什么都不做
            }
        }

        private void Exit_Click(object sender, System.EventArgs e)
        {
            System.Windows.Application.Current.Shutdown();
        }

        private void ScrollViewer_MouseWheel(object sender, System.Windows.Input.MouseWheelEventArgs e)
        {
            // 自定义鼠标滚轮滚动的行为
            double delta = e.Delta;
            double scrollSpeed = 20; // 你可以根据需要调整滚动速度

            var myScrollViewer = sender as ScrollViewer;
            if (myScrollViewer != null)
            {
                if (e.Delta > 0)
                {
                    // 向上滚动
                    myScrollViewer.ScrollToVerticalOffset(myScrollViewer.VerticalOffset - scrollSpeed);
                }
                else
                {
                    // 向下滚动
                    myScrollViewer.ScrollToVerticalOffset(myScrollViewer.VerticalOffset + scrollSpeed);
                }

                e.Handled = true;
            }
        }

        private void restart_Click(object sender, RoutedEventArgs e)
        {
            // 获取当前应用程序的完整路径
            string appPath = Process.GetCurrentProcess().MainModule.FileName;
            // 启动新实例
            ProcessStartInfo startInfo = new ProcessStartInfo(appPath)
            {
                // 如果需要，可以在这里设置启动参数
                //Arguments = "your arguments"
            };
            Process.Start(startInfo);
            // 关闭当前实例
            System.Windows.Application.Current.Shutdown();
        }

        /// <summary>
        /// 退出程序
        /// </summary>
        private void exitBtn_click(object sender, RoutedEventArgs e)
        {
            ConfirmAgain();
        }

        private void ConfirmAgain()
        {
            // 弹出确认窗口并获取返回值
            var passwordWindow = new PasswordWindow();
            var result = passwordWindow.ShowDialog();

            if (result == true) // 如果返回值为true，则输入的是"1"
            {
                // 确保清理托盘图标
                if (_notifyIcon != null)
                {
                    _notifyIcon.Visible = false;
                    _notifyIcon.Dispose();
                }
                System.Environment.Exit(0);
            }
            else
            {
                MessageBox.Show("密码错误，程序继续运行");
            }
        }

        private void SendRunstateFtpFile(object sender, RoutedEventArgs e)
        {
            _ftpFileSenderWindow.Show();
        }

        private void ToggleMoreOptions(object sender, RoutedEventArgs e)
        {
            bool isVisible = SecondRowPanel.Visibility == Visibility.Visible;
            SecondRowPanel.Visibility = isVisible ? Visibility.Collapsed : Visibility.Visible;
            MoreButton.Content = isVisible ? "更多..." : "收起";
        }
    }
}