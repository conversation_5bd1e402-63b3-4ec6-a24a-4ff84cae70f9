﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImDeviceva
    {
        public string Id { get; set; }
        public string Vaname { get; set; }
        public string Unit { get; set; }
        public string Expr { get; set; }

        public virtual ImDevicedata IdNavigation { get; set; }
    }
}
