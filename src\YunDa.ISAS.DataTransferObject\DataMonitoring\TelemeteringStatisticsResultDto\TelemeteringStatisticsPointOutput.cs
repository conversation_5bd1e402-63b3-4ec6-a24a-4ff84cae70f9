using System;
using System.Collections.Generic;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;

namespace YunDa.ISAS.DataTransferObject.DataMonitoring.TelemeteringStatisticsResultDto
{
    /// <summary>
    /// 遥测统计数据点输出DTO
    /// </summary>
    public class TelemeteringStatisticsPointOutput
    {
        /// <summary>
        /// 遥测配置ID
        /// </summary>
        public Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 遥测配置名称
        /// </summary>
        public string TelemeteringConfigurationName { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public StatisticsTypeEnum StatisticsType { get; set; }

        /// <summary>
        /// 统计类型名称
        /// </summary>
        public string StatisticsTypeName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 上限值
        /// </summary>
        public float? UpperLimit { get; set; }

        /// <summary>
        /// 下限值
        /// </summary>
        public float? LowerLimit { get; set; }

        /// <summary>
        /// 数据点列表
        /// </summary>
        public List<TelemeteringStatisticsDataPoint> Points { get; set; } = new List<TelemeteringStatisticsDataPoint>();
    }

    /// <summary>
    /// 遥测统计数据点
    /// </summary>
    public class TelemeteringStatisticsDataPoint
    {
        /// <summary>
        /// 结果值
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        public DateTime DateTime { get; set; }
    }
} 