﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImPuWavechl
    {
        public string Id { get; set; }
        public int Puctgycode { get; set; }
        public string Chltype { get; set; }
        public int Chlnum { get; set; }
        public string Chlname { get; set; }
        public string Chlunit1 { get; set; }
        public string Chlunit2 { get; set; }

        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
    }
}
