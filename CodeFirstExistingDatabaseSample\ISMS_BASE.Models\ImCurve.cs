﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImCurve
    {
        public ImCurve()
        {
            ImCurveItems = new HashSet<ImCurveItem>();
        }

        public string Id { get; set; } = null!;
        public string CurveType { get; set; } = null!;
        public string CurveName { get; set; } = null!;
        public int Minutes { get; set; }
        public double Ymax { get; set; }
        public double Ymin { get; set; }
        public int Sampling { get; set; }
        public DateTime CreTime { get; set; }

        public virtual ImCurveType CurveTypeNavigation { get; set; } = null!;
        public virtual ICollection<ImCurveItem> ImCurveItems { get; set; }
    }
}
