﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class VwAidVideoChannel
    {
        public string ChanId { get; set; } = null!;
        public string DevId { get; set; } = null!;
        public string ChanName { get; set; } = null!;
        public string? ChanType { get; set; }
        public int ChanNo { get; set; }
        public string? IdinPlat { get; set; }
        public string IsPtz { get; set; } = null!;
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public string UseState { get; set; } = null!;
        public int? SeqNo { get; set; }
        public string? Comment { get; set; }
        public string DevName { get; set; } = null!;
        public string? Ip { get; set; }
        public int? Port { get; set; }
    }
}
