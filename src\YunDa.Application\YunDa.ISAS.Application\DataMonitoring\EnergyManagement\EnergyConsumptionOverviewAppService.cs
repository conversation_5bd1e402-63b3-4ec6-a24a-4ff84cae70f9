﻿using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Logging;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.Application.DataMonitoring.EnergyManagement.Caching;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using YunDa.SOMS.Entities.DataMonitoring;
using MongoDB.Bson;
using MongoDB.Driver;
using YunDa.ISAS.MongoDB.Repositories;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;
using static Yunda.SOMS.MongoDB.Entities.DataMonitoring.StatisticsTypeEnum;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    public class EnergyConsumptionOverviewAppService : IEnergyConsumptionOverviewAppService
    {
        private readonly string _telemeteringModelListRediskey = "telemeteringModelList";
        /// <summary>
        /// 遥测数据实时库
        /// </summary>
        private readonly IRedisRepository<TelemeteringModel, string> _telemeteringModelListRedis;
        private readonly string _telesignalisationModelListRediskey = "telesignalisationModelList";
        /// <summary>
        /// 遥信数据实时库
        /// </summary>
        private readonly IRedisRepository<TelesignalisationModel, string> _telesignalisationModelListRedis;
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoRepository;
        private readonly IRepository<EnergyOperationCriteria, Guid> _operationCriteriaRepository;
        private readonly IRepository<EnergyConsumptionDevice, Guid> _energyDeviceRepository;
        private readonly IRedisRepository<string, string> _configRedis;
        private readonly EnergyCriteriaCache _criteriaCache;
        private readonly IRepository<EnergyConsumptionConfig, Guid> _configRepository;
        /// <summary>
        /// MongoDB存储库
        /// </summary>
        private readonly IMongoDbRepository<BsonDocument, Guid> _mongoRepository;


        // 缓存超时时间（秒）
        private TimeSpan CACHE_TIMEOUT = TimeSpan.FromMinutes(5);// 300; // 5分钟

        public EnergyConsumptionOverviewAppService(
             IRedisRepository<TelemeteringModel, string> telemeteringModelListRedis,
             IRedisRepository<TelesignalisationModel, string> telesignalisationModelListRedis,
             IRepository<EquipmentInfo, Guid> equipmentInfoRepository,
             IRepository<EnergyOperationCriteria, Guid> operationCriteriaRepository,
             IRepository<EnergyConsumptionDevice, Guid> energyDeviceRepository,
             IRedisRepository<string, string> configRedis,
             IRepository<EnergyConsumptionConfig, Guid> configRepository,
             IMongoDbRepository<BsonDocument, Guid> mongoRepository
             )
        {
            _telemeteringModelListRedis = telemeteringModelListRedis;
            _telesignalisationModelListRedis = telesignalisationModelListRedis;
            _equipmentInfoRepository = equipmentInfoRepository;
            _operationCriteriaRepository = operationCriteriaRepository;
            _energyDeviceRepository = energyDeviceRepository;
            _configRedis = configRedis;
            _configRepository = configRepository;
            _criteriaCache = new EnergyCriteriaCache(configRedis);
            _mongoRepository = mongoRepository;
        }
        /// <summary>
        /// 获取功率数据
        /// </summary>
        /// <param name="RealTimePowerType"></param>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [Description("获取功率数据")]
        public async Task<RequestResult<RealTimePower>> GetPowerStatistics(RealTimePowerTypeEnum RealTimePowerType)
        {
            RequestResult<RealTimePower> rst = new RequestResult<RealTimePower>();
            try
            {
                if (RealTimePowerType == RealTimePowerTypeEnum.RealTime)
                {
                    rst.Message = "无法查询实时的，最小时间粒度是小时";
                    return rst;
                }
                // 1. 查询有功功率遥测配置
                var activePowerTelemeterings = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.TelemeteringConfiguration != null && t.IsActive && t.TelemeteringConfiguration.IsActive)
                    .Where(t => t.Name == "实时功率监控" && t.Description == "有功功率")
                    .Select(t => t.TelemeteringConfiguration)
                    .ToList();

                // 无功功率遥测配置
                var reactivePowerTelemeterings = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.TelemeteringConfiguration != null && t.IsActive && t.TelemeteringConfiguration.IsActive)
                    .Where(t => t.Name == "实时功率监控" && t.Description == "无功功率")
                    .Select(t => t.TelemeteringConfiguration)
                    .ToList();

                if (!activePowerTelemeterings.Any() && !reactivePowerTelemeterings.Any())
                {
                    rst.Message = "未找到有功或无功功率相关的遥测配置";
                    return rst;
                }

                // 2. 确定时间范围和格式
                DateTime endTime = DateTime.Now;
                // 调整截止时间，前移一段时间，确保查询到的都是有效数据
                // 因为数据存储最小间隔是一分钟，所以需要前移一段时间
                endTime = endTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(RealTimePowerType));
                DateTime startTime;
                string timeFormat;
                int dataPoints;

                // 根据不同的查询类型设置时间范围和格式
                switch (RealTimePowerType)
                {
                    
                    case RealTimePowerTypeEnum.Hourly:
                        startTime = endTime.AddHours(-1); // 最近1小时
                        dataPoints = 60; // 每分钟一个数据点
                        timeFormat = "HH:mm";
                        break;
                    case RealTimePowerTypeEnum.Daily:
                        startTime = endTime.AddDays(-1); // 最近24小时
                        dataPoints = 24; // 每小时一个数据点
                        timeFormat = "MM-dd HH";
                        break;
                    case RealTimePowerTypeEnum.Weekly:
                        startTime = endTime.AddDays(-7); // 最近7天
                        dataPoints = 7; // 每天一个数据点
                        timeFormat = "MM-dd";
                        break;
                    case RealTimePowerTypeEnum.Monthly:
                        startTime = endTime.AddMonths(-1); // 最近1个月
                        dataPoints = 30; // 每天一个数据点
                        timeFormat = "yyyyMMdd";
                        break;
                    case RealTimePowerTypeEnum.Yearly:
                        startTime = endTime.AddYears(-1); // 最近1年
                        dataPoints = 12; // 每月一个数据点
                        timeFormat = "yyyyMM";
                        break;
                    default:
                        startTime = endTime.AddHours(-1);
                        dataPoints = 60;
                        timeFormat = "HH:mm";
                        break;
                }
                timeFormat = "yyyy-MM-dd HH:mm:ss";
                // 3. 从MongoDB中获取历史数据
                FixedIntervalEnum fixedInterval = ConvertToFixedInterval(RealTimePowerType);
                string intervalSuffix = GetIntervalSuffix(fixedInterval);
                string dateSuffix = DateTime.Now.ToString("yyyy");

                // 构建MongoDB集合名称，参考TelemeteringResultSaveTask中的命名方式
                string collectionName = $"TelemeteringModel_Zongzi_{intervalSuffix}_{dateSuffix}";
                _mongoRepository.CollectionName = collectionName;

                Log4Helper.Info(this.GetType(), $"查询MongoDB集合: {collectionName}, 时间范围: {startTime} 至 {endTime}");

                // 4. 构建MongoDB查询
                var activePowerTelemeteringIds = activePowerTelemeterings.Select(t => t.Id).ToList();
                var reactivePowerTelemeteringIds = reactivePowerTelemeterings.Select(t => t.Id).ToList();

                // 创建字典存储按时间点汇总的功率数据
                var activePowerByTime = new Dictionary<DateTime, float>();
                var reactivePowerByTime = new Dictionary<DateTime, float>();
                
                // 4.1 构建查询条件 - 有功功率
                if (activePowerTelemeteringIds.Any())
                {
                    var filterBuilder = Builders<BsonDocument>.Filter;
                    var filter = filterBuilder.And(
                        filterBuilder.In("TelemeteringConfigurationId", activePowerTelemeteringIds),
                        filterBuilder.Gte("ResultTime", startTime),
                        filterBuilder.Lte("ResultTime", endTime)
                    );

                    try
                    {
                        // 查询MongoDB获取有功功率数据
                        var activePowerDocs = _mongoRepository.GetAllIncludeToFindFluent(filter).ToList();
                        
                        if (activePowerDocs != null && activePowerDocs.Any())
                        {
                            Log4Helper.Info(this.GetType(), $"查询到有功功率数据 {activePowerDocs.Count()} 条");
                            
                            // 处理每条有功功率记录
                            foreach (var doc in activePowerDocs)
                            {
                                if (doc.Contains("ResultTime") && doc.Contains("ResultValue"))
                                {
                                    DateTime recordTime;
                                    if (DateTime.TryParse(doc["ResultTime"].ToString(), out recordTime))
                                    {
                                        // 根据查询类型规整时间点
                                        DateTime periodStart = GetPeriodStartTime(recordTime, RealTimePowerType);
                                        
                                        // 获取功率值
                                        float powerValue = 0;
                                        if (doc["ResultValue"].IsDouble)
                                        {
                                            powerValue = (float)doc["ResultValue"].AsDouble;
                                        }
                                        
                                        // 累加同一时间点的功率值
                                        if (activePowerByTime.ContainsKey(periodStart))
                                        {
                                            activePowerByTime[periodStart] += powerValue;
                                        }
                                        else
                                        {
                                            activePowerByTime[periodStart] = powerValue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), $"查询MongoDB有功功率数据异常: {ex.Message}", ex);
                    }
                }

                // 4.2 构建查询条件 - 无功功率
                if (reactivePowerTelemeteringIds.Any())
                {
                    var filterBuilder = Builders<BsonDocument>.Filter;
                    var filter = filterBuilder.And(
                        filterBuilder.In("TelemeteringConfigurationId", reactivePowerTelemeteringIds),
                        filterBuilder.Gte("ResultTime", startTime),
                        filterBuilder.Lte("ResultTime", endTime)
                    );

                    try
                    {
                        // 查询MongoDB获取无功功率数据
                        var reactivePowerDocs = _mongoRepository.GetAllIncludeToFindFluent(filter).ToList();
                        
                        if (reactivePowerDocs != null && reactivePowerDocs.Any())
                        {
                            Log4Helper.Info(this.GetType(), $"查询到无功功率数据 {reactivePowerDocs.Count()} 条");
                            
                            // 处理每条无功功率记录
                            foreach (var doc in reactivePowerDocs)
                            {
                                if (doc.Contains("ResultTime") && doc.Contains("ResultValue"))
                                {
                                    DateTime recordTime;
                                    if (DateTime.TryParse(doc["ResultTime"].ToString(), out recordTime))
                                    {
                                        // 根据查询类型规整时间点
                                        DateTime periodStart = GetPeriodStartTime(recordTime, RealTimePowerType);
                                        
                                        // 获取功率值
                                        float powerValue = 0;
                                        if (doc["ResultValue"].IsDouble)
                                        {
                                            powerValue = (float)doc["ResultValue"].AsDouble;
                                        }
                                        
                                        // 累加同一时间点的功率值
                                        if (reactivePowerByTime.ContainsKey(periodStart))
                                        {
                                            reactivePowerByTime[periodStart] += powerValue;
                                        }
                                        else
                                        {
                                            reactivePowerByTime[periodStart] = powerValue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), $"查询MongoDB无功功率数据异常: {ex.Message}", ex);
                    }
                }

                // 5. 如果没有数据或数据不足，使用模拟数据
                if (activePowerByTime.Count < 2 && reactivePowerByTime.Count < 2)
                {
                    Log4Helper.Info(this.GetType(), "数据不足，使用模拟数据");
                    
                    // 生成时间点
                    var timePoints = GenerateTimePoints(startTime, endTime, RealTimePowerType, dataPoints);
                    
                    // 生成模拟数据
                    GenerateSimulatedPowerData(timePoints, activePowerByTime, reactivePowerByTime, RealTimePowerType);
                }

                // 6. 格式化数据为返回格式
                var activePowerResults = new List<FloatTimeOutput>();
                var reactivePowerResults = new List<FloatTimeOutput>();
                
                // 按时间排序的键
                var sortedTimes = activePowerByTime.Keys.Union(reactivePowerByTime.Keys)
                    .OrderBy(t => t)
                    .ToList();
                
                foreach (var time in sortedTimes)
                {
                    string formattedTime = time.ToString(timeFormat);
                    
                    // 有功功率
                    if (activePowerByTime.ContainsKey(time))
                    {
                        activePowerResults.Add(new FloatTimeOutput 
                        { 
                            Time = formattedTime,
                            Value = activePowerByTime[time]
                        });
                    }
                    
                    // 无功功率
                    if (reactivePowerByTime.ContainsKey(time))
                    {
                        reactivePowerResults.Add(new FloatTimeOutput 
                        { 
                            Time = formattedTime,
                            Value = reactivePowerByTime[time]
                        });
                    }
                }
                
                // 7. 构建并返回结果
                rst.ResultData = new RealTimePower
                {
                    ActivePower = activePowerResults,
                    ReactivePower = reactivePowerResults,
                    RealTimePowerType = RealTimePowerType
                };
                
                rst.Flag = true;
                rst.Message = "获取实时功率数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取实时功率数据失败", ex);
                rst.Message = "获取实时功率数据失败: " + ex.Message;
            }
            
            return rst;
        }

        // 生成时间点列表
        private List<DateTime> GenerateTimePoints(DateTime startTime, DateTime endTime, RealTimePowerTypeEnum interval, int desiredPoints)
        {
            var timePoints = new List<DateTime>();
            DateTime current = startTime;
            
            // 根据不同时间间隔生成时间点
            switch (interval)
            {
                case RealTimePowerTypeEnum.RealTime:
                    while (current <= endTime && timePoints.Count < desiredPoints)
                    {
                        timePoints.Add(current);
                        current = current.AddSeconds(10); // 每10秒一个点
                    }
                    break;
                case RealTimePowerTypeEnum.Hourly:
                    while (current <= endTime && timePoints.Count < desiredPoints)
                    {
                        timePoints.Add(current);
                        current = current.AddMinutes(1);
                    }
                    break;
                case RealTimePowerTypeEnum.Daily:
                    while (current <= endTime && timePoints.Count < desiredPoints)
                    {
                        timePoints.Add(current);
                        current = current.AddHours(1);
                    }
                    break;
                case RealTimePowerTypeEnum.Weekly:
                    while (current <= endTime && timePoints.Count < desiredPoints)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
                case RealTimePowerTypeEnum.Monthly:
                    while (current <= endTime && timePoints.Count < desiredPoints)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
                case RealTimePowerTypeEnum.Yearly:
                    while (current <= endTime && timePoints.Count < desiredPoints)
                    {
                        timePoints.Add(current);
                        current = current.AddMonths(1);
                    }
                    break;
            }
            
            // 确保至少有两个点
            if (timePoints.Count < 2)
            {
                timePoints.Clear();
                
                // 均匀分布点
                for (int i = 0; i < desiredPoints; i++)
                {
                    double fraction = desiredPoints > 1 ? (double)i / (desiredPoints - 1) : 0;
                    TimeSpan timeRange = endTime - startTime;
                    DateTime point = startTime.AddTicks((long)(timeRange.Ticks * fraction));
                    timePoints.Add(point);
                }
            }
            
            return timePoints;
        }

        // 生成模拟功率数据
        private void GenerateSimulatedPowerData(List<DateTime> timePoints, 
            Dictionary<DateTime, float> activePowerByTime, 
            Dictionary<DateTime, float> reactivePowerByTime,
            RealTimePowerTypeEnum interval)
        {
            Random random = new Random();
            float basePower;
            float variance;
            float reactiveFactor;
            
            // 根据不同类型设置基础功率和波动范围
            switch (interval)
            {
                case RealTimePowerTypeEnum.RealTime:
                    basePower = 200; // kW
                    variance = 50;   // 波动范围
                    reactiveFactor = 0.3f; // 无功/有功比例
                    break;
                case RealTimePowerTypeEnum.Hourly:
                    basePower = 250; // kW
                    variance = 40;
                    reactiveFactor = 0.35f;
                    break;
                case RealTimePowerTypeEnum.Daily:
                    basePower = 300; // kW
                    variance = 100;
                    reactiveFactor = 0.32f;
                    break;
                case RealTimePowerTypeEnum.Weekly:
                    basePower = 320; // kW
                    variance = 120;
                    reactiveFactor = 0.31f;
                    break;
                case RealTimePowerTypeEnum.Monthly:
                    basePower = 350; // kW
                    variance = 130;
                    reactiveFactor = 0.33f;
                    break;
                case RealTimePowerTypeEnum.Yearly:
                    basePower = 380; // kW
                    variance = 150;
                    reactiveFactor = 0.34f;
                    break;
                default:
                    basePower = 300;
                    variance = 100;
                    reactiveFactor = 0.3f;
                    break;
            }
            
            // 生成有规律的模拟数据
            int pointCount = timePoints.Count;
            for (int i = 0; i < pointCount; i++)
            {
                // 按正弦波形变化，模拟一天内负荷变化规律
                double phase = (double)i / pointCount * 2 * Math.PI;
                float sinFactor = 0.5f * (1 + (float)Math.Sin(phase));
                
                // 生成有功功率，基础功率+正弦波动+随机小波动
                float activePower = basePower + variance * sinFactor + (float)random.NextDouble() * 20 - 10;
                activePowerByTime[timePoints[i]] = Math.Max(0, activePower);
                
                // 生成无功功率，与有功功率保持一定比例关系
                float reactivePower = activePower * reactiveFactor * (0.8f + 0.4f * (float)random.NextDouble());
                reactivePowerByTime[timePoints[i]] = Math.Max(0, reactivePower);
            }
        }

        /// <summary>
        /// 获取实时功率因数数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [Description("获取实时功率因数数据")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<float>> GetRealTimePowerFactor()
        {
            RequestResult<float> rst = new RequestResult<float>();
            try
            {
                // 1. 查询有功功率遥测配置
                var activePowerTelemeterings = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.TelemeteringConfiguration != null && t.IsActive && t.TelemeteringConfiguration.IsActive)
                    .Where(t => t.Name == "实时功率监控" && t.Description == "有功功率")
                    .Select(t => t.TelemeteringConfiguration)
                    .ToList();

                // 无功功率遥测配置
                var reactivePowerTelemeterings = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.TelemeteringConfiguration != null && t.IsActive && t.TelemeteringConfiguration.IsActive)
                    .Where(t => t.Name == "实时功率监控" && t.Description == "无功功率")
                    .Select(t => t.TelemeteringConfiguration)
                    .ToList();

                if (!activePowerTelemeterings.Any() && !reactivePowerTelemeterings.Any())
                {
                    rst.Message = "未找到有功或无功功率相关的遥测配置";
                    return rst;
                }
                
                // 调整查询时间，确保获取的是有效数据
                // 因为数据存储最小间隔是一分钟，所以需要前移一段时间
                DateTime queryTime = DateTime.Now.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(RealTimePowerTypeEnum.RealTime));
                
                string redisKey = "telemeteringModelList_Zongzi";
                // 2. 从实时库telemeteringModelListRedis中查询数据
                var telemeterings = await _telemeteringModelListRedis.HashSetGetAllAsync(redisKey);

                float totalActivePower = 0;
                float totalReactivePower = 0;
                bool hasData = false;
                
                // 3. 获取所有有功功率数据并求和
                if (activePowerTelemeterings.Any() && telemeterings != null && telemeterings.Any())
                {
                    var activePowerIds = activePowerTelemeterings.Select(t => t.Id).ToList();
                    
                    // 查找实时数据中匹配有功功率ID的数据并累加
                    var activePowerData = telemeterings
                        .Where(t => activePowerIds.Contains(t.Id) && t.ResultValue > 0)
                        .ToList();
                        
                    if (activePowerData.Any())
                    {
                        totalActivePower = activePowerData.Sum(t => t.ResultValue);
                        hasData = true;
                        Log4Helper.Info(this.GetType(), $"获取到有功功率实时数据: {totalActivePower} kW");
                    }
                }
                
                // 4. 获取所有无功功率数据并求和
                if (reactivePowerTelemeterings.Any() && telemeterings != null && telemeterings.Any())
                {
                    var reactivePowerIds = reactivePowerTelemeterings.Select(t => t.Id).ToList();
                    
                    // 查找实时数据中匹配无功功率ID的数据并累加
                    var reactivePowerData = telemeterings
                        .Where(t => reactivePowerIds.Contains(t.Id) && t.ResultValue > 0)
                        .ToList();
                        
                    if (reactivePowerData.Any())
                    {
                        totalReactivePower = reactivePowerData.Sum(t => t.ResultValue);
                        hasData = true;
                        Log4Helper.Info(this.GetType(), $"获取到无功功率实时数据: {totalReactivePower} kVar");
                    }
                }
                
                // 5. 计算功率因数
                float powerFactor = 0;
                
                // 如果有实际数据，进行功率因数计算
                if (hasData && (totalActivePower > 0 || totalReactivePower > 0))
                {
                    // 计算视在功率 (S = √(P² + Q²))
                    float apparentPower = (float)Math.Sqrt(totalActivePower * totalActivePower + totalReactivePower * totalReactivePower);
                    
                    // 计算功率因数 (PF = P / S)，如果视在功率大于0
                    if (apparentPower > 0)
                    {
                        powerFactor = totalActivePower / apparentPower;
                        Log4Helper.Info(this.GetType(), $"计算得到功率因数: {powerFactor}");
                    }
                }
                else
                {
                    // 如果没有实际数据，使用模拟数据
                    Log4Helper.Info(this.GetType(), "未获取到实时功率数据，使用模拟数据");
                    Random random = new Random();
                    // 模拟数据通常在0.85-0.95之间
                    powerFactor = 0.85f + (float)(random.NextDouble() * 0.1);
                }
                
                // 确保功率因数在合理范围内(0-1)
                powerFactor = Math.Max(0, Math.Min(1, powerFactor));
                
                rst.ResultData = powerFactor;
                rst.Flag = true;
                rst.Message = "获取实时功率因数数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取实时功率因数数据失败", ex);
                rst.Message = "获取实时功率因数数据失败: " + ex.Message;
                rst.Flag = false;
            }
            return rst;
        }

        /// <summary>
        /// 根据记录时间和查询类型获取所属时间段的起始时间
        /// </summary>
        /// <param name="recordTime">记录时间</param>
        /// <param="interval">时间间隔类型</param>
        /// <returns>所属时间段的起始时间</returns>
        private DateTime GetPeriodStartTime(DateTime recordTime, RealTimePowerTypeEnum interval)
        {
            switch (interval)
            {
                case RealTimePowerTypeEnum.RealTime:
                    return new DateTime(recordTime.Year, recordTime.Month, recordTime.Day, 
                                      recordTime.Hour, recordTime.Minute, 0);
                                      
                case RealTimePowerTypeEnum.Hourly:
                    return new DateTime(recordTime.Year, recordTime.Month, recordTime.Day, 
                                      recordTime.Hour, 0, 0);
                                      
                case RealTimePowerTypeEnum.Daily:
                    return new DateTime(recordTime.Year, recordTime.Month, recordTime.Day);
                
                case RealTimePowerTypeEnum.Weekly:
                    // 计算当前日期所在周的开始（周一）
                    int diff = (7 + (recordTime.DayOfWeek - DayOfWeek.Monday)) % 7;
                    return new DateTime(recordTime.Year, recordTime.Month, recordTime.Day).AddDays(-1 * diff);
                
                case RealTimePowerTypeEnum.Monthly:
                    return new DateTime(recordTime.Year, recordTime.Month, 1);
                
                case RealTimePowerTypeEnum.Yearly:
                    return new DateTime(recordTime.Year, 1, 1);
                
                default:
                    return new DateTime(recordTime.Year, recordTime.Month, recordTime.Day);
            }
        }
        /// <summary>
        /// 获取馈线能耗分布
        /// </summary>
        /// <param name="interval"></param>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [Description("获取馈线能耗分布")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<FeederEnergyDistribution>> GetFeederEnergyDistribution(RealTimePowerTypeEnum interval = RealTimePowerTypeEnum.Daily)
        {
            // 注意：此方法使用差值(Difference)统计类型的数据，表示每个时间段内的能耗增量
            //不要获取实时的，只需要小时，天，周，月，年的数据
            RequestResult<FeederEnergyDistribution> rst = new RequestResult<FeederEnergyDistribution>();
            if (interval == RealTimePowerTypeEnum.RealTime)
            {
                rst.Message = "无法查询实时的，最小时间粒度是小时";
                return rst;
            }
            try
            {
                // 1. 获取馈线设备
                var feederDevices = _energyDeviceRepository.GetAllIncluding(t => t.EquipmentInfo)
                    .Where(d => d.IsActive && d.DeviceType == 3) // DeviceType == 3 表示馈线设备
                    .ToList();

                if (!feederDevices.Any())
                {
                    rst.Message = "未找到馈线设备";
                    return rst;
                }

                // 2. 获取与馈线能耗相关的配置
                var feederConfigs = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.IsActive && 
                           t.TelemeteringConfiguration != null && 
                           t.TelemeteringConfiguration.IsActive &&
                           (t.Name.Contains("馈线能耗分布") || t.Name.Contains("feeder energy")) &&
                           feederDevices.Select(d => d.Id).Contains(t.EnergyConsumptionDeviceId.Value))
                    .ToList();

                // 如果没有特定的"馈线能耗分布"配置，尝试获取与馈线设备关联的任何能耗配置
                if (!feederConfigs.Any())
                {
                    feederConfigs = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                        .Where(t => t.IsActive && 
                               t.TelemeteringConfiguration != null && 
                               t.TelemeteringConfiguration.IsActive &&
                               feederDevices.Select(d => d.Id).Contains(t.EnergyConsumptionDeviceId.Value))
                        .ToList();
                }

                // 3. 准备数据结构
                var feederNames = new List<string>();
                var feederData = new List<FeederEnergyItem>();
                var deviceConsumptionDict = new Dictionary<Guid, float>();
                float totalConsumption = 0;

                // 4. 获取MongoDB中的能耗数据
                FixedIntervalEnum fixedInterval = ConvertToFixedInterval(interval);
                string intervalSuffix = GetIntervalSuffix(fixedInterval);
                string dateSuffix = DateTime.Now.ToString("yyyy");
                
                // 构建与TelemeteringResultSaveTask相同的集合名称 - 使用差值统计类型的集合
                _mongoRepository.CollectionName = $"TelemeteringModel_PowerConsumptionData_{intervalSuffix}_{dateSuffix}";

                // 设置查询的时间范围
                DateTime endTime = DateTime.Now;
                // 调整截止时间，前移一段时间，确保查询到的都是有效数据
                // 因为数据存储最小间隔是一分钟，所以需要前移一段时间
                endTime = endTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(interval));
                DateTime startTime = GetStartTimeByInterval(interval);

                // 5. 构建MongoDB查询，获取馈线设备的能耗数据
                var telemeteringConfigurationIds = feederConfigs.ToDictionary(t => t.TelemeteringId);

                var filterBuilder = Builders<BsonDocument>.Filter;
                var filter = filterBuilder.And(
                    filterBuilder.In("TelemeteringConfigurationId", telemeteringConfigurationIds.Keys),
                    filterBuilder.Gte("EndTime", startTime),
                    filterBuilder.Lte("EndTime", endTime),
                    // 使用差值统计类型，适用于能耗分布
                    filterBuilder.Eq("StatisticsType", (int)Difference)
                );

                try
                {
                    // 查询MongoDB，获取时间范围内的能耗数据
                    var powerConsumptionDocs = _mongoRepository.GetAllIncludeToFindFluent(filter).ToList();
                    
                    if (powerConsumptionDocs != null && powerConsumptionDocs.Any())
                    {
                        // 计算每个设备的总有功能耗
                        foreach (var doc in powerConsumptionDocs)
                        {
                            // 使用doc中TelemeteringConfigurationId匹配telemeteringConfigurationIds中的遥测信息
                            if (doc.Contains("TelemeteringConfigurationId"))
                            {
                                Guid telemeteringConfigId;
                                
                                if (Guid.TryParse(doc["TelemeteringConfigurationId"].ToString(), out telemeteringConfigId))
                                {
                                    if (telemeteringConfigurationIds.ContainsKey(telemeteringConfigId))
                                    {
                                       string deviceName = telemeteringConfigurationIds[telemeteringConfigId].EnergyConsumptionDevice.Name;
                                        // 获取能耗值 - 使用差值数据
                                        float energyConsumption = 0;
                                        if (doc.Contains("ResultValue") && doc["ResultValue"].IsDouble)
                                        {
                                            energyConsumption = (float)doc["ResultValue"].AsDouble;
                                        }
                                        else if (doc.Contains("EnergyConsumption") && doc["EnergyConsumption"].IsDouble)
                                        {
                                            energyConsumption = (float)doc["EnergyConsumption"].AsDouble;
                                        }
                                        feederData.Add(new FeederEnergyItem
                                        {
                                            Name = deviceName,
                                            Value = energyConsumption
                                        });
                                    }
                                   
                                }
                            }
                        }

                        Log4Helper.Info(this.GetType(), $"查询到馈线能耗数据 {powerConsumptionDocs.Count()} 条, 总有功能耗: {totalConsumption} kWh");
                    }
                    else
                    {
                        Log4Helper.Info(this.GetType(), $"未查询到馈线能耗数据，使用模拟数据");
                    }
                }
                catch (Exception ex)
                {
                    Log4Helper.Error(this.GetType(), $"查询MongoDB馈线能耗数据异常: {ex.Message}", ex);
                }
                // 如果结果数据为空或能耗总和为0，则使用模拟数据
                if (!feederData.Any())
                {
                    Log4Helper.Info(this.GetType(), "馈线能耗数据为空或总能耗为0，使用模拟数据");

                    // 4. 直接为每个馈线设备生成模拟数据
                    foreach (var feeder in feederDevices)
                    {
                        string feederName = feeder.Name;

                        // 添加馈线名称到列表
                        feederNames.Add(feederName);

                        // 生成模拟数据
                        Random random = new Random(feeder.GetHashCode());

                        // 根据不同时间间隔调整数据范围
                        int baseValue = 120; // 馈线基准值
                        int variance = 0;

                        switch (interval)
                        {
                            case RealTimePowerTypeEnum.Hourly:
                                variance = random.Next(-10, 40);
                                break;
                            case RealTimePowerTypeEnum.Daily:
                                variance = random.Next(-5, 45);
                                break;
                            case RealTimePowerTypeEnum.Weekly:
                                variance = random.Next(0, 50);
                                break;
                            case RealTimePowerTypeEnum.Monthly:
                                variance = random.Next(5, 55);
                                break;
                            case RealTimePowerTypeEnum.Yearly:
                                variance = random.Next(10, 60);
                                break;
                            default:
                                variance = random.Next(-10, 40);
                                break;
                        }

                        float energyValue = baseValue + variance;

                        // 添加到结果数据
                        feederData.Add(new FeederEnergyItem
                        {
                            Name = feederName,
                            Value = energyValue,
                        });
                    }
                }

                

                // 构建并返回结果
                rst.ResultData = new FeederEnergyDistribution
                {
                    FeederNames = feederNames,
                    FeederData = feederData,
                    IntervalType = interval
                };
                
                rst.Flag = true;
                rst.Message = "获取馈线能耗分布数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取馈线能耗分布数据", ex);
                rst.Message = "获取馈线能耗分布数据失败: " + ex.Message;
            }

            return rst;
        }


        /// <summary>
        /// 获取馈线累计能耗
        /// </summary>
        /// <param name="interval"></param>
        /// <returns></returns>
        [HttpGet]
        [ShowApi]
        [Description("获取馈线累计能耗")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<FeederCumulativeEnergy>> GetFeederCumulativeEnergyConsumption(RealTimePowerTypeEnum interval)
        {
           // 注意：此方法使用实时值(RealTime)统计类型的数据，表示电表的累计电量读数

            RequestResult<FeederCumulativeEnergy> rst = new RequestResult<FeederCumulativeEnergy>();
            // 不查询实时的，只查询小时，天，周，月，年
            if (interval == RealTimePowerTypeEnum.RealTime)
            {
                rst.Message = "无法查询实时的，最小时间粒度是小时";
                return rst;
            }
            try
            {
                // 1. 获取馈线设备
                var feederDevices = _energyDeviceRepository.GetAllIncluding(t => t.EquipmentInfo)
                    .Where(d => d.IsActive && d.DeviceType == 3) // DeviceType == 3 表示馈线设备
                    .ToList();

                if (!feederDevices.Any())
                {
                    rst.Message = "未找到馈线设备";
                    return rst;
                }
                
                // 2. 查询所有馈线的有功总电能
                var activePowerTelemeterings = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.TelemeteringConfiguration != null && t.IsActive && t.TelemeteringConfiguration.IsActive)
                    .Where(t => t.Name == "累计能耗" && t.Description.Contains("有功"))
                    .ToList();
                
                // 查询所有馈线的无功总电能
                var reactivePowerTelemeterings = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.TelemeteringConfiguration != null && t.IsActive && t.TelemeteringConfiguration.IsActive)
                    .Where(t => t.Name == "累计能耗" && t.Description.Contains("无功"))
                    .ToList();

                // 3. 设置查询的时间范围
                DateTime endTime = DateTime.Now;
                // 调整截止时间，前移一段时间，确保查询到的都是有效数据
                // 因为数据存储最小间隔是一分钟，所以需要前移一段时间
                endTime = endTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(interval));
                DateTime startTime = GetStartTimeByInterval(interval);
                
                // 4. 获取MongoDB中的能耗数据
                FixedIntervalEnum fixedInterval = ConvertToFixedInterval(interval);
                string intervalSuffix = GetIntervalSuffix(fixedInterval);
                string dateSuffix = DateTime.Now.ToString("yyyy");
                
                // 构建MongoDB集合名称 - 使用实时值统计类型的集合
                _mongoRepository.CollectionName = $"TelemeteringModel_PowerConsumptionData_{intervalSuffix}_{dateSuffix}";
                
                // 5. 准备数据结构
                var timeLabels = new List<string>();
                var feederSeriesList = new List<FeederEnergySeries>();
                
                // 为每个馈线设备创建一个数据系列
                foreach (var feeder in feederDevices)
                {
                    feederSeriesList.Add(new FeederEnergySeries
                    {
                        FeederName = feeder.EquipmentInfo?.Name ?? feeder.Name,
                        ActiveValues = new List<float>(),
                        ReactiveValues = new List<float>()
                    });
                }
                
                // 6. 根据时间间隔生成时间点
                var timePoints = GenerateTimePointsByInterval(startTime, endTime, interval);
                string timeFormat = GetTimeFormatByInterval(interval);
                
                // 将时间点转换为标签
                foreach (var timePoint in timePoints)
                {
                    if (interval == RealTimePowerTypeEnum.Weekly)
                    {
                        // 对于周数据，添加中文星期几
                        string formattedDate = timePoint.ToString("MM-dd");
                        string chineseWeekday = TimeAdjustmentHelper.GetChineseDayOfWeek(timePoint);
                        timeLabels.Add($"{formattedDate} {chineseWeekday}");
                    }
                    else
                    {
                        timeLabels.Add(timePoint.ToString(timeFormat));
                    }
                }
                
                // 7. 查询MongoDB获取能耗数据
                var feederConfigs = activePowerTelemeterings.Union(reactivePowerTelemeterings)
                    .Where(t => feederDevices.Select(d => d.Id).Contains(t.EnergyConsumptionDeviceId.Value))
                    .ToList();
                
                if (feederConfigs.Any())
                {
                    var telemeteringIds = feederConfigs.Select(t => t.TelemeteringId).ToList();
                    
                    var filterBuilder = Builders<BsonDocument>.Filter;
                    var filter = filterBuilder.And(
                        filterBuilder.In("TelemeteringConfigurationId", telemeteringIds),
                        filterBuilder.Gte("ResultTime", startTime),
                        filterBuilder.Lte("ResultTime", endTime),
                        // 使用实时值统计类型，适用于累计能耗
                        filterBuilder.Eq("StatisticsType", (int)RealTime)
                );
                    
                    try
                    {
                        var energyDocs = _mongoRepository.GetAllIncludeToFindFluent(filter).ToList();
                        
                        if (energyDocs != null && energyDocs.Any())
                        {
                            Log4Helper.Info(this.GetType(), $"查询到馈线能耗数据 {energyDocs.Count()} 条");
                            
                            // 处理查询结果
                            foreach (var doc in energyDocs)
                            {
                                if (doc.Contains("TelemeteringConfigurationId") && 
                                    doc.Contains("ResultTime") && 
                                    doc.Contains("ResultValue"))
                                {
                                    // 获取遥测点ID
                                    Guid telemeteringId;
                                    if (Guid.TryParse(doc["TelemeteringConfigurationId"].ToString(), out telemeteringId))
                                    {
                                        // 查找对应的配置
                                        var config = feederConfigs.FirstOrDefault(t => t.TelemeteringId == telemeteringId);
                                        if (config != null)
                                        {
                                            // 查找对应的馈线设备
                                            var feeder = feederDevices.FirstOrDefault(d => d.Id == config.EnergyConsumptionDeviceId);
                                            if (feeder != null)
                                            {
                                                // 获取时间和值
                                                DateTime recordTime;
                                                if (DateTime.TryParse(doc["ResultTime"].ToString(), out recordTime))
                                                {
                                                    // 查找最接近的时间点索引
                                                    int timeIndex = FindClosestTimePointIndex(timePoints, recordTime);
                                                    if (timeIndex >= 0)
                                                    {
                                                        // 查找对应的馈线数据系列
                                                        var feederSeries = feederSeriesList.FirstOrDefault(s => 
                                                            s.FeederName == (feeder.EquipmentInfo?.Name ?? feeder.Name));
                                                        
                                                        if (feederSeries != null)
                                                        {
                                                            // 确保列表长度足够
                                                            while (feederSeries.ActiveValues.Count <= timeIndex)
                                                            {
                                                                feederSeries.ActiveValues.Add(0);
                                                            }
                                                            while (feederSeries.ReactiveValues.Count <= timeIndex)
                                                            {
                                                                feederSeries.ReactiveValues.Add(0);
                                                            }
                                                            
                                                            // 根据遥测类型更新值 - 使用实时值
                                                            float value = 0;
                                                            if (doc.Contains("ResultValue") && doc["ResultValue"].IsDouble)
                                                            {
                                                                // 获取电表的累计电量读数
                                                                value = (float)doc["ResultValue"].AsDouble;
                                                            }
                                                            
                                                            if (config.Description.Contains("有功"))
                                                            {
                                                                feederSeries.ActiveValues[timeIndex] += value;
                                                            }
                                                            else if (config.Description.Contains("无功"))
                                                            {
                                                                feederSeries.ReactiveValues[timeIndex] += value;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            Log4Helper.Info(this.GetType(), "未查询到馈线能耗数据，将使用模拟数据");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log4Helper.Error(this.GetType(), $"查询MongoDB馈线能耗数据异常: {ex.Message}", ex);
                    }
                }
                
                // 8. 如果数据不足，使用模拟数据
                foreach (var feederSeries in feederSeriesList)
                {
                    // 确保每个馈线的数据点数量与时间点数量一致
                    while (feederSeries.ActiveValues.Count < timeLabels.Count)
                    {
                        feederSeries.ActiveValues.Add(0);
                    }
                    while (feederSeries.ReactiveValues.Count < timeLabels.Count)
                    {
                        feederSeries.ReactiveValues.Add(0);
                    }
                    
                    // 如果没有实际数据，生成模拟数据
                    bool hasData = feederSeries.ActiveValues.Any(v => v > 0) || feederSeries.ReactiveValues.Any(v => v > 0);
                    if (!hasData)
                    {
                        Random random = new Random(feederSeries.FeederName.GetHashCode());
                        
                        // 基础能耗值和波动范围
                        float baseActiveValue = 100; // kWh
                        float baseReactiveValue = 30; // kVarh
                        
                        // 根据不同的时间间隔调整基础值
                        switch (interval)
                        {
                            case RealTimePowerTypeEnum.Hourly:
                                baseActiveValue = 20;
                                baseReactiveValue = 6;
                                break;
                            case RealTimePowerTypeEnum.Daily:
                                baseActiveValue = 100;
                                baseReactiveValue = 30;
                                break;
                            case RealTimePowerTypeEnum.Weekly:
                                baseActiveValue = 500;
                                baseReactiveValue = 150;
                                break;
                            case RealTimePowerTypeEnum.Monthly:
                                baseActiveValue = 2000;
                                baseReactiveValue = 600;
                                break;
                            case RealTimePowerTypeEnum.Yearly:
                                baseActiveValue = 24000;
                                baseReactiveValue = 7200;
                                break;
                        }
                        
                        // 生成有规律的模拟数据
                        for (int i = 0; i < timeLabels.Count; i++)
                        {
                            // 按正弦波形变化，模拟一天内负荷变化规律
                            double phase = (double)i / timeLabels.Count * 2 * Math.PI;
                            float sinFactor = 0.5f * (1 + (float)Math.Sin(phase));
                            
                            // 生成有功能耗，基础能耗+正弦波动+随机小波动
                            float activeVariance = baseActiveValue * 0.3f;
                            float activeEnergy = baseActiveValue + activeVariance * sinFactor + 
                                                (float)random.NextDouble() * activeVariance * 0.2f - activeVariance * 0.1f;
                            
                            // 生成无功能耗，与有功能耗保持一定比例关系
                            float reactiveVariance = baseReactiveValue * 0.3f;
                            float reactiveEnergy = baseReactiveValue + reactiveVariance * sinFactor + 
                                                  (float)random.NextDouble() * reactiveVariance * 0.2f - reactiveVariance * 0.1f;
                            
                            feederSeries.ActiveValues[i] = Math.Max(0, activeEnergy);
                            feederSeries.ReactiveValues[i] = Math.Max(0, reactiveEnergy);
                        }
                    }
                }
                
                // 计算所有馈线在每个时间点的能耗总和
                var allFeederSeriesActiveValues = new List<float>();
                var allFeederSeriesReactiveValues = new List<float>();
                
                // 初始化列表，确保长度与时间标签数量一致
                for (int i = 0; i < timeLabels.Count; i++)
                {
                    allFeederSeriesActiveValues.Add(0);
                    allFeederSeriesReactiveValues.Add(0);
                }
                
                // 累加每个馈线在每个时间点的能耗值
                foreach (var feederSeries in feederSeriesList)
                {
                    for (int i = 0; i < timeLabels.Count; i++)
                    {
                        if (i < feederSeries.ActiveValues.Count)
                        {
                            allFeederSeriesActiveValues[i] += feederSeries.ActiveValues[i];
                        }
                        
                        if (i < feederSeries.ReactiveValues.Count)
                        {
                            allFeederSeriesReactiveValues[i] += feederSeries.ReactiveValues[i];
                        }
                    }
                }
                
                // 9. 构建并返回结果
                rst.ResultData = new FeederCumulativeEnergy
                {
                    TimeLabels = timeLabels,
                    FeederSeries = feederSeriesList,
                    AllFeederSeriesActiveValues = allFeederSeriesActiveValues,
                    AllFeederSeriesReactiveValues = allFeederSeriesReactiveValues,
                    IntervalType = interval
                };
                
                rst.Flag = true;
                rst.Message = "获取馈线累计能耗数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取馈线累计能耗数据失败", ex);
                rst.Message = "获取馈线累计能耗数据失败: " + ex.Message;
            }
            
            return rst;
        }
        
        /// <summary>
        /// 根据时间间隔生成时间点列表
        /// </summary>
        private List<DateTime> GenerateTimePointsByInterval(DateTime startTime, DateTime endTime, RealTimePowerTypeEnum interval)
        {
            var timePoints = new List<DateTime>();
            DateTime current = startTime;
            
            // 根据不同时间间隔生成时间点
            switch (interval)
            {
               
                case RealTimePowerTypeEnum.Hourly:
                    while (current <= endTime)
                    {
                        timePoints.Add(current);
                        current = current.AddMinutes(1); // 每分钟一个点
                    }
                    break;
                case RealTimePowerTypeEnum.Daily:
                    while (current <= endTime)
                    {
                        timePoints.Add(current);
                        current = current.AddHours(1); // 每小时一个点
                    }
                    break;
                case RealTimePowerTypeEnum.Weekly:
                    while (current <= endTime)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1); // 每天一个点
                    }
                    break;
                case RealTimePowerTypeEnum.Monthly:
                    while (current <= endTime)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1); // 每天一个点
                    }
                    break;
                case RealTimePowerTypeEnum.Yearly:
                    while (current <= endTime)
                    {
                        timePoints.Add(current);
                        current = current.AddMonths(1); // 每月一个点
                    }
                    break;
            }
            
            // 如果生成的点太多，进行抽样
            if (timePoints.Count > 60)
            {
                var sampledPoints = new List<DateTime>();
                double step = (double)timePoints.Count / 60;
                
                for (int i = 0; i < 60; i++)
                {
                    int index = (int)(i * step);
                    if (index < timePoints.Count)
                    {
                        sampledPoints.Add(timePoints[index]);
                    }
                }
                
                return sampledPoints;
            }
            
            return timePoints;
        }
        
        /// <summary>
        /// 查找最接近指定时间的时间点索引
        /// </summary>
        private int FindClosestTimePointIndex(List<DateTime> timePoints, DateTime target)
        {
            if (timePoints == null || !timePoints.Any())
                return -1;
                
            int closestIndex = 0;
            TimeSpan minDifference = TimeSpan.MaxValue;
            
            for (int i = 0; i < timePoints.Count; i++)
            {
                TimeSpan difference = timePoints[i] > target ? 
                    timePoints[i] - target : target - timePoints[i];
                    
                if (difference < minDifference)
                {
                    minDifference = difference;
                    closestIndex = i;
                }
            }
            
            return closestIndex;
        }
        
        /// <summary>
        /// 获取时间格式字符串
        /// </summary>
        private string GetTimeFormatByInterval(RealTimePowerTypeEnum interval)
        {
            switch (interval)
            {
                case RealTimePowerTypeEnum.RealTime:
                    return "mm:ss"; // 分:秒
                case RealTimePowerTypeEnum.Hourly:
                    return "HH:mm"; // 时:分
                case RealTimePowerTypeEnum.Daily:
                    return "MM-dd HH"; // 月-日 时
                case RealTimePowerTypeEnum.Weekly:
                    return "MM-dd dddd"; // 月-日 星期几
                case RealTimePowerTypeEnum.Monthly:
                    return "yyyy-MM-dd"; // 年-月-日
                case RealTimePowerTypeEnum.Yearly:
                    return "yyyy-MM"; // 年-月
                default:
                    return "yyyy-MM-dd HH:mm:ss"; // 默认完整格式
            }
        }
        
        /// <summary>
        /// 根据时间间隔获取起始时间
        /// </summary>
        private DateTime GetStartTimeByInterval(RealTimePowerTypeEnum interval)
        {
            DateTime endTime = DateTime.Now;
            
            switch (interval)
            {
                case RealTimePowerTypeEnum.Hourly:
                    return endTime.AddMinutes(-60); // 最近60分钟
                case RealTimePowerTypeEnum.Daily:
                    return endTime.AddHours(-24); // 最近24小时
                case RealTimePowerTypeEnum.Weekly:
                    return endTime.AddDays(-7); // 最近7天
                case RealTimePowerTypeEnum.Monthly:
                    return endTime.AddDays(-30); // 最近30天
                case RealTimePowerTypeEnum.Yearly:
                    return endTime.AddMonths(-12); // 最近3个月
                default:
                    return endTime.AddDays(-7); // 默认最近7天
            }
        }
        /// <summary>
        /// Converts FixedIntervalEnum to a string suffix.
        /// </summary>
        /// <param name="interval">The fixed interval.</param>
        /// <returns>The corresponding string suffix.</returns>
        private string GetIntervalSuffix(FixedIntervalEnum interval)
        {
            switch (interval)
            {
                case FixedIntervalEnum.Minute1:
                    return "1Min";
                case FixedIntervalEnum.Minute5:
                    return "5Min";
                case FixedIntervalEnum.Minute30:
                    return "30Min";
                case FixedIntervalEnum.Hour1:
                    return "1Hour";
                case FixedIntervalEnum.Hour6:
                    return "6Hour";
                case FixedIntervalEnum.Hour12:
                    return "12Hour";
                case FixedIntervalEnum.Day1:
                    return "1Day";
                case FixedIntervalEnum.Day7:
                    return "7Day";
                case FixedIntervalEnum.Day30:
                    return "30Day";
                default:
                    throw new ArgumentOutOfRangeException(nameof(interval), interval, "Unsupported interval type.");
            }
        }
        /// <summary>
        /// Converts RealTimePowerTypeEnum to FixedIntervalEnum.
        /// </summary>
        /// <param name="realTimePowerType">The real-time power type.</param>
        /// <returns>The corresponding FixedIntervalEnum value.</returns>
        private FixedIntervalEnum ConvertToFixedInterval(RealTimePowerTypeEnum realTimePowerType)
        {
            switch (realTimePowerType)
            {
                case RealTimePowerTypeEnum.RealTime:
                    return FixedIntervalEnum.Minute1;
                case RealTimePowerTypeEnum.Hourly:
                    return FixedIntervalEnum.Minute5;
                case RealTimePowerTypeEnum.Daily:
                    return FixedIntervalEnum.Hour1;
                case RealTimePowerTypeEnum.Weekly:
                    return FixedIntervalEnum.Day1;
                case RealTimePowerTypeEnum.Monthly:
                    return FixedIntervalEnum.Day7;
                case RealTimePowerTypeEnum.Yearly:
                    return FixedIntervalEnum.Day30;
                default:
                    throw new ArgumentOutOfRangeException(nameof(realTimePowerType), realTimePowerType, "Unsupported RealTimePowerTypeEnum value.");
            }
        }

        /// <summary>
        /// 根据时间间隔类型获取需要前移的时间偏移量
        /// 为了保持代码兼容性，直接调用TimeAdjustmentHelper.GetEndTimeSpanInterval
        /// </summary>
        /// <param name="interval">时间间隔类型</param>
        /// <returns>需要前移的时间偏移量</returns>
        private TimeSpan GetEndTimeSpanInterval(RealTimePowerTypeEnum interval)
        {
            return TimeAdjustmentHelper.GetEndTimeSpanInterval(interval);
        }

    }


}
