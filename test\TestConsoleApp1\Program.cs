﻿using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using SimpleExpressionEvaluator;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
//using Yunda.ISAS.RobotCommunicationServer.TCPSocket;

namespace TestConsoleApp1
{
    internal class Program
    {
        public class InspectionFileUploadedNotify
        {
            public delegate void InspectionFileUploadedNotifyDelegate(Guid? inspectionItemResultId, string fileRelativePath = null);
            public event InspectionFileUploadedNotifyDelegate InspectionFileUploadedNotifyEvent;
            public void FileUploadedNotify(Guid? inspectionItemResultId, string fileRelativePath = null)
            {
                if (InspectionFileUploadedNotifyEvent != null)
                {
                    InspectionFileUploadedNotifyEvent.Invoke(inspectionItemResultId, fileRelativePath);
                }
            }

        }
        private static void Main(string[] args)
        {
            var date1 = DateTime.Parse("2021-10-13 10:10:11");
            var date2 = DateTime.Parse("2021-10-13 10:10:12");
            var date3 = DateTime.Parse("2021-10-13 10:10:14");
            var date4 = DateTime.Parse("2021-10-13 10:10:15");
            List<DateTime> dateTimes = new List<DateTime>();
            dateTimes.Add(date1);
            dateTimes.Add(date2);
            dateTimes.Add(date3);
            dateTimes.Add(date4);
            var dateTimesBY = dateTimes.OrderBy(t => t);

            //if (date == DateTime.Now.Date)
            //{
            //    Console.WriteLine();

            //}
            //InspectionFileUploadedNotify inspectionFileUploadedNotify = new InspectionFileUploadedNotify();

            //// HashSet<string> vs = new HashSet<string>();
            ////for (int i = 0; i < 10; i++)
            ////{
            ////    vs.Add(@"C:\Users\<USER>\Desktop\123.jpg");
            ////}
            //for (int i = 0; i < 10000000; i++)
            //{
            //    Task.Run(()=> {

            //        inspectionFileUploadedNotify.InspectionFileUploadedNotifyEvent += (s, e) => {
            //            Console.WriteLine(e);
            //        };
            //        Task.Delay(2000).Wait();
            //    });

            //}
            //for (int i = 0; i < 10; i++)
            //{
            //    inspectionFileUploadedNotify?.FileUploadedNotify(null,i+"  test");
            //}
            var x = "周界围墙_3号位置_激光对射".ToArray().Intersect("周界围墙_3号位置_激光对射".ToArray()).Count();

            Console.WriteLine(x);

            //var b = File.ReadAllBytes(@"C:\Users\<USER>\Desktop\123.jpg");

            //FileStream fileStream = File.OpenRead(@"C:\Users\<USER>\Pictures\1.jpg");
            //byte[] vs = new byte[fileStream.Length];
            //fileStream.Read();
            //var b1 = ImageChangeSize.CutImageSize(b);
            //Console.WriteLine();

            //File.WriteAllBytes(@"C:\Users\<USER>\Desktop\1233.jpg", b);

            //using (StreamWriter se = new StreamWriter(@"C: \Users\guor\Pictures\2.jpg"))
            //{
            //    se.Write(b1);
            //}
            //string fileName = "D:\\Tools\\ffmpeg-20200324-e5d25d1-win64-static\\bin\\1.mp4";
            //var path = Path.GetDirectoryName(fileName);
            //var fileNameWithoutEx = Path.GetFileNameWithoutExtension(fileName);
            //var ex = Path.GetExtension(fileName);
            //var f =  Path.Combine(path, fileNameWithoutEx+ex);
            //Console.WriteLine(f);
            //TimeWorkService timeWorkService = new TimeWorkService();
            //timeWorkService.StartAsync(new CancellationToken());
            //Console.WriteLine(1);
            //Host.CreateDefaultBuilder().ConfigureServices(service=> { service.AddHostedService<TimeWorkService>(); }).UseConsoleLifetime().Build().RunAsync();
            //Console.ReadKey();
            //string testSignal = "0.01";
            //var si = int.Parse(testSignal);
            //Console.WriteLine(si);
            //string s_line = "asddw-110.612sdsdfd8dfdf8.9aa567";
            //var reg = "(?<!\\w_)[-+]?\\d*\\.?\\d*(?!\\d)";
            //var reg1 = "-?[0-9]*\\.[0-9]+";
            //var x=  Regex.Match(s_line,reg);
            //var x1 = Regex.Matches(s_line, reg1);
            //var arrs = Regex.Matches(s_line, reg1);
            //foreach (var item in arrs)
            //{
            //    var formatDecimal = float.Parse(item.ToString()).ToString("0.00");

            //    s_line = s_line.Replace(item.ToString(), formatDecimal);
            //}
            //Console.WriteLine(x);
            //string str1 = " Robot01//2021//03//14////CCD//28_Robot01_20210314164316.jpg";
            //string str2 = str1.Replace("////","//").Replace("//","\\");
            //Console.WriteLine(str2);
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            var strtest = "eb9009000000000000006c0200003c3f786d6c2076657273696f6e3d22312e302220656e636f64696e673d225554462d3822203f3e0a3c526f626f743e0a202020203c53656e64436f64653e526f626f7430313c2f53656e64436f64653e0a202020203c52656365697665436f64653e53657276657230313c2f52656365697665436f64653e0a202020203c547970653e323c2f547970653e0a202020203c4974656d733e0a20202020202020203c4974656d20726f626f745f6e616d653d22e794b5e58a9be5b7a1e6a380e69cbae599a8e4baba2220726f626f745f636f64653d22526f626f743031222074696d653d22323032312d30342d30312030393a34353a34332220747970653d2231222076616c75653d22302e33222076616c75655f756e69743d22302e336d2f732220756e69743d226d2f7322202f3e0a20202020202020203c4974656d20726f626f745f6e616d653d22e794b5e58a9be5b7a1e6a380e69cbae599a8e4baba2220726f626f745f636f64653d22526f626f743031222074696d653d22323032312d30342d30312030393a34353a34332220747970653d2232222076616c75653d22302e30222076616c75655f756e69743d22302e306d2220756e69743d226d22202f3e0a20202020202020203c4974656d20726f626f745f6e616d653d22e794b5e58a9be5b7a1e6a380e69cbae599a8e4baba2220726f626f745f636f64653d22526f626f743031222074696d653d22323032312d30342d30312030393a34353a34332220747970653d2233222076616c75653d2230222076616c75655f756e69743d223025252220756e69743d22252522202f3e0a202020203c2f4974656d733e0a3c2f526f626f743e0aeb90";


            List<byte> bytes = new List<byte>();


            var arrstart = strtest.ToList<char>();
            for (int i = 0; i < arrstart.Count; i++)
            {
                var v = $"{arrstart[i]}{arrstart[i + 1]}";
                //ReadOnlySpan<char> readOnlySpan = new ReadOnlySpan<char>(,) { arrstart[i], arrstart[i + 1] };
                var chars = new char[2] { arrstart[i], arrstart[i + 1] };
                bytes.Add(byte.Parse(chars.AsSpan()));
            }



            var arr = bytes.Skip(12).Take(bytes.Count - 14).ToArray();
            var content = Encoding.GetEncoding("GBK").GetString(arr);
            var arr1 = Encoding.GetEncoding("GBK").GetBytes(content);
            if (compareArr<byte>(arr, arr1))
            {
                Console.WriteLine("1");
            }
            content = Encoding.UTF8.GetString(arr);
            arr1 = Encoding.UTF8.GetBytes(content);
            if (compareArr<byte>(arr, arr1))
            {
                Console.WriteLine("2");
            }
            content = Encoding.ASCII.GetString(arr);
            arr1 = Encoding.ASCII.GetBytes(content);
            if (compareArr<byte>(arr, arr1))
            {
                Console.WriteLine("3");
            }

        }

        public static bool compareArr<T>(T[] arr1, T[] arr2)
        {
            var q = from a in arr1 join b in arr2 on a equals b select a;
            bool flag = arr1.Length == arr2.Length && q.Count() == arr1.Length;
            return flag;//内容相同返回true,反之返回false。
        }
        [MaxLength(10)]
        private static DateTime dateTime { get; set; }

        private static void Main1(string[] args)
        {
            string test16Str = "EB-90-03-00-00-00-03-00-00-00-6E-01-00-00-3C-3F-78-6D-6C-20-76-65-72-73-69-6F-6E-3D-22-31-2E-30-22-20-65-6E-63-6F-64-69-6E-67-3D-22-55-54-46-2D-38-22-20-3F-3E-0A-3C-52-6F-62-6F-74-3E-0A-20-20-20-20-3C-53-65-6E-64-43-6F-64-65-3E-43-30-38-35-34-36-42-35-2D-30-39-46-45-2D-30-32-31-31-2D-46-37-36-39-2D-45-33-37-34-34-35-42-34-41-41-44-38-3C-2F-53-65-6E-64-43-6F-64-65-3E-0A-20-20-20-20-3C-52-65-63-65-69-76-65-43-6F-64-65-3E-43-38-44-43-42-44-33-39-2D-32-35-36-32-2D-34-39-39-30-2D-41-30-44-38-2D-39-42-33-32-43-43-35-31-30-36-43-41-3C-2F-52-65-63-65-69-76-65-43-6F-64-65-3E-0A-20-20-20-20-3C-54-79-70-65-3E-31-3C-2F-54-79-70-65-3E-0A-20-20-20-20-3C-49-74-65-6D-73-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-31-22-20-76-61-6C-75-65-3D-22-31-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-31-22-20-75-6E-69-74-3D-22-22-20-2F-3E-0A-20-20-20-20-3C-2F-49-74-65-6D-73-3E-0A-3C-2F-52-6F-62-6F-74-3E-0A-00-00-EB-90-03-00-00-00-03-00-00-00-6E-01-00-00-3C-3F-78-6D-6C-20-76-65-72-73-69-6F-6E-3D-22-31-2E-30-22-20-65-6E-63-6F-64-69-6E-67-3D-22-55-54-46-2D-38-22-20-3F-3E-0A-3C-52-6F-62-6F-74-3E-0A-20-20-20-20-3C-53-65-6E-64-43-6F-64-65-3E-43-30-38-35-34-36-42-35-2D-30-39-46-45-2D-30-32-31-31-2D-46-37-36-39-2D-45-33-37-34-34-35-42-34-41-41-44-38-3C-2F-53-65-6E-64-43-6F-64-65-3E-0A-20-20-20-20-3C-52-65-63-65-69-76-65-43-6F-64-65-3E-43-38-44-43-42-44-33-39-2D-32-35-36-32-2D-34-39-39-30-2D-41-30-44-38-2D-39-42-33-32-43-43-35-31-30-36-43-41-3C-2F-52-65-63-65-69-76-65-43-6F-64-65-3E-0A-20-20-20-20-3C-54-79-70-65-3E-31-3C-2F-54-79-70-65-3E-0A-20-20-20-20-3C-49-74-65-6D-73-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-31-22-20-76-61-6C-75-65-3D-22-31-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-31-22-20-75-6E-69-74-3D-22-22-20-2F-3E-0A-20-20-20-20-3C-2F-49-74-65-6D-73-3E-0A-3C-2F-52-6F-62-6F-74-3E-0A-EB-90-EB-90-04-00-00-00-04-00-00-00-90-02-00-00-3C-3F-78-6D-6C-20-76-65-72-73-69-6F-6E-3D-22-31-2E-30-22-20-65-6E-63-6F-64-69-6E-67-3D-22-55-54-46-2D-38-22-20-3F-3E-0A-3C-52-6F-62-6F-74-3E-0A-20-20-20-20-3C-53-65-6E-64-43-6F-64-65-3E-43-30-38-35-34-36-42-35-2D-30-39-46-45-2D-30-32-31-31-2D-46-37-36-39-2D-45-33-37-34-34-35-42-34-41-41-44-38-3C-2F-53-65-6E-64-43-6F-64-65-3E-0A-20-20-20-20-3C-52-65-63-65-69-76-65-43-6F-64-65-3E-43-38-44-43-42-44-33-39-2D-32-35-36-32-2D-34-39-39-30-2D-41-30-44-38-2D-39-42-33-32-43-43-35-31-30-36-43-41-3C-2F-52-65-63-65-69-76-65-43-6F-64-65-3E-0A-20-20-20-20-3C-54-79-70-65-3E-32-3C-2F-54-79-70-65-3E-0A-20-20-20-20-3C-49-74-65-6D-73-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-31-22-20-76-61-6C-75-65-3D-22-30-2E-33-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-30-2E-33-6D-2F-73-22-20-75-6E-69-74-3D-22-6D-2F-73-22-20-2F-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-32-22-20-76-61-6C-75-65-3D-22-30-2E-30-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-30-2E-30-6D-22-20-75-6E-69-74-3D-22-6D-22-20-2F-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-33-22-20-76-61-6C-75-65-3D-22-30-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-30-25-25-22-20-75-6E-69-74-3D-22-25-25-22-20-2F-3E-0A-20-20-20-20-3C-2F-49-74-65-6D-73-3E-0A-3C-2F-52-6F-62-6F-74-3E-0A-00-00-EB-90-04-00-00-00-04-00-00-00-90-02-00-00-3C-3F-78-6D-6C-20-76-65-72-73-69-6F-6E-3D-22-31-2E-30-22-20-65-6E-63-6F-64-69-6E-67-3D-22-55-54-46-2D-38-22-20-3F-3E-0A-3C-52-6F-62-6F-74-3E-0A-20-20-20-20-3C-53-65-6E-64-43-6F-64-65-3E-43-30-38-35-34-36-42-35-2D-30-39-46-45-2D-30-32-31-31-2D-46-37-36-39-2D-45-33-37-34-34-35-42-34-41-41-44-38-3C-2F-53-65-6E-64-43-6F-64-65-3E-0A-20-20-20-20-3C-52-65-63-65-69-76-65-43-6F-64-65-3E-43-38-44-43-42-44-33-39-2D-32-35-36-32-2D-34-39-39-30-2D-41-30-44-38-2D-39-42-33-32-43-43-35-31-30-36-43-41-3C-2F-52-65-63-65-69-76-65-43-6F-64-65-3E-0A-20-20-20-20-3C-54-79-70-65-3E-32-3C-2F-54-79-70-65-3E-0A-20-20-20-20-3C-49-74-65-6D-73-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-31-22-20-76-61-6C-75-65-3D-22-30-2E-33-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-30-2E-33-6D-2F-73-22-20-75-6E-69-74-3D-22-6D-2F-73-22-20-2F-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-32-22-20-76-61-6C-75-65-3D-22-30-2E-30-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-30-2E-30-6D-22-20-75-6E-69-74-3D-22-6D-22-20-2F-3E-0A-20-20-20-20-20-20-20-20-3C-49-74-65-6D-20-72-6F-62-6F-74-5F-6E-61-6D-65-3D-22-E7-94-B5-E5-8A-9B-E5-B7-A1-E6-A3-80-E6-9C-BA-E5-99-A8-E4-BA-BA-22-20-72-6F-62-6F-74-5F-63-6F-64-65-3D-22-22-20-74-69-6D-65-3D-22-32-30-32-30-2D-31-32-2D-30-34-20-31-35-3A-35-34-3A-35-35-22-20-74-79-70-65-3D-22-33-22-20-76-61-6C-75-65-3D-22-30-22-20-76-61-6C-75-65-5F-75-6E-69-74-3D-22-30-25-25-22-20-75-6E-69-74-3D-22-25-25-22-20-2F-3E-0A-20-20-20-20-3C-2F-49-74-65-6D-73-3E-0A-3C-2F-52-6F-62-6F-74-3E-0A-EB-90".Replace("-", "");
            string str = "";
            Guid? g = JsonConvert.DeserializeObject<Guid?>(str);
            Console.WriteLine("测试" + (g == null ? "" : g.ToString()));
            Console.ReadLine();
        }

        /// <summary>
        /// 字符串转16进制字节数组
        /// </summary>
        /// <param name="hexString"></param>
        /// <returns></returns>
        private static byte[] strToToHexByte(string hexString)
        {
            hexString = hexString.Replace(" ", "").Replace("-", "");
            if ((hexString.Length % 2) != 0)
                hexString += " ";
            byte[] returnBytes = new byte[hexString.Length / 2];
            for (int i = 0; i < returnBytes.Length; i++)
                returnBytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
            return returnBytes;
        }
        public static byte[] StructToBytes(object structObj)
        {
            // 得到结构体的大小
            int size = Marshal.SizeOf(structObj);
            // 创建byte数组
            byte[] bytes = new byte[size];
            // 分配结构体大小的内存空间
            IntPtr structPtr = Marshal.AllocHGlobal(size);
            // 将结构体拷到分配好的内存空间
            Marshal.StructureToPtr(structObj, structPtr, false);
            //从 内存空间拷到byte数组
            Marshal.Copy(structPtr, bytes, 0, size);
            // 释放内存空间
            Marshal.FreeHGlobal(structPtr);
            // 返回byte数组
            return bytes;
        }
        ///   <summary>
        ///  byte数组转结构体
        ///   </summary>
        ///   <param name="bytes"> byte数组 </param>
        ///   <param name="type"> 结构体类型 </param>
        ///   <returns> 转换后的结构体 </returns>
        public static object BytesToStuct(byte[] bytes, Type type)
        {
            // 得到结构体的大小
            int size = Marshal.SizeOf(type);
            // byte数组长度小于结构体的大小
            if (size > bytes.Length)
            {
                // 返回空
                return null;
            }
            // 分配结构体大小的内存空间
            IntPtr structPtr = Marshal.AllocHGlobal(size);
            // 将byte数组拷到分配好的内存空间
            Marshal.Copy(bytes, 0, structPtr, size);
            // 将内存空间转换为目标结构体
            object obj = Marshal.PtrToStructure(structPtr, type);
            // 释放内存空间
            Marshal.FreeHGlobal(structPtr);
            // 返回结构体
            return obj;
        }
        private static List<TaskDemo> TestDemo(List<TaskDemo> demos)
        {

            return demos;
        }
        private static void Test()
        {
            try
            {
                Console.WriteLine("Test1：" + 1);
                return;
            }
            catch { }
            finally
            {
                Console.WriteLine("Test2：" + 2);
            }

        }
        private static ExpressionEvaluator engine = new ExpressionEvaluator();

        private static object EvalExpress(string sExpression)
        {
            return engine.Evaluate(sExpression);
        }

        private static bool RecursionAnalyze(ArrayList e)
        {
            bool isSuccess = true;
            if (e == null) return isSuccess;
            foreach (object f in e)
            {
                if (f is bool)
                {
                    isSuccess = isSuccess && Convert.ToBoolean(f);
                }
                else if (f is ArrayList)
                {
                    isSuccess = isSuccess || RecursionAnalyze(f as ArrayList);
                }
            }
            return isSuccess;
        }

        public static string MD5Encrypt64(string password)

        {
            string cl = password;
            //string pwd = "";
            MD5 md5 = MD5.Create(); //实例化一个md5对像
            SHA256 SHA256 = SHA256.Create();
            // 加密后是一个字节类型的数组，这里要注意编码UTF8/Unicode等的选择　
            byte[] s = SHA256.ComputeHash(Encoding.UTF8.GetBytes(cl));
            return Convert.ToBase64String(s);
        }

        public static bool HandleTask(TaskDemo t)
        {
            Task.Run(() =>
            {
                //TODO 巡检任务
            });
            return true;
        }
    }
    public class TaskDemo
    {
        public string ExWeek { get; set; }
        public string ExTime { get; set; }
    }
    public class TaskDemo1
    {
        public int Week { get; set; }
        public int Time { get; set; }
    }
    public class TaskDemo2
    {
        public string Week { get; set; }
        public string Time { get; set; }
    }
    public enum WeekEnum
    {
        [Description("星期一")]
        Monday = 1,

        [Description("星期二")]
        Tuesday = 2,

        [Description("星期三")]
        Wednesday = 3,

        [Description("星期四")]
        Thursday = 4,

        [Description("星期五")]
        Friday = 5,

        [Description("星期六")]
        Saturday = 6,

        [Description("星期日")]
        Sunday = 7,
    }
    public class TimeWorkService : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            int index = 0;
            while (!stoppingToken.IsCancellationRequested)
            {
                Console.WriteLine(index++);
                await Task.Delay(1000);
            }
        }
    }
}