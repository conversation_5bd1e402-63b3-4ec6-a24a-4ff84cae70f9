﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImGlyph
    {
        public ImGlyph()
        {
            ImGlyphHots = new HashSet<ImGlyphHot>();
        }

        public string Id { get; set; } = null!;
        public string? ParentId { get; set; }
        public string DiagId { get; set; } = null!;
        public int GlyphType { get; set; }
        public double? Left { get; set; }
        public double? Top { get; set; }
        public double? Right { get; set; }
        public double? Bottom { get; set; }
        public short? LineWidth { get; set; }
        public int? LineColor { get; set; }
        public int? BkColor { get; set; }
        public short? LineStyle { get; set; }
        public short? Transparent { get; set; }
        public short? Direction { get; set; }
        public string? Name { get; set; }
        public short? ZOrder { get; set; }
        public short? State { get; set; }
        public string? LinkDiagId { get; set; }
        public int? PwrOnColor { get; set; }
        public int? PwrOffColor { get; set; }
        public int? SwitchOnColor { get; set; }
        public int? SwitchOffColor { get; set; }
        public int? SwitchUnknownColor { get; set; }
        public int? Locked { get; set; }
        public double? PwrOnThreshold { get; set; }
        public string? Reserved1 { get; set; }
        public string? Reserved2 { get; set; }

        public virtual Im3posSwitch? Im3posSwitch { get; set; }
        public virtual ImDataViewer? ImDataViewer { get; set; }
        public virtual ImDiagram? ImDiagram { get; set; }
        public virtual ImGlyphDevDatum? ImGlyphDevDatum { get; set; }
        public virtual ImGrid? ImGrid { get; set; }
        public virtual ImLabel? ImLabel { get; set; }
        public virtual ICollection<ImGlyphHot> ImGlyphHots { get; set; }
    }
}
