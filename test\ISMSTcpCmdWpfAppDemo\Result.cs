﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISMSTcpCmdWpfAppDemo
{
    public struct WebResult<T> where T:class
    {
        public bool IsSuccess { get; set; }
        public int StatusCode { get; set; }
        public T Content { get; set; }
        public Exception Error { get; set; }


        public WebResult(bool success, int code, T content, Exception error = null)
        {
            IsSuccess = success;
            StatusCode = code;
            Content = content;
            Error = error;
        }

        // 预定义常用结果
        public static WebResult<T> Ok(T content) => new WebResult<T>(true, 200, content);
        public static WebResult<T> NotFound() => new WebResult<T>(false, 404, "Not Found" as T);
        public static WebResult<T> ErrorResult(int code, Exception ex) => new WebResult<T>(false, code, ex.Message as T, ex);
    }

}
