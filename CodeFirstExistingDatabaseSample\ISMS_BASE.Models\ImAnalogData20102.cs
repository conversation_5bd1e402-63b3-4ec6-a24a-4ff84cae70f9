﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImAnalogData20102
    {
        public int PuctgyCode { get; set; }
        public int DataCode { get; set; }
        public string DataName { get; set; } = null!;
        public string? Sym1 { get; set; }
        public double Cof1 { get; set; }
        public int Precise1 { get; set; }
        public string? Sym2 { get; set; }
        public double Cof2 { get; set; }
        public int Precise2 { get; set; }
        public string? EnumStr { get; set; }

        public virtual ImPuCtgy PuctgyCodeNavigation { get; set; } = null!;
    }
}
