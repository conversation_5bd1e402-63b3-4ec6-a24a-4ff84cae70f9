﻿<Window x:Class="UniqueSelectorGrid.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="唯一选择列表" Height="350" Width="500"
        xmlns:local="clr-namespace:UniqueSelectorGrid">

    <Grid Margin="10">
        <DataGrid ItemsSource="{Binding Rows}" AutoGenerateColumns="False" CanUserAddRows="True">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="数据来源" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding DataSourceCategoryName}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <ComboBox
                                ItemsSource="{Binding FilteredCategoryList}"
                                SelectedItem="{Binding DataSourceCategoryName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="设备名称" Binding="{Binding DeviceName}" Width="150"/>
                <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsEnabled}" Width="60"/>
                <DataGridTextColumn Header="类型" Binding="{Binding DataSourceType}" Width="100"/>
                <DataGridTextColumn Header="时间戳" Binding="{Binding Timestamp}" Width="160"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Window>
