﻿<UserControl x:Class="Yunda.SOMS.DataMonitoringServer.WPF.UserControlers.ConnectionGridControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
                xmlns:local="clr-namespace:Yunda.SOMS.DataMonitoringServer.WPF.UserControlers"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="1200">
    <UserControl.Resources>
        <Style x:Key="EditableCellStyle" TargetType="DataGridCell">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <EventSetter Event="PreviewMouseLeftButtonDown" Handler="DataGridCell_PreviewMouseLeftButtonDown"/>
        </Style>
        <Style TargetType="DataGridRow">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}" />
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                    <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    <Grid>
        <DataGrid Grid.Row="1" 
                  ItemsSource="{Binding Connections}"
                  SelectedItem="{Binding SelectedConnection, Mode=TwoWay}"
                  AutoGenerateColumns="False"
                  IsReadOnly="False"
                  SelectionMode="Single"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  CanUserSortColumns="False"
                  GridLinesVisibility="All"
                  BorderThickness="1"
                  BorderBrush="{DynamicResource MaterialDesignDivider}"
                  Background="{DynamicResource MaterialDesignPaper}"
                  RowBackground="{DynamicResource MaterialDesignPaper}"
                  AlternatingRowBackground="{DynamicResource MaterialDesignBackground}"
                  RowHeaderWidth="0"
                  EnableRowVirtualization="True"
                  RowHeight="32" 
                  Margin="5"
                  AlternationCount="100000"
                  CellStyle="{StaticResource EditableCellStyle}">

            

            <DataGrid.Columns>
                <DataGridTemplateColumn Header="序号" Width="60" IsReadOnly="True">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=AlternationIndex, Converter={StaticResource IndexPlusOneConverter}}" 
                       HorizontalAlignment="Center" />
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridCheckBoxColumn Header="选择" Binding="{Binding IsSelected}" Width="60"/>
                <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="120"/>
                <DataGridTextColumn Header="IP地址" Binding="{Binding Ip}" Width="110"/>
                <DataGridTextColumn Header="端口" Binding="{Binding Port}" Width="60"/>
                <DataGridTextColumn Header="RTU地址" Binding="{Binding RtuAddress}" Width="60"/>
                <DataGridTemplateColumn Header="保存模式" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding SaveModeDisplay}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <ComboBox ItemsSource="{Binding SaveModeList}"
                                      DisplayMemberPath="Description"
                                      SelectedValuePath="Value"
                                      SelectedValue="{Binding SaveMode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="状态" IsReadOnly="True" Binding="{Binding Status, UpdateSourceTrigger=PropertyChanged}" Width="60">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="TextWrapping" Value="NoWrap"/>
                            <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Status}" Value="已连接">
                                    <Setter Property="Foreground" Value="Green"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Status}" Value="未连接">
                                    <Setter Property="Foreground" Value="Gray"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Status}" Value="连接失败">
                                    <Setter Property="Foreground" Value="Red"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
             

                <DataGridTemplateColumn Header="数据来源" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding DataSourceCategoryDisplay}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <ComboBox ItemsSource="{Binding StaticCategories}"
                                      DisplayMemberPath="Description"
                                      SelectedValuePath="Value"
                                      SelectedValue="{Binding DataSourceCategoryName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="综自通信模式" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding DataAccessModeDisplay}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <ComboBox ItemsSource="{Binding DataAccessModes}"
                      DisplayMemberPath="Description"
                      SelectedValuePath="Value"
                      SelectedValue="{Binding DataAccessMode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>

