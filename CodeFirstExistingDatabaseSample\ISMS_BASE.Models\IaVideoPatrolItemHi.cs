﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class IaVideoPatrolItemHi
    {
        public string PatItemHisId { get; set; } = null!;
        public string PatHisId { get; set; } = null!;
        public string Piname { get; set; } = null!;
        public string? Description { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int PiseqNo { get; set; }
        public string State { get; set; } = null!;
        public string? PiId { get; set; }
        public string CompareRet { get; set; } = null!;
        public string? CompareMsg { get; set; }

        public virtual IaVideoPatrolHi PatHis { get; set; } = null!;
    }
}
