﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbSession
    {
        public string Sessionid { get; set; }
        public DateTime Lasttime { get; set; }
        public string Username { get; set; }
        public string Clientip { get; set; }
        public string Softinfo { get; set; }
    }
}
