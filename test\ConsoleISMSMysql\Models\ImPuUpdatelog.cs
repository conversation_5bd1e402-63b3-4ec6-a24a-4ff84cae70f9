﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImPuUpdatelog
    {
        public string Id { get; set; }
        public string Upddate { get; set; }
        public string Editor { get; set; }
        public string Upddesc { get; set; }
        public DateTime Createtime { get; set; }
    }
}
