﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;
using Polly.Timeout;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;


namespace Decompress
{
    class Program
    {
        public enum ResponseState
        {
            Success = 0,
            Error = 1,
            Timeout = 2,
        }
        public class MyClass
        {
            public string Name;
            public string Description;
        }
        private static string _path = "./appsettings.json";
        public struct IecServerData
        {
            /// <summary>
            /// 数据类型 1遥测 2遥信
            /// </summary>
            public int dataType { get; set; }
            /// <summary>
            /// 装置地址
            /// </summary>
            public byte devAddr { get; set; }
            /// <summary>
            /// cpu扇区号
            /// </summary>
            public byte devCpu { get; set; }
            /// <summary>
            /// 信息体地址
            /// </summary>
            public int inf { get; set; }
            /// <summary>
            /// 遥测值
            /// </summary>
            public float ycValue { get; set; }
            /// <summary>
            /// 遥信值
            /// </summary>
            public int yxValue { get; set; }
            /// <summary>
            /// 时间
            /// </summary>
            public DateTime dateTime { get; set; }
            /// <summary>
            /// 装置名称
            /// </summary>
            public string devName { get; set; }
          
        }
        static async Task Main(string[] args)
        {

            DateTime time = default;
            var timeStr = string.Format("{0:d2}:{1:d2}:{2:d2}.{3:d3}", 1, 1, 1, 1);
            if (!DateTime.TryParse(timeStr, out time))
            {
                time = DateTime.Now;
            }
            await Console.Out.WriteLineAsync();
            List<IecServerData> list = new List<IecServerData>();

            var test = list.FirstOrDefault();
            Console.WriteLine();
            bool bFlag = true;
            var test100 = bFlag.ToString().ToLower();
            Console.WriteLine(bFlag.ToString());

            MemoryCache cache = new MemoryCache(Options.Create<MemoryCacheOptions>(new MemoryCacheOptions()));
            cache.Set("test", new MyClass() { Name = "guor", Description = "test" }, TimeSpan.FromSeconds(15));
            Task.Delay(2000).Wait();
            var obj1 = cache.Get("test");
            cache.Remove("test");
            Console.WriteLine(obj1);
            var x1 = DateTime.Now - TimeSpan.FromSeconds(6);
            if (x1 > DateTime.Now)
            {
                Console.WriteLine("cao");
            }
            var str = "D:\\ISAS\\Data\\SysAttachment\\Robot\\Robot01\\2021\\11\\18\\6df578e3a90b4a42ba331addc3c7b1c3\\CCD\\21_Robot01_20211118140205.jpg";
            var parh = Path.GetDirectoryName(str);
            Console.WriteLine(parh);
            return;
            DateTime dt = DateTime.ParseExact("202109", "yyyyMM", null, DateTimeStyles.AssumeLocal);
            Console.WriteLine(dt);
            using (StreamReader reader = File.OpenText(_path))
            {
                JObject obj = (JObject)JToken.ReadFrom(new JsonTextReader(reader));
                //var token = obj.SelectToken(key);
                //token.
                //obj.SelectToken(key) = value;

                obj["ServiceConfig"]["SubstationName"] = "test";
                Console.WriteLine();
            }
            return;
            var policy = Policy<HttpWebResponse>.Handle<Exception>()
            .FallbackAsync(c =>
           {
               ////熔断后再来个通知
               //Console.WriteLine("熔断完成，通知一下");
               return null;
           });
            //设置一个超时时间，里面加个回调函数给个提示
            var pTimeout = Policy.TimeoutAsync(5, TimeoutStrategy.Pessimistic,
              async (context, timespan, task) =>
              {
                  Console.WriteLine("Timeout!");
                  //throw new Exception("Timeout!");
              });
            var excPolicy = policy.WrapAsync(pTimeout);
            var response = await excPolicy.ExecuteAsync(async () =>
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create("https://localhost:4431/swagger/index.html");
                var result = await request.GetResponseAsync();
                return (HttpWebResponse)result;
            });


            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                string rstStr = reader.ReadToEnd();
                try
                {
                    //rstObj = JsonConvert.DeserializeObject<T>(rstStr);
                    Console.WriteLine(rstStr);
                }
                catch
                {
                    throw new Exception("请检查主站url是否正确！");
                }
                reader.Close();
                reader.Dispose();
            }
            Console.WriteLine("hello");
            Console.ReadLine();

        }
        public static void DecompressFileWithBrotli(string fileName)
        {
            FileStream inputStream = File.OpenRead(fileName);
            using (MemoryStream outputStream = new MemoryStream())
            using (var compressStream = new BrotliStream(inputStream, CompressionMode.Decompress))
            {
                compressStream.CopyTo(outputStream);
                outputStream.Seek(0, SeekOrigin.Begin);
                inputStream.Dispose();
                string filenameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                FileStream fileStream = new FileStream(filenameWithoutExtension + ".xlsx", FileMode.OpenOrCreate);
                var bytes = new byte[outputStream.Length];
                outputStream.Read(bytes);
                fileStream.Write(bytes);
            }

        }
    }
}
