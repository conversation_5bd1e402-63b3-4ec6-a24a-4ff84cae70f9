﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class TbVirtualtable
    {
        public string Tablename { get; set; }
        public string Orderby { get; set; }
        public int Seqno { get; set; }
        public string Basetableid { get; set; }
        public string Derivetableid { get; set; }
        public string Ctgyinbasetable { get; set; }
        public string Ctgyfldinbasetable { get; set; }

        public virtual TbTable Basetable { get; set; }
        public virtual TbTable Derivetable { get; set; }
    }
}
