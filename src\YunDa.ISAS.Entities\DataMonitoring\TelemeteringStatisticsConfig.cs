using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using YunDa.ISAS.Entities.AuditCommon;
using YunDa.ISAS.Entities.AuditCommon.IAdudit;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;

namespace YunDa.ISAS.Entities.DataMonitoring
{
    /// <summary>
    /// 遥测统计配置表
    /// </summary>
    [Table("dm_telemetering_statistics_config")]
    public class TelemeteringStatisticsConfig : ISASAuditedEntity, IISASPassivable
    {
        /// <summary>
        /// 关联的遥测配置ID
        /// </summary>
        public virtual Guid TelemeteringConfigurationId { get; set; }

        /// <summary>
        /// 关联的遥测配置
        /// </summary>
        [ForeignKey("TelemeteringConfigurationId")]
        public virtual TelemeteringConfiguration TelemeteringConfiguration { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public virtual int StatisticsType { get; set; }

        /// <summary>
        /// 时间间隔类型
        /// </summary>
        public virtual int IntervalType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [DefaultValue(true)]
        public virtual bool IsActive { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(200)]
        public virtual string Description { get; set; }
    }
} 