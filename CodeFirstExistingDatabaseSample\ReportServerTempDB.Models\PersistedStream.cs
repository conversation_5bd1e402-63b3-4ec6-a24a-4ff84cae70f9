﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServerTempDB.Models
{
    public partial class PersistedStream
    {
        public string SessionId { get; set; } = null!;
        public int Index { get; set; }
        public byte[]? Content { get; set; }
        public string? Name { get; set; }
        public string? MimeType { get; set; }
        public string? Extension { get; set; }
        public string? Encoding { get; set; }
        public string? Error { get; set; }
        public int RefCount { get; set; }
        public DateTime ExpirationDate { get; set; }
    }
}
