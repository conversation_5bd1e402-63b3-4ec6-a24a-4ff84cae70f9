﻿using Abp.EntityFrameworkCore;
using Abp.EntityFrameworkCore.Configuration;
using Abp.Modules;
using Abp.Reflection.Extensions;
using CodeFirstExistingDatabaseSample.ISMS_BASE;
using Microsoft.Extensions.Configuration;
using YunDa.ISAS.Core;
using YunDa.ISAS.Core.Configuration;
using YunDa.ISAS.Core.Web;

namespace CodeFirstExistingDatabaseSample
{
    //[DependsOn(
    //     typeof(ISASCoreModule),
    //     typeof(AbpEntityFrameworkCoreModule))]
    public class ISMS_BASEEntityFrameworkCoreModule : AbpModule
    {
        public bool SkipDbSeed { get; set; }

        public override void PreInitialize()
        {
            var configuration = ISASConfiguration.Get(WebContentDirectoryFinder.CalculateContentRootFolder());

            Configuration.Modules.AbpEfCore().AddDbContext<ISMS_BASEContext>(options =>
            {
                DbContextOptionsConfigurer.Configure(options.DbContextOptions, configuration.GetConnectionString(ISASConsts.ISMS_BASESqlServerSettingStringKey));
            });
        }

        public override void Initialize()
        {
            IocManager.RegisterAssemblyByConvention(typeof(ISMS_BASEEntityFrameworkCoreModule).GetAssembly());
        }

        public override void PostInitialize()
        {
            //if (!SkipDbSeed)
            //{
            //    SeedHelper.SeedHostDb(IocManager);
            //}
        }
    }
}