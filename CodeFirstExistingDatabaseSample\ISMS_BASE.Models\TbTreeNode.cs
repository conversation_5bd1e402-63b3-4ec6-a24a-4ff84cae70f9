﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbTreeNode
    {
        public string NodeId { get; set; } = null!;
        public string Ctgy { get; set; } = null!;
        public string NodeName { get; set; } = null!;
        public string? ParentId { get; set; }
        public int SeqNo { get; set; }
        public string? Desc { get; set; }

        public virtual TbBuMan? TbBuMan { get; set; }
    }
}
