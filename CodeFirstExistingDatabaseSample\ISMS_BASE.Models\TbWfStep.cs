﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class TbWfStep
    {
        public TbWfStep()
        {
            TbBusiHistories = new HashSet<TbBusiHistory>();
            TbBusinesses = new HashSet<TbBusiness>();
        }

        public string Id { get; set; } = null!;
        public string WfId { get; set; } = null!;
        public string StepName { get; set; } = null!;
        public int SeqNo { get; set; }
        public string StatName { get; set; } = null!;
        public string Dllname { get; set; } = null!;
        public string Dllfunc { get; set; } = null!;

        public virtual TbWorkflow Wf { get; set; } = null!;
        public virtual TbWfStepUser? TbWfStepUser { get; set; }
        public virtual ICollection<TbBusiHistory> TbBusiHistories { get; set; }
        public virtual ICollection<TbBusiness> TbBusinesses { get; set; }
    }
}
