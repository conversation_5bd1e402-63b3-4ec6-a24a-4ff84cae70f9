﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ReportServerTempDB.Models
{
    public partial class TempCatalog
    {
        public TempCatalog()
        {
            TempDataSets = new HashSet<TempDataSet>();
            TempDataSources = new HashSet<TempDataSource>();
        }

        public string EditSessionId { get; set; } = null!;
        public Guid TempCatalogId { get; set; }
        public string ContextPath { get; set; } = null!;
        public string Name { get; set; } = null!;
        public byte[]? Content { get; set; }
        public string? Description { get; set; }
        public Guid? Intermediate { get; set; }
        public bool IntermediateIsPermanent { get; set; }
        public string? Property { get; set; }
        public string? Parameter { get; set; }
        public Guid OwnerId { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime ExpirationTime { get; set; }
        public byte[]? DataCacheHash { get; set; }

        public virtual ICollection<TempDataSet> TempDataSets { get; set; }
        public virtual ICollection<TempDataSource> TempDataSources { get; set; }
    }
}
