﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImVProtectDeviceTmp
    {
        public string Id { get; set; } = null!;
        public string DeviceName { get; set; } = null!;
        public int PuctgyCode { get; set; }
        public string PuctgyName { get; set; } = null!;
        public string Manufacturer { get; set; } = null!;
        public string DevCtgy { get; set; } = null!;
        public string Generation { get; set; } = null!;
        public int DzzoneCount { get; set; }
        public string? Model { get; set; }
        public int CanSwDzzone { get; set; }
        public int Support12yc { get; set; }
        public int SupportVersion { get; set; }
        public int SupportDkjl { get; set; }
        public int? StartOfDkjl { get; set; }
        public int? EndOfDkjl { get; set; }
        public string AnalogParseMode { get; set; } = null!;
        public string EventParseMode { get; set; } = null!;
        public int SupportGuZhangBg { get; set; }
        public int SupportShiJianBg { get; set; }
        public int SupportZiJianBg { get; set; }
        public int SupportLuBoWj { get; set; }
        public int SupportDz { get; set; }
        public int DzreadOnly { get; set; }
        public int SupportFhluBo { get; set; }
    }
}
