﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImManufacturer
    {
        public ImManufacturer()
        {
            ImAsdu140FltActTypes = new HashSet<ImAsdu140FltActType>();
            ImAsdu140FltCurrInfos = new HashSet<ImAsdu140FltCurrInfo>();
            ImAsdu142infos = new HashSet<ImAsdu142info>();
            ImCtrlWordDefs = new HashSet<ImCtrlWordDef>();
            ImDeviceDzenums = new HashSet<ImDeviceDzenum>();
            ImPuCtgies = new HashSet<ImPuCtgy>();
        }

        public string ManuCode { get; set; } = null!;
        public string ManuName { get; set; } = null!;

        public virtual ICollection<ImAsdu140FltActType> ImAsdu140FltActTypes { get; set; }
        public virtual ICollection<ImAsdu140FltCurrInfo> ImAsdu140FltCurrInfos { get; set; }
        public virtual ICollection<ImAsdu142info> ImAsdu142infos { get; set; }
        public virtual ICollection<ImCtrlWordDef> ImCtrlWordDefs { get; set; }
        public virtual ICollection<ImDeviceDzenum> ImDeviceDzenums { get; set; }
        public virtual ICollection<ImPuCtgy> ImPuCtgies { get; set; }
    }
}
