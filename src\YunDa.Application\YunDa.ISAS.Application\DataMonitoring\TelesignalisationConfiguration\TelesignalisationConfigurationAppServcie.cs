﻿//#define Test

using Abp.Auditing;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using YunDa.ISAS.Application.Core;
using YunDa.ISAS.Application.Core.Session;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.DataTransferObject.Account;
using YunDa.ISAS.DataTransferObject.CommonDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelecommandConfigurationDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto;
using YunDa.ISAS.DataTransferObject.DataMonitoring.TelesignalisationConfigurationDto.SearchCondition;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;

namespace YunDa.ISAS.Application.DataMonitoring
{
    /// <summary>
    /// 遥信配置管理服务
    /// </summary>
    [Description("遥信配置管理服务")]
    public class TelesignalisationConfigurationAppService : ISASAppServiceBase, ITelesignalisationConfigurationAppService
    {
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoResitory;
        private readonly IRepository<TelesignalisationConfiguration, Guid> _telesignalisationConfigurationResitory;
        private LoginUserOutput _currentUser;
        private readonly IRepository<EquipmentType, Guid> _equipmentTypeRepository;

        public TelesignalisationConfigurationAppService(
            IRepository<TelesignalisationConfiguration, Guid> telesignalisationConfigurationResitory,
            IRepository<EquipmentInfo, Guid> equipmentInfoResitory,
            IRepository<EquipmentType, Guid> equipmentTypeRepository,
            ISessionAppService sessionAppService) : base(sessionAppService)
        {
            _telesignalisationConfigurationResitory = telesignalisationConfigurationResitory;
            _equipmentInfoResitory = equipmentInfoResitory;
            _equipmentTypeRepository = equipmentTypeRepository;
            _currentUser = sessionAppService.GetCurrentLoginInformations().User;
        }


        #region 增/改

        /// <summary>
        /// 遥信数据增加或修改
        /// </summary>
        /// <param name="input">遥信数据体</param>
        /// <returns></returns>
        [HttpPost, Audited, Description("遥信数据增加或修改")]
        public async Task<RequestResult<TelesignalisationConfigurationOutput>> CreateOrUpdateAsync(
            EditTelesignalisationConfigurationInput input)
        {
            if (input == null) return null;
            if (_currentUser == null)
                _currentUser = base.GetCurrentUser();
            return input.Id != null
                ? await UpdateAsync(input).ConfigureAwait(false)
                : await CreateAsync(input).ConfigureAwait(false);
        }

        private async Task<RequestResult<TelesignalisationConfigurationOutput>> CreateAsync(
            EditTelesignalisationConfigurationInput input)
        {
            var rst = new RequestResult<TelesignalisationConfigurationOutput>();
            try
            {
                input.CreationTime = DateTime.Now;
                input.CreatorUserId = _currentUser.Id;
                var data = ObjectMapper.Map<TelesignalisationConfiguration>(input);
                data = await _telesignalisationConfigurationResitory.InsertAsync(data).ConfigureAwait(false);
                rst.ResultData = ObjectMapper.Map<TelesignalisationConfigurationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }

            return rst;
        }

        private async Task<RequestResult<TelesignalisationConfigurationOutput>> UpdateAsync(
            EditTelesignalisationConfigurationInput input)
        {
            var rst = new RequestResult<TelesignalisationConfigurationOutput>();
            try
            {
                var data = await _telesignalisationConfigurationResitory.FirstOrDefaultAsync(u => u.Id == input.Id)
                    .ConfigureAwait(false);
                input.CreationTime = data.CreationTime;
                input.CreatorUserId = data.CreatorUserId;
                input.LastModificationTime = DateTime.Now;
                input.CreatorUserId = _currentUser.Id;
                input.DataSourceCategory = data.DataSourceCategory;
                ObjectMapper.Map(input, data);
                rst.ResultData = ObjectMapper.Map<TelesignalisationConfigurationOutput>(data);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }

            return rst;
        }

        #endregion 增/改

        #region 删除

        /// <summary>
        /// 删除单个遥信数据
        /// </summary>
        /// <param name="id">遥信id</param>
        /// <returns></returns>
        [HttpPost]
        [Audited]
        [Description("删除单个遥信数据")]
        public async Task<RequestEasyResult> DeleteByIdAsync(Guid id)
        {
            var rst = new RequestEasyResult();
            try
            {
                await _telesignalisationConfigurationResitory.DeleteAsync(u => u.Id == id).ConfigureAwait(false);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }

            return rst;
        }

        /// <summary>
        /// 删除多个遥信数据
        /// </summary>
        /// <param name="ids">遥信id集合</param>
        /// <returns></returns>
        [HttpPost]
        [Audited]
        [Description("删除多个遥信数据")]
        public async Task<RequestEasyResult> DeleteByIdsAsync(List<Guid> ids)
        {
            var rst = new RequestEasyResult();
            try
            {
                //ids.ForEach(async t => await _telesignalisationConfigurationResitory.DeleteAsync(u => u.Id == t).ConfigureAwait(false));
                //ids.ForEach(t => _telesignalisationConfigurationResitory.Delete(u => u.Id == t));
                //await _telesignalisationConfigurationResitory.DeleteAsync(u => ids.Contains(u.Id));
                var entitys = _telesignalisationConfigurationResitory.GetAll().Where(u => ids.Contains(u.Id));
                _telesignalisationConfigurationResitory.GetDbContext().RemoveRange(entitys);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }

            return rst;
        }

        #endregion 删除

        #region 查询

        /// <summary>
        /// 查询遥信数据
        /// </summary>
        /// <param name="searchCondition">查询条件</param>
        /// <returns></returns>
        [HttpPost,  Description("查询遥信数据")]
        public RequestPageResult<TelesignalisationConfigurationOutput> FindDatas(
            PageSearchCondition<TelesignalisationConfigurationSearchConditionInput> searchCondition)
        {
            var rst = new RequestPageResult<TelesignalisationConfigurationOutput>();
            rst.Flag = true;
            try
            {
                var equipTypeG = _equipmentTypeRepository.GetAll()
                    .WhereIf(searchCondition.SearchCondition.EquipmentTypeTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.SearchCondition.EquipmentTypeTypeId);
                var datas = _telesignalisationConfigurationResitory.GetAllIncluding(t => t.EquipmentType,
                    t=>t.SelfCheckingConfiguration,
                    t => t.EquipmentInfo, t => t.DMAlarmCategory)
                    .Where(t => t.EquipmentType.IsActive)
                    ;
                datas = datas
                    .WhereIf(!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name),
                        t => t.Name.Contains(searchCondition.SearchCondition.Name))
                    .WhereIf(searchCondition.SearchCondition.TransformerSubstationId.HasValue,
                        t => t.TransformerSubstationId == searchCondition.SearchCondition.TransformerSubstationId)
                    .WhereIf(searchCondition.SearchCondition.EquipmentTypeId.HasValue,
                        t => t.EquipmentTypeId == searchCondition.SearchCondition.EquipmentTypeId)
                    .WhereIf(searchCondition.SearchCondition.DataSourceCategory.HasValue,
                        t => t.DataSourceCategory == searchCondition.SearchCondition.DataSourceCategory)
                    .WhereIf(searchCondition.SearchCondition.Id.HasValue,
                        t => t.Id == searchCondition.SearchCondition.Id).WhereIf(
                        searchCondition.SearchCondition.EquipmentInfoId.HasValue,
                        t => t.EquipmentInfoId == searchCondition.SearchCondition.EquipmentInfoId)
                    .Join(equipTypeG, f => f.EquipmentTypeId, s => s.Id, (f, s) => f);

                var rstDatas = datas.ToList().AsQueryable();
                if (!string.IsNullOrWhiteSpace(searchCondition.SearchCondition.Name))
                {
                    rstDatas = rstDatas.Where(t => t.Name.Contains(searchCondition.SearchCondition.Name));
                    //.Where(item => item.Name.ToArray().Intersect(searchCondition.SearchCondition.Name.ToArray()).Count() == searchCondition.SearchCondition.Name.ToArray().Distinct().Count()).AsQueryable();
                }

                rstDatas = rstDatas.OrderBy(t => t.EquipmentType.SeqNo).ThenBy(t => t.EquipmentInfo.SeqNo).ThenBy(t => t.DispatcherAddress);
                rst.TotalCount = rstDatas.Count();

                var skipCount = searchCondition.PageIndex <= 0
                    ? -1
                    : (searchCondition.PageIndex - 1) * searchCondition.PageSize;
                if (skipCount != -1)
                {
                    // lsit = lsit.PageBy(skipCount, searchCondition.PageSize);
                    rstDatas = rstDatas.Skip(skipCount).Take(searchCondition.PageSize);
                }
                rst.ResultData = ObjectMapper.Map<List<TelesignalisationConfigurationOutput>>(rstDatas);
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }
            return rst;
        }


        /// <summary>
        ///     查询所有遥信数据
        /// </summary>
        /// <returns></returns>
        [HttpGet, AbpAllowAnonymous]
        public RequestResult<List<TelesignalisationConfigurationOutput>> FindAllDatas()
        {
            var rst = new RequestResult<List<TelesignalisationConfigurationOutput>>();
            try
            {
                var datas = _telesignalisationConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo,
                    t => t.EquipmentType);
                datas = datas.OrderBy(t => t.EquipmentInfoId);
                datas = datas.OrderBy(t => t.SeqNo).ThenBy(t => t.InfoAddress);
                rst.ResultData = ObjectMapper.Map<List<TelesignalisationConfigurationOutput>>(datas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }

            return rst;
        }
        /// <summary>
        ///  查询所有遥信数据
        /// </summary>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        public RequestResult<List<TelesignalisationConfigurationOutput>> FindListDatas(TelesignalisationConfigurationSearchConditionInput input)
        {
            var rst = new RequestResult<List<TelesignalisationConfigurationOutput>>();
            try
            {
                var datas = _telesignalisationConfigurationResitory.GetAllIncluding(t => t.EquipmentInfo,
                    t=>t.SelfCheckingConfiguration,
                    t => t.EquipmentType)
                     .WhereIf(!string.IsNullOrWhiteSpace(input.Name),t => t.Name.Contains(input.Name))
                    .WhereIf(input.TransformerSubstationId.HasValue,t => t.TransformerSubstationId == input.TransformerSubstationId)
                    .WhereIf(input.EquipmentTypeId.HasValue,t => t.EquipmentTypeId == input.EquipmentTypeId)
                    .WhereIf(input.Id.HasValue,t => t.Id == input.Id)
                    .WhereIf(input.EquipmentInfoId.HasValue,t => t.EquipmentInfoId == input.EquipmentInfoId)
                    .WhereIf(input.IsHasSelfChecking.HasValue&& input.IsHasSelfChecking.Value, t => t.SelfCheckingConfigurationId!=default)
                    .WhereIf(input.IsVirtualDevice.HasValue , t => t.IsVirtualDevice == input.IsVirtualDevice)
                    ;
                datas = datas.OrderBy(t => t.EquipmentInfoId);
                datas = datas.OrderBy(t => t.SeqNo).ThenBy(t => t.InfoAddress);
                rst.ResultData = ObjectMapper.Map<List<TelesignalisationConfigurationOutput>>(datas);
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "查询所有遥信数据", ex);

            }

            return rst;
        }
        /// <summary>
        ///     查询遥信通信数据结构
        /// </summary>
        /// <returns></returns>
        [HttpPost, AbpAllowAnonymous]
        public RequestResult<dynamic> FindTelesignalisationConfigurationDataStruct(TelesignalisationConfigurationSearchConditionInput telesignalisationConfigurationSearchConditionInput)
        {
            var rst = new RequestResult<dynamic>();
            try
            {
                var datas = _telesignalisationConfigurationResitory.GetAll().
                    Where(t => t.IsActive && t.TransformerSubstationId == telesignalisationConfigurationSearchConditionInput.TransformerSubstationId).ToList();
                var TelesignalisationCount = datas.Count;
                var TelesignalisationStartAddress = datas.Min(t => t.DispatcherAddress);
                var Address = datas.First().DeviceAddress;
                rst.ResultData = new
                {
                    TelesignalisationCount,
                    TelesignalisationStartAddress,
                    Address,
                };
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }

            return rst;
        }
        /// <summary>
        /// 根据条件查询遥信下拉框内容
        /// </summary>
        /// <param name="searchCondition"></param>
        /// <returns></returns>
        [HttpPost]
        [ShowApi]
        [AllowAnonymous]
        public RequestResult<List<SelectModelOutput>> FindTelesignalisationConfigurationForSelect(SelectTelesignalisationSearchConditionInput searchCondition)
        {
            var rst = new RequestResult<List<SelectModelOutput>> { Flag = false };
            try
            {
                var datas = _telesignalisationConfigurationResitory.GetAll().Where(m => m.IsActive)
                     .WhereIf(searchCondition.EquipmentInfoId.HasValue, m => m.EquipmentInfoId == searchCondition.EquipmentInfoId)
                     .WhereIf(searchCondition.IsVirtualDevice.HasValue, m => m.IsVirtualDevice == searchCondition.IsVirtualDevice)
                     .WhereIf(searchCondition.TransformerSubstationId.HasValue, m => m.TransformerSubstationId == searchCondition.TransformerSubstationId);
                datas = datas.OrderBy(t => t.EquipmentInfoId);
                datas = datas.OrderBy(t => t.SeqNo).ThenBy(t => t.InfoAddress);

                rst.ResultData = datas.Select(m => new SelectModelOutput
                {
                    Key = m.Id,
                    Text = m.Name,
                    Value = m.Id.ToString().ToLower()
                }).ToList();
                rst.Flag = true;
            }
            catch (Exception ex)
            {
                rst.Message = ex.Message;
                rst.Flag = false;
                ToolLibrary.LogHelper.Log4Helper.Error(this.GetType(), "遥信模板管理服务", ex);

            }
            return rst;
        }

        public Task<RequestEasyResult> CreateByTemplatesAsync(EquipmentInfo equipmentInfo, IEnumerable<TelesignalisationTemplate> telesignalisationTemplates)
        {
            throw new NotImplementedException();
        }
        
        #endregion 查询
    }
}