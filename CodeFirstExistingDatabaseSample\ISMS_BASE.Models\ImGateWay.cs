﻿using System;
using System.Collections.Generic;

namespace CodeFirstExistingDatabaseSample.ISMS_BASE.Models
{
    public partial class ImGateWay
    {
        public ImGateWay()
        {
            ImProtectDevices = new HashSet<ImProtectDevice>();
        }

        public string GateWayId { get; set; } = null!;
        public string GateWayName { get; set; } = null!;
        public int PhyAddr { get; set; }
        public string StatCode { get; set; } = null!;
        public string? GatewayIp1 { get; set; }
        public int? GatewayPort1 { get; set; }
        public string? GatewayIp2 { get; set; }
        public int? GatewayPort2 { get; set; }
        public string? Protocol { get; set; }

        public virtual ImStation StatCodeNavigation { get; set; } = null!;
        public virtual ICollection<ImProtectDevice> ImProtectDevices { get; set; }
    }
}
