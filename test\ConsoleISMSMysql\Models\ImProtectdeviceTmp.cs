﻿using System;
using System.Collections.Generic;

// Code scaffolded by EF Core assumes nullable reference types (NRTs) are not used or disabled.
// If you have enabled NRTs for your project, then un-comment the following line:
// #nullable disable

namespace ConsoleISMSMysql.Models
{
    public partial class ImProtectdeviceTmp
    {
        public ImProtectdeviceTmp()
        {
            ImDevicedataTmp = new HashSet<ImDevicedataTmp>();
            ImDevicedzTmp = new HashSet<ImDevicedzTmp>();
        }

        public string Id { get; set; }
        public int Puctgycode { get; set; }
        public string Devicename { get; set; }

        public virtual ImPuCtgy PuctgycodeNavigation { get; set; }
        public virtual ICollection<ImDevicedataTmp> ImDevicedataTmp { get; set; }
        public virtual ICollection<ImDevicedzTmp> ImDevicedzTmp { get; set; }
    }
}
