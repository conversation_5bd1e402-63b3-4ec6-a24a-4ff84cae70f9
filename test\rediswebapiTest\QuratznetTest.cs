﻿using Microsoft.Extensions.Caching.Memory;
using Quartz;
using Quartz.Impl;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace rediswebapiTest
{
    public class QuratznetTest
    {
        public QuratznetTest()
        {
            StartAsync();
        }
        public async void StartAsync()
        {
            StdSchedulerFactory factory = new StdSchedulerFactory();

            // get a scheduler
            IScheduler scheduler = await factory.GetScheduler();
            await scheduler.Start();

            // define the job and tie it to our HelloJob class
            Guid guid = Guid.NewGuid();
            IJobDetail job = JobBuilder.Create<HelloJob>()
                .WithIdentity(guid.ToString())
                .Build();
            guid = Guid.NewGuid();
            // Trigger the job to run now, and then every 40 seconds
            ITrigger trigger = TriggerBuilder.Create()
                .WithIdentity(guid.ToString())
                .StartNow()
                .WithSimpleSchedule(x => x
                    .WithIntervalInSeconds(1)
                    .RepeatForever())
            .Build();

            await scheduler.ScheduleJob(job, trigger);
        }

    }
    public class HelloJob : IJob
    {
        private IMemoryCache _cache;
        public HelloJob(IMemoryCache cache)
        {
            _cache = cache;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            //_cache.TryGetValue();
            Debug.WriteLine(DateTime.Now);
        }
    }
}
