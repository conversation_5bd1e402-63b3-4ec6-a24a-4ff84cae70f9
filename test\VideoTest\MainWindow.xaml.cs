﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Interop;
using System.Xml.Linq;
using VideoSurveillanceAdapter;
using VideoSurveillanceAdapter.Model;

namespace VideoTest
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        bool isInit = false;
        public MainWindow()
        {
            InitializeComponent();
            Closed += MainWindow_Closed;
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            IntPtr hwnd = new WindowInteropHelper(this).Handle; //----this就是要获取句柄的窗体的类名；
            //IntPtr hwnd1 = ((HwndSource)PresentationSource.FromVisual(uielement)).Handle; //----uielement就是要获取句柄的控件，该控件必须继承自UIElement。
        }

        private void MainWindow_Closed(object sender, EventArgs e)
        {
            if (VideoPlayer == null) return;
            VideoPlayer.StopRealPlay();
            VideoPlayer.LogoutCamera();
            VideoPlayer.DisposeSDK();
            isInit = false;
        }

        private VideoPlayer VideoPlayer { get; set; }

        private void Login_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!isInit)
                    isInit = VideoPlayer.InitSDK();
                if (VideoPlayer == null)
                {
                    string DVRIPAddress = textBoxIP.Text; //设备IP地址或者域名
                    int DVRPortNumber = int.Parse(textBoxPort.Text);//设备服务端口号
                    string DVRUserName = textBoxUserName.Text;//设备登录用户名
                    string DVRPassword = textBoxPassword.Text;//设备登录密码
                    VideoPlayer = new VideoPlayer();
                    string brandStr = cbBrand.SelectedValue.ToString();
                    CameraBrand brand = CameraBrand.None;
                    Enum.TryParse(brandStr, false, out brand);
                    if (brand == CameraBrand.None) return;
                    VideoPlayer.CameraConfigure = new CameraConfigure
                    {
                        ID = Guid.NewGuid().ToString(),
                        Name = "相机1",
                        Ip = DVRIPAddress,
                        Port = DVRPortNumber,
                        UserName = DVRUserName,
                        Password = DVRPassword,
                        IsRecordVideo = false,
                        VideoPath = AppDomain.CurrentDomain.BaseDirectory + @"Video\",
                        CapturePath = AppDomain.CurrentDomain.BaseDirectory + @"\Capture\",
                        CameraBrand = brand,
                        PlayFrame = PlayFrame.MainFrame
                    };
                    if (!string.IsNullOrWhiteSpace(cbPlayChannel.Text))
                    {
                        VideoPlayer.CameraConfigure.PlayChannel = Convert.ToInt32(cbPlayChannel.Text);

                    }
                    VideoPlayer.PlayWndHandle = ((HwndSource)PresentationSource.FromVisual(this.WinBox)).Handle;
                }
                Button btn = sender as Button;
                if (btn.Content.ToString() == "登录")
                {
                    bool isSuccess = VideoPlayer.LoginCamera();
                    LogText.Text = isSuccess ? "登录成功！" : "登录失败！";
                    btn.Content = isSuccess ? "登出" : "登录";
                }
                else
                {
                    bool isSuccess = VideoPlayer.StopRealPlay();
                    LogText.Text = isSuccess ? "停止成功！" : "停止失败！";
                    isSuccess = VideoPlayer.LogoutCamera();
                    LogText.Text = isSuccess ? "登出成功！" : "登出失败！";
                    btn.Content = isSuccess ? "登录" : "登出";
                    if (isSuccess)
                        VideoPlayer = null;
                }
            }
            catch (Exception ex)
            {

                MessageBox.Show(ex.ToString());
            }
            
        }

        private void VideoPlay_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (VideoPlayer == null) return;
                Button btn = sender as Button;
                if (btn.Content.ToString() == "播放")
                {
                    VideoPlayer.CameraConfigure.PlayChannel =
                       Convert.ToInt32(String.IsNullOrWhiteSpace(cbPlayChannel.Text) ? "0" : cbPlayChannel.Text);
                    var channelArr = this.channels.Text.Split(new char[] { ',', '，' }).Select(t => int.Parse(t));
                    if (channelArr.Count() > 0)
                    {
                        int index = 0;
                        IntPtr intPtrBox = VideoPlayer.PlayWndHandle = ((HwndSource)PresentationSource.FromVisual(this.WinBox)).Handle;

                        foreach (var item in channelArr)
                        {
                            if (index == 1)
                            {
                                intPtrBox = this.WinBox1.Handle;
                            }
                            bool isSuccess = VideoPlayer.StartRealNVRPlay(item, intPtrBox);
                            //VideoPlayer.StartRealPlayM4(VideoPlayer.CameraConfigure.PlayChannel, WinBox1.Handle);
                            if (!Directory.Exists(VideoPlayer.CameraConfigure.VideoPath))
                                Directory.CreateDirectory(VideoPlayer.CameraConfigure.VideoPath);
                            string fullName = VideoPlayer.CameraConfigure.VideoPath + DateTime.Now.ToString("yyyyMMddHHmmss") + ".mp4";
                            VideoPlayer.RecordVideo(fullName);
                            LogText.Text = isSuccess ? "播放成功！" : "播放失败！";
                            btn.Content = isSuccess ? "停止" : "播放";
                            index++;
                        }
                    }


                }
                else
                {
                    VideoPlayer.StopRecordVideo();
                    bool isSuccess = VideoPlayer.StopRealPlay();
                    LogText.Text = isSuccess ? "停止成功！" : "停止失败！";
                    btn.Content = isSuccess ? "播放" : "停止";
                }
            }
            catch (Exception ex)
            {

                MessageBox.Show(ex.ToString());

            }

        }

        private void Capture_Click(object sender, RoutedEventArgs e)
        {
            string filePath = VideoPlayer.CameraConfigure.CapturePath;
            if (!Directory.Exists(filePath))
                Directory.CreateDirectory(filePath);
            var channels = this.channels.Text.Split(',');
            LogText.Text = "";
            foreach (var item in channels)
            {

                //Task.Run(() =>
                //{
                string fileName = VideoPlayer.CameraConfigure.Name + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "channel" + item + ".jpg";
                bool isSuccess = VideoPlayer.CaptureJPEGPicture(filePath + fileName, int.Parse(item));
                Dispatcher.Invoke(() => LogText.Text += isSuccess ? "截图成功！" : "截图失败！");
                //});
            }

        }

        private void MeasureTemperature_Click(object sender, RoutedEventArgs e)
        {
            if (VideoPlayer == null) return;
            RstText.Text = "正在获取温度，请等待... ...";
            int tNo = 0;
            if (int.TryParse(cbMeasureTemperature.Text, out _))
            {
                tNo = Convert.ToInt32(cbMeasureTemperature.Text);
            }
            int pNo = Convert.ToInt32(cbPreset.Text);
            Task<bool> goTask = Task.Run(() =>
            {
                bool isSccess = VideoPlayer.GotoPreset(pNo);
                Thread.Sleep(1000);
                return isSccess;
            });
            int channel = int.Parse(cbPlayChannel.Text);
            goTask.GetAwaiter().OnCompleted(() =>
            {
                if (goTask.Result)
                {
                    for (int i = 0; i < 1000; i++)
                    {
                        //Task<List<RealTimeTemperature>> task = Task.Run(() =>
                        //{
                        //    return VideoPlayer.GetRealTimeTemperature(pNo, tNo, channel);
                        //});
                       
                        List<RealTimeTemperature> tRst = VideoPlayer.GetRealTimeTemperature(pNo, tNo, channel);
                        StringBuilder rstSB = new StringBuilder();
                        foreach (var rst in tRst)
                        {
                            rstSB.Append("--------------------" + "\n");
                            rstSB.Append("时间：" + rst.MeasureTime.ToString("yyyy-MM-dd HH:mm:ss") + "\n");
                            rstSB.Append("预置点号：" + rst.PresetNo + "\n");
                            rstSB.Append("规则ID：" + rst.RuleID + "\n");
                            rstSB.Append("规则名称：" + rst.RuleName + "\n");
                            rstSB.Append("规则标定类型：" + rst.RuleType + "\n");
                            rstSB.Append("测温信息：" + rst.TemperatureValue + "（" + rst.TemperatureUnit + "）" + "\n");
                            rstSB.Append("--------------------");
                        }

                        Dispatcher.Invoke(() =>
                        {
                            RstText.Text = rstSB.ToString();
                            Debug.WriteLine(rstSB.ToString());

                        });
                        //task.GetAwaiter().OnCompleted(() =>
                        //{
                            
                        //});
                        Task.Delay(1000).Wait();
                    }
           
                }
            });
        }

        //private void cbPreset_SelectionChanged(object sender, SelectionChangedEventArgs e)
        //{
        //    if (VideoPlayer == null) return;

        //    int pNo = Convert.ToInt32(cbPreset.Text);
        //    VideoPlayer.GotoPreset(pNo);
        //}
        #region
        private void Up_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Up, 0);
        }

        private void Up_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Up, 1);
        }

        private void Down_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Down, 0);
        }

        private void Down_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Down, 1);
        }

        private void Left_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Left, 0);
        }

        private void Left_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Left, 1);
        }

        private void Right_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Right, 0);
        }

        private void Right_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            PTZControl(PTZCategory.Right, 1);
        }
        private void PTZControl(PTZCategory category, uint dwStop)
        {
            if (VideoPlayer == null) return;
            VideoPlayer.PTZControl(category, dwStop);
        }
        #endregion


        private void TurnTopreset_Click(object sender, RoutedEventArgs e)
        {
            if (VideoPlayer == null) return;

            int pNo = Convert.ToInt32(cbPreset.Text);
            var success = VideoPlayer.GotoPreset(pNo);
            Console.WriteLine(success);
        }

        private void Setpreset_Click(object sender, RoutedEventArgs e)
        {
            if (VideoPlayer == null) return;

            int pNo = Convert.ToInt32(cbPreset.Text);
            int channel = int.Parse(cbPlayChannel.Text);
            var success = VideoPlayer.SetPreset(pNo, channel, "测试预置位名");
            Console.WriteLine(success);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DownloadPlayback_Click(object sender, RoutedEventArgs e)
        {
            if (VideoPlayer == null) return;

            bool isSccess = VideoPlayer.DownloadFileByTime(Path.GetFullPath("./video/video.mp4"),
                DateTime.Now - TimeSpan.FromMinutes(120),
                DateTime.Now - TimeSpan.FromMinutes(18),
                new DownloadFileCallBack(arg =>
                {
                    Debug.WriteLine("Sccess");
                }), int.Parse(cbPlayChannel.Text));
            Dispatcher.Invoke(() =>
            {
                RstText.Text = isSccess.ToString();
            });
        }

        private void Playbackt_Click(object sender, RoutedEventArgs e)
        {
            //VideoPlayer.st
            try
            {
                var channelArr = this.channels.Text.Split(new char[] { ',', '，' }).Select(t => int.Parse(t));
                if (channelArr.Count() > 0)
                {
                    int index = 0;
                    IntPtr intPtrBox = ((HwndSource)PresentationSource.FromVisual(this.WinBox)).Handle;//.Handle;

                    foreach (var item in channelArr)
                    {
                        if (index == 1)
                        {
                            intPtrBox = this.WinBox1.Handle;
                        }
                        var startDate = DateTime.Parse(startTime.Text);
                        var endDate = DateTime.Parse(endTime.Text);

                        bool isSuccess = VideoPlayer.StartPlayBackByTime(startDate, endDate, item, intPtrBox);
                      
                        LogText.Text = isSuccess ? "播放成功！" : "播放失败！";
                       
                        index++;
                    }
                }
               

            }
            catch (Exception ex)
            {

                MessageBox.Show(ex.Message);
            }
        }

        private void StartRealPlayM4_Click(object sender, RoutedEventArgs e)
        {
            VideoPlayer.CameraConfigure.PlayChannel = Convert.ToInt32(cbPlayChannel.Text);
            bool isSuccess = VideoPlayer.StartRealPlayM4(VideoPlayer.CameraConfigure.PlayChannel, WinBox1.Handle);
        }

        private void ShowOSD_Click(object sender, RoutedEventArgs e)
        {
            VideoPlayer.ShowOSDInfo(true);
        }

        private void CloseOSD_Click(object sender, RoutedEventArgs e)
        {
            VideoPlayer.ShowOSDInfo(false);

        }
    }
}